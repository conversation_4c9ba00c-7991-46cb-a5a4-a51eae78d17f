"""
Memory-Efficient Augmentation Pipeline for Large Medical Imaging Datasets

This module provides comprehensive data augmentation for medical imaging datasets
with memory optimization for 100GB+ datasets. Supports both streaming and 
cached augmentation strategies.

Key Features:
- Memory-efficient augmentation during streaming
- Medical imaging specific augmentations
- Configurable augmentation policies
- Real-time augmentation monitoring
- GPU-accelerated augmentations where applicable
"""

# pyright: reportCallIssue=false

import json
import time
import random
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Callable, Any
from dataclasses import dataclass
from enum import Enum

import numpy as np
import cv2
import tensorflow as tf
import albumentations as A

from ...core.utils.logging_config import get_logger
from .memory_monitor import MemoryMonitor

logger = get_logger(__name__)


class AugmentationMode(Enum):
    """Augmentation execution modes."""
    STREAMING = "streaming"      # Apply during data loading
    CACHED = "cached"           # Pre-compute and cache augmented versions
    MIXED = "mixed"             # Hybrid approach
    DISABLED = "disabled"       # No augmentation


@dataclass
class AugmentationConfig:
    """Configuration for augmentation pipeline."""
    
    # Basic augmentation parameters
    rotation_range: float = 15.0
    width_shift_range: float = 0.1
    height_shift_range: float = 0.1
    zoom_range: float = 0.1
    horizontal_flip: bool = True
    vertical_flip: bool = False
    
    # Intensity augmentations
    brightness_range: Tuple[float, float] = (0.8, 1.2)
    contrast_range: Tuple[float, float] = (0.8, 1.2)
    saturation_range: Tuple[float, float] = (0.9, 1.1)
    hue_shift_limit: float = 10.0
    
    # Medical-specific augmentations
    elastic_transform: bool = True
    elastic_alpha: float = 1.0
    elastic_sigma: float = 50.0
    grid_distortion: bool = True
    optical_distortion: bool = True
    
    # Noise and blur
    gaussian_noise: bool = True
    noise_variance: Tuple[float, float] = (10.0, 50.0)
    gaussian_blur: bool = True
    blur_limit: int = 3
    
    # Frequency and probability
    augmentation_probability: float = 0.8
    max_augmentations_per_image: int = 3
    
    # Memory and performance
    cache_augmented_samples: bool = True
    max_cache_size_gb: float = 2.0
    use_gpu_acceleration: bool = True
    
    # Medical imaging specific
    preserve_aspect_ratio: bool = True
    maintain_intensity_range: bool = True
    anatomical_consistency: bool = True


class MedicalImageAugmentor:
    """Medical imaging specific augmentation operations."""
    
    def __init__(self, config: AugmentationConfig):
        self.config = config
        
        # Build Albumentations pipeline
        self.albumentations_pipeline = self._build_albumentations_pipeline()
        
        # Build TensorFlow pipeline for GPU acceleration
        self.tf_pipeline = self._build_tensorflow_pipeline()
        
        logger.info("Medical image augmentor initialized")
    
    def _build_albumentations_pipeline(self) -> A.Compose:
        """Build Albumentations augmentation pipeline."""
        
        transforms = []
        
        # Geometric augmentations
        if self.config.rotation_range > 0:
            transforms.append(
                A.Rotate(
                    limit=self.config.rotation_range,
                    interpolation=cv2.INTER_LINEAR,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    p=0.5
                )
            )
        
        if self.config.width_shift_range > 0 or self.config.height_shift_range > 0:
            transforms.append(
                A.ShiftScaleRotate(
                    shift_limit_x=self.config.width_shift_range,
                    shift_limit_y=self.config.height_shift_range,
                    scale_limit=self.config.zoom_range,
                    rotate_limit=0,  # Rotation handled separately
                    interpolation=cv2.INTER_LINEAR,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    p=0.5
                )
            )
        
        # Flipping
        if self.config.horizontal_flip:
            transforms.append(A.HorizontalFlip(p=0.5))
        
        if self.config.vertical_flip:
            transforms.append(A.VerticalFlip(p=0.3))
        
        # Elastic deformations (medical imaging specific)
        if self.config.elastic_transform:
            transforms.append(
                A.ElasticTransform(
                    alpha=self.config.elastic_alpha,
                    sigma=self.config.elastic_sigma,
                    alpha_affine=50,
                    interpolation=cv2.INTER_LINEAR,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    p=0.3
                )
            )
        
        # Grid and optical distortions
        if self.config.grid_distortion:
            transforms.append(
                A.GridDistortion(
                    num_steps=5,
                    distort_limit=0.1,
                    interpolation=cv2.INTER_LINEAR,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    p=0.3
                )
            )
        
        if self.config.optical_distortion:
            transforms.append(
                A.OpticalDistortion(
                    distort_limit=0.1,
                    shift_limit=0.1,
                    interpolation=cv2.INTER_LINEAR,
                    border_mode=cv2.BORDER_CONSTANT,
                    value=0,
                    p=0.3
                )
            )
        
        # Intensity augmentations
        transforms.append(
            A.OneOf([
                A.RandomBrightnessContrast(
                    brightness_limit=self.config.brightness_range,
                    contrast_limit=self.config.contrast_range,
                    p=1.0
                ),
                A.RandomGamma(
                    gamma_limit=(0.8, 1.2),
                    p=1.0
                ),
                A.CLAHE(
                    clip_limit=2.0,
                    tile_grid_size=(8, 8),
                    p=1.0
                )
            ], p=0.6)
        )
        
        # Noise augmentations
        if self.config.gaussian_noise:
            transforms.append(
                A.GaussNoise(
                    var_limit=self.config.noise_variance,
                    mean=0,
                    per_channel=True,
                    p=0.3
                )
            )
        
        # Blur augmentations
        if self.config.gaussian_blur:
            transforms.append(
                A.OneOf([
                    A.GaussianBlur(blur_limit=self.config.blur_limit, p=1.0),
                    A.MedianBlur(blur_limit=3, p=1.0),
                    A.MotionBlur(blur_limit=3, p=1.0)
                ], p=0.2)
            )
        
        # Medical-specific augmentations
        transforms.extend([
            # Coarse dropout (simulating missing tissue/artifacts)
            A.CoarseDropout(
                max_holes=8,
                max_height=8,
                max_width=8,
                min_holes=1,
                min_height=4,
                min_width=4,
                fill_value=0,
                p=0.2
            ),
            
            # Random shadows (simulating imaging artifacts)
            A.RandomShadow(
                shadow_roi=(0, 0, 1, 1),
                num_shadows_lower=1,
                num_shadows_upper=2,
                shadow_dimension=5,
                p=0.1
            )
        ])
        
        return A.Compose(transforms, p=self.config.augmentation_probability)
    
    def _build_tensorflow_pipeline(self) -> Callable:
        """Build TensorFlow augmentation pipeline for GPU acceleration."""
        
        def tf_augment(image: Any) -> Any:
            """TensorFlow-based augmentation function."""
            
            # Random brightness and contrast
            if random.random() < 0.5:
                image = tf.image.random_brightness(image, max_delta=0.2)
                image = tf.image.random_contrast(image, lower=0.8, upper=1.2)
            
            # Random flip
            if self.config.horizontal_flip and random.random() < 0.5:
                image = tf.image.flip_left_right(image)
            
            if self.config.vertical_flip and random.random() < 0.3:
                image = tf.image.flip_up_down(image)
            
            # Skip rotation in TensorFlow pipeline (use Albumentations for this)
            # tf.contrib.image.rotate is deprecated and complex to implement manually
            
            # Ensure values are in valid range
            image = tf.clip_by_value(image, 0.0, 1.0)
            
            return image
        
        return tf_augment
    
    def augment_image(self, image: np.ndarray, method: str = "albumentations") -> np.ndarray:
        """Apply augmentation to a single image."""
        
        if method == "albumentations":
            return self._augment_with_albumentations(image)
        elif method == "tensorflow":
            return self._augment_with_tensorflow(image)
        else:
            raise ValueError(f"Unknown augmentation method: {method}")
    
    def _augment_with_albumentations(self, image: np.ndarray) -> np.ndarray:
        """Apply Albumentations augmentation."""
        
        # Ensure image is in correct format
        if len(image.shape) == 2:
            image = np.expand_dims(image, axis=-1)
        
        # Convert to uint8 if needed (Albumentations requirement)
        if image.dtype != np.uint8:
            if image.max() <= 1.0:
                image = (image * 255).astype(np.uint8)
            else:
                image = image.astype(np.uint8)
        
        # Apply augmentation
        augmented = self.albumentations_pipeline(image=image)
        augmented_image = augmented['image']
        
        # Convert back to float32 and normalize
        if augmented_image.dtype == np.uint8:
            augmented_image = augmented_image.astype(np.float32) / 255.0
        
        return augmented_image
    
    def _augment_with_tensorflow(self, image: np.ndarray) -> np.ndarray:
        """Apply TensorFlow augmentation."""
        
        # Convert to tensor
        image_tensor = tf.convert_to_tensor(image, dtype=tf.float32)
        
        # Ensure correct shape
        if len(image_tensor.shape) == 2:
            image_tensor = tf.expand_dims(image_tensor, axis=-1)
        
        # Apply augmentation
        augmented_tensor = self.tf_pipeline(image_tensor)
        
        # Convert back to numpy
        return augmented_tensor.numpy()
    
    def validate_augmentation(self, original: np.ndarray, augmented: np.ndarray) -> bool:
        """Validate that augmentation preserves medical image integrity."""
        
        # Check that image dimensions are preserved
        if original.shape != augmented.shape:
            logger.warning("Augmentation changed image shape")
            return False
        
        # Check intensity range preservation (if enabled)
        if self.config.maintain_intensity_range:
            orig_min, orig_max = original.min(), original.max()
            aug_min, aug_max = augmented.min(), augmented.max()
            
            # Allow some tolerance for intensity changes
            if abs(aug_min - orig_min) > 0.3 or abs(aug_max - orig_max) > 0.3:
                logger.warning("Augmentation significantly changed intensity range")
                return False
        
        # Check for anatomical consistency (basic checks)
        if self.config.anatomical_consistency:
            # Ensure image isn't completely corrupted
            if np.isnan(augmented).any() or np.isinf(augmented).any():
                logger.warning("Augmentation produced invalid values")
                return False
            
            # Check that significant features are preserved
            orig_mean = original.mean()
            aug_mean = augmented.mean()
            if abs(aug_mean - orig_mean) > orig_mean * 0.5:
                logger.warning("Augmentation changed image too drastically")
                return False
        
        return True


class AugmentationCache:
    """Cache for augmented images to reduce computation."""
    
    def __init__(self, max_size_gb: float = 2.0):
        self.max_size_bytes = int(max_size_gb * 1024**3)
        self.cache = {}
        self.current_size_bytes = 0
        self.access_count = {}
        
        logger.info(f"Augmentation cache initialized with {max_size_gb}GB limit")
    
    def get(self, key: str) -> Optional[np.ndarray]:
        """Get cached augmented image."""
        if key in self.cache:
            self.access_count[key] = self.access_count.get(key, 0) + 1
            return self.cache[key]
        return None
    
    def put(self, key: str, image: np.ndarray):
        """Cache augmented image."""
        
        image_size = image.nbytes
        
        # Check if we need to free space
        while self.current_size_bytes + image_size > self.max_size_bytes and self.cache:
            self._evict_least_used()
        
        # Store image
        self.cache[key] = image.copy()
        self.current_size_bytes += image_size
        self.access_count[key] = 1
    
    def _evict_least_used(self):
        """Remove least recently used item from cache."""
        if not self.cache:
            return
        
        # Find least used item
        least_used_key = min(self.access_count.keys(), key=lambda k: self.access_count[k])
        
        # Remove from cache
        if least_used_key in self.cache:
            image_size = self.cache[least_used_key].nbytes
            del self.cache[least_used_key]
            del self.access_count[least_used_key]
            self.current_size_bytes -= image_size
    
    def clear(self):
        """Clear entire cache."""
        self.cache.clear()
        self.access_count.clear()
        self.current_size_bytes = 0
    
    def get_stats(self) -> Dict:
        """Get cache statistics."""
        return {
            "cache_size_mb": self.current_size_bytes / (1024**2),
            "max_size_mb": self.max_size_bytes / (1024**2),
            "num_items": len(self.cache),
            "hit_rate": sum(self.access_count.values()) / max(len(self.cache), 1)
        }


class AugmentationPipeline:
    """Main augmentation pipeline with memory optimization."""
    
    def __init__(
        self,
        config: Optional[AugmentationConfig] = None,
        mode: AugmentationMode = AugmentationMode.STREAMING,
        memory_limit_gb: float = 16.0
    ):
        self.config = config or AugmentationConfig()
        self.mode = mode
        
        # Initialize components
        self.augmentor = MedicalImageAugmentor(self.config)
        self.cache = AugmentationCache(self.config.max_cache_size_gb) if self.config.cache_augmented_samples else None
        
        # Memory monitoring
        self.memory_monitor = MemoryMonitor(
            max_ram_usage_gb=memory_limit_gb,
            max_vram_usage_gb=8.0
        )
        
        # Statistics tracking
        self.stats = {
            "total_augmentations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "validation_failures": 0,
            "processing_time_ms": []
        }
        
        logger.info(f"Augmentation pipeline initialized in {mode.value} mode")
    
    def augment_batch(
        self,
        images: np.ndarray,
        labels: Optional[np.ndarray] = None,
        file_paths: Optional[List[str]] = None
    ) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Augment a batch of images."""
        
        if self.mode == AugmentationMode.DISABLED:
            return images, labels
        
        batch_size = images.shape[0]
        augmented_images = np.zeros_like(images)
        
        for i in range(batch_size):
            # Check memory before processing each image
            if not self.memory_monitor.check_memory_limits()["overall_within_limit"]:
                logger.warning("Memory limit reached, skipping augmentation")
                augmented_images[i] = images[i]
                continue
            
            start_time = time.time()
            
            # Generate cache key if caching is enabled
            cache_key = None
            if self.cache and file_paths:
                cache_key = f"{file_paths[i]}_{hash(str(self.config))}"
                cached_image = self.cache.get(cache_key)
                
                if cached_image is not None:
                    augmented_images[i] = cached_image
                    self.stats["cache_hits"] += 1
                    continue
                else:
                    self.stats["cache_misses"] += 1
            
            # Apply augmentation
            try:
                augmented_image = self.augmentor.augment_image(images[i])
                
                # Validate augmentation
                if self.augmentor.validate_augmentation(images[i], augmented_image):
                    augmented_images[i] = augmented_image
                    
                    # Cache if enabled
                    if self.cache and cache_key:
                        self.cache.put(cache_key, augmented_image)
                else:
                    augmented_images[i] = images[i]  # Use original if validation fails
                    self.stats["validation_failures"] += 1
                
            except Exception as e:
                logger.warning(f"Augmentation failed for image {i}: {e}")
                augmented_images[i] = images[i]  # Fallback to original
            
            # Track timing
            processing_time = (time.time() - start_time) * 1000
            self.stats["processing_time_ms"].append(processing_time)
            self.stats["total_augmentations"] += 1
        
        return augmented_images, labels
    
    def augment_single(
        self,
        image: np.ndarray,
        file_path: Optional[str] = None
    ) -> np.ndarray:
        """Augment a single image."""
        
        if self.mode == AugmentationMode.DISABLED:
            return image
        
        # Check memory
        if not self.memory_monitor.check_memory_limits()["overall_within_limit"]:
            logger.warning("Memory limit reached, returning original image")
            return image
        
        # Try cache first
        cache_key = None
        if self.cache and file_path:
            cache_key = f"{file_path}_{hash(str(self.config))}"
            cached_image = self.cache.get(cache_key)
            
            if cached_image is not None:
                self.stats["cache_hits"] += 1
                return cached_image
            else:
                self.stats["cache_misses"] += 1
        
        # Apply augmentation
        try:
            start_time = time.time()
            augmented_image = self.augmentor.augment_image(image)
            
            # Validate
            if self.augmentor.validate_augmentation(image, augmented_image):
                # Cache if enabled
                if self.cache and cache_key:
                    self.cache.put(cache_key, augmented_image)
                
                processing_time = (time.time() - start_time) * 1000
                self.stats["processing_time_ms"].append(processing_time)
                self.stats["total_augmentations"] += 1
                
                return augmented_image
            else:
                self.stats["validation_failures"] += 1
                return image
                
        except Exception as e:
            logger.warning(f"Augmentation failed: {e}")
            return image
    
    def get_tf_dataset_augmentation(self) -> Callable:
        """Get TensorFlow dataset augmentation function."""
        
        def tf_augment_fn(image: Any, label: Any) -> Tuple[Any, Any]:
            """TensorFlow dataset augmentation function."""
            
            if self.mode == AugmentationMode.DISABLED:
                return image, label
            
            # Apply TensorFlow-based augmentation
            augmented_image = self.augmentor.tf_pipeline(image)
            
            return augmented_image, label
        
        return tf_augment_fn
    
    def update_config(self, new_config: AugmentationConfig):
        """Update augmentation configuration."""
        self.config = new_config
        self.augmentor = MedicalImageAugmentor(new_config)
        
        # Clear cache as config changed
        if self.cache:
            self.cache.clear()
        
        logger.info("Augmentation configuration updated")
    
    def get_stats(self) -> Dict:
        """Get augmentation pipeline statistics."""
        
        base_stats = {
            "mode": self.mode.value,
            "total_augmentations": self.stats["total_augmentations"],
            "validation_failures": self.stats["validation_failures"],
            "failure_rate": self.stats["validation_failures"] / max(self.stats["total_augmentations"], 1),
            "avg_processing_time_ms": np.mean(self.stats["processing_time_ms"]) if self.stats["processing_time_ms"] else 0,
            "memory_usage": self.memory_monitor.get_current_memory_info()
        }
        
        # Add cache stats if available
        if self.cache:
            cache_stats = self.cache.get_stats()
            base_stats.update({
                "cache_hits": self.stats["cache_hits"],
                "cache_misses": self.stats["cache_misses"],
                "cache_hit_rate": self.stats["cache_hits"] / max(self.stats["cache_hits"] + self.stats["cache_misses"], 1),
                "cache_size_mb": cache_stats["cache_size_mb"],
                "cache_utilization": cache_stats["cache_size_mb"] / cache_stats["max_size_mb"]
            })
        
        return base_stats
    
    def clear_cache(self):
        """Clear augmentation cache."""
        if self.cache:
            self.cache.clear()
            logger.info("Augmentation cache cleared")
    
    def save_config(self, filepath: str):
        """Save augmentation configuration to file."""
        config_dict = {
            "rotation_range": self.config.rotation_range,
            "width_shift_range": self.config.width_shift_range,
            "height_shift_range": self.config.height_shift_range,
            "zoom_range": self.config.zoom_range,
            "horizontal_flip": self.config.horizontal_flip,
            "vertical_flip": self.config.vertical_flip,
            "brightness_range": self.config.brightness_range,
            "contrast_range": self.config.contrast_range,
            "elastic_transform": self.config.elastic_transform,
            "gaussian_noise": self.config.gaussian_noise,
            "augmentation_probability": self.config.augmentation_probability,
            "max_augmentations_per_image": self.config.max_augmentations_per_image
        }
        
        with open(filepath, 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        logger.info(f"Augmentation config saved to {filepath}")
    
    def load_config(self, filepath: str):
        """Load augmentation configuration from file."""
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        
        # Update config with loaded values
        for key, value in config_dict.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        # Reinitialize augmentor with new config
        self.augmentor = MedicalImageAugmentor(self.config)
        
        logger.info(f"Augmentation config loaded from {filepath}")


# Utility functions
def create_augmentation_pipeline(
    config: Optional[Dict] = None,
    mode: str = "streaming",
    memory_limit_gb: float = 16.0
) -> AugmentationPipeline:
    """Create augmentation pipeline with default configuration."""
    
    # Convert config dict to AugmentationConfig if provided
    aug_config = AugmentationConfig()
    if config:
        for key, value in config.items():
            if hasattr(aug_config, key):
                setattr(aug_config, key, value)
    
    # Convert mode string to enum
    mode_enum = AugmentationMode(mode.lower())
    
    return AugmentationPipeline(
        config=aug_config,
        mode=mode_enum,
        memory_limit_gb=memory_limit_gb
    )


def test_augmentation_pipeline(
    sample_image_path: str,
    output_dir: str = "test_augmentations",
    num_augmentations: int = 10
):
    """Test the augmentation pipeline with a sample image."""
    
    try:
        import matplotlib.pyplot as plt  # type: ignore  # noqa: F401
        _MATPLOTLIB_AVAILABLE = True
    except ImportError:
        _MATPLOTLIB_AVAILABLE = False
        plt = None  # type: ignore  # noqa: F841
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Load sample image
    image = cv2.imread(sample_image_path, cv2.IMREAD_GRAYSCALE)
    if image is None:
        raise ValueError(f"Could not load image: {sample_image_path}")
    
    # Normalize to [0, 1]
    image = image.astype(np.float32) / 255.0
    
    # Create augmentation pipeline
    pipeline = create_augmentation_pipeline(mode="streaming")
    
    # Generate augmentations
    logger.info(f"Generating {num_augmentations} augmentations...")
    
    for i in range(num_augmentations):
        augmented = pipeline.augment_single(image)
        
        # Save augmented image
        output_file = output_path / f"augmented_{i:02d}.png"
        cv2.imwrite(str(output_file), (augmented * 255).astype(np.uint8))
    
    # Save original for comparison
    cv2.imwrite(str(output_path / "original.png"), (image * 255).astype(np.uint8))
    
    # Get and print statistics
    stats = pipeline.get_stats()
    logger.info(f"Augmentation stats: {json.dumps(stats, indent=2)}")
    
    logger.info(f"Test augmentations saved to: {output_path}")


# Example usage and CLI
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test augmentation pipeline")
    parser.add_argument("--image", required=True, help="Path to test image")
    parser.add_argument("--output-dir", default="test_augmentations", help="Output directory")
    parser.add_argument("--num-augmentations", type=int, default=10, help="Number of augmentations to generate")
    parser.add_argument("--mode", default="streaming", choices=["streaming", "cached", "mixed", "disabled"], help="Augmentation mode")
    parser.add_argument("--config", help="Path to augmentation config JSON file")
    
    args = parser.parse_args()
    
    # Test the pipeline
    test_augmentation_pipeline(
        sample_image_path=args.image,
        output_dir=args.output_dir,
        num_augmentations=args.num_augmentations
    )
