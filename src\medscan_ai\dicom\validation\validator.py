"""
DICOM file validation utilities.
"""

import os
from pathlib import Path
from typing import List, Optional, <PERSON><PERSON>

import pydicom
from pydicom.dataset import Dataset
from pydicom.errors import InvalidDicomError

from ..exceptions import (
    DicomCorruptedFileError,
    DicomFileNotFoundError,
    DicomFormatError,
    DicomValidationError,
)


class DicomValidator:
    """Validates DICOM files and data integrity."""

    # Required DICOM tags for medical imaging
    REQUIRED_PATIENT_TAGS = ["PatientName", "PatientID"]

    REQUIRED_STUDY_TAGS = ["StudyInstanceUID", "StudyDate"]

    REQUIRED_SERIES_TAGS = ["SeriesInstanceUID", "Modality"]

    REQUIRED_IMAGE_TAGS = ["SOPInstanceUID", "Rows", "Columns"]

    # Supported modalities for medical imaging
    SUPPORTED_MODALITIES = [
        "CR",  # Computed Radiography
        "DX",  # Digital Radiography
        "CT",  # Computed Tomography
        "MR",  # Magnetic Resonance
        "US",  # Ultrasound
        "XA",  # X-Ray Angiography
        "RF",  # Radio Fluoroscopy
        "MG",  # Mammography
        "PT",  # Positron Emission Tomography
        "NM",  # Nuclear Medicine
        "SC",  # Secondary Capture
        "OT",  # Other
    ]

    @staticmethod
    def validate_file_exists(file_path: str) -> bool:
        """Check if DICOM file exists."""
        if not os.path.exists(file_path):
            raise DicomFileNotFoundError(f"DICOM file not found: {file_path}")
        return True

    @staticmethod
    def validate_file_extension(file_path: str) -> bool:
        """Validate file extension for DICOM files."""
        path = Path(file_path)
        valid_extensions = [
            ".dcm",
            ".dicom",
            ".dic",
            "",
        ]  # DICOM files may have no extension

        if path.suffix.lower() not in valid_extensions and len(path.suffix) > 0:
            # Allow files without extension or with common DICOM extensions
            return False
        return True

    @staticmethod
    def validate_dicom_format(file_path: str) -> Tuple[bool, Optional[str]]:
        """Validate if file is a valid DICOM format."""
        try:
            # Try to read the file with pydicom
            dataset = pydicom.dcmread(file_path, force=True)

            # Check if it has basic DICOM structure
            if not hasattr(dataset, "file_meta"):
                return False, "File does not contain DICOM file meta information"

            return True, None

        except InvalidDicomError as e:
            return False, f"Invalid DICOM format: {str(e)}"
        except Exception as e:
            return False, f"Error reading file: {str(e)}"

    @staticmethod
    def validate_required_tags(dataset: Dataset) -> Tuple[bool, List[str]]:
        """Validate that dataset contains required DICOM tags."""
        missing_tags = []

        # Check required patient tags
        for tag in DicomValidator.REQUIRED_PATIENT_TAGS:
            if not hasattr(dataset, tag) or getattr(dataset, tag) is None:
                missing_tags.append(f"Patient.{tag}")

        # Check required study tags
        for tag in DicomValidator.REQUIRED_STUDY_TAGS:
            if not hasattr(dataset, tag) or getattr(dataset, tag) is None:
                missing_tags.append(f"Study.{tag}")

        # Check required series tags
        for tag in DicomValidator.REQUIRED_SERIES_TAGS:
            if not hasattr(dataset, tag) or getattr(dataset, tag) is None:
                missing_tags.append(f"Series.{tag}")

        # Check required image tags
        for tag in DicomValidator.REQUIRED_IMAGE_TAGS:
            if not hasattr(dataset, tag) or getattr(dataset, tag) is None:
                missing_tags.append(f"Image.{tag}")

        is_valid = len(missing_tags) == 0
        return is_valid, missing_tags

    @staticmethod
    def validate_modality(dataset: Dataset) -> Tuple[bool, Optional[str]]:
        """Validate that the modality is supported."""
        if not hasattr(dataset, "Modality"):
            return False, "Modality tag is missing"

        modality = str(dataset.Modality).upper()
        if modality not in DicomValidator.SUPPORTED_MODALITIES:
            return (
                False,
                f"Unsupported modality: {modality}. Supported: {', '.join(DicomValidator.SUPPORTED_MODALITIES)}",
            )

        return True, None

    @staticmethod
    def validate_pixel_data(dataset: Dataset) -> Tuple[bool, Optional[str]]:
        """Validate pixel data integrity."""
        try:
            if not hasattr(dataset, "pixel_array"):
                return False, "No pixel data found in DICOM file"

            # Try to access pixel array
            pixel_array = dataset.pixel_array

            if pixel_array is None:
                return False, "Pixel array is None"

            if pixel_array.size == 0:
                return False, "Pixel array is empty"

            # Validate image dimensions
            if hasattr(dataset, "Rows") and hasattr(dataset, "Columns"):
                expected_shape = (int(dataset.Rows), int(dataset.Columns))
                if pixel_array.shape[:2] != expected_shape:
                    return (
                        False,
                        f"Pixel array shape {pixel_array.shape[:2]} does not match expected {expected_shape}",
                    )

            return True, None

        except Exception as e:
            return False, f"Error accessing pixel data: {str(e)}"

    @staticmethod
    def validate_file_integrity(file_path: str) -> Tuple[bool, List[str]]:
        """Perform comprehensive file integrity validation."""
        errors = []

        try:
            # Check file existence
            DicomValidator.validate_file_exists(file_path)

            # Check file extension (warning, not error)
            if not DicomValidator.validate_file_extension(file_path):
                errors.append(f"Warning: Unusual file extension for DICOM file")

            # Validate DICOM format
            is_dicom, format_error = DicomValidator.validate_dicom_format(file_path)
            if not is_dicom:
                errors.append(format_error)
                return False, errors

            # Read dataset for further validation
            dataset = pydicom.dcmread(file_path)

            # Validate required tags
            has_required, missing_tags = DicomValidator.validate_required_tags(dataset)
            if not has_required:
                errors.extend([f"Missing required tag: {tag}" for tag in missing_tags])

            # Validate modality
            valid_modality, modality_error = DicomValidator.validate_modality(dataset)
            if not valid_modality:
                errors.append(modality_error)

            # Validate pixel data
            valid_pixels, pixel_error = DicomValidator.validate_pixel_data(dataset)
            if not valid_pixels:
                errors.append(pixel_error)

            is_valid = len([e for e in errors if not e.startswith("Warning:")]) == 0
            return is_valid, errors

        except DicomFileNotFoundError as e:
            errors.append(str(e))
            return False, errors
        except Exception as e:
            errors.append(f"Unexpected error during validation: {str(e)}")
            return False, errors

    @staticmethod
    def validate_dataset(dataset: Dataset) -> Tuple[bool, List[str]]:
        """Validate a pydicom dataset."""
        errors = []

        try:
            # Validate required tags
            has_required, missing_tags = DicomValidator.validate_required_tags(dataset)
            if not has_required:
                errors.extend([f"Missing required tag: {tag}" for tag in missing_tags])

            # Validate modality
            valid_modality, modality_error = DicomValidator.validate_modality(dataset)
            if not valid_modality:
                errors.append(modality_error)

            # Validate pixel data
            valid_pixels, pixel_error = DicomValidator.validate_pixel_data(dataset)
            if not valid_pixels:
                errors.append(pixel_error)

            is_valid = len(errors) == 0
            return is_valid, errors

        except Exception as e:
            errors.append(f"Unexpected error during dataset validation: {str(e)}")
            return False, errors

    @staticmethod
    def get_validation_summary(file_path: str) -> dict:
        """Get a comprehensive validation summary for a DICOM file."""
        summary = {
            "file_path": file_path,
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "file_exists": False,
            "is_dicom_format": False,
            "has_required_tags": False,
            "has_valid_modality": False,
            "has_valid_pixel_data": False,
        }

        try:
            # File existence check
            summary["file_exists"] = os.path.exists(file_path)
            if not summary["file_exists"]:
                summary["errors"].append(f"File not found: {file_path}")
                return summary

            # DICOM format check
            is_dicom, format_error = DicomValidator.validate_dicom_format(file_path)
            summary["is_dicom_format"] = is_dicom
            if not is_dicom:
                summary["errors"].append(format_error)
                return summary

            # Read dataset
            dataset = pydicom.dcmread(file_path)

            # Required tags check
            has_required, missing_tags = DicomValidator.validate_required_tags(dataset)
            summary["has_required_tags"] = has_required
            if not has_required:
                summary["errors"].extend([f"Missing: {tag}" for tag in missing_tags])

            # Modality check
            valid_modality, modality_error = DicomValidator.validate_modality(dataset)
            summary["has_valid_modality"] = valid_modality
            if not valid_modality:
                summary["errors"].append(modality_error)

            # Pixel data check
            valid_pixels, pixel_error = DicomValidator.validate_pixel_data(dataset)
            summary["has_valid_pixel_data"] = valid_pixels
            if not valid_pixels:
                summary["errors"].append(pixel_error)

            # File extension warning
            if not DicomValidator.validate_file_extension(file_path):
                summary["warnings"].append("Unusual file extension for DICOM")

            # Overall validation
            summary["is_valid"] = (
                summary["file_exists"]
                and summary["is_dicom_format"]
                and summary["has_required_tags"]
                and summary["has_valid_modality"]
                and summary["has_valid_pixel_data"]
            )

        except Exception as e:
            summary["errors"].append(f"Validation error: {str(e)}")

        return summary

    # ------------------------------------------------------------------
    # Legacy alias for backward-compatibility
    # ------------------------------------------------------------------
    def validate(self, dataset: Dataset) -> dict:  # pragma: no cover
        """Validate dataset and return summary dict compatible with tests."""
        is_valid, errors = DicomValidator.validate_dataset(dataset)

        # Determine modality for convenience
        modality = str(getattr(dataset, "Modality", "")).upper() if hasattr(dataset, "Modality") else None

        return {
            "is_valid": is_valid,
            "findings": errors if errors else "OK",
            "modality": modality,
        }
