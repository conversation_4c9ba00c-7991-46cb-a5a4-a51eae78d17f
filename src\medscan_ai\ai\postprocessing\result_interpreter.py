"""
AI Model Result Interpretation and Post-processing System

This module provides comprehensive interpretation and analysis of AI model outputs
for medical imaging applications, specifically designed for anomaly detection in
X-ray images. Supports multiple model output formats and advanced post-processing.

Author: MedScan AI Team
Date: 2025-01-27
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime
import json

# Configure logging
logger = logging.getLogger(__name__)


class AnomalyType(Enum):
    """Enumeration of supported anomaly types in medical imaging."""
    UNKNOWN = "unknown"
    FRACTURE = "fracture"
    PNEUMONIA = "pneumonia"
    NODULE = "nodule"
    INFILTRATE = "infiltrate"
    CONSOLIDATION = "consolidation"
    ATELECTASIS = "atelectasis"
    PLEURAL_EFFUSION = "pleural_effusion"
    CARDIOMEGALY = "cardiomegaly"
    MASS = "mass"
    CALCIFICATION = "calcification"
    FOREIGN_OBJECT = "foreign_object"
    ARTIFACT = "artifact"


class ConfidenceLevel(Enum):
    """Confidence level classifications for medical interpretation."""
    VERY_LOW = "very_low"     # 0.0 - 0.2
    LOW = "low"               # 0.2 - 0.4  
    MODERATE = "moderate"     # 0.4 - 0.6
    HIGH = "high"             # 0.6 - 0.8
    VERY_HIGH = "very_high"   # 0.8 - 1.0


class SeverityLevel(Enum):
    """Medical severity classifications."""
    MINIMAL = "minimal"
    MILD = "mild"
    MODERATE = "moderate"
    SEVERE = "severe"
    CRITICAL = "critical"


@dataclass
class BoundingBox:
    """
    Represents a bounding box for anomaly localization.
    """
    x1: float  # Top-left x coordinate (normalized 0-1)
    y1: float  # Top-left y coordinate (normalized 0-1)
    x2: float  # Bottom-right x coordinate (normalized 0-1) 
    y2: float  # Bottom-right y coordinate (normalized 0-1)
    
    @property
    def width(self) -> float:
        """Get bounding box width."""
        return self.x2 - self.x1
    
    @property
    def height(self) -> float:
        """Get bounding box height."""
        return self.y2 - self.y1
    
    @property
    def center(self) -> Tuple[float, float]:
        """Get bounding box center coordinates."""
        return ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)
    
    @property
    def area(self) -> float:
        """Get bounding box area."""
        return self.width * self.height
    
    def to_pixel_coordinates(self, image_width: int, image_height: int) -> 'BoundingBox':
        """Convert normalized coordinates to pixel coordinates."""
        return BoundingBox(
            x1=self.x1 * image_width,
            y1=self.y1 * image_height,
            x2=self.x2 * image_width,
            y2=self.y2 * image_height
        )
    
    def iou(self, other: 'BoundingBox') -> float:
        """Calculate Intersection over Union with another bounding box."""
        # Calculate intersection area
        x1_inter = max(self.x1, other.x1)
        y1_inter = max(self.y1, other.y1)
        x2_inter = min(self.x2, other.x2)
        y2_inter = min(self.y2, other.y2)
        
        if x1_inter >= x2_inter or y1_inter >= y2_inter:
            return 0.0
        
        intersection_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        union_area = self.area + other.area - intersection_area
        
        return intersection_area / union_area if union_area > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert bounding box to dictionary for serialization."""
        return {
            'x1': self.x1,
            'y1': self.y1,
            'x2': self.x2,
            'y2': self.y2,
            'width': self.width,
            'height': self.height,
            'center': self.center,
            'area': self.area
        }


@dataclass
class AnomalyDetection:
    """
    Represents a single anomaly detection with comprehensive metadata.
    """
    anomaly_type: AnomalyType
    confidence_score: float
    bounding_box: Optional[BoundingBox] = None
    severity: Optional[SeverityLevel] = None
    confidence_level: ConfidenceLevel = field(init=False)
    clinical_significance: str = ""
    additional_metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization to set derived fields."""
        self.confidence_level = self._classify_confidence(self.confidence_score)
    
    @staticmethod
    def _classify_confidence(score: float) -> ConfidenceLevel:
        """Classify confidence score into discrete levels."""
        if score >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif score >= 0.6:
            return ConfidenceLevel.HIGH
        elif score >= 0.4:
            return ConfidenceLevel.MODERATE
        elif score >= 0.2:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert detection to dictionary for serialization."""
        result = {
            'anomaly_type': self.anomaly_type.value,
            'confidence_score': self.confidence_score,
            'confidence_level': self.confidence_level.value,
            'clinical_significance': self.clinical_significance,
            'additional_metadata': self.additional_metadata
        }
        
        if self.bounding_box:
            result['bounding_box'] = {
                'x1': self.bounding_box.x1,
                'y1': self.bounding_box.y1,
                'x2': self.bounding_box.x2,
                'y2': self.bounding_box.y2,
                'width': self.bounding_box.width,
                'height': self.bounding_box.height,
                'center': self.bounding_box.center,
                'area': self.bounding_box.area
            }
        
        if self.severity:
            result['severity'] = self.severity.value
            
        return result


@dataclass
class InterpretationResult:
    """
    Comprehensive interpretation result for AI model output.
    """
    overall_anomaly_detected: bool
    overall_confidence: float
    detections: List[AnomalyDetection]
    processing_metadata: Dict[str, Any]
    clinical_summary: str = ""
    recommendations: List[str] = field(default_factory=list)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    @property
    def detection_count(self) -> int:
        """Get total number of detections."""
        return len(self.detections)
    
    @property
    def high_confidence_detections(self) -> List[AnomalyDetection]:
        """Get detections with high or very high confidence."""
        return [d for d in self.detections 
                if d.confidence_level in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH]]
    
    @property
    def critical_findings(self) -> List[AnomalyDetection]:
        """Get detections with critical or severe severity."""
        return [d for d in self.detections 
                if d.severity in [SeverityLevel.CRITICAL, SeverityLevel.SEVERE]]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for serialization."""
        return {
            'overall_anomaly_detected': self.overall_anomaly_detected,
            'overall_confidence': self.overall_confidence,
            'detection_count': self.detection_count,
            'detections': [d.to_dict() for d in self.detections],
            'high_confidence_count': len(self.high_confidence_detections),
            'critical_findings_count': len(self.critical_findings),
            'processing_metadata': self.processing_metadata,
            'clinical_summary': self.clinical_summary,
            'recommendations': self.recommendations,
            'timestamp': self.timestamp
        }


class ResultInterpreterError(Exception):
    """Custom exception for result interpretation operations."""
    pass


class ResultInterpreter:
    """
    Comprehensive AI model result interpretation system for medical imaging.
    
    This class provides advanced post-processing capabilities including:
    - Multiple model output format support
    - Non-maximum suppression (NMS)
    - Confidence threshold filtering
    - Medical anomaly type classification
    - Clinical significance assessment
    - Bounding box processing and visualization
    """
    
    def __init__(self, 
                 confidence_threshold: float = 0.5,
                 nms_threshold: float = 0.5,
                 max_detections: int = 50):
        """
        Initialize the ResultInterpreter.
        
        Args:
            confidence_threshold: Minimum confidence for valid detections
            nms_threshold: IoU threshold for non-maximum suppression
            max_detections: Maximum number of detections to return
        """
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.max_detections = max_detections
        
        # Medical knowledge base for anomaly interpretation
        self.anomaly_patterns = self._initialize_anomaly_patterns()
        self.clinical_guidelines = self._initialize_clinical_guidelines()
        
        logger.info(f"ResultInterpreter initialized with confidence_threshold={confidence_threshold}, "
                   f"nms_threshold={nms_threshold}, max_detections={max_detections}")
    
    def interpret_classification_output(self, 
                                      raw_output: np.ndarray,
                                      class_names: Optional[List[str]] = None,
                                      image_metadata: Optional[Dict[str, Any]] = None) -> InterpretationResult:
        """
        Interpret binary or multi-class classification model output.
        
        Args:
            raw_output: Raw model output tensor
            class_names: Optional list of class names
            image_metadata: Optional metadata about the source image
            
        Returns:
            InterpretationResult with comprehensive analysis
        """
        try:
            # Handle different output shapes
            if raw_output.ndim > 1:
                probabilities = raw_output.flatten()
            else:
                probabilities = raw_output
            
            detections = []
            processing_metadata = {
                'output_shape': raw_output.shape,
                'output_type': 'classification',
                'confidence_threshold': self.confidence_threshold,
                'processing_time': datetime.now().isoformat()
            }
            
            if len(probabilities) == 1:
                # Binary classification
                confidence = float(probabilities[0])
                anomaly_detected = confidence > self.confidence_threshold
                
                if anomaly_detected:
                    detection = AnomalyDetection(
                        anomaly_type=AnomalyType.UNKNOWN,
                        confidence_score=confidence,
                        clinical_significance=self._assess_clinical_significance(
                            AnomalyType.UNKNOWN, confidence, image_metadata
                        )
                    )
                    detections.append(detection)
                
                overall_confidence = confidence
                
            else:
                # Multi-class classification
                max_confidence = float(np.max(probabilities))
                max_class_idx = int(np.argmax(probabilities))
                
                # Only consider detections above threshold
                valid_indices = np.where(probabilities > self.confidence_threshold)[0]
                
                for idx in valid_indices:
                    confidence = float(probabilities[idx])
                    anomaly_type = self._map_class_to_anomaly_type(idx, class_names)
                    
                    detection = AnomalyDetection(
                        anomaly_type=anomaly_type,
                        confidence_score=confidence,
                        severity=self._assess_severity(anomaly_type, confidence),
                        clinical_significance=self._assess_clinical_significance(
                            anomaly_type, confidence, image_metadata
                        ),
                        additional_metadata={'class_index': idx}
                    )
                    detections.append(detection)
                
                overall_confidence = max_confidence
                anomaly_detected = max_confidence > self.confidence_threshold
            
            # Generate clinical summary and recommendations
            clinical_summary = self._generate_clinical_summary(detections, image_metadata)
            recommendations = self._generate_recommendations(detections, image_metadata)
            
            result = InterpretationResult(
                overall_anomaly_detected=anomaly_detected,
                overall_confidence=overall_confidence,
                detections=detections,
                processing_metadata=processing_metadata,
                clinical_summary=clinical_summary,
                recommendations=recommendations
            )
            
            logger.info(f"Classification interpretation completed: {len(detections)} detections, "
                       f"overall_confidence={overall_confidence:.3f}")
            
            return result
            
        except Exception as e:
            raise ResultInterpreterError(f"Classification interpretation failed: {str(e)}")
    
    def interpret_detection_output(self,
                                 raw_output: Union[np.ndarray, Dict[str, np.ndarray]],
                                 image_shape: Tuple[int, int],
                                 class_names: Optional[List[str]] = None,
                                 image_metadata: Optional[Dict[str, Any]] = None) -> InterpretationResult:
        """
        Interpret object detection model output with bounding boxes.
        
        Args:
            raw_output: Raw detection output (boxes, scores, classes)
            image_shape: Original image shape (height, width)
            class_names: Optional list of class names
            image_metadata: Optional metadata about the source image
            
        Returns:
            InterpretationResult with bounding box detections
        """
        try:
            # Parse detection output format
            if isinstance(raw_output, dict):
                boxes = raw_output.get('boxes', np.array([]))
                scores = raw_output.get('scores', np.array([]))
                classes = raw_output.get('classes', np.array([]))
            else:
                # Assume concatenated format: [x1, y1, x2, y2, confidence, class]
                if raw_output.ndim == 2 and raw_output.shape[1] >= 6:
                    boxes = raw_output[:, :4]
                    scores = raw_output[:, 4]
                    classes = raw_output[:, 5]
                else:
                    raise ResultInterpreterError("Invalid detection output format")
            
            # Filter by confidence threshold
            valid_indices = scores > self.confidence_threshold
            boxes = boxes[valid_indices]
            scores = scores[valid_indices]
            classes = classes[valid_indices]
            
            # Apply Non-Maximum Suppression
            if len(boxes) > 0:
                keep_indices = self._apply_nms(boxes, scores, self.nms_threshold)
                boxes = boxes[keep_indices]
                scores = scores[keep_indices]
                classes = classes[keep_indices]
            
            # Limit to max detections
            if len(boxes) > self.max_detections:
                # Sort by confidence and take top detections
                sort_indices = np.argsort(scores)[::-1][:self.max_detections]
                boxes = boxes[sort_indices]
                scores = scores[sort_indices]
                classes = classes[sort_indices]
            
            # Create detection objects
            detections = []
            for i, (box, score, cls) in enumerate(zip(boxes, scores, classes)):
                # Normalize bounding box coordinates
                bbox = BoundingBox(
                    x1=float(box[0]) / image_shape[1],
                    y1=float(box[1]) / image_shape[0],
                    x2=float(box[2]) / image_shape[1],
                    y2=float(box[3]) / image_shape[0]
                )
                
                anomaly_type = self._map_class_to_anomaly_type(int(cls), class_names)
                
                detection = AnomalyDetection(
                    anomaly_type=anomaly_type,
                    confidence_score=float(score),
                    bounding_box=bbox,
                    severity=self._assess_severity(anomaly_type, float(score)),
                    clinical_significance=self._assess_clinical_significance(
                        anomaly_type, float(score), image_metadata
                    ),
                    additional_metadata={
                        'class_index': int(cls),
                        'detection_rank': i + 1,
                        'pixel_coordinates': bbox.to_pixel_coordinates(*image_shape[::-1]).to_dict()
                    }
                )
                detections.append(detection)
            
            # Calculate overall metrics
            overall_confidence = float(np.max(scores)) if len(scores) > 0 else 0.0
            anomaly_detected = len(detections) > 0
            
            processing_metadata = {
                'output_type': 'detection',
                'original_detections': len(valid_indices.nonzero()[0]),
                'post_nms_detections': len(detections),
                'confidence_threshold': self.confidence_threshold,
                'nms_threshold': self.nms_threshold,
                'image_shape': image_shape,
                'processing_time': datetime.now().isoformat()
            }
            
            # Generate clinical analysis
            clinical_summary = self._generate_clinical_summary(detections, image_metadata)
            recommendations = self._generate_recommendations(detections, image_metadata)
            
            result = InterpretationResult(
                overall_anomaly_detected=anomaly_detected,
                overall_confidence=overall_confidence,
                detections=detections,
                processing_metadata=processing_metadata,
                clinical_summary=clinical_summary,
                recommendations=recommendations
            )
            
            logger.info(f"Detection interpretation completed: {len(detections)} detections, "
                       f"overall_confidence={overall_confidence:.3f}")
            
            return result
            
        except Exception as e:
            raise ResultInterpreterError(f"Detection interpretation failed: {str(e)}")
    
    def _apply_nms(self, boxes: np.ndarray, scores: np.ndarray, threshold: float) -> np.ndarray:
        """
        Apply Non-Maximum Suppression to remove overlapping detections.
        
        Args:
            boxes: Bounding boxes array (N, 4)
            scores: Confidence scores array (N,)
            threshold: IoU threshold for suppression
            
        Returns:
            Indices of boxes to keep
        """
        if len(boxes) == 0:
            return np.array([], dtype=int)
        
        # Sort by confidence scores (highest first)
        order = scores.argsort()[::-1]
        
        keep = []
        while len(order) > 0:
            # Pick the box with highest confidence
            i = order[0]
            keep.append(i)
            
            if len(order) == 1:
                break
            
            # Calculate IoU with remaining boxes
            current_box = BoundingBox(boxes[i, 0], boxes[i, 1], boxes[i, 2], boxes[i, 3])
            
            # Remove boxes with high IoU
            remaining_indices = []
            for j in order[1:]:
                other_box = BoundingBox(boxes[j, 0], boxes[j, 1], boxes[j, 2], boxes[j, 3])
                if current_box.iou(other_box) < threshold:
                    remaining_indices.append(j)
            
            order = np.array(remaining_indices)
        
        return np.array(keep)
    
    def _map_class_to_anomaly_type(self, class_index: int, class_names: Optional[List[str]]) -> AnomalyType:
        """Map model class index to medical anomaly type."""
        if class_names and 0 <= class_index < len(class_names):
            class_name = class_names[class_index].lower()
            
            # Medical anomaly mapping
            mapping = {
                'fracture': AnomalyType.FRACTURE,
                'pneumonia': AnomalyType.PNEUMONIA,
                'nodule': AnomalyType.NODULE,
                'infiltrate': AnomalyType.INFILTRATE,
                'consolidation': AnomalyType.CONSOLIDATION,
                'atelectasis': AnomalyType.ATELECTASIS,
                'pleural_effusion': AnomalyType.PLEURAL_EFFUSION,
                'cardiomegaly': AnomalyType.CARDIOMEGALY,
                'mass': AnomalyType.MASS,
                'calcification': AnomalyType.CALCIFICATION,
                'foreign_object': AnomalyType.FOREIGN_OBJECT,
                'artifact': AnomalyType.ARTIFACT
            }
            
            for key, anomaly_type in mapping.items():
                if key in class_name:
                    return anomaly_type
        
        return AnomalyType.UNKNOWN
    
    def _assess_severity(self, anomaly_type: AnomalyType, confidence: float) -> SeverityLevel:
        """Assess medical severity based on anomaly type and confidence."""
        # High-risk anomaly types
        critical_types = {AnomalyType.FRACTURE, AnomalyType.PNEUMONIA, AnomalyType.MASS}
        severe_types = {AnomalyType.CONSOLIDATION, AnomalyType.PLEURAL_EFFUSION, AnomalyType.CARDIOMEGALY}
        
        if anomaly_type in critical_types:
            if confidence > 0.8:
                return SeverityLevel.CRITICAL
            elif confidence > 0.6:
                return SeverityLevel.SEVERE
            else:
                return SeverityLevel.MODERATE
        elif anomaly_type in severe_types:
            if confidence > 0.7:
                return SeverityLevel.SEVERE
            elif confidence > 0.5:
                return SeverityLevel.MODERATE
            else:
                return SeverityLevel.MILD
        else:
            if confidence > 0.8:
                return SeverityLevel.MODERATE
            elif confidence > 0.6:
                return SeverityLevel.MILD
            else:
                return SeverityLevel.MINIMAL
    
    def _assess_clinical_significance(self, 
                                    anomaly_type: AnomalyType, 
                                    confidence: float,
                                    image_metadata: Optional[Dict[str, Any]]) -> str:
        """Generate clinical significance assessment."""
        base_significance = self.clinical_guidelines.get(anomaly_type, {})
        
        if confidence > 0.8:
            confidence_modifier = "High confidence finding"
        elif confidence > 0.6:
            confidence_modifier = "Moderate confidence finding"
        else:
            confidence_modifier = "Low confidence finding - requires clinical correlation"
        
        significance = base_significance.get('description', f'{anomaly_type.value} detected')
        urgency = base_significance.get('urgency', 'routine')
        
        return f"{confidence_modifier}. {significance}. Recommended action: {urgency}."
    
    def _generate_clinical_summary(self, 
                                 detections: List[AnomalyDetection],
                                 image_metadata: Optional[Dict[str, Any]]) -> str:
        """Generate comprehensive clinical summary."""
        if not detections:
            return "No significant abnormalities detected in the analyzed image."
        
        high_conf_detections = [d for d in detections if d.confidence_score > 0.7]
        critical_findings = [d for d in detections if d.severity in [SeverityLevel.CRITICAL, SeverityLevel.SEVERE]]
        
        summary_parts = []
        
        # Overall assessment
        if critical_findings:
            summary_parts.append(f"URGENT: {len(critical_findings)} critical finding(s) detected.")
        elif high_conf_detections:
            summary_parts.append(f"{len(high_conf_detections)} high-confidence abnormality(ies) identified.")
        else:
            summary_parts.append(f"{len(detections)} potential abnormality(ies) detected.")
        
        # Specific findings
        for detection in detections[:3]:  # Top 3 findings
            summary_parts.append(f"- {detection.anomaly_type.value.title()}: "
                                f"{detection.confidence_score:.1%} confidence"
                                f"{f', {detection.severity.value} severity' if detection.severity else ''}")
        
        if len(detections) > 3:
            summary_parts.append(f"... and {len(detections) - 3} additional finding(s).")
        
        return " ".join(summary_parts)
    
    def _generate_recommendations(self, 
                                detections: List[AnomalyDetection],
                                image_metadata: Optional[Dict[str, Any]]) -> List[str]:
        """Generate clinical recommendations based on findings."""
        if not detections:
            return ["Regular follow-up as clinically indicated."]
        
        recommendations = []
        critical_findings = [d for d in detections if d.severity in [SeverityLevel.CRITICAL, SeverityLevel.SEVERE]]
        
        if critical_findings:
            recommendations.append("IMMEDIATE clinical correlation and urgent follow-up recommended.")
            recommendations.append("Consider additional imaging or specialist consultation.")
        
        fractures = [d for d in detections if d.anomaly_type == AnomalyType.FRACTURE]
        if fractures:
            recommendations.append("Orthopedic evaluation recommended for suspected fracture.")
        
        pneumonia = [d for d in detections if d.anomaly_type == AnomalyType.PNEUMONIA]
        if pneumonia:
            recommendations.append("Clinical correlation for pneumonia - consider laboratory studies and treatment.")
        
        masses = [d for d in detections if d.anomaly_type == AnomalyType.MASS]
        if masses:
            recommendations.append("Further characterization of mass lesion(s) recommended - consider CT or MRI.")
        
        # General recommendations
        if len(detections) > 1:
            recommendations.append("Multiple abnormalities detected - comprehensive clinical assessment advised.")
        
        low_conf_detections = [d for d in detections if d.confidence_score < 0.6]
        if low_conf_detections:
            recommendations.append("Some findings have lower confidence - clinical judgment essential.")
        
        if not recommendations:
            recommendations.append("Clinical correlation recommended for detected abnormalities.")
        
        return recommendations
    
    def _initialize_anomaly_patterns(self) -> Dict[AnomalyType, Dict[str, Any]]:
        """Initialize medical anomaly pattern knowledge base."""
        return {
            AnomalyType.FRACTURE: {
                'typical_locations': ['ribs', 'clavicle', 'spine'],
                'urgency': 'high',
                'follow_up': 'orthopedic'
            },
            AnomalyType.PNEUMONIA: {
                'typical_locations': ['lungs', 'lower_lobes'],
                'urgency': 'high',
                'follow_up': 'pulmonary'
            },
            AnomalyType.NODULE: {
                'typical_locations': ['lungs', 'peripheral'],
                'urgency': 'moderate',
                'follow_up': 'follow_up_imaging'
            }
            # Add more patterns as needed
        }
    
    def _initialize_clinical_guidelines(self) -> Dict[AnomalyType, Dict[str, str]]:
        """Initialize clinical interpretation guidelines."""
        return {
            AnomalyType.FRACTURE: {
                'description': 'Bone discontinuity suggestive of fracture',
                'urgency': 'immediate orthopedic consultation'
            },
            AnomalyType.PNEUMONIA: {
                'description': 'Pulmonary consolidation consistent with pneumonia',
                'urgency': 'clinical correlation and treatment consideration'
            },
            AnomalyType.MASS: {
                'description': 'Space-occupying lesion requiring further evaluation',
                'urgency': 'additional imaging and possible biopsy'
            },
            AnomalyType.PLEURAL_EFFUSION: {
                'description': 'Fluid accumulation in pleural space',
                'urgency': 'clinical assessment and possible drainage'
            }
            # Add more guidelines as needed
        }
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Update confidence threshold for detection filtering."""
        if not 0.0 <= threshold <= 1.0:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
        
        old_threshold = self.confidence_threshold
        self.confidence_threshold = threshold
        logger.info(f"Updated confidence threshold: {old_threshold} -> {threshold}")
    
    def set_nms_threshold(self, threshold: float) -> None:
        """Update NMS IoU threshold."""
        if not 0.0 <= threshold <= 1.0:
            raise ValueError("NMS threshold must be between 0.0 and 1.0")
        
        old_threshold = self.nms_threshold
        self.nms_threshold = threshold
        logger.info(f"Updated NMS threshold: {old_threshold} -> {threshold}")
    
    def __repr__(self) -> str:
        """String representation of ResultInterpreter instance."""
        return (f"ResultInterpreter(confidence_threshold={self.confidence_threshold}, "
                f"nms_threshold={self.nms_threshold}, max_detections={self.max_detections})") 