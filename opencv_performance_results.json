{"system_info": {"cpu_cores": 16, "memory_gb": 15.420856475830078, "platform": "win32"}, "resize_results": {"small": {"standard": {"128x128": {"avg_time": 0.0010001659393310547, "min_time": 0.0, "avg_memory_mb": 0.0359375, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.00703125, "output_shape": [256, 256]}}, "optimized": {"128x128": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.00234375, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [256, 256]}}}, "medium": {"standard": {"128x128": {"avg_time": 0.0002002716064453125, "min_time": 0.0, "avg_memory_mb": 0.01015625, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [256, 256]}, "512x512": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.0328125, "output_shape": [512, 512]}}, "optimized": {"128x128": {"avg_time": 0.00019993782043457032, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [256, 256]}, "512x512": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.05, "output_shape": [512, 512]}}}, "large": {"standard": {"128x128": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.00020022392272949218, "min_time": 0.0, "avg_memory_mb": 0.01171875, "output_shape": [256, 256]}, "512x512": {"avg_time": 0.0015027999877929687, "min_time": 0.0, "avg_memory_mb": 0.2296875, "output_shape": [512, 512]}, "1024x1024": {"avg_time": 0.0004002094268798828, "min_time": 0.0, "avg_memory_mb": 0.2015625, "output_shape": [1024, 1024]}}, "optimized": {"128x128": {"avg_time": 0.00039944648742675783, "min_time": 0.0, "avg_memory_mb": 0.00078125, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0004000663757324219, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [256, 256]}, "512x512": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.053125, "output_shape": [512, 512]}, "1024x1024": {"avg_time": 0.0006007671356201172, "min_time": 0.0, "avg_memory_mb": 0.20078125, "output_shape": [1024, 1024]}}}, "xl": {"standard": {"128x128": {"avg_time": 0.00030126571655273435, "min_time": 0.0, "avg_memory_mb": -0.20078125, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0, "min_time": 0.0, "avg_memory_mb": 0.0, "output_shape": [256, 256]}, "512x512": {"avg_time": 0.00019941329956054687, "min_time": 0.0, "avg_memory_mb": 0.02734375, "output_shape": [512, 512]}, "1024x1024": {"avg_time": 0.0005025863647460938, "min_time": 0.0, "avg_memory_mb": 0.21015625, "output_shape": [1024, 1024]}}, "optimized": {"128x128": {"avg_time": 0.001201486587524414, "min_time": 0.0009975433349609375, "avg_memory_mb": -0.20078125, "output_shape": [128, 128]}, "256x256": {"avg_time": 0.0012013435363769532, "min_time": 0.0009982585906982422, "avg_memory_mb": 0.0, "output_shape": [256, 256]}, "512x512": {"avg_time": 0.0008012294769287109, "min_time": 0.0, "avg_memory_mb": 0.02421875, "output_shape": [512, 512]}, "1024x1024": {"avg_time": 0.00039997100830078123, "min_time": 0.0, "avg_memory_mb": 0.20078125, "output_shape": [1024, 1024]}}}}, "batch_results": {"batch_5": {"standard": {"error": "float division by zero"}, "optimized": {"error": "float division by zero"}}, "batch_10": {"standard": {"total_time": 0.0009992122650146484, "avg_time_per_image": 9.992122650146485e-05, "throughput_images_per_sec": 10007.883560009544}, "optimized": {"error": "float division by zero"}}, "batch_20": {"standard": {"total_time": 0.003998994827270508, "avg_time_per_image": 0.00019994974136352539, "throughput_images_per_sec": 5001.256781732546}, "optimized": {"total_time": 0.002000093460083008, "avg_time_per_image": 0.00010000467300415039, "throughput_images_per_sec": 9999.532721420908}}}, "other_results": {"contrast_normalization": {"time": 0.0045146942138671875, "input_shape": [512, 512], "output_shape": [512, 512], "input_dtype": "uint8", "output_dtype": "uint8"}, "denoising": {"bilateral": {"time": 0.005997896194458008, "output_shape": [512, 512]}, "gaussian": {"time": 0.003003835678100586, "output_shape": [512, 512]}, "median": {"time": 0.0029969215393066406, "output_shape": [512, 512]}}, "edge_detection": {"canny": {"time": 0.007514238357543945, "output_shape": [512, 512]}, "sobel": {"time": 0.0070002079010009766, "output_shape": [512, 512]}, "laplacian": {"time": 0.003523588180541992, "output_shape": [512, 512]}}}, "pooling_results": {"pooling_test": {"total_time": 0.0, "operations_count": 20, "avg_time_per_op": 0.0, "memory_pool_stats": {"hits": 0, "misses": 0, "allocations": 0, "total_memory_mb": 0.0, "hit_rate": 0.0, "pool_keys": 0, "total_pooled_arrays": 0}, "operation_stats": {}}}}