#!/usr/bin/env python3
"""Test script for GUI imports to debug issues."""

print("Testing GUI imports...")

try:
    print("1. Testing basic GUI package...")
    import src.medscan_ai.gui
    print("✅ Basic GUI package import successful")
except Exception as e:
    print(f"❌ Basic GUI Error: {e}")
    import traceback
    traceback.print_exc()

try:
    print("\n2. Testing GUI window package...")
    import src.medscan_ai.gui.window
    print("✅ GUI window package import successful")
except Exception as e:
    print(f"❌ GUI window Error: {e}")
    import traceback
    traceback.print_exc()

try:
    print("\n3. Testing main window class...")
    from src.medscan_ai.gui.window.main_window import MedScanMainWindow
    print("✅ MedScanMainWindow import successful")
except Exception as e:
    print(f"❌ MedScanMainWindow Error: {e}")
    import traceback
    traceback.print_exc()

try:
    print("\n4. Testing GUI main module...")
    from src.medscan_ai.gui import main
    print("✅ GUI main module import successful")
except Exception as e:
    print(f"❌ GUI main Error: {e}")
    import traceback
    traceback.print_exc()

try:
    print("\n5. Testing GUI utils...")
    from src.medscan_ai.gui.utils import InteractiveImageViewer, ImageDisplayHelper
    print("✅ GUI utils import successful")
except Exception as e:
    print(f"❌ GUI utils Error: {e}")
    import traceback
    traceback.print_exc()

print("\nAll GUI import tests completed!") 