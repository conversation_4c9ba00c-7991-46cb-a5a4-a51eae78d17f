"""Image repository for DICOM image operations."""

from typing import List

from sqlalchemy.orm import Session

from ..models.image import Image
from .base import BaseRepository


class ImageRepository(BaseRepository[Image]):
    """Repository for DICOM image operations."""

    def __init__(self, session: Session):
        super().__init__(Image, session)

    def find_by_study_id(self, study_id: int) -> List[Image]:
        """Find all images for a specific study."""
        return self.filter_by(study_id=study_id)
