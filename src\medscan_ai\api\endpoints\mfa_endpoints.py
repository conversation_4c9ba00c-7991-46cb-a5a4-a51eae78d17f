"""Multi-Factor Authentication API Endpoints for MedScan AI.

Provides REST API endpoints for MFA operations:
- MFA enrollment and setup
- MFA verification during authentication
- MFA status management
- MFA disable functionality
"""

import logging
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from flask import Blueprint, g, jsonify, request
from werkzeug.exceptions import BadRequest, Forbidden, Unauthorized

from ...database.engine import get_session
from ...security.access_control.rbac_manager import RBACManager
from ...security.authentication import (
    AuthenticationResult,
    AuthenticationService, 
    RegistrationResult,
    UserCredentialService,
)
from ...security.authentication.authorization import (
    AuthorizationMiddleware,
    admin_only,
    medical_staff_only,
    physician_only,
    require_authentication,
    require_role,
    require_permission,
    audit_access,
)
from ...security.authentication.session_manager import SessionMiddleware
from ...security.audit import get_audit_service, AuditEvent, AuditContext, ActionType, ActionCategory

logger = logging.getLogger(__name__)

def create_mfa_blueprint(
    auth_service: AuthenticationService,
    session_middleware: SessionMiddleware,
    auth_middleware: AuthorizationMiddleware
) -> Blueprint:
    """Create MFA endpoints blueprint."""
    
    mfa_bp = Blueprint("mfa", __name__, url_prefix="/api/auth")

    @mfa_bp.route("/mfa/enroll", methods=["POST"])
    def enroll_mfa():
        """
        Enroll user in Multi-Factor Authentication.

        Requires:
            Authorization header with Bearer token

        Returns:
            QR code data and backup codes for MFA setup
        """
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                response_data = {
                    "status": "error",
                    "message": "Authorization header required",
                }
                return jsonify(response_data), 401

            # Validate token and get user info
            is_valid, user_info, error = auth_service.validate_request_token(authorization)
            if not is_valid:
                response_data = {"status": "error", "message": error}
                return jsonify(response_data), 401

            user_id = uuid.UUID(user_info["user_id"])
            success, enrollment_data, error_msg = auth_service.enroll_mfa(user_id)

            if success:
                response_data = {
                    "status": "success",
                    "message": "MFA enrollment successful. Please verify setup with authenticator app.",
                    "data": enrollment_data,
                }
                logger.info(f"MFA enrollment initiated for user {user_id}")
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": error_msg}
                return jsonify(response_data), 400

        except Exception as e:
            logger.error(f"MFA enrollment endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "MFA enrollment service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @mfa_bp.route("/mfa/verify-setup", methods=["POST"])
    def verify_mfa_setup():
        """
        Verify MFA setup and enable MFA for user.

        Requires:
            Authorization header with Bearer token

        Request body:
            totp_code: 6-digit TOTP code from authenticator app

        Returns:
            MFA activation confirmation
        """
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                response_data = {
                    "status": "error",
                    "message": "Authorization header required",
                }
                return jsonify(response_data), 401

            data = request.get_json()
            if not data or not data.get("totp_code"):
                response_data = {
                    "status": "error",
                    "message": "TOTP code is required",
                }
                return jsonify(response_data), 400

            # Validate token and get user info
            is_valid, user_info, error = auth_service.validate_request_token(authorization)
            if not is_valid:
                response_data = {"status": "error", "message": error}
                return jsonify(response_data), 401

            user_id = uuid.UUID(user_info["user_id"])
            totp_code = data["totp_code"]

            success, message = auth_service.verify_mfa_setup(user_id, totp_code)

            if success:
                response_data = {
                    "status": "success",
                    "message": message,
                }
                logger.info(f"MFA enabled for user {user_id}")
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": message}
                return jsonify(response_data), 400

        except Exception as e:
            logger.error(f"MFA setup verification endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "MFA setup verification service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @mfa_bp.route("/mfa/verify", methods=["POST"])
    def verify_mfa():
        """
        Verify MFA code during authentication.

        Request body:
            email: User email
            password: User password  
            mfa_code: 6-digit TOTP code or backup code
            is_backup_code: Whether the code is a backup code (optional, default: false)

        Returns:
            Authentication response with JWT tokens
        """
        try:
            data = request.get_json()
            if not data:
                return (
                    jsonify({"status": "error", "message": "Request body is required"}),
                    400,
                )

            email = data.get("email")
            password = data.get("password")
            mfa_code = data.get("mfa_code")

            if not email or not password or not mfa_code:
                return (
                    jsonify(
                        {"status": "error", "message": "Email, password, and MFA code are required"}
                    ),
                    400,
                )

            # Extract client information for session tracking
            client_info = {
                "ip_address": request.environ.get("REMOTE_ADDR"),
                "user_agent": request.headers.get("User-Agent", "Unknown"),
                "device_info": {
                    "platform": "web",
                    "timestamp": datetime.utcnow().isoformat(),
                },
            }

            # Authenticate user with MFA
            result, auth_data = auth_service.authenticate_user(
                email, password, mfa_code=mfa_code, client_info=client_info
            )

            if result == AuthenticationResult.SUCCESS:
                response_data = {
                    "status": "success",
                    "message": "Authentication with MFA successful",
                    "data": auth_data,
                }
                logger.info(f"User MFA login successful: {email}")
                return jsonify(response_data), 200

            elif result == AuthenticationResult.MFA_REQUIRED:
                response_data = {
                    "status": "mfa_required",
                    "message": "Invalid MFA code, please try again",
                    "data": auth_data,
                }
                return jsonify(response_data), 400

            else:
                response_data = {"status": "error", "message": "Authentication failed"}
                return jsonify(response_data), 401

        except Exception as e:
            logger.error(f"MFA verification endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "MFA verification service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @mfa_bp.route("/mfa/disable", methods=["POST"])
    def disable_mfa():
        """
        Disable Multi-Factor Authentication for user.

        Requires:
            Authorization header with Bearer token

        Returns:
            MFA disable confirmation
        """
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                response_data = {
                    "status": "error",
                    "message": "Authorization header required",
                }
                return jsonify(response_data), 401

            # Validate token and get user info
            is_valid, user_info, error = auth_service.validate_request_token(authorization)
            if not is_valid:
                response_data = {"status": "error", "message": error}
                return jsonify(response_data), 401

            user_id = uuid.UUID(user_info["user_id"])
            success, message = auth_service.disable_mfa(user_id)

            if success:
                response_data = {
                    "status": "success",
                    "message": message,
                }
                logger.info(f"MFA disabled for user {user_id}")
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": message}
                return jsonify(response_data), 400

        except Exception as e:
            logger.error(f"MFA disable endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "MFA disable service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @mfa_bp.route("/mfa/status", methods=["GET"])
    def get_mfa_status():
        """
        Get MFA status for current user.

        Requires:
            Authorization header with Bearer token

        Returns:
            MFA status information
        """
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                response_data = {
                    "status": "error",
                    "message": "Authorization header required",
                }
                return jsonify(response_data), 401

            # Validate token and get user info
            is_valid, user_info, error = auth_service.validate_request_token(authorization)
            if not is_valid:
                response_data = {"status": "error", "message": error}
                return jsonify(response_data), 401

            user_id = uuid.UUID(user_info["user_id"])
            mfa_status = auth_service.get_mfa_status(user_id)

            if mfa_status is not None:
                response_data = {
                    "status": "success",
                    "message": "MFA status retrieved successfully",
                    "data": mfa_status,
                }
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": "User not found"}
                return jsonify(response_data), 404

        except Exception as e:
            logger.error(f"MFA status endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "MFA status service temporarily unavailable",
            }
            return jsonify(response_data), 500

    return mfa_bp
