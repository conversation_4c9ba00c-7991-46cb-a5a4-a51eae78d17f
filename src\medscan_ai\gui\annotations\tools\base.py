"""
Base class for annotation tools.
"""

from typing import Optional
import uuid
from PySide6.QtCore import QPoint
from PySide6.QtGui import QColor, QPen, QBrush
from PySide6.QtWidgets import QGraphicsScene, QGraphicsItem

from ..types import AnnotationTool, ManualAnnotation


class AnnotationToolBase:
    """
    Abstract base class for all annotation tools.
    Provides common interface and functionality for drawing tools.
    """
    
    def __init__(self, tool_type: AnnotationTool):
        """Initialize base annotation tool."""
        self.tool_type = tool_type
        self.is_active = False
        self.is_drawing = False
        self.current_scene = None
        self.preview_item = None
        
        # Visual style properties
        self.pen_color = QColor(255, 0, 0)  # Red by default
        self.pen_width = 2
        self.brush_color = QColor(255, 0, 0, 50)  # Semi-transparent red
        
    def activate(self):
        """Activate the tool."""
        self.is_active = True
        
    def deactivate(self):
        """Deactivate the tool and clean up any preview."""
        self.is_active = False
        self.cancel_drawing()
        
    def start_drawing(self, scene_pos: QPoint, scene: QGraphicsScene) -> bool:
        """
        Start drawing at the given position.
        
        Args:
            scene_pos: Position in scene coordinates
            scene: The graphics scene to draw on
            
        Returns:
            bool: True if drawing started successfully
        """
        if not self.is_active or self.is_drawing:
            return False
            
        self.current_scene = scene
        self.is_drawing = True
        
        return self._start_drawing_impl(scene_pos, scene)
        
    def continue_drawing(self, scene_pos: QPoint) -> bool:
        """
        Continue drawing at the given position.
        
        Args:
            scene_pos: Current position in scene coordinates
            
        Returns:
            bool: True if drawing continued successfully
        """
        if not self.is_drawing:
            return False
            
        return self._continue_drawing_impl(scene_pos)
        
    def finish_drawing(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """
        Finish drawing and create the annotation.
        
        Args:
            scene_pos: Final position in scene coordinates
            
        Returns:
            ManualAnnotation if successful, None otherwise
        """
        if not self.is_drawing:
            return None
            
        annotation = self._finish_drawing_impl(scene_pos)
        
        # Clean up
        self.is_drawing = False
        self._clear_preview()
        self.current_scene = None
        
        return annotation
        
    def cancel_drawing(self):
        """Cancel current drawing operation."""
        if self.is_drawing:
            self._cancel_drawing_impl()
            self.is_drawing = False
            self._clear_preview()
            self.current_scene = None
            
    # Abstract methods to be implemented by subclasses
    def _start_drawing_impl(self, scene_pos: QPoint, scene: QGraphicsScene) -> bool:
        """Implementation specific start drawing logic."""
        raise NotImplementedError
        
    def _continue_drawing_impl(self, scene_pos: QPoint) -> bool:
        """Implementation specific continue drawing logic."""
        raise NotImplementedError
        
    def _finish_drawing_impl(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """Implementation specific finish drawing logic."""
        raise NotImplementedError
        
    def _cancel_drawing_impl(self):
        """Implementation specific cancel drawing logic."""
        pass
        
    def _clear_preview(self):
        """Clear preview graphics item from scene."""
        if self.preview_item and self.current_scene:
            self.current_scene.removeItem(self.preview_item)
            self.preview_item = None
            
    def set_visual_style(self, pen_color: QColor = None, pen_width: int = None, 
                        brush_color: QColor = None):
        """Set visual style properties for the tool."""
        if pen_color:
            self.pen_color = pen_color
        if pen_width:
            self.pen_width = pen_width
        if brush_color:
            self.brush_color = brush_color 