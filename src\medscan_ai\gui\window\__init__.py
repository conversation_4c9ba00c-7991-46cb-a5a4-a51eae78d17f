"""
Window module for MedScan AI GUI.

This module provides the main window implementation and associated components
for the medical imaging application interface.
"""

from .main_window import MedScanMainWindow
from .themes import MedicalThemeManager

# Export handler mixins for advanced usage
from .handlers import (
    FileHandlerMixin,
    ImageHandlerMixin,
    ViewHandlerMixin,
    WindowingHandlerMixin,
)

# Export component mixins for advanced usage
from .menus import MenuManagerMixin
from .panels import PanelManagerMixin
from .controls import ControlsManagerMixin

__all__ = [
    # Main window class
    "MedScanMainWindow",
    
    # Theme management
    "MedicalThemeManager",
    
    # Handler mixins (for advanced usage)
    "FileHandlerMixin",
    "ImageHandlerMixin", 
    "ViewHandlerMixin",
    "WindowingHandlerMixin",
    
    # Component mixins (for advanced usage)
    "MenuManagerMixin",
    "PanelManagerMixin", 
    "ControlsManagerMixin",
]

# Module version
__version__ = "0.1.0" 