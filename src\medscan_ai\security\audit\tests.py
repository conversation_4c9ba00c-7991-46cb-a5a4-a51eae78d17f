"""Tests for Audit Service Module."""

import unittest
from unittest.mock import Mock, patch
import asyncio
from datetime import datetime

from .service import AuditService, AuditEvent, AuditContext, ActionType, ActionCategory
from .async_processor import AsyncAuditProcessor
from .context import audit_data_access


class TestAuditService(unittest.TestCase):
    """Test cases for AuditService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_session = Mock()
        self.mock_repository = Mock()
        self.audit_service = AuditService(session=self.mock_session)
        self.audit_service._repository = self.mock_repository
    
    def test_log_event_success(self):
        """Test successful event logging."""
        # Arrange
        context = AuditContext(user_id="test_user", ip_address="127.0.0.1")
        event = AuditEvent(
            action=ActionType.LOGIN,
            action_category=ActionCategory.AUTHENTICATION,
            success=True,
            context=context,
            phi_accessed=False
        )
        
        mock_audit_log = Mock()
        self.mock_repository.create.return_value = mock_audit_log
        
        # Act
        result = self.audit_service.log_event(event)
        
        # Assert
        self.assertIsNotNone(result)
        self.mock_repository.create.assert_called_once()
    
    def test_log_authentication_event(self):
        """Test authentication event logging."""
        # Arrange
        context = AuditContext(user_id="test_user", ip_address="127.0.0.1")
        mock_audit_log = Mock()
        self.mock_repository.create.return_value = mock_audit_log
        
        # Act
        result = self.audit_service.log_authentication_event(
            user_id="test_user",
            action=ActionType.LOGIN,
            success=True,
            context=context
        )
        
        # Assert
        self.assertIsNotNone(result)
        self.mock_repository.create.assert_called_once()
    
    def test_log_data_access_event(self):
        """Test data access event logging."""
        # Arrange
        context = AuditContext(user_id="test_user")
        mock_audit_log = Mock()
        self.mock_repository.create.return_value = mock_audit_log
        
        # Act
        result = self.audit_service.log_data_access_event(
            user_id="test_user",
            resource_type="PATIENT",
            resource_id="patient_123",
            context=context
        )
        
        # Assert
        self.assertIsNotNone(result)
        self.mock_repository.create.assert_called_once()


class TestAsyncAuditProcessor(unittest.TestCase):
    """Test cases for AsyncAuditProcessor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_audit_service = Mock()
        self.processor = AsyncAuditProcessor(self.mock_audit_service, batch_size=2)
    
    def test_processor_initialization(self):
        """Test processor initialization."""
        self.assertEqual(self.processor.batch_size, 2)
        self.assertFalse(self.processor._running)
        self.assertIsNotNone(self.processor._queue)
    
    @patch('asyncio.create_task')
    async def test_start_processor(self, mock_create_task):
        """Test starting the processor."""
        await self.processor.start()
        self.assertTrue(self.processor._running)
        mock_create_task.assert_called_once()
    
    async def test_queue_event(self):
        """Test queuing an event."""
        context = AuditContext(user_id="test_user")
        event = AuditEvent(
            action=ActionType.READ,
            action_category=ActionCategory.DATA_ACCESS,
            success=True,
            context=context
        )
        
        result = await self.processor.queue_event(event)
        self.assertTrue(result)


class TestAuditContext(unittest.TestCase):
    """Test cases for audit context managers."""
    
    @patch('medscan_ai.security.audit.context.get_audit_service')
    def test_audit_data_access_context(self, mock_get_audit_service):
        """Test audit data access context manager."""
        mock_service = Mock()
        mock_get_audit_service.return_value = mock_service
        
        # Test successful operation
        with audit_data_access("test_user", "PATIENT", "patient_123") as operation_id:
            self.assertIsNotNone(operation_id)
        
        # Verify audit logging was called
        mock_service.log_event.assert_called()


class TestAuditEvent(unittest.TestCase):
    """Test cases for AuditEvent data structure."""
    
    def test_audit_event_creation(self):
        """Test creating an audit event."""
        context = AuditContext(
            user_id="test_user",
            session_id="session_123",
            ip_address="127.0.0.1"
        )
        
        event = AuditEvent(
            action=ActionType.READ,
            action_category=ActionCategory.DATA_ACCESS,
            success=True,
            context=context,
            resource_type="PATIENT",
            resource_id="patient_123",
            phi_accessed=True
        )
        
        self.assertEqual(event.action, ActionType.READ)
        self.assertEqual(event.action_category, ActionCategory.DATA_ACCESS)
        self.assertTrue(event.success)
        self.assertEqual(event.context.user_id, "test_user")
        self.assertTrue(event.phi_accessed)


class TestIntegration(unittest.TestCase):
    """Integration tests for audit module."""
    
    @patch('medscan_ai.security.audit.service.get_session')
    def test_full_audit_flow(self, mock_get_session):
        """Test complete audit flow integration."""
        # Mock database session
        mock_session = Mock()
        mock_get_session.return_value = mock_session
        
        # Create audit service
        audit_service = AuditService()
        
        # Create and log event
        context = AuditContext(user_id="integration_test_user")
        event = AuditEvent(
            action=ActionType.LOGIN,
            action_category=ActionCategory.AUTHENTICATION,
            success=True,
            context=context
        )
        
        # Mock repository behavior
        with patch.object(audit_service, 'repository') as mock_repo:
            mock_audit_log = Mock()
            mock_repo.create.return_value = mock_audit_log
            
            result = audit_service.log_event(event)
            
            self.assertIsNotNone(result)
            mock_repo.create.assert_called_once()


if __name__ == '__main__':
    unittest.main() 