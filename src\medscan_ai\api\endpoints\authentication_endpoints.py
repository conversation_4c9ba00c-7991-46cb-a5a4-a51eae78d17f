"""Core Authentication API Endpoints for MedScan AI.

Provides REST API endpoints for user authentication operations:
- Login with JWT session creation
- Token refresh operations  
- User logout and session management
- Token validation
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from flask import Blueprint, g, jsonify, request
from werkzeug.exceptions import BadRequest, Forbidden, Unauthorized

from ...database.engine import get_session
from ...security.access_control.rbac_manager import RBACManager
from ...security.authentication import (
    AuthenticationResult,
    AuthenticationService, 
    RegistrationResult,
    UserCredentialService,
)
from ...security.authentication.authorization import (
    AuthorizationMiddleware,
    admin_only,
    medical_staff_only,
    physician_only,
    require_authentication,
    require_role,
    require_permission,
    audit_access,
)
from ...security.authentication.session_manager import SessionMiddleware
from ...security.audit import get_audit_service, AuditEvent, AuditContext, ActionType, ActionCategory

logger = logging.getLogger(__name__)

def create_authentication_blueprint(
    auth_service: AuthenticationService,
    session_middleware: SessionMiddleware,
    auth_middleware: AuthorizationMiddleware
) -> Blueprint:
    """Create authentication endpoints blueprint."""
    
    auth_bp = Blueprint("authentication", __name__, url_prefix="/api/auth")

    @auth_bp.route("/login", methods=["POST"])
    def login():
        """
        Authenticate user and create JWT session.

        Request body:
            email: User email
            password: User password

        Returns:
            Authentication response with JWT tokens and user information
        """
        audit_service = get_audit_service()
        client_ip = request.environ.get("REMOTE_ADDR", "unknown")
        user_agent = request.headers.get("User-Agent", "unknown")
        
        try:
            data = request.get_json()
            if not data:
                return (
                    jsonify({"status": "error", "message": "Request body is required"}),
                    400,
                )

            email = data.get("email")
            password = data.get("password")

            if not email or not password:
                return (
                    jsonify(
                        {"status": "error", "message": "Email and password are required"}
                    ),
                    400,
                )

            # Extract client information for session tracking
            client_info = {
                "ip_address": client_ip,
                "user_agent": user_agent,
                "device_info": {
                    "platform": "web",
                    "timestamp": datetime.utcnow().isoformat(),
                },
            }

            # Get MFA code if provided
            mfa_code = data.get("mfa_code")

            # Authenticate user
            result, auth_data = auth_service.authenticate_user(
                email, password, mfa_code=mfa_code, client_info=client_info
            )

            # Create audit context for logging
            audit_context = AuditContext(
                user_id=email,  # Use email as identifier for failed attempts
                ip_address=client_ip,
                user_agent=user_agent
            )

            if result == AuthenticationResult.SUCCESS:
                # Log successful login
                audit_event = AuditEvent(
                    action=ActionType.LOGIN,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=True,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    resource_id=auth_data.get("user_id"),
                    details={
                        "login_method": "password",
                        "mfa_used": bool(mfa_code),
                        "session_id": auth_data.get("session_id"),
                        "user_role": auth_data.get("role")
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "success",
                    "message": "Authentication successful",
                    "data": auth_data,
                }
                logger.info(f"User login successful: {email}")
                return jsonify(response_data), 200

            elif result == AuthenticationResult.PASSWORD_EXPIRED:
                # Log password expired event
                audit_event = AuditEvent(
                    action=ActionType.LOGIN,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "password_expired",
                        "requires_action": "password_change"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "password_expired",
                    "message": "Password has expired and must be changed",
                    "data": auth_data,
                    "action_required": "change_password",
                }
                return jsonify(response_data), 202

            elif result == AuthenticationResult.REGISTRATION_PENDING:
                # Log pending registration attempt
                audit_event = AuditEvent(
                    action=ActionType.LOGIN,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "registration_pending",
                        "requires_action": "admin_approval"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "pending_approval",
                    "message": "Account registration is pending administrative approval",
                    "data": auth_data,
                }
                return jsonify(response_data), 202

            elif result == AuthenticationResult.ACCOUNT_LOCKED:
                # Log account locked attempt - high security risk
                audit_event = AuditEvent(
                    action=ActionType.LOGIN_FAILED,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "account_locked",
                        "security_event": "multiple_failed_attempts",
                        "risk_level": "high"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Account is temporarily locked due to multiple failed login attempts",
                }
                return jsonify(response_data), 423

            elif result == AuthenticationResult.ACCOUNT_DISABLED:
                # Log disabled account attempt
                audit_event = AuditEvent(
                    action=ActionType.LOGIN_FAILED,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "account_disabled",
                        "security_event": "disabled_account_access_attempt"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {"status": "error", "message": "Account has been disabled"}
                return jsonify(response_data), 403

            elif result == AuthenticationResult.LICENSE_EXPIRED:
                # Log license expired attempt
                audit_event = AuditEvent(
                    action=ActionType.LOGIN_FAILED,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "license_expired",
                        "compliance_issue": "medical_license_expired"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Medical license has expired or is invalid",
                }
                return jsonify(response_data), 403

            elif result == AuthenticationResult.MFA_REQUIRED:
                # Log MFA requirement
                audit_event = AuditEvent(
                    action=ActionType.LOGIN,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "stage": "mfa_required",
                        "first_factor": "password_verified"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "mfa_required",
                    "message": "Multi-factor authentication code required",
                    "data": auth_data,
                }
                return jsonify(response_data), 200

            else:
                # Log invalid credentials
                audit_event = AuditEvent(
                    action=ActionType.LOGIN_FAILED,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "invalid_credentials",
                        "attempted_email": email
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {"status": "error", "message": "Invalid email or password"}
                return jsonify(response_data), 401
                    
        except Exception as e:
            # Log system error during authentication
            audit_event = AuditEvent(
                action=ActionType.LOGIN_FAILED,
                action_category=ActionCategory.AUTHENTICATION,
                success=False,
                context=AuditContext(
                    user_id=data.get("email") if data else "unknown",
                    ip_address=client_ip,
                    user_agent=user_agent
                ),
                resource_type="USER_SESSION",
                details={
                    "failure_reason": "system_error",
                    "error_message": str(e)
                },
                phi_accessed=False
            )
            audit_service.log_event(audit_event)
            
            logger.error(f"Login endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "Authentication service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @auth_bp.route("/refresh", methods=["POST"])
    def refresh_token():
        """
        Refresh access token using refresh token.

        Request body:
            refresh_token: JWT refresh token

        Returns:
            New access token or error
        """
        try:
            data = request.get_json()
            if not data:
                return (
                    jsonify({"status": "error", "message": "Request body is required"}),
                    400,
                )

            refresh_token = data.get("refresh_token")
            if not refresh_token:
                response_data = {"status": "error", "message": "Refresh token is required"}
                return jsonify(response_data), 400

            success, token_data, error = auth_service.refresh_token(refresh_token)

            if success:
                response_data = {
                    "status": "success",
                    "message": "Token refreshed successfully",
                    "data": token_data,
                }
                logger.info("Token refresh successful")
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": error}
                return jsonify(response_data), 401
                    
        except Exception as e:
            logger.error(f"Refresh token endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "Token refresh service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @auth_bp.route("/logout", methods=["POST"])
    def logout():
        """
        Logout user by revoking current session.

        Requires:
            Authorization header with Bearer token

        Returns:
            Logout confirmation
        """
        audit_service = get_audit_service()
        client_ip = request.environ.get("REMOTE_ADDR", "unknown")
        user_agent = request.headers.get("User-Agent", "unknown")
        
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                # Log missing authorization attempt
                audit_event = AuditEvent(
                    action=ActionType.LOGOUT,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=AuditContext(
                        user_id="anonymous",
                        ip_address=client_ip,
                        user_agent=user_agent
                    ),
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "missing_authorization_header"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Authorization header required",
                }
                return jsonify(response_data), 401

            # Extract token from Bearer scheme
            parts = authorization.split()
            if len(parts) != 2 or parts[0].lower() != "bearer":
                # Log invalid authorization format
                audit_event = AuditEvent(
                    action=ActionType.LOGOUT,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=AuditContext(
                        user_id="anonymous",
                        ip_address=client_ip,
                        user_agent=user_agent
                    ),
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "invalid_authorization_format"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Invalid authorization header format",
                }
                return jsonify(response_data), 401

            access_token = parts[1]
            
            # Validate token to get user info for audit logging
            is_valid, user_info, error = auth_service.validate_request_token(authorization)
            user_id = user_info.get("user_id", "unknown") if is_valid else "unknown"
            
            success, message = auth_service.logout_user(access_token)

            # Create audit context
            audit_context = AuditContext(
                user_id=user_id,
                ip_address=client_ip,
                user_agent=user_agent
            )

            if success:
                # Log successful logout
                audit_event = AuditEvent(
                    action=ActionType.LOGOUT,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=True,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "logout_method": "manual",
                        "session_ended": True
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {"status": "success", "message": message}
                logger.info("User logout successful")
                return jsonify(response_data), 200
            else:
                # Log failed logout attempt
                audit_event = AuditEvent(
                    action=ActionType.LOGOUT,
                    action_category=ActionCategory.AUTHENTICATION,
                    success=False,
                    context=audit_context,
                    resource_type="USER_SESSION",
                    details={
                        "failure_reason": "invalid_token",
                        "error_message": message
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {"status": "error", "message": message}
                return jsonify(response_data), 400
                    
        except Exception as e:
            # Log system error during logout
            audit_event = AuditEvent(
                action=ActionType.LOGOUT,
                action_category=ActionCategory.AUTHENTICATION,
                success=False,
                context=AuditContext(
                    user_id="unknown",
                    ip_address=client_ip,
                    user_agent=user_agent
                ),
                resource_type="USER_SESSION",
                details={
                    "failure_reason": "system_error",
                    "error_message": str(e)
                },
                phi_accessed=False
            )
            audit_service.log_event(audit_event)
            
            logger.error(f"Logout endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "Logout service temporarily unavailable",
            }
            return jsonify(response_data), 500

    @auth_bp.route("/validate", methods=["POST"])
    def validate_token():
        """
        Validate authentication token and return user information.

        Requires:
            Authorization header with Bearer token

        Returns:
            Token validation result and user data
        """
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                response_data = {
                    "status": "error",
                    "message": "Authorization header required",
                }
                return jsonify(response_data), 401

            is_valid, user_info, error = auth_service.validate_request_token(authorization)

            if is_valid:
                response_data = {
                    "status": "success",
                    "message": "Token is valid",
                    "data": user_info,
                }
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": error}
                return jsonify(response_data), 401
                
        except Exception as e:
            logger.error(f"Token validation endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "Token validation service temporarily unavailable",
            }
            return jsonify(response_data), 500

    return auth_bp 