"""
AI-optimized DICOM pixel data extraction and normalization.

This module provides AI-specific interfaces for extracting and normalizing 
DICOM pixel data for machine learning models, building on the medical-grade
DICOM processing capabilities from the dicom module.
"""

from typing import Any, Dict, Optional, Tuple, Union

import numpy as np
from pydicom.dataset import Dataset

from ...dicom.exceptions import DicomPixelDataError, DicomPixelProcessingError
from ...dicom.processing.pixel_processor import PixelProcessor
from ...dicom.io.reader import DicomReader


class AIPixelExtractor:
    """
    AI-optimized DICOM pixel data extractor for machine learning workflows.
    
    Provides standardized interfaces for extracting and normalizing DICOM pixel
    data specifically for AI/ML model input requirements.
    """
    
    def __init__(self):
        """Initialize AI pixel extractor."""
        self._pixel_processor = PixelProcessor()
        self._supported_bit_depths = {8, 16, 32}
        self._ai_output_ranges = {
            'uint8': (0, 255),
            'float32': (0.0, 1.0), 
            'float64': (0.0, 1.0),
            'normalized': (-1.0, 1.0)  # Common for neural networks
        }
    
    def extract_for_ai_inference(
        self,
        dataset: Dataset,
        output_format: str = 'float32',
        target_size: Optional[Tuple[int, int]] = None,
        apply_windowing: bool = True,
        ensure_grayscale: bool = True
    ) -> Dict[str, Any]:
        """
        Extract and normalize pixel data specifically for AI inference.
        
        Args:
            dataset: DICOM dataset
            output_format: Output format ('uint8', 'float32', 'float64', 'normalized')
            target_size: Optional target size (height, width) for resizing
            apply_windowing: Whether to apply DICOM windowing/leveling
            ensure_grayscale: Ensure output is grayscale for models expecting single channel
            
        Returns:
            Dictionary containing:
                - pixel_array: Processed pixel data ready for AI inference
                - metadata: Relevant metadata for AI processing
                - preprocessing_info: Information about applied transformations
                
        Raises:
            DicomPixelProcessingError: If extraction or processing fails
        """
        try:
            # Validate output format
            if output_format not in self._ai_output_ranges:
                raise DicomPixelProcessingError(
                    f"Unsupported output format: {output_format}. "
                    f"Supported: {list(self._ai_output_ranges.keys())}"
                )
            
            # Extract pixel information for metadata
            pixel_info = self._pixel_processor.get_pixel_info(dataset)
            
            # Validate pixel data availability
            if not hasattr(dataset, 'pixel_array'):
                raise DicomPixelDataError("No pixel data available in DICOM dataset")
            
            # Get processed pixel data using medical-grade pipeline
            output_range = self._ai_output_ranges[output_format]
            
            if apply_windowing:
                pixel_array = self._pixel_processor.process_full_pipeline(
                    dataset, output_range=output_range
                )
            else:
                # Extract without windowing for AI models that need raw data
                pixel_array = self._pixel_processor.extract_pixel_array(dataset)
                pixel_array = self._pixel_processor.apply_modality_transforms(pixel_array, dataset)
                pixel_array = self._pixel_processor.handle_photometric_interpretation(pixel_array, dataset)
                pixel_array = self._pixel_processor.normalize_to_display_range(pixel_array, output_range)
            
            # Ensure grayscale if requested (many AI models expect single channel)
            if ensure_grayscale and len(pixel_array.shape) > 2:
                pixel_array = self._convert_to_grayscale(pixel_array, pixel_info)
            
            # Resize if target size specified
            if target_size is not None:
                pixel_array = self._resize_for_ai(pixel_array, target_size)
            
            # Convert to appropriate data type
            pixel_array = self._convert_to_output_format(pixel_array, output_format)
            
            # Prepare metadata for AI processing
            ai_metadata = self._extract_ai_relevant_metadata(dataset, pixel_info)
            
            # Record preprocessing information
            preprocessing_info = {
                'original_shape': (pixel_info.get('rows', 0), pixel_info.get('columns', 0)),
                'output_shape': pixel_array.shape,
                'output_format': output_format,
                'output_range': output_range,
                'windowing_applied': apply_windowing,
                'grayscale_conversion': ensure_grayscale and len(pixel_array.shape) == 2,
                'resized': target_size is not None,
                'target_size': target_size,
                'bit_depth': pixel_info.get('bits_stored', 'unknown'),
                'photometric_interpretation': pixel_info.get('photometric_interpretation', 'unknown')
            }
            
            return {
                'pixel_array': pixel_array,
                'metadata': ai_metadata,
                'preprocessing_info': preprocessing_info
            }
            
        except Exception as e:
            raise DicomPixelProcessingError(
                f"AI pixel extraction failed: {str(e)}"
            )
    
    def extract_standardized_batch(
        self,
        datasets: list[Dataset],
        output_format: str = 'float32',
        target_size: Tuple[int, int] = (512, 512),
        apply_windowing: bool = True
    ) -> Dict[str, Any]:
        """
        Extract multiple DICOM datasets as standardized batch for AI processing.
        
        Args:
            datasets: List of DICOM datasets
            output_format: Output format for all images
            target_size: Standard size for all images (height, width)
            apply_windowing: Whether to apply windowing to all images
            
        Returns:
            Dictionary containing batched pixel arrays and metadata
            
        Raises:
            DicomPixelProcessingError: If batch processing fails
        """
        try:
            batch_arrays = []
            batch_metadata = []
            batch_preprocessing_info = []
            
            for i, dataset in enumerate(datasets):
                try:
                    result = self.extract_for_ai_inference(
                        dataset=dataset,
                        output_format=output_format,
                        target_size=target_size,
                        apply_windowing=apply_windowing,
                        ensure_grayscale=True
                    )
                    
                    batch_arrays.append(result['pixel_array'])
                    batch_metadata.append(result['metadata'])
                    batch_preprocessing_info.append(result['preprocessing_info'])
                    
                except Exception as e:
                    raise DicomPixelProcessingError(
                        f"Failed to process dataset {i}: {str(e)}"
                    )
            
            # Stack arrays into batch format (batch_size, height, width, channels)
            stacked_arrays = np.stack(batch_arrays, axis=0)
            
            # Add channel dimension if grayscale
            if len(stacked_arrays.shape) == 3:
                stacked_arrays = np.expand_dims(stacked_arrays, axis=-1)
            
            return {
                'pixel_arrays': stacked_arrays,
                'metadata_batch': batch_metadata,
                'preprocessing_info_batch': batch_preprocessing_info,
                'batch_size': len(datasets),
                'standardized_shape': stacked_arrays.shape[1:]
            }
            
        except Exception as e:
            raise DicomPixelProcessingError(
                f"Batch processing failed: {str(e)}"
            )
    
    def _convert_to_grayscale(self, pixel_array: np.ndarray, pixel_info: Dict) -> np.ndarray:
        """Convert color images to grayscale using appropriate weights."""
        if len(pixel_array.shape) == 2:
            return pixel_array
        
        if len(pixel_array.shape) == 3 and pixel_array.shape[2] == 3:
            # RGB to grayscale using luminance weights
            weights = np.array([0.299, 0.587, 0.114])
            return np.dot(pixel_array, weights)
        
        # For other formats, take first channel
        return pixel_array[:, :, 0] if len(pixel_array.shape) == 3 else pixel_array
    
    def _resize_for_ai(self, pixel_array: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """Resize pixel array to target size for AI models."""
        try:
            # Use optimized OpenCV operations with memory pooling and smart interpolation
            from ...core.utils.optimized_cv_ops import resize_optimized
            # Convert target_size from (height, width) to (width, height) for OpenCV
            cv_target_size = (target_size[1], target_size[0])
            return resize_optimized(pixel_array, cv_target_size)
        except ImportError:
            # Fallback to basic OpenCV
            try:
                import cv2
                if len(pixel_array.shape) == 2:
                    return cv2.resize(pixel_array, target_size[::-1], interpolation=cv2.INTER_AREA)
                else:
                    return cv2.resize(pixel_array, target_size[::-1], interpolation=cv2.INTER_AREA)
            except ImportError:
                # Final fallback to scikit-image
                from skimage.transform import resize
                return resize(pixel_array, target_size, anti_aliasing=True, preserve_range=True)
    
    def _convert_to_output_format(self, pixel_array: np.ndarray, output_format: str) -> np.ndarray:
        """Convert pixel array to specified output format."""
        if output_format == 'uint8':
            return pixel_array.astype(np.uint8)
        elif output_format == 'float32':
            return pixel_array.astype(np.float32)
        elif output_format == 'float64':
            return pixel_array.astype(np.float64)
        elif output_format == 'normalized':
            # Convert to -1 to 1 range for neural networks
            return (pixel_array.astype(np.float32) * 2.0) - 1.0
        else:
            return pixel_array
    
    def _extract_ai_relevant_metadata(self, dataset: Dataset, pixel_info: Dict) -> Dict[str, Any]:
        """Extract metadata relevant for AI processing."""
        ai_metadata = {}
        
        # Basic image properties
        ai_metadata['modality'] = getattr(dataset, 'Modality', 'unknown')
        ai_metadata['body_part'] = getattr(dataset, 'BodyPartExamined', 'unknown')
        ai_metadata['patient_age'] = getattr(dataset, 'PatientAge', 'unknown')
        ai_metadata['patient_sex'] = getattr(dataset, 'PatientSex', 'unknown')
        
        # Technical imaging parameters
        ai_metadata['kvp'] = getattr(dataset, 'KVP', None)
        ai_metadata['exposure_time'] = getattr(dataset, 'ExposureTime', None)
        ai_metadata['pixel_spacing'] = getattr(dataset, 'PixelSpacing', None)
        
        # Image characteristics
        ai_metadata['photometric_interpretation'] = pixel_info.get('photometric_interpretation')
        ai_metadata['bits_stored'] = pixel_info.get('bits_stored')
        ai_metadata['pixel_representation'] = pixel_info.get('pixel_representation')
        
        # Windowing information (important for AI interpretation)
        ai_metadata['window_center'] = pixel_info.get('window_center')
        ai_metadata['window_width'] = pixel_info.get('window_width')
        ai_metadata['rescale_slope'] = pixel_info.get('rescale_slope', 1.0)
        ai_metadata['rescale_intercept'] = pixel_info.get('rescale_intercept', 0.0)
        
        return ai_metadata
    
    def validate_for_ai_processing(self, dataset: Dataset) -> Dict[str, Any]:
        """
        Validate DICOM dataset for AI processing compatibility.
        
        Args:
            dataset: DICOM dataset to validate
            
        Returns:
            Validation results with compatibility assessment
        """
        validation_results = {
            'is_compatible': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        try:
            # Check for pixel data
            if not hasattr(dataset, 'pixel_array'):
                validation_results['errors'].append("No pixel data available")
                validation_results['is_compatible'] = False
            
            # Check bit depth
            bits_stored = getattr(dataset, 'BitsStored', None)
            if bits_stored and bits_stored not in self._supported_bit_depths:
                validation_results['warnings'].append(
                    f"Unusual bit depth: {bits_stored}. Common depths: {self._supported_bit_depths}"
                )
            
            # Check image dimensions
            rows = getattr(dataset, 'Rows', None)
            cols = getattr(dataset, 'Columns', None)
            if rows and cols:
                if rows < 64 or cols < 64:
                    validation_results['warnings'].append(
                        f"Small image size: {rows}x{cols}. May affect AI model performance"
                    )
                if rows > 4096 or cols > 4096:
                    validation_results['recommendations'].append(
                        f"Large image size: {rows}x{cols}. Consider resizing for AI processing"
                    )
            
            # Check modality
            modality = getattr(dataset, 'Modality', None)
            if modality and modality not in ['CR', 'DX', 'CT', 'MR', 'US', 'MG', 'XA']:
                validation_results['warnings'].append(
                    f"Uncommon modality for AI: {modality}"
                )
            
            return validation_results
            
        except Exception as e:
            validation_results['errors'].append(f"Validation failed: {str(e)}")
            validation_results['is_compatible'] = False
            return validation_results
    
    @property
    def supported_output_formats(self) -> list[str]:
        """Get list of supported output formats."""
        return list(self._ai_output_ranges.keys())
    
    @property 
    def supported_bit_depths(self) -> set[int]:
        """Get set of supported bit depths."""
        return self._supported_bit_depths.copy() 