"""Audit Context Manager for MedScan AI.

Provides context management for audit operations, enabling automatic logging
of operations with proper context tracking and correlation.
"""

import logging
import uuid
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Dict, Any
from datetime import datetime

from .service import AuditEvent, AuditContext, ActionType, ActionCategory, get_audit_service

logger = logging.getLogger(__name__)


class AuditContextManager:
    """Context manager for audit operations."""
    
    def __init__(self, audit_service=None):
        """Initialize context manager."""
        self.audit_service = audit_service or get_audit_service()
        self._active_contexts: Dict[str, AuditContext] = {}
    
    @contextmanager
    def audit_operation(self, action: ActionType, action_category: ActionCategory,
                       context: AuditContext, resource_type: Optional[str] = None,
                       resource_id: Optional[str] = None, phi_accessed: bool = False):
        """Context manager for auditing operations."""
        operation_id = str(uuid.uuid4())
        correlation_id = context.correlation_id or str(uuid.uuid4())
        start_time = datetime.now()
        
        # Store context
        self._active_contexts[operation_id] = context
        
        try:
            logger.debug(f"Starting audited operation: {action} (ID: {operation_id})")
            yield operation_id
            
            # Log successful completion
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            event = AuditEvent(
                action=action,
                action_category=action_category,
                success=True,
                context=context,
                resource_type=resource_type,
                resource_id=resource_id,
                phi_accessed=phi_accessed,
                details={"operation_id": operation_id},
                correlation_id=correlation_id
            )
            
            self.audit_service.log_event(event)
            
        except Exception as e:
            # Log failed operation
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            event = AuditEvent(
                action=action,
                action_category=action_category,
                success=False,
                context=context,
                resource_type=resource_type,
                resource_id=resource_id,
                phi_accessed=phi_accessed,
                details={"operation_id": operation_id, "error": str(e)},
                correlation_id=correlation_id
            )
            
            self.audit_service.log_event(event)
            raise
            
        finally:
            # Cleanup
            if operation_id in self._active_contexts:
                del self._active_contexts[operation_id]
    
    @asynccontextmanager
    async def audit_operation_async(self, action: ActionType, action_category: ActionCategory,
                                  context: AuditContext, resource_type: Optional[str] = None,
                                  resource_id: Optional[str] = None, phi_accessed: bool = False):
        """Async context manager for auditing operations."""
        operation_id = str(uuid.uuid4())
        correlation_id = context.correlation_id or str(uuid.uuid4())
        start_time = datetime.now()
        
        self._active_contexts[operation_id] = context
        
        try:
            logger.debug(f"Starting async audited operation: {action} (ID: {operation_id})")
            yield operation_id
            
            # Log successful completion
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            event = AuditEvent(
                action=action,
                action_category=action_category,
                success=True,
                context=context,
                resource_type=resource_type,
                resource_id=resource_id,
                phi_accessed=phi_accessed,
                details={"operation_id": operation_id},
                correlation_id=correlation_id
            )
            
            # Use async logging if available
            if hasattr(self.audit_service, 'log_event_async'):
                await self.audit_service.log_event_async(event)
            else:
                self.audit_service.log_event(event)
            
        except Exception as e:
            # Log failed operation
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            event = AuditEvent(
                action=action,
                action_category=action_category,
                success=False,
                context=context,
                resource_type=resource_type,
                resource_id=resource_id,
                phi_accessed=phi_accessed,
                details={"operation_id": operation_id, "error": str(e)},
                correlation_id=correlation_id
            )
            
            if hasattr(self.audit_service, 'log_event_async'):
                await self.audit_service.log_event_async(event)
            else:
                self.audit_service.log_event(event)
            raise
            
        finally:
            if operation_id in self._active_contexts:
                del self._active_contexts[operation_id]


# Global context manager
_context_manager: Optional[AuditContextManager] = None


def get_audit_context_manager() -> AuditContextManager:
    """Get global audit context manager."""
    global _context_manager
    if not _context_manager:
        _context_manager = AuditContextManager()
    return _context_manager


# Convenience functions
@contextmanager
def audit_data_access(user_id: str, resource_type: str, resource_id: str,
                     ip_address: Optional[str] = None, phi_accessed: bool = True):
    """Context manager for data access operations."""
    context = AuditContext(
        user_id=user_id,
        ip_address=ip_address,
        correlation_id=str(uuid.uuid4())
    )
    
    with get_audit_context_manager().audit_operation(
        action=ActionType.READ,
        action_category=ActionCategory.DATA_ACCESS,
        context=context,
        resource_type=resource_type,
        resource_id=resource_id,
        phi_accessed=phi_accessed
    ) as operation_id:
        yield operation_id


@contextmanager
def audit_dicom_operation(action: ActionType, user_id: str, dicom_id: str,
                         ip_address: Optional[str] = None):
    """Context manager for DICOM operations."""
    context = AuditContext(
        user_id=user_id,
        ip_address=ip_address,
        correlation_id=str(uuid.uuid4())
    )
    
    with get_audit_context_manager().audit_operation(
        action=action,
        action_category=ActionCategory.DATA_ACCESS,
        context=context,
        resource_type="DICOM",
        resource_id=dicom_id,
        phi_accessed=True
    ) as operation_id:
        yield operation_id


@contextmanager
def audit_ai_analysis(user_id: str, analysis_id: str, ip_address: Optional[str] = None):
    """Context manager for AI analysis operations."""
    context = AuditContext(
        user_id=user_id,
        ip_address=ip_address,
        correlation_id=str(uuid.uuid4())
    )
    
    with get_audit_context_manager().audit_operation(
        action=ActionType.AI_ANALYSIS,
        action_category=ActionCategory.DATA_ACCESS,
        context=context,
        resource_type="AI_ANALYSIS",
        resource_id=analysis_id,
        phi_accessed=True
    ) as operation_id:
        yield operation_id 