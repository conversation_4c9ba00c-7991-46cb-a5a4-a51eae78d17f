"""
Context menu handling for annotation operations.
Separated from AnnotationManager for better modularity.
"""

from typing import Optional, Callable, Dict, Any
from PySide6.QtCore import QObject, QPoint, Signal

from .types import AnnotationTool, ManualAnnotation
from .context_menu import AnnotationContextMenu


class AnnotationContextMenuHandler(QObject):
    """Handles context menu operations for annotations."""
    
    # Signals for manager coordination
    duplicate_annotation = Signal(str)  # annotation_id
    delete_annotation = Signal(str)  # annotation_id
    lock_annotation = Signal(str, bool)  # annotation_id, locked
    bring_to_front = Signal(str)  # annotation_id
    send_to_back = Signal(str)  # annotation_id
    update_graphics = Signal(str)  # annotation_id
    
    def __init__(self, parent_widget, annotation_manager=None):
        """Initialize context menu handler."""
        # Use None as parent since annotation_manager is not a QObject
        super().__init__(None)
        self.parent_widget = parent_widget
        self.annotation_manager = annotation_manager
        self.context_menu = AnnotationContextMenu(parent_widget)
        self._setup_connections()
    
    def _setup_connections(self):
        """Set up context menu signal connections."""
        self.context_menu.edit_properties_requested.connect(self._on_edit_properties)
        self.context_menu.duplicate_annotation_requested.connect(self._on_duplicate_annotation)
        self.context_menu.delete_annotation_requested.connect(self._on_delete_annotation)
        self.context_menu.lock_annotation_requested.connect(self._on_lock_annotation)
        self.context_menu.bring_to_front_requested.connect(self._on_bring_to_front)
        self.context_menu.send_to_back_requested.connect(self._on_send_to_back)
    
    def show_context_menu(self, annotation: ManualAnnotation, global_pos: QPoint):
        """Show context menu for annotation."""
        menu = self.context_menu.create_menu(annotation, global_pos)
        menu.popup(global_pos)
    
    def _on_edit_properties(self, annotation_id: str):
        """Handle edit properties request."""
        # Properties dialog will be opened by context menu
        # Signal to update graphics after property changes
        self.update_graphics.emit(annotation_id)
    
    def _on_duplicate_annotation(self, annotation_id: str):
        """Handle duplicate annotation request."""
        self.duplicate_annotation.emit(annotation_id)
    
    def _on_delete_annotation(self, annotation_id: str):
        """Handle delete annotation request."""
        self.delete_annotation.emit(annotation_id)
    
    def _on_lock_annotation(self, annotation_id: str, locked: bool):
        """Handle lock/unlock annotation request."""
        self.lock_annotation.emit(annotation_id, locked)
    
    def _on_bring_to_front(self, annotation_id: str):
        """Handle bring to front request."""
        self.bring_to_front.emit(annotation_id)
    
    def _on_send_to_back(self, annotation_id: str):
        """Handle send to back request."""
        self.send_to_back.emit(annotation_id) 