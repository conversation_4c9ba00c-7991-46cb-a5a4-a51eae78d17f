"""
Core utilities for MedScan AI.

Provides common utility functions and optimized operations used across the application.
"""

from .logging_config import get_logger
from .optimized_cv_ops import (
    OptimizedCVOperations,
    resize_optimized,
    batch_resize,
    normalize_contrast,
    denoise,
    get_cv_performance_stats,
    pooled_array,
    MemoryPool
)

__all__ = [
    # Logging utilities

    'get_logger',
    
    # Optimized OpenCV operations
    'OptimizedCVOperations',
    'resize_optimized',
    'batch_resize', 
    'normalize_contrast',
    'denoise',
    'get_cv_performance_stats',
    'pooled_array',
    'MemoryPool'
]
