"""Patient repository for HIPAA-compliant patient data operations."""

from typing import List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Session

from ..models.patient import Patient
from .base import BaseRepository


class PatientRepository(BaseRepository[Patient]):
    """Repository for patient data operations with HIPAA compliance."""

    def __init__(self, session: Session):
        super().__init__(Patient, session)

    def get_by_anonymized_id(self, anonymized_id: str) -> Optional[Patient]:
        """Get patient by anonymized ID."""
        return self.find_one_by(anonymized_id=anonymized_id)

    def search_by_demographics(
        self, age_group: str = None, gender: str = None
    ) -> List[Patient]:
        """Search patients by demographic criteria."""
        filters = {}
        if age_group:
            filters["age_group"] = age_group
        if gender:
            filters["gender"] = gender
        return self.filter_by(**filters)

    def get_expired_patients(self) -> List[Patient]:
        """Get patients with expired data retention."""
        return [p for p in self.get_all() if p.is_retention_expired()]

    def count_by_age_group(self) -> dict:
        """Get patient count by age group."""
        results = (
            self.session.query(Patient.age_group, func.count(Patient.id))
            .group_by(Patient.age_group)
            .all()
        )

        return {age_group: count for age_group, count in results}

    def get_patients_with_studies(self) -> List[Patient]:
        """Get patients who have associated studies."""
        return self.session.query(Patient).filter(Patient.studies.any()).all()
