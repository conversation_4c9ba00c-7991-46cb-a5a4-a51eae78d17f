"""
MedScan AI - Advanced Medical Imaging Analysis Application

A comprehensive medical imaging analysis application with AI-powered anomaly detection,
DICOM support, and comprehensive security features.
"""

__version__ = "0.1.0"
__author__ = "MedScan AI Team"
__email__ = "<EMAIL>"

# Package metadata
__all__ = [
    "__version__",
    "__author__", 
    "__email__",
]

# Import main modules when package is imported
try:
    from . import core
    from . import gui
    from . import models
    from . import utils
    __all__.extend(["core", "gui", "models", "utils"])
except ImportError:
    # During initial setup, modules may not exist yet
    pass 