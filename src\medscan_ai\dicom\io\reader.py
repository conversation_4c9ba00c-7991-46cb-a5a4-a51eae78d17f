"""
Core DICOM file reading and processing functionality.
"""

import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

import numpy as np
import pydicom
from pydicom.dataset import Dataset
from pydicom.errors import InvalidDicomError

from ..exceptions import (
    DicomFileNotFoundError,
    DicomFileReadError,
    DicomFormatError,
    DicomPixelDataError,
    DicomPixelProcessingError,
)
from ..metadata import DicomMetadata, DicomMetadataExtractor
from ..processing import PixelProcessor
from ..validation import DicomValidator


class DicomReader:
    """
    Main class for reading and processing DICOM files.

    Provides functionality to:
    - Load DICOM files safely with validation
    - Extract comprehensive metadata
    - Access pixel data with proper handling
    - Support various DICOM modalities
    """

    def __init__(self, validate_on_load: bool = True):
        """
        Initialize DICOM reader.

        Args:
            validate_on_load: Whether to validate DICOM files when loading
        """
        self.validate_on_load = validate_on_load
        self._current_dataset: Optional[Dataset] = None
        self._current_metadata: Optional[DicomMetadata] = None
        self._current_file_path: Optional[str] = None
        self._pixel_processor = PixelProcessor()

    def load_file(self, file_path: str, force: bool = False) -> Dataset:
        """
        Load a DICOM file and return the dataset.

        Args:
            file_path: Path to the DICOM file
            force: Whether to force reading even if validation fails

        Returns:
            pydicom Dataset object

        Raises:
            DicomFileNotFoundError: If file doesn't exist
            DicomFileReadError: If file cannot be read
            DicomFormatError: If file is not valid DICOM
        """
        try:
            # Validate file existence
            if not os.path.exists(file_path):
                raise DicomFileNotFoundError(f"File not found: {file_path}")

            # Validate file if required
            if self.validate_on_load and not force:
                is_valid, errors = DicomValidator.validate_file_integrity(file_path)
                if not is_valid:
                    error_msg = "; ".join(
                        [e for e in errors if not e.startswith("Warning:")]
                    )
                    raise DicomFormatError(f"DICOM validation failed: {error_msg}")

            # Read the DICOM file
            try:
                dataset = pydicom.dcmread(file_path, force=force)
            except InvalidDicomError as e:
                raise DicomFormatError(f"Invalid DICOM format: {str(e)}")
            except Exception as e:
                raise DicomFileReadError(f"Error reading DICOM file: {str(e)}")

            # Store current state
            self._current_dataset = dataset
            self._current_file_path = file_path
            self._current_metadata = None  # Will be extracted on demand

            return dataset

        except (DicomFileNotFoundError, DicomFileReadError, DicomFormatError):
            raise
        except Exception as e:
            raise DicomFileReadError(f"Unexpected error loading DICOM file: {str(e)}")

    def get_metadata(self, dataset: Optional[Dataset] = None) -> DicomMetadata:
        """
        Extract complete metadata from DICOM dataset.

        Args:
            dataset: Optional dataset, uses current if not provided

        Returns:
            DicomMetadata object with extracted information
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        # Use cached metadata if available
        if self._current_metadata is not None and dataset == self._current_dataset:
            return self._current_metadata

        # Extract metadata
        metadata = DicomMetadataExtractor.extract_complete_metadata(
            dataset, self._current_file_path
        )

        # Cache if this is the current dataset
        if dataset == self._current_dataset:
            self._current_metadata = metadata

        return metadata

    def get_pixel_array(self, dataset: Optional[Dataset] = None) -> np.ndarray:
        """
        Extract pixel data from DICOM dataset as NumPy array.

        Args:
            dataset: Optional dataset, uses current if not provided

        Returns:
            NumPy array containing pixel data

        Raises:
            DicomPixelDataError: If pixel data cannot be extracted
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        try:
            if not hasattr(dataset, "pixel_array"):
                raise DicomPixelDataError("No pixel data found in DICOM file")

            pixel_array = dataset.pixel_array

            if pixel_array is None:
                raise DicomPixelDataError("Pixel array is None")

            if pixel_array.size == 0:
                raise DicomPixelDataError("Pixel array is empty")

            return pixel_array

        except Exception as e:
            if isinstance(e, DicomPixelDataError):
                raise
            raise DicomPixelDataError(f"Error extracting pixel data: {str(e)}")

    def get_normalized_pixel_array(
        self,
        dataset: Optional[Dataset] = None,
        apply_windowing: bool = True,
        window_center: Optional[float] = None,
        window_width: Optional[float] = None,
        output_range: tuple = (0, 255),
    ) -> np.ndarray:
        """
        Get normalized pixel array with proper scaling and windowing.

        Args:
            dataset: Optional dataset, uses current if not provided
            apply_windowing: Whether to apply window/level if available
            window_center: Override window center value
            window_width: Override window width value
            output_range: Output range for normalization (default: 0-255)

        Returns:
            Normalized pixel array ready for display

        Raises:
            DicomPixelProcessingError: If processing fails
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        try:
            if apply_windowing:
                # Use full processing pipeline with windowing
                normalized = self._pixel_processor.process_full_pipeline(
                    dataset,
                    window_center=window_center,
                    window_width=window_width,
                    output_range=(output_range[0] / 255.0, output_range[1] / 255.0),
                )
                # Scale to output range
                normalized = normalized * 255.0
            else:
                # Apply only modality transforms and photometric interpretation
                pixel_array = self._pixel_processor.extract_pixel_array(dataset)
                pixel_array = self._pixel_processor.apply_modality_transforms(
                    pixel_array, dataset
                )
                pixel_array = self._pixel_processor.handle_photometric_interpretation(
                    pixel_array, dataset
                )
                normalized = self._pixel_processor.normalize_to_display_range(
                    pixel_array, (output_range[0] / 255.0, output_range[1] / 255.0)
                )
                # Scale to output range
                normalized = normalized * 255.0

            return normalized.astype(np.uint8)

        except Exception as e:
            raise DicomPixelProcessingError(
                f"Failed to get normalized pixel array: {str(e)}"
            )

    def get_processed_pixel_array(
        self,
        dataset: Optional[Dataset] = None,
        window_center: Optional[float] = None,
        window_width: Optional[float] = None,
        output_range: tuple = (0.0, 1.0),
    ) -> np.ndarray:
        """
        Get fully processed pixel array using advanced processing pipeline.

        Args:
            dataset: Optional dataset, uses current if not provided
            window_center: Override window center value
            window_width: Override window width value
            output_range: Output value range

        Returns:
            Fully processed pixel array

        Raises:
            DicomPixelProcessingError: If processing fails
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        return self._pixel_processor.process_full_pipeline(
            dataset,
            window_center=window_center,
            window_width=window_width,
            output_range=output_range,
        )

    def get_pixel_info(self, dataset: Optional[Dataset] = None) -> Dict[str, Any]:
        """
        Get comprehensive pixel information from dataset.

        Args:
            dataset: Optional dataset, uses current if not provided

        Returns:
            Dictionary with detailed pixel information
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        return self._pixel_processor.get_pixel_info(dataset)

    def apply_custom_windowing(
        self,
        dataset: Optional[Dataset] = None,
        window_center: float = 0,
        window_width: float = 1,
        output_range: tuple = (0, 255),
    ) -> np.ndarray:
        """
        Apply custom windowing parameters to pixel data.

        Args:
            dataset: Optional dataset, uses current if not provided
            window_center: Custom window center value
            window_width: Custom window width value
            output_range: Output range for display

        Returns:
            Windowed pixel array
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        try:
            # Extract and apply modality transforms
            pixel_array = self._pixel_processor.extract_pixel_array(dataset)
            pixel_array = self._pixel_processor.apply_modality_transforms(
                pixel_array, dataset
            )
            pixel_array = self._pixel_processor.handle_photometric_interpretation(
                pixel_array, dataset
            )

            # Apply custom windowing
            windowed = self._pixel_processor.apply_voi_transforms(
                pixel_array, dataset, window_center, window_width
            )

            # Normalize to output range
            normalized = self._pixel_processor.normalize_to_display_range(
                windowed, (output_range[0] / 255.0, output_range[1] / 255.0)
            )

            return (normalized * 255.0).astype(np.uint8)

        except Exception as e:
            raise DicomPixelProcessingError(
                f"Failed to apply custom windowing: {str(e)}"
            )

    def _normalize_array(self, array: np.ndarray) -> np.ndarray:
        """Normalize array to 0-255 range."""
        array_min = np.min(array)
        array_max = np.max(array)

        if array_max == array_min:
            return np.zeros_like(array)

        normalized = (array - array_min) / (array_max - array_min)
        return normalized * 255

    def get_image_info(self, dataset: Optional[Dataset] = None) -> Dict[str, Any]:
        """
        Get essential image information for display.

        Args:
            dataset: Optional dataset, uses current if not provided

        Returns:
            Dictionary with image display information
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        info = {}

        # Basic image dimensions
        if hasattr(dataset, "Rows"):
            info["rows"] = int(dataset.Rows)
        if hasattr(dataset, "Columns"):
            info["columns"] = int(dataset.Columns)

        # Pixel information
        if hasattr(dataset, "BitsAllocated"):
            info["bits_allocated"] = int(dataset.BitsAllocated)
        if hasattr(dataset, "BitsStored"):
            info["bits_stored"] = int(dataset.BitsStored)
        if hasattr(dataset, "PixelRepresentation"):
            info["pixel_representation"] = int(dataset.PixelRepresentation)

        # Photometric interpretation
        if hasattr(dataset, "PhotometricInterpretation"):
            info["photometric_interpretation"] = str(dataset.PhotometricInterpretation)

        # Spacing information
        if hasattr(dataset, "PixelSpacing"):
            try:
                spacing = dataset.PixelSpacing
                info["pixel_spacing"] = [float(spacing[0]), float(spacing[1])]
            except (IndexError, ValueError, TypeError):
                pass

        if hasattr(dataset, "SliceThickness"):
            try:
                info["slice_thickness"] = float(dataset.SliceThickness)
            except (ValueError, TypeError):
                pass

        # Window/Level information
        if hasattr(dataset, "WindowCenter"):
            try:
                info["window_center"] = float(dataset.WindowCenter)
            except (ValueError, TypeError):
                pass

        if hasattr(dataset, "WindowWidth"):
            try:
                info["window_width"] = float(dataset.WindowWidth)
            except (ValueError, TypeError):
                pass

        return info

    def get_patient_summary(self, dataset: Optional[Dataset] = None) -> Dict[str, str]:
        """
        Get patient summary information for display.

        Args:
            dataset: Optional dataset, uses current if not provided

        Returns:
            Dictionary with patient summary
        """
        if dataset is None:
            if self._current_dataset is None:
                raise DicomFileReadError("No DICOM file loaded")
            dataset = self._current_dataset

        summary = {}

        # Patient information
        if hasattr(dataset, "PatientName"):
            summary["patient_name"] = str(dataset.PatientName)
        if hasattr(dataset, "PatientID"):
            summary["patient_id"] = str(dataset.PatientID)
        if hasattr(dataset, "PatientSex"):
            summary["patient_sex"] = str(dataset.PatientSex)
        if hasattr(dataset, "PatientAge"):
            summary["patient_age"] = str(dataset.PatientAge)

        # Study information
        if hasattr(dataset, "StudyDate"):
            summary["study_date"] = str(dataset.StudyDate)
        if hasattr(dataset, "StudyDescription"):
            summary["study_description"] = str(dataset.StudyDescription)
        if hasattr(dataset, "Modality"):
            summary["modality"] = str(dataset.Modality)

        # Series information
        if hasattr(dataset, "SeriesDescription"):
            summary["series_description"] = str(dataset.SeriesDescription)
        if hasattr(dataset, "BodyPartExamined"):
            summary["body_part"] = str(dataset.BodyPartExamined)

        return summary

    def validate_current_file(self) -> Dict[str, Any]:
        """
        Validate the currently loaded DICOM file.

        Returns:
            Validation summary dictionary
        """
        if self._current_file_path is None:
            raise DicomFileReadError("No DICOM file loaded")

        return DicomValidator.get_validation_summary(self._current_file_path)

    def close(self):
        """Clear current dataset and free memory."""
        self._current_dataset = None
        self._current_metadata = None
        self._current_file_path = None

    @property
    def is_loaded(self) -> bool:
        """Check if a DICOM file is currently loaded."""
        return self._current_dataset is not None

    @property
    def current_file_path(self) -> Optional[str]:
        """Get the path of the currently loaded file."""
        return self._current_file_path

    @property
    def current_dataset(self) -> Optional[Dataset]:
        """Get the current dataset (read-only access)."""
        return self._current_dataset

    def get_dataset(self) -> Optional[Dataset]:
        """
        Get the current dataset.

        Returns:
            Current pydicom Dataset object or None if no file loaded
        """
        return self._current_dataset

    @staticmethod
    def is_dicom_file(file_path: str) -> bool:
        """
        Quick check if a file is likely a DICOM file.

        Args:
            file_path: Path to check

        Returns:
            True if file appears to be DICOM
        """
        try:
            is_valid, _ = DicomValidator.validate_dicom_format(file_path)
            return is_valid
        except Exception:
            return False

    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        Get basic file information without full loading.

        Args:
            file_path: Path to DICOM file

        Returns:
            Dictionary with basic file information
        """
        info = {
            "file_path": file_path,
            "file_size": 0,
            "is_dicom": False,
            "modality": None,
            "patient_id": None,
            "study_date": None,
        }

        try:
            # File size
            if os.path.exists(file_path):
                info["file_size"] = os.path.getsize(file_path)

            # Quick DICOM check
            info["is_dicom"] = DicomReader.is_dicom_file(file_path)

            if info["is_dicom"]:
                # Read minimal metadata
                dataset = pydicom.dcmread(file_path, stop_before_pixels=True)

                if hasattr(dataset, "Modality"):
                    info["modality"] = str(dataset.Modality)
                if hasattr(dataset, "PatientID"):
                    info["patient_id"] = str(dataset.PatientID)
                if hasattr(dataset, "StudyDate"):
                    info["study_date"] = str(dataset.StudyDate)

        except Exception:
            pass  # Return basic info even if metadata extraction fails

        return info
