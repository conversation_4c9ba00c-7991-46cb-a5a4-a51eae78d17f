#!/usr/bin/env python3
"""
MedScan AI - Medical Imaging Analysis Assistant
Setup script for package installation and distribution
"""

from setuptools import setup, find_packages
import os
import sys

# Ensure Python 3.10+ requirement
if sys.version_info < (3, 10):
    sys.exit("Python 3.10 or higher is required for MedScan AI")

# Read long description from README
def read_file(filename):
    with open(os.path.join(os.path.dirname(__file__), filename), 'r', encoding='utf-8') as f:
        return f.read()

# Read requirements from requirements.txt
def read_requirements():
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    # Basic package information
    name="medscan-ai",
    version="1.0.0",
    description="Cross-platform desktop medical imaging analysis assistant with AI-powered anomaly detection",
    long_description=read_file("README.md"),
    long_description_content_type="text/markdown",
    
    # Author and maintainer information
    author="MedScan AI Development Team",
    author_email="<EMAIL>",
    maintainer="MedScan AI Development Team",
    maintainer_email="<EMAIL>",
    
    # URLs
    url="https://github.com/medscan-ai/medscan-ai",
    project_urls={
        "Documentation": "https://docs.medscan-ai.com",
        "Source Code": "https://github.com/medscan-ai/medscan-ai",
        "Bug Tracker": "https://github.com/medscan-ai/medscan-ai/issues",
        "Funding": "https://github.com/sponsors/medscan-ai",
    },
    
    # Package discovery
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # Include non-Python files
    include_package_data=True,
    package_data={
        "medscan": [
            "resources/*.json",
            "resources/*.yaml",
            "resources/icons/*.png",
            "resources/icons/*.svg",
            "resources/models/*.tflite",
            "resources/templates/*.html",
            "resources/templates/*.jinja2",
        ],
    },
    
    # Dependencies
    install_requires=read_requirements(),
    
    # Python version requirement
    python_requires=">=3.10",
    
    # Entry points for command line scripts
    entry_points={
        "console_scripts": [
            "medscan=medscan.main:main",
            "medscan-cli=medscan.cli.main:main",
        ],
        "gui_scripts": [
            "medscan-gui=medscan.gui.main:main",
        ],
    },
    
    # Package classification
    classifiers=[
        # Development Status
        "Development Status :: 4 - Beta",
        
        # Intended Audience
        "Intended Audience :: Healthcare Industry",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        
        # Topic
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Topic :: Software Development :: Libraries :: Python Modules",
        
        # License
        "License :: OSI Approved :: MIT License",
        
        # Programming Language
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3 :: Only",
        
        # Operating System
        "Operating System :: OS Independent",
        "Operating System :: Microsoft :: Windows :: Windows 10",
        "Operating System :: Microsoft :: Windows :: Windows 11",
        "Operating System :: MacOS :: MacOS X",
        "Operating System :: POSIX :: Linux",
        
        # Environment
        "Environment :: X11 Applications :: Qt",
        "Environment :: Win32 (MS Windows)",
        "Environment :: MacOS X",
        
        # Natural Language
        "Natural Language :: English",
    ],
    
    # Keywords for discovery
    keywords=[
        "medical imaging", "dicom", "ai", "machine learning", "radiology",
        "x-ray", "mri", "anomaly detection", "healthcare", "medical device",
        "pyside6", "qt", "cross-platform", "desktop application",
        "hipaa", "gdpr", "compliance", "security"
    ],
    
    # License
    license="MIT",
    
    # Extras for optional dependencies
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "pytest-qt>=4.2.0",
            "pytest-mock>=3.11.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
            "isort>=5.12.0",
            "bandit>=1.7.0",
            "safety>=2.3.0",
        ],
        "docs": [
            "sphinx>=7.1.0",
            "sphinx-rtd-theme>=1.3.0",
            "sphinx-autodoc-typehints>=1.24.0",
        ],
        "performance": [
            "numba>=0.58.0",
            "cython>=3.0.0",
        ],
        "cloud": [
            "boto3>=1.28.0",
            "azure-storage-blob>=12.17.0",
            "google-cloud-storage>=2.10.0",
        ],
    },
    
    # Data files
    data_files=[
        ("share/applications", ["resources/medscan.desktop"]),
        ("share/pixmaps", ["resources/icons/medscan.png"]),
        ("share/doc/medscan", ["docs/README.md", "docs/LICENSE"]),
    ],
    
    # Zip safety
    zip_safe=False,
    
    # Metadata
    platforms=["Windows", "macOS", "Linux"],
    
    # Additional metadata for medical software
    project_classification="Medical Device Software",
    regulatory_status="Class II Medical Device Software",
    intended_use="Diagnostic Aid for Medical Imaging Analysis",
    
    # Security and compliance metadata
    security_clearance="HIPAA/GDPR Compliant",
    data_encryption="AES-256",
    audit_logging="Comprehensive",
) 