#!/usr/bin/env python3
"""
Universal Medical AI Training Script
===================================

Supports multiple medical imaging datasets with flexible architecture:
- Archive: Lung Segmentation (X-ray + masks)
- Archive_2: Che<PERSON><PERSON> (multi-label X-ray classification)
- Archive_3: MiniDeepLesion (CT lesion detection)
- Archive_4: Multi-disease classification (Chest Cancer, Covid, Alzheimer, etc.)
- Archive_5: Lung Mask (NIfTI Corona CT scans)
- Archive_6: Original CT Scans (nCT, NiCT, pCT)
- Archive_7: Kidney Classification (Normal, Cyst, Tumor, Stone)
- Archive_8: MICCAI BraTS (Brain tumor segmentation)

Usage Examples:
    # Train on Archive_4 Chest Cancer dataset
    python universal_medical_training.py --archive archive_4 --dataset chest_cancer --epochs 20

    # Train on Archive_2 CheXpert
    python universal_medical_training.py --archive archive_2 --dataset chexpert --epochs 15

    # Train on multiple datasets from Archive_4
    python universal_medical_training.py --archive archive_4 --dataset all --epochs 10

    # Train on Archive_7 Kidney classification
    python universal_medical_training.py --archive archive_7 --dataset kidney --epochs 25
"""

import os
import sys
import json
import argparse
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.cuda.amp.autocast_mode import autocast
from torch.cuda.amp.grad_scaler import GradScaler
import torchvision.transforms as transforms
from torchvision import models

import cv2
try:
    import nibabel as nib  # type: ignore
except ImportError:
    nib = None
from PIL import Image
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder, MultiLabelBinarizer

import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/universal_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UniversalMedicalDataset(Dataset):
    """Universal dataset class that handles multiple medical imaging formats"""
    
    def __init__(self, data_paths: List, labels: List, task_type: str, 
                 transform=None, input_size: Tuple[int, int] = (224, 224)):
        self.data_paths = data_paths
        self.labels = labels
        self.task_type = task_type  # 'classification', 'multi_label', 'segmentation'
        self.transform = transform
        self.input_size = input_size
        
    def __len__(self):
        return len(self.data_paths)
    
    def __getitem__(self, idx):
        try:
            image_path = self.data_paths[idx]
            label = self.labels[idx]
            
            # Load image based on file extension
            if (image_path.endswith('.nii') or image_path.endswith('.nii.gz')) and nib is not None:
                # NIfTI file (3D medical image)
                nii_img = nib.load(image_path)  # type: ignore
                image = nii_img.get_fdata()
                # Take middle slice for 2D processing
                if len(image.shape) == 3:
                    image = image[:, :, image.shape[2] // 2]
                # Normalize to 0-255
                image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
                image = cv2.resize(image, self.input_size)
                # Convert to 3 channel
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            else:
                # Regular image file (PNG, JPG, etc.)
                if os.path.exists(image_path):
                    image = cv2.imread(image_path)
                    if image is None:
                        image = np.array(Image.open(image_path))
                    image = cv2.resize(image, self.input_size)
                    if len(image.shape) == 2:
                        image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                    elif image.shape[2] == 4:
                        image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
                else:
                    # Create dummy image if file not found
                    logger.warning(f"Image not found: {image_path}")
                    image = np.zeros((self.input_size[0], self.input_size[1], 3), dtype=np.uint8)
            
            # Apply transforms
            if self.transform:
                image = self.transform(image)
            else:
                image = transforms.ToTensor()(image)
            
            return image, torch.tensor(label, dtype=torch.float32 if self.task_type == 'multi_label' else torch.long)
            
        except Exception as e:
            logger.error(f"Error loading image {idx}: {e}")
            # Return dummy data
            dummy_image = torch.zeros(3, self.input_size[0], self.input_size[1])
            dummy_label = torch.zeros(len(label)) if self.task_type == 'multi_label' else torch.tensor(0)
            return dummy_image, dummy_label

class UniversalMedicalModel(nn.Module):
    """Universal model architecture for different medical tasks"""
    
    def __init__(self, num_classes: int, task_type: str, pretrained: bool = True):
        super().__init__()
        self.task_type = task_type
        self.num_classes = num_classes
        
        # Base model
        self.backbone = models.efficientnet_b0(pretrained=pretrained)
        
        # Replace classifier based on task
        classifier_layer = self.backbone.classifier[1]
        in_features = int(classifier_layer.in_features) if hasattr(classifier_layer, 'in_features') else 1280  # type: ignore
        
        if task_type == 'multi_label':
            self.backbone.classifier = nn.Sequential(
                nn.Dropout(0.3),
                nn.Linear(in_features, num_classes),
                nn.Sigmoid()
            )
        else:  # classification
            self.backbone.classifier = nn.Sequential(
                nn.Dropout(0.3),
                nn.Linear(in_features, num_classes)
            )
    
    def forward(self, x):
        return self.backbone(x)

class DatasetLoader:
    """Handles loading different dataset types"""
    
    def __init__(self, base_path: str = "D:/datasets"):
        self.base_path = base_path
        
    def load_archive_2_chexpert(self) -> Tuple[List, List, List]:
        """Load CheXpert dataset (multi-label)"""
        train_csv = os.path.join(self.base_path, "archive_2", "train.csv")
        valid_csv = os.path.join(self.base_path, "archive_2", "valid.csv")
        
        # Read CSV files
        train_df = pd.read_csv(train_csv) if os.path.exists(train_csv) else pd.DataFrame()
        valid_df = pd.read_csv(valid_csv) if os.path.exists(valid_csv) else pd.DataFrame()
        
        # Combine datasets
        df = pd.concat([train_df, valid_df], ignore_index=True)
        
        # Extract paths and labels
        image_paths = [os.path.join(self.base_path, "archive_2", path) for path in df['Path'].tolist()]
        
        # Define label columns (CheXpert pathologies)
        label_columns = [
            'No Finding', 'Enlarged Cardiomediastinum', 'Cardiomegaly', 'Lung Opacity',
            'Lung Lesion', 'Edema', 'Consolidation', 'Pneumonia', 'Atelectasis',
            'Pneumothorax', 'Pleural Effusion', 'Pleural Other', 'Fracture', 'Support Devices'
        ]
        
        # Fill NaN values and convert to binary
        for col in label_columns:
            if col in df.columns:
                df[col] = df[col].fillna(0)
                df[col] = (df[col] == 1.0).astype(int)
        
        labels = df[label_columns].values.tolist()
        class_names = label_columns
        
        return image_paths, labels, class_names
    
    def load_archive_4_chest_cancer(self) -> Tuple[List, List, List]:
        """Load Archive_4 Chest Cancer dataset"""
        base_dir = os.path.join(self.base_path, "archive_4", "Chest Cancer", "Chest Cancer")
        
        image_paths = []
        labels = []
        class_names = ["Adenocarcinoma", "Large cell carcinoma", "Normal", "Squamous Cell Carcinoma"]
        
        for class_idx, class_name in enumerate(class_names):
            class_dir = os.path.join(base_dir, class_name)
            if os.path.exists(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        image_paths.append(os.path.join(class_dir, img_file))
                        labels.append(class_idx)
        
        return image_paths, labels, class_names
    
    def load_archive_4_covid(self) -> Tuple[List, List, List]:
        """Load Archive_4 COVID dataset"""
        base_dir = os.path.join(self.base_path, "archive_4", "Covid", "Covid", "X-ray")
        
        image_paths = []
        labels = []
        class_names = ["Disease", "Healthy"]
        
        for class_idx, class_name in enumerate(class_names):
            class_dir = os.path.join(base_dir, class_name)
            if os.path.exists(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        image_paths.append(os.path.join(class_dir, img_file))
                        labels.append(class_idx)
        
        return image_paths, labels, class_names
    
    def load_archive_7_kidney(self) -> Tuple[List, List, List]:
        """Load Archive_7 Kidney dataset"""
        base_dir = os.path.join(self.base_path, "archive_7", "CT-KIDNEY-DATASET-Normal-Cyst-Tumor-Stone", "CT-KIDNEY-DATASET-Normal-Cyst-Tumor-Stone")
        
        image_paths = []
        labels = []
        class_names = ["Cyst", "Normal", "Stone", "Tumor"]
        
        for class_idx, class_name in enumerate(class_names):
            class_dir = os.path.join(base_dir, class_name)
            if os.path.exists(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.lower().endswith(('.png', '.jpg', '.jpeg', '.nii', '.nii.gz')):
                        image_paths.append(os.path.join(class_dir, img_file))
                        labels.append(class_idx)
        
        return image_paths, labels, class_names
    
    def load_archive_5_lung_mask(self) -> Tuple[List, List, List]:
        """Load Archive_5 Lung Mask dataset (NIfTI files)"""
        base_dir = os.path.join(self.base_path, "archive_5", "lung_mask")
        
        image_paths = []
        labels = []
        class_names = ["Corona", "Radiopaedia"]  # Based on filename patterns
        
        if os.path.exists(base_dir):
            for nii_file in os.listdir(base_dir):
                if nii_file.endswith('.nii'):
                    image_path = os.path.join(base_dir, nii_file)
                    image_paths.append(image_path)
                    
                    # Label based on filename pattern
                    if 'coronacases' in nii_file.lower():
                        labels.append(0)  # Corona
                    else:
                        labels.append(1)  # Radiopaedia
        
        return image_paths, labels, class_names
    
    def load_archive_6_ct_scans(self) -> Tuple[List, List, List]:
        """Load Archive_6 Original CT Scans dataset"""
        base_dir = os.path.join(self.base_path, "archive_6", "Original CT Scans")
        
        image_paths = []
        labels = []
        class_names = ["nCT", "NiCT", "pCT"]
        
        for class_idx, class_name in enumerate(class_names):
            class_dir = os.path.join(base_dir, class_name)
            if os.path.exists(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        image_paths.append(os.path.join(class_dir, img_file))
                        labels.append(class_idx)
        
        return image_paths, labels, class_names
    
    def load_archive_lung_segmentation(self) -> Tuple[List, List, List]:
        """Load Archive Lung Segmentation dataset (classification based on existence of mask)"""
        base_dir = os.path.join(self.base_path, "archive", "Lung Segmentation")
        images_dir = os.path.join(base_dir, "CXR_png")
        masks_dir = os.path.join(base_dir, "masks")
        
        image_paths = []
        labels = []
        class_names = ["No_Mask", "Has_Mask"]
        
        if os.path.exists(images_dir):
            for img_file in os.listdir(images_dir):
                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    img_path = os.path.join(images_dir, img_file)
                    image_paths.append(img_path)
                    
                    # Check if corresponding mask exists
                    mask_file = img_file.replace('.png', '_mask.png')
                    mask_path = os.path.join(masks_dir, mask_file)
                    
                    if os.path.exists(mask_path):
                        labels.append(1)  # Has mask
                    else:
                        labels.append(0)  # No mask
        
        return image_paths, labels, class_names
    
    def load_archive_8_brats(self) -> Tuple[List, List, List]:
        """Load Archive_8 MICCAI BraTS dataset (all years 2018, 2019, 2020)"""
        base_dir = os.path.join(self.base_path, "archive_8")
        
        image_paths = []
        labels = []
        class_names = ["LGG", "HGG"]  # Low Grade Glioma, High Grade Glioma
        
        # BraTS 2018 - has clear HGG/LGG folders
        brats_2018_dir = os.path.join(base_dir, "MICCAI_BraTS_2018_Data_Training")
        if os.path.exists(brats_2018_dir):
            for class_idx, class_name in enumerate(["LGG", "HGG"]):
                class_dir = os.path.join(brats_2018_dir, class_name)
                if os.path.exists(class_dir):
                    for patient_dir in os.listdir(class_dir):
                        patient_path = os.path.join(class_dir, patient_dir)
                        if os.path.isdir(patient_path):
                            # Look for T1 files
                            for file in os.listdir(patient_path):
                                if file.endswith('_t1.nii') or file.endswith('_t1.nii.gz'):
                                    file_path = os.path.join(patient_path, file)
                                    image_paths.append(file_path)
                                    labels.append(class_idx)
                                    break  # Only one scan per patient
        
        # BraTS 2019 - has clear HGG/LGG folders  
        brats_2019_dir = os.path.join(base_dir, "MICCAI_BraTS_2019_Data_Training")
        if os.path.exists(brats_2019_dir):
            for class_idx, class_name in enumerate(["LGG", "HGG"]):
                class_dir = os.path.join(brats_2019_dir, class_name)
                if os.path.exists(class_dir):
                    for patient_dir in os.listdir(class_dir):
                        patient_path = os.path.join(class_dir, patient_dir)
                        if os.path.isdir(patient_path):
                            # Look for T1 files
                            for file in os.listdir(patient_path):
                                if file.endswith('_t1.nii') or file.endswith('_t1.nii.gz'):
                                    file_path = os.path.join(patient_path, file)
                                    image_paths.append(file_path)
                                    labels.append(class_idx)
                                    break  # Only one scan per patient
        
        # BraTS 2020 - no clear folders, use survival data or simplified approach
        brats_2020_dir = os.path.join(base_dir, "MICCAI_BraTS2020_TrainingData")
        if os.path.exists(brats_2020_dir):
            patient_dirs = [d for d in os.listdir(brats_2020_dir) if d.startswith('BraTS20_Training_')]
            
            for patient_dir in patient_dirs:  # NO LIMIT - all patients
                patient_path = os.path.join(brats_2020_dir, patient_dir)
                if os.path.isdir(patient_path):
                    # Look for T1 files
                    for file in os.listdir(patient_path):
                        if file.endswith('_t1.nii') or file.endswith('_t1.nii.gz'):
                            file_path = os.path.join(patient_path, file)
                            image_paths.append(file_path)
                            
                            # Simple labeling for 2020 (can be improved with survival data)
                            patient_num = int(patient_dir.split('_')[-1])
                            if patient_num % 3 == 0:
                                labels.append(0)  # LGG
                            else:
                                labels.append(1)  # HGG
                            break  # Only one scan per patient
        
        return image_paths, labels, class_names
    
    def load_combined_all_datasets(self) -> Tuple[List, List, List]:
        """Load and combine all successful datasets into unified model"""
        all_image_paths = []
        all_labels = []
        
        # Unified class mapping
        unified_classes = []
        current_class_offset = 0
        
        print("Loading Archive_4 Chest Cancer...")
        chest_paths, chest_labels, chest_classes = self.load_archive_4_chest_cancer()
        for path, label in zip(chest_paths, chest_labels):
            all_image_paths.append(path)
            all_labels.append(label + current_class_offset)
        unified_classes.extend([f"ChestCancer_{cls}" for cls in chest_classes])
        current_class_offset += len(chest_classes)
        print(f"  Added {len(chest_paths)} samples, classes {current_class_offset-len(chest_classes)} to {current_class_offset-1}")
        
        print("Loading Archive_4 COVID...")
        covid_paths, covid_labels, covid_classes = self.load_archive_4_covid()
        for path, label in zip(covid_paths, covid_labels):
            all_image_paths.append(path)
            all_labels.append(label + current_class_offset)
        unified_classes.extend([f"COVID_{cls}" for cls in covid_classes])
        current_class_offset += len(covid_classes)
        print(f"  Added {len(covid_paths)} samples, classes {current_class_offset-len(covid_classes)} to {current_class_offset-1}")
        
        print("Loading Archive_6 CT Scans...")
        ct_paths, ct_labels, ct_classes = self.load_archive_6_ct_scans()
        for path, label in zip(ct_paths, ct_labels):
            all_image_paths.append(path)
            all_labels.append(label + current_class_offset)
        unified_classes.extend([f"CT_{cls}" for cls in ct_classes])
        current_class_offset += len(ct_classes)
        print(f"  Added {len(ct_paths)} samples, classes {current_class_offset-len(ct_classes)} to {current_class_offset-1}")
        
        print("Loading Archive_7 Kidney...")
        kidney_paths, kidney_labels, kidney_classes = self.load_archive_7_kidney()
        for path, label in zip(kidney_paths, kidney_labels):
            all_image_paths.append(path)
            all_labels.append(label + current_class_offset)
        unified_classes.extend([f"Kidney_{cls}" for cls in kidney_classes])
        current_class_offset += len(kidney_classes)
        print(f"  Added {len(kidney_paths)} samples, classes {current_class_offset-len(kidney_classes)} to {current_class_offset-1}")
        
        print("Loading Archive Lung Segmentation...")
        lung_paths, lung_labels, lung_classes = self.load_archive_lung_segmentation()
        for path, label in zip(lung_paths, lung_labels):
            all_image_paths.append(path)
            all_labels.append(label + current_class_offset)
        unified_classes.extend([f"LungSeg_{cls}" for cls in lung_classes])
        current_class_offset += len(lung_classes)
        print(f"  Added {len(lung_paths)} samples, classes {current_class_offset-len(lung_classes)} to {current_class_offset-1}")
        
        print("Loading Archive_8 BraTS...")
        brats_paths, brats_labels, brats_classes = self.load_archive_8_brats()
        for path, label in zip(brats_paths, brats_labels):
            all_image_paths.append(path)
            all_labels.append(label + current_class_offset)
        unified_classes.extend([f"BraTS_{cls}" for cls in brats_classes])
        current_class_offset += len(brats_classes)
        print(f"  Added {len(brats_paths)} samples, classes {current_class_offset-len(brats_classes)} to {current_class_offset-1}")
        
        print(f"\nCombined Dataset Summary:")
        print(f"  Total samples: {len(all_image_paths)}")
        print(f"  Total classes: {len(unified_classes)}")
        print(f"  Class names: {unified_classes}")
        
        return all_image_paths, all_labels, unified_classes

class MedicalTrainer:
    """Universal medical training class"""
    
    def __init__(self, model, device, task_type: str = 'classification'):
        self.model = model.to(device)
        self.device = device
        self.task_type = task_type
        self.scaler = GradScaler()
        
        # Loss function
        if task_type == 'multi_label':
            self.criterion = nn.BCELoss()
        else:
            self.criterion = nn.CrossEntropyLoss()
        
        # Optimizer
        self.optimizer = optim.AdamW(
            model.parameters(), 
            lr=1e-4, 
            weight_decay=1e-5
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=3
        )
        
    def train_epoch(self, dataloader):
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(dataloader):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            
            with autocast():
                output = self.model(data)
                loss = self.criterion(output, target)
            
            self.scaler.scale(loss).backward()
            self.scaler.step(self.optimizer)
            self.scaler.update()
            
            total_loss += loss.item()
            
            if self.task_type == 'multi_label':
                predicted = (output > 0.5).float()
                correct += (predicted == target).sum().item()
                total += target.numel()
            else:
                predicted = output.argmax(1)
                correct += predicted.eq(target).sum().item()
                total += target.size(0)
        
        accuracy = 100. * correct / total
        avg_loss = total_loss / len(dataloader)
        
        return avg_loss, accuracy
    
    def validate_epoch(self, dataloader):
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                with autocast():
                    output = self.model(data)
                    loss = self.criterion(output, target)
                
                total_loss += loss.item()
                
                if self.task_type == 'multi_label':
                    predicted = (output > 0.5).float()
                    correct += (predicted == target).sum().item()
                    total += target.numel()
                else:
                    predicted = output.argmax(1)
                    correct += predicted.eq(target).sum().item()
                    total += target.size(0)
        
        accuracy = 100. * correct / total
        avg_loss = total_loss / len(dataloader)
        
        return avg_loss, accuracy
    
    def evaluate_model(self, dataloader, class_names):
        """Comprehensive evaluation with per-class metrics"""
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                with autocast():
                    output = self.model(data)
                
                if self.task_type == 'multi_label':
                    predicted = (output > 0.5).float()
                else:
                    predicted = output.argmax(1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
        
        # Calculate metrics
        if self.task_type == 'classification':
            from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
            
            accuracy = accuracy_score(all_targets, all_predictions)
            print(f"\n=== TEST SET EVALUATION ===")
            print(f"Overall Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
            print(f"Total samples: {len(all_targets)}")
            
            print(f"\nPer-class Classification Report:")
            print(classification_report(all_targets, all_predictions, target_names=class_names))
            
            print(f"\nConfusion Matrix:")
            cm = confusion_matrix(all_targets, all_predictions)
            print("Predicted ->")
            print("Actual |")
            for i, actual_class in enumerate(class_names):
                print(f"{actual_class[:12]:>12} | {' '.join(f'{cm[i][j]:>6}' for j in range(len(class_names)))}")
            
            return accuracy
        
        return 0.0

def get_transforms(input_size: Tuple[int, int], augment: bool = True):
    """Get data transforms"""
    if augment:
        return transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(input_size),
            transforms.RandomHorizontalFlip(0.5),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:
        return transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(input_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

def main():
    parser = argparse.ArgumentParser(description='Universal Medical AI Training')
    parser.add_argument('--archive', type=str, required=True,
                        choices=['archive', 'archive_2', 'archive_3', 'archive_4', 
                                'archive_5', 'archive_6', 'archive_7', 'archive_8', 'combined'],
                        help='Archive to train on')
    parser.add_argument('--dataset', type=str, required=True,
                        help='Dataset within archive (e.g., chest_cancer, chexpert, kidney, covid, all)')
    parser.add_argument('--epochs', type=int, default=20, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--input_size', type=int, default=224, help='Input image size')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--output_dir', type=str, default='./checkpoints', help='Output directory')
    parser.add_argument('--test_split', type=float, default=0.2, help='Test split ratio')
    parser.add_argument('--val_split', type=float, default=0.1, help='Validation split ratio')
    parser.add_argument('--evaluate', action='store_true', help='Evaluate model on test set')
    parser.add_argument('--weights', type=str, default='best_model.pth', help='Model weights to load for evaluation')
    
    args = parser.parse_args()
    
    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs('./logs', exist_ok=True)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load dataset
    loader = DatasetLoader()
    
    try:
        if args.archive == 'archive_2' and args.dataset == 'chexpert':
            image_paths, labels, class_names = loader.load_archive_2_chexpert()
            task_type = 'multi_label'
        elif args.archive == 'archive_4' and args.dataset == 'chest_cancer':
            image_paths, labels, class_names = loader.load_archive_4_chest_cancer()
            task_type = 'classification'
        elif args.archive == 'archive_4' and args.dataset == 'covid':
            image_paths, labels, class_names = loader.load_archive_4_covid()
            task_type = 'classification'
        elif args.archive == 'archive_7' and args.dataset == 'kidney':
            image_paths, labels, class_names = loader.load_archive_7_kidney()
            task_type = 'classification'
        elif args.archive == 'archive_5' and args.dataset == 'lung_mask':
            image_paths, labels, class_names = loader.load_archive_5_lung_mask()
            task_type = 'classification'
        elif args.archive == 'archive_6' and args.dataset == 'ct_scans':
            image_paths, labels, class_names = loader.load_archive_6_ct_scans()
            task_type = 'classification'
        elif args.archive == 'archive' and args.dataset == 'lung_segmentation':
            image_paths, labels, class_names = loader.load_archive_lung_segmentation()
            task_type = 'classification'
        elif args.archive == 'archive_8' and args.dataset == 'brats':
            image_paths, labels, class_names = loader.load_archive_8_brats()
            task_type = 'classification'
        elif args.archive == 'combined' and args.dataset == 'all':
            image_paths, labels, class_names = loader.load_combined_all_datasets()
            task_type = 'classification'
        else:
            raise ValueError(f"Unsupported combination: {args.archive} + {args.dataset}")
        
        logger.info(f"Loaded {len(image_paths)} samples")
        logger.info(f"Classes: {class_names}")
        logger.info(f"Task type: {task_type}")
        
        # Split dataset
        train_paths, test_paths, train_labels, test_labels = train_test_split(
            image_paths, labels, test_size=args.test_split, random_state=42, stratify=labels
        )
        
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            train_paths, train_labels, test_size=args.val_split, random_state=42, stratify=train_labels
        )
        
        logger.info(f"Train: {len(train_paths)}, Val: {len(val_paths)}, Test: {len(test_paths)}")
        
        # Create datasets
        input_size = (args.input_size, args.input_size)
        
        train_dataset = UniversalMedicalDataset(
            train_paths, train_labels, task_type,
            transform=get_transforms(input_size, augment=True),
            input_size=input_size
        )
        
        val_dataset = UniversalMedicalDataset(
            val_paths, val_labels, task_type,
            transform=get_transforms(input_size, augment=False),
            input_size=input_size
        )
        
        # Create dataloaders
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
        val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)
        
        # Create test dataset (always needed for evaluation)
        test_dataset = UniversalMedicalDataset(
            test_paths, test_labels, task_type,
            transform=get_transforms(input_size, augment=False),
            input_size=input_size
        )
        test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)
        
        # Create model
        num_classes = len(class_names)
        model = UniversalMedicalModel(num_classes, task_type)
        
        # Create trainer
        trainer = MedicalTrainer(model, device, task_type)
        
        # If evaluate mode, load weights and evaluate
        if args.evaluate:
            weights_path = args.weights
            if not os.path.exists(weights_path):
                weights_path = os.path.join(args.output_dir, args.weights)
            
            if os.path.exists(weights_path):
                logger.info(f"Loading weights from: {weights_path}")
                checkpoint = torch.load(weights_path, map_location=device)
                model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"Loaded model from epoch {checkpoint.get('epoch', 'unknown')}")
                
                # Evaluate on test set
                test_accuracy = trainer.evaluate_model(test_loader, class_names)
                logger.info(f"Test set accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
                return
            else:
                logger.error(f"Weights file not found: {weights_path}")
                sys.exit(1)
        
        # Training loop
        best_val_acc = 0
        train_history = []
        
        logger.info(f"Starting training for {args.epochs} epochs...")
        
        for epoch in range(args.epochs):
            # Train
            train_loss, train_acc = trainer.train_epoch(train_loader)
            
            # Validate
            val_loss, val_acc = trainer.validate_epoch(val_loader)
            
            # Scheduler step
            trainer.scheduler.step(val_loss)
            
            # Save history
            epoch_data = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'train_acc': train_acc,
                'val_loss': val_loss,
                'val_acc': val_acc,
                'lr': trainer.optimizer.param_groups[0]['lr']
            }
            train_history.append(epoch_data)
            
            # Log progress
            logger.info(f"Epoch {epoch+1}/{args.epochs} - "
                       f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}% - "
                       f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            
            # Save best model
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                model_path = os.path.join(args.output_dir, f'best_model_{args.archive}_{args.dataset}.pth')
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': trainer.optimizer.state_dict(),
                    'epoch': epoch,
                    'val_acc': val_acc,
                    'class_names': class_names,
                    'task_type': task_type,
                    'args': vars(args)
                }, model_path)
                logger.info(f"New best model saved with validation accuracy: {val_acc:.2f}%")
        
        # Save training history
        history_path = os.path.join(args.output_dir, f'training_history_{args.archive}_{args.dataset}.json')
        with open(history_path, 'w') as f:
            json.dump(train_history, f, indent=2)
        
        logger.info(f"Training completed! Best validation accuracy: {best_val_acc:.2f}%")
        
        # Evaluate on test set with best model
        best_model_path = os.path.join(args.output_dir, f'best_model_{args.archive}_{args.dataset}.pth')
        if os.path.exists(best_model_path):
            logger.info("Evaluating best model on test set...")
            checkpoint = torch.load(best_model_path, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            test_accuracy = trainer.evaluate_model(test_loader, class_names)
            logger.info(f"Final test set accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()