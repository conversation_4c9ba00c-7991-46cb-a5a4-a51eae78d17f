"""
Memory Monitor for Large Dataset Processing

This module provides real-time memory monitoring capabilities for processing
100GB+ medical imaging datasets. Monitors both system RAM and GPU VRAM usage.

Features:
- Real-time RAM and VRAM monitoring
- Memory usage alerts and limits
- Performance metrics tracking
- Memory leak detection
- Resource optimization suggestions
"""

import os
import sys
import time
import json
import threading
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import logging

import psutil

# Try to import GPU monitoring libraries
try:
    import pynvml  # type: ignore
    PYNVML_AVAILABLE = True
except ImportError:
    PYNVML_AVAILABLE = False

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

from ...core.utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class MemorySnapshot:
    """Single memory usage snapshot."""
    timestamp: datetime
    ram_used_gb: float
    ram_total_gb: float
    ram_percent: float
    vram_used_gb: float
    vram_total_gb: float
    vram_percent: float
    gpu_temperature: float
    process_count: int
    cpu_percent: float


class GPUMonitor:
    """Monitor GPU memory and temperature."""
    
    def __init__(self):
        self.available = False
        self.gpu_count = 0
        
        if PYNVML_AVAILABLE:
            try:
                pynvml.nvmlInit()
                self.gpu_count = pynvml.nvmlDeviceGetCount()
                self.available = True
                logger.info(f"GPU monitoring initialized: {self.gpu_count} GPU(s) detected")
            except Exception as e:
                logger.warning(f"GPU monitoring initialization failed: {e}")
        else:
            logger.warning("pynvml not available, GPU monitoring disabled")
    
    def get_gpu_memory_info(self, gpu_index: int = 0) -> Dict[str, float]:
        """Get GPU memory information."""
        if not self.available or gpu_index >= self.gpu_count:
            return {
                "used_gb": 0.0,
                "total_gb": 0.0,
                "percent": 0.0,
                "temperature": 0.0
            }
        
        try:
            handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_index)
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            
            # Get temperature
            try:
                temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
            except:
                temperature = 0.0
            
            return {
                "used_gb": int(mem_info.used) / (1024**3),
                "total_gb": int(mem_info.total) / (1024**3),
                "percent": (int(mem_info.used) / int(mem_info.total)) * 100,
                "temperature": temperature
            }
        except Exception as e:
            logger.warning(f"Error getting GPU memory info: {e}")
            return {
                "used_gb": 0.0,
                "total_gb": 0.0,
                "percent": 0.0,
                "temperature": 0.0
            }
    
    def get_all_gpus_info(self) -> List[Dict[str, float]]:
        """Get memory information for all GPUs."""
        return [self.get_gpu_memory_info(i) for i in range(self.gpu_count)]


class SystemResourceMonitor:
    """Monitor system resources including RAM, CPU, and disk."""
    
    def __init__(self):
        self.gpu_monitor = GPUMonitor()
    
    def get_memory_info(self) -> Dict[str, float]:
        """Get comprehensive system memory information."""
        # System RAM
        ram = psutil.virtual_memory()
        
        # GPU VRAM (primary GPU)
        gpu_info = self.gpu_monitor.get_gpu_memory_info(0)
        
        # CPU information
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Process count
        process_count = len(psutil.pids())
        
        return {
            "ram_used_gb": int(ram.used) / (1024**3),
            "ram_total_gb": int(ram.total) / (1024**3),
            "ram_percent": ram.percent,
            "ram_available_gb": int(ram.available) / (1024**3),
            "vram_used_gb": gpu_info["used_gb"],
            "vram_total_gb": gpu_info["total_gb"],
            "vram_percent": gpu_info["percent"],
            "gpu_temperature": gpu_info["temperature"],
            "cpu_percent": cpu_percent,
            "process_count": process_count
        }
    
    def get_disk_info(self, path: str = ".") -> Dict[str, float]:
        """Get disk usage information for specified path."""
        try:
            disk_usage = psutil.disk_usage(path)
            return {
                "total_gb": int(disk_usage.total) / (1024**3),
                "used_gb": int(disk_usage.used) / (1024**3),
                "free_gb": int(disk_usage.free) / (1024**3),
                "percent": (int(disk_usage.used) / int(disk_usage.total)) * 100
            }
        except Exception as e:
            logger.warning(f"Error getting disk info for {path}: {e}")
            return {
                "total_gb": 0.0,
                "used_gb": 0.0,
                "free_gb": 0.0,
                "percent": 0.0
            }
    
    def get_process_memory_info(self, pid: Optional[int] = None) -> Dict[str, float]:
        """Get memory information for specific process."""
        if pid is None:
            pid = os.getpid()
        
        try:
            process = psutil.Process(pid)
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            return {
                "rss_mb": memory_info.rss / (1024**2),  # Resident Set Size
                "vms_mb": memory_info.vms / (1024**2),  # Virtual Memory Size
                "percent": memory_percent,
                "num_threads": process.num_threads()
            }
        except Exception as e:
            logger.warning(f"Error getting process memory info for PID {pid}: {e}")
            return {
                "rss_mb": 0.0,
                "vms_mb": 0.0,
                "percent": 0.0,
                "num_threads": 0
            }


class MemoryMonitor:
    """Main memory monitoring class with alerts and limits."""
    
    def __init__(
        self,
        max_ram_usage_gb: float = 16.0,
        max_vram_usage_gb: float = 8.0,
        warning_threshold: float = 0.8,  # 80% warning threshold
        critical_threshold: float = 0.9,  # 90% critical threshold
        monitoring_interval: float = 5.0,  # 5 seconds
        enable_logging: bool = True
    ):
        self.max_ram_usage_gb = max_ram_usage_gb
        self.max_vram_usage_gb = max_vram_usage_gb
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.monitoring_interval = monitoring_interval
        self.enable_logging = enable_logging
        
        self.system_monitor = SystemResourceMonitor()
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_thread = None
        self.snapshots: List[MemorySnapshot] = []
        self.max_snapshots = 1000  # Keep last 1000 snapshots
        
        # Alert callbacks
        self.warning_callbacks: List[Callable[[MemorySnapshot], None]] = []
        self.critical_callbacks: List[Callable[[MemorySnapshot], None]] = []
        
        # Lock for thread safety
        self.lock = threading.Lock()
        
        logger.info(f"Memory monitor initialized - RAM limit: {max_ram_usage_gb}GB, VRAM limit: {max_vram_usage_gb}GB")
    
    def add_warning_callback(self, callback: Callable[[MemorySnapshot], None]):
        """Add callback for warning threshold breaches."""
        self.warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback: Callable[[MemorySnapshot], None]):
        """Add callback for critical threshold breaches."""
        self.critical_callbacks.append(callback)
    
    def get_current_memory_info(self) -> Dict[str, float]:
        """Get current memory usage information."""
        return self.system_monitor.get_memory_info()
    
    def check_memory_limits(self) -> Dict[str, bool]:
        """Check if memory usage is within acceptable limits."""
        memory_info = self.get_current_memory_info()
        
        ram_within_limit = memory_info["ram_used_gb"] <= self.max_ram_usage_gb
        vram_within_limit = memory_info["vram_used_gb"] <= self.max_vram_usage_gb
        
        # Check warning thresholds
        ram_warning = memory_info["ram_used_gb"] >= (self.max_ram_usage_gb * self.warning_threshold)
        vram_warning = memory_info["vram_used_gb"] >= (self.max_vram_usage_gb * self.warning_threshold)
        
        # Check critical thresholds
        ram_critical = memory_info["ram_used_gb"] >= (self.max_ram_usage_gb * self.critical_threshold)
        vram_critical = memory_info["vram_used_gb"] >= (self.max_vram_usage_gb * self.critical_threshold)
        
        return {
            "ram_within_limit": ram_within_limit,
            "vram_within_limit": vram_within_limit,
            "overall_within_limit": ram_within_limit and vram_within_limit,
            "ram_warning": ram_warning,
            "vram_warning": vram_warning,
            "ram_critical": ram_critical,
            "vram_critical": vram_critical
        }
    
    def create_snapshot(self) -> MemorySnapshot:
        """Create a memory usage snapshot."""
        memory_info = self.get_current_memory_info()
        
        snapshot = MemorySnapshot(
            timestamp=datetime.now(),
            ram_used_gb=memory_info["ram_used_gb"],
            ram_total_gb=memory_info["ram_total_gb"],
            ram_percent=memory_info["ram_percent"],
            vram_used_gb=memory_info["vram_used_gb"],
            vram_total_gb=memory_info["vram_total_gb"],
            vram_percent=memory_info["vram_percent"],
            gpu_temperature=memory_info["gpu_temperature"],
            process_count=int(memory_info["process_count"]),
            cpu_percent=memory_info["cpu_percent"]
        )
        
        return snapshot
    
    def _add_snapshot(self, snapshot: MemorySnapshot):
        """Add snapshot to history (thread-safe)."""
        with self.lock:
            self.snapshots.append(snapshot)
            
            # Keep only recent snapshots
            if len(self.snapshots) > self.max_snapshots:
                self.snapshots = self.snapshots[-self.max_snapshots:]
    
    def _monitoring_loop(self):
        """Main monitoring loop (runs in separate thread)."""
        logger.info("Memory monitoring started")
        
        while self.is_monitoring:
            try:
                # Create snapshot
                snapshot = self.create_snapshot()
                self._add_snapshot(snapshot)
                
                # Check for threshold breaches
                limits = self.check_memory_limits()
                
                # Log current status if enabled
                if self.enable_logging:
                    logger.debug(
                        f"Memory: RAM {snapshot.ram_used_gb:.1f}/{snapshot.ram_total_gb:.1f}GB "
                        f"({snapshot.ram_percent:.1f}%), "
                        f"VRAM {snapshot.vram_used_gb:.1f}/{snapshot.vram_total_gb:.1f}GB "
                        f"({snapshot.vram_percent:.1f}%)"
                    )
                
                # Trigger warning callbacks
                if (limits["ram_warning"] or limits["vram_warning"]) and not (limits["ram_critical"] or limits["vram_critical"]):
                    for callback in self.warning_callbacks:
                        try:
                            callback(snapshot)
                        except Exception as e:
                            logger.error(f"Error in warning callback: {e}")
                
                # Trigger critical callbacks
                if limits["ram_critical"] or limits["vram_critical"]:
                    for callback in self.critical_callbacks:
                        try:
                            callback(snapshot)
                        except Exception as e:
                            logger.error(f"Error in critical callback: {e}")
                
                # Sleep until next monitoring cycle
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
        
        logger.info("Memory monitoring stopped")
    
    def start_monitoring(self):
        """Start continuous memory monitoring."""
        if self.is_monitoring:
            logger.warning("Memory monitoring is already running")
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("Memory monitoring started")
    
    def stop_monitoring(self):
        """Stop continuous memory monitoring."""
        if not self.is_monitoring:
            logger.warning("Memory monitoring is not running")
            return
        
        self.is_monitoring = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=self.monitoring_interval * 2)
        
        logger.info("Memory monitoring stopped")
    
    def get_memory_stats(self, last_n_snapshots: int = 100) -> Dict:
        """Get memory usage statistics."""
        with self.lock:
            if not self.snapshots:
                return {"error": "No snapshots available"}
            
            recent_snapshots = self.snapshots[-last_n_snapshots:]
            
            ram_values = [s.ram_used_gb for s in recent_snapshots]
            vram_values = [s.vram_used_gb for s in recent_snapshots]
            cpu_values = [s.cpu_percent for s in recent_snapshots]
            temp_values = [s.gpu_temperature for s in recent_snapshots if s.gpu_temperature > 0]
            
            return {
                "snapshot_count": len(recent_snapshots),
                "time_range_minutes": (recent_snapshots[-1].timestamp - recent_snapshots[0].timestamp).total_seconds() / 60,
                "ram_stats": {
                    "current_gb": ram_values[-1],
                    "max_gb": max(ram_values),
                    "min_gb": min(ram_values),
                    "avg_gb": sum(ram_values) / len(ram_values),
                    "limit_gb": self.max_ram_usage_gb
                },
                "vram_stats": {
                    "current_gb": vram_values[-1],
                    "max_gb": max(vram_values),
                    "min_gb": min(vram_values),
                    "avg_gb": sum(vram_values) / len(vram_values),
                    "limit_gb": self.max_vram_usage_gb
                },
                "cpu_stats": {
                    "current_percent": cpu_values[-1],
                    "max_percent": max(cpu_values),
                    "min_percent": min(cpu_values),
                    "avg_percent": sum(cpu_values) / len(cpu_values)
                },
                "gpu_temperature": {
                    "current": temp_values[-1] if temp_values else 0,
                    "max": max(temp_values) if temp_values else 0,
                    "avg": sum(temp_values) / len(temp_values) if temp_values else 0
                }
            }
    
    def save_snapshots_to_file(self, filepath: str):
        """Save memory snapshots to JSON file."""
        with self.lock:
            data = []
            for snapshot in self.snapshots:
                data.append({
                    "timestamp": snapshot.timestamp.isoformat(),
                    "ram_used_gb": snapshot.ram_used_gb,
                    "ram_total_gb": snapshot.ram_total_gb,
                    "ram_percent": snapshot.ram_percent,
                    "vram_used_gb": snapshot.vram_used_gb,
                    "vram_total_gb": snapshot.vram_total_gb,
                    "vram_percent": snapshot.vram_percent,
                    "gpu_temperature": snapshot.gpu_temperature,
                    "process_count": snapshot.process_count,
                    "cpu_percent": snapshot.cpu_percent
                })
            
            try:
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2)
                logger.info(f"Memory snapshots saved to {filepath}")
            except Exception as e:
                logger.error(f"Error saving snapshots to {filepath}: {e}")
    
    def get_optimization_suggestions(self) -> List[str]:
        """Get memory optimization suggestions based on current usage."""
        memory_info = self.get_current_memory_info()
        suggestions = []
        
        # RAM suggestions
        if memory_info["ram_percent"] > 80:
            suggestions.append("High RAM usage detected. Consider reducing batch size.")
            suggestions.append("Enable data streaming with tf.data.Dataset.prefetch().")
            suggestions.append("Use memory-mapped files for large datasets.")
        
        # VRAM suggestions
        if memory_info["vram_percent"] > 80:
            suggestions.append("High VRAM usage detected. Consider reducing model size.")
            suggestions.append("Enable mixed precision training (float16).")
            suggestions.append("Use gradient accumulation to simulate larger batches.")
        
        # Temperature warnings
        if memory_info["gpu_temperature"] > 80:
            suggestions.append(f"GPU temperature is high ({memory_info['gpu_temperature']}°C). Check cooling.")
        
        # General suggestions
        if len(suggestions) == 0:
            suggestions.append("Memory usage is within acceptable limits.")
        
        return suggestions
    
    def __enter__(self):
        """Context manager entry."""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_monitoring()


# Utility functions for easy monitoring
def log_memory_usage(prefix: str = "Memory"):
    """Simple function to log current memory usage."""
    monitor = MemoryMonitor()
    memory_info = monitor.get_current_memory_info()
    
    logger.info(
        f"{prefix}: RAM {memory_info['ram_used_gb']:.1f}/{memory_info['ram_total_gb']:.1f}GB "
        f"({memory_info['ram_percent']:.1f}%), "
        f"VRAM {memory_info['vram_used_gb']:.1f}/{memory_info['vram_total_gb']:.1f}GB "
        f"({memory_info['vram_percent']:.1f}%)"
    )


def check_memory_requirements(required_ram_gb: float, required_vram_gb: float) -> bool:
    """Check if system has sufficient memory for operation."""
    monitor = MemoryMonitor()
    memory_info = monitor.get_current_memory_info()
    
    available_ram = memory_info["ram_total_gb"] - memory_info["ram_used_gb"]
    available_vram = memory_info["vram_total_gb"] - memory_info["vram_used_gb"]
    
    ram_sufficient = available_ram >= required_ram_gb
    vram_sufficient = available_vram >= required_vram_gb
    
    if not ram_sufficient:
        logger.warning(f"Insufficient RAM: need {required_ram_gb}GB, available {available_ram:.1f}GB")
    
    if not vram_sufficient:
        logger.warning(f"Insufficient VRAM: need {required_vram_gb}GB, available {available_vram:.1f}GB")
    
    return ram_sufficient and vram_sufficient


# Example usage and testing
if __name__ == "__main__":
    # Test basic memory monitoring
    print("Testing memory monitor...")
    
    # Create monitor
    monitor = MemoryMonitor(
        max_ram_usage_gb=16.0,
        max_vram_usage_gb=8.0,
        monitoring_interval=1.0,
        enable_logging=True
    )
    
    # Add alert callbacks
    def warning_alert(snapshot: MemorySnapshot):
        print(f"⚠️  WARNING: High memory usage - RAM: {snapshot.ram_used_gb:.1f}GB, VRAM: {snapshot.vram_used_gb:.1f}GB")
    
    def critical_alert(snapshot: MemorySnapshot):
        print(f"🚨 CRITICAL: Very high memory usage - RAM: {snapshot.ram_used_gb:.1f}GB, VRAM: {snapshot.vram_used_gb:.1f}GB")
    
    monitor.add_warning_callback(warning_alert)
    monitor.add_critical_callback(critical_alert)
    
    # Test current memory info
    log_memory_usage("Initial")
    
    # Test memory requirements check
    if check_memory_requirements(8.0, 4.0):
        print("✅ System has sufficient memory for large dataset processing")
    else:
        print("❌ System may not have sufficient memory")
    
    # Test monitoring with context manager
    print("\nStarting 10-second monitoring test...")
    with monitor:
        time.sleep(10)
    
    # Get statistics
    stats = monitor.get_memory_stats()
    print(f"\nMemory statistics: {json.dumps(stats, indent=2)}")
    
    # Get optimization suggestions
    suggestions = monitor.get_optimization_suggestions()
    print("\nOptimization suggestions:")
    for suggestion in suggestions:
        print(f"  - {suggestion}")
    
    print("\nMemory monitor test completed!")
