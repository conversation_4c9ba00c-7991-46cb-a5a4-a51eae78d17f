# MedScan AI - Git Ignore Configuration

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project is is generally recommended to exclude these files:
.idea/
*.iws
*.iml
*.ipr

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Cursor IDE
.cursor/

# Taskmaster
.taskmaster/

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows shortcuts
*.lnk

# Linux
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# Medical Imaging Specific Files
# DICOM files (sensitive medical data - never commit)
*.dcm
*.dicom
*.DCM
*.DICOM

# Medical image formats
*.nii
*.nii.gz
*.img
*.hdr

# Patient data directories
patient_data/
medical_images/
dicom_files/
test_images/
sample_data/

# AI/ML Model Files
# Large model files
*.h5
*.pb
*.tflite
*.onnx
*.pth
*.pt
*.pkl
*.joblib
*.model

# Training data and checkpoints
checkpoints/
models/
weights/
training_logs/
tensorboard_logs/

# Database Files
# SQLite databases
*.db
*.sqlite
*.sqlite3
*.db-journal

# Encrypted database files
*.sqlitedb
*.encrypted

# Application Specific
# Configuration files with sensitive data
config/local_settings.py
config/production.ini
config/development.ini
.env.local
.env.production
.env.development

# Logs
logs/
*.log
application.log
error.log
debug.log
audit.log

# Temporary files
temp/
tmp/
cache/
.cache/

# User data
user_data/
profiles/
settings/

# Reports and exports
reports/
exports/
output/
generated_reports/

# Backup files
*.backup
*.bak
*.old
backup/

# Security and compliance
# Private keys and certificates
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer

# Audit logs (contain sensitive information)
audit_logs/
security_logs/
compliance_reports/

# Development and testing
# Test data (may contain sensitive information)
test_data/
mock_data/
fixtures/private/

# Performance profiling
*.prof
profile_results/

# Documentation builds
docs/build/
docs/_build/
*.pdf
*.docx

# IDE and editor files
.vscode/launch.json
.vscode/settings.json
*.swp
*.swo
*~

# Dependency directories
node_modules/

# Package files
*.deb
*.rpm
*.dmg
*.exe
*.msi

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# Version control
.git/
.svn/
.hg/

# CI/CD
.github/workflows/secrets/
.gitlab-ci-local/

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# Kubernetes
*.kubeconfig
kube-config

# Cloud storage
.aws/
.azure/
.gcp/

# License and compliance
compliance_cache/
license_cache/

# Build artifacts
build-artifacts/
deployment/
installers/

# System files
.DS_Store?
Thumbs.db
*.tmp

# Application logs
application_logs/
system_logs/
user_activity_logs/

logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/
