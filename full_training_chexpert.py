#!/usr/bin/env python3
"""
Full CheXpert Training - Uses ALL samples in the dataset
This will train on the complete 65,000+ images dataset
"""

import sys
import os
sys.path.append('src')

from medscan_ai.ai.training_pipeline import PyTorchTrainingPipeline, TrainingConfig

def main():
    print("🚀 Starting FULL CheXpert training with ALL samples...")
    print("⚠️  This will use all 65,000+ images and take several hours!")
    
    # Full dataset configuration - NO SAMPLE LIMITING
    config = TrainingConfig(
        # Dataset config
        dataset_path="D:/datasets/archive_2",
        max_samples_per_class=None,  # ✅ NO LIMIT - Use ALL samples
        image_size=(224, 224),
        batch_size=32,  # Larger batch for efficiency
        
        # Training config
        epochs=20,  # More epochs for full convergence
        initial_learning_rate=0.001,
        num_classes=14,  # CheXpert has 14 pathologies
        is_multilabel=True,  # Enable multi-label mode
        
        # Model config
        base_model="resnet50",
        dense_layers=[512, 256],  # Good capacity for 14 classes
        dropout_rate=0.4,
        
        # System config
        num_workers=6,  # More workers for large dataset
        mixed_precision=True,  # GPU optimization crucial for large dataset
        device="auto"  # Auto-detect GPU
    )
    
    try:
        # Create pipeline
        print("📊 Creating pipeline...")
        pipeline = PyTorchTrainingPipeline(config)
        
        # Load datasets
        print("📂 Loading FULL datasets (this may take a few minutes)...")
        train_loader, val_loader, test_loader = pipeline.load_datasets()
        
        # Create model
        print("🧠 Creating model...")
        model = pipeline.create_model()
        
        print("✅ Setup completed successfully!")
        print(f"📈 Train samples: {len(train_loader.dataset):,}")  # Should be ~60,000+
        print(f"📈 Val samples: {len(val_loader.dataset):,}")
        print(f"📈 Test samples: {len(test_loader.dataset):,}")
        print(f"🎯 Model device: {model.device}")
        print(f"🔄 Batch size: {config.batch_size}")
        print(f"⏱️  Estimated batches per epoch: {len(train_loader):,}")
        
        # Confirmation before starting long training
        print("\n" + "="*50)
        print("⚠️  WARNING: This is FULL DATASET training!")
        print(f"⏱️  Estimated time: 3-5 hours on RTX 3060")
        print(f"💾 GPU Memory usage will be high")
        print("="*50)
        
        response = input("Continue with full training? (y/N): ").strip().lower()
        if response != 'y':
            print("Training cancelled.")
            return
            
        # Start FULL training
        print("\n🎯 Starting FULL training...")
        print("This will take several hours. Monitor GPU usage!")
        
        # Start training
        history = pipeline.train()
        
        print("\n🎉 FULL TRAINING COMPLETED!")
        print(f"✅ Final train accuracy: {history['train_acc'][-1]:.2f}%")
        print(f"✅ Final val accuracy: {history['val_acc'][-1]:.2f}%")
        print("🎯 Model saved automatically to ./checkpoints/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 