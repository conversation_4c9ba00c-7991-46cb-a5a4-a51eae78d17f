"""Report repository for medical report operations."""

from typing import List

from sqlalchemy.orm import Session

from ..models.report import Report
from .base import BaseRepository


class ReportRepository(BaseRepository[Report]):
    """Repository for medical report operations."""

    def __init__(self, session: Session):
        super().__init__(Report, session)

    def find_by_study_id(self, study_id: int) -> List[Report]:
        """Find all reports for a specific study."""
        return self.filter_by(study_id=study_id)
