"""
Overlay management system for medical image viewer.
Handles multiple overlay layers for annotations, AI findings, and measurements.
"""

from typing import Dict, List, Optional
from enum import Enum

from PySide6.QtWidgets import QGraphicsItemGroup, QGraphicsScene
from PySide6.QtCore import Qt


class LayerType(Enum):
    """Enumeration for different overlay layer types."""
    BASE_IMAGE = "base_image"
    AI_FINDINGS = "ai_findings"
    MANUAL_ANNOTATIONS = "manual_annotations"
    MEASUREMENTS = "measurements"
    COMMENTS = "comments"


class OverlayLayer:
    """
    Represents a single overlay layer with graphics items and metadata.
    """
    
    def __init__(self, layer_type: LayerType, name: str, z_value: int = 0):
        """
        Initialize overlay layer.
        
        Args:
            layer_type: Type of the layer
            name: Human-readable name for the layer
            z_value: Z-order value (higher values appear on top)
        """
        self.layer_type = layer_type
        self.name = name
        self.z_value = z_value
        self.group = QGraphicsItemGroup()
        self.group.setZValue(z_value)
        
        # Layer properties
        self.visible = True
        self.opacity = 1.0
        self.locked = False
        
        # Graphics items in this layer
        self.items = []
        
    def add_item(self, item):
        """Add a graphics item to this layer."""
        self.group.addToGroup(item)
        self.items.append(item)
        
    def remove_item(self, item):
        """Remove a graphics item from this layer."""
        if item in self.items:
            self.group.removeFromGroup(item)
            self.items.remove(item)
            
    def clear(self):
        """Clear all items from this layer, fully removing them from the scene."""
        for item in self.items:
            # Remove from group first so it's detached
            self.group.removeFromGroup(item)
            # Then remove from scene completely if still in scene
            if item.scene() is not None:
                item.scene().removeItem(item)
        self.items.clear()
        
    def set_visible(self, visible: bool):
        """Set layer visibility."""
        self.visible = visible
        self.group.setVisible(visible)
        
    def set_opacity(self, opacity: float):
        """Set layer opacity (0.0 - 1.0)."""
        self.opacity = max(0.0, min(1.0, opacity))
        self.group.setOpacity(self.opacity)
        
    def set_z_value(self, z_value: int):
        """Set layer Z-order value."""
        self.z_value = z_value
        self.group.setZValue(z_value)


class OverlayManager:
    """
    Manages multiple overlay layers for the image viewer.
    Handles layer creation, organization, and rendering order.
    """
    
    # Predefined Z-values for different layer types
    LAYER_Z_VALUES = {
        LayerType.BASE_IMAGE: 0,
        LayerType.AI_FINDINGS: 100,
        LayerType.MANUAL_ANNOTATIONS: 200,
        LayerType.MEASUREMENTS: 150,
        LayerType.COMMENTS: 250
    }
    
    def __init__(self, scene: QGraphicsScene):
        """
        Initialize overlay manager.
        
        Args:
            scene: QGraphicsScene to manage overlays for
        """
        self.scene = scene
        self.layers: Dict[str, OverlayLayer] = {}
        self.layer_counter = 0
        
    def create_layer(self, layer_type: LayerType, name: Optional[str] = None) -> str:
        """
        Create a new overlay layer.
        
        Args:
            layer_type: Type of layer to create
            name: Optional custom name for the layer
            
        Returns:
            String identifier for the created layer
        """
        if name is None:
            self.layer_counter += 1
            name = f"{layer_type.value}_{self.layer_counter}"
            
        # Get default Z-value for this layer type
        z_value = self.LAYER_Z_VALUES.get(layer_type, 50)
        
        # Create the layer
        layer = OverlayLayer(layer_type, name, z_value)
        
        # Add to scene
        self.scene.addItem(layer.group)
        
        # Store in manager
        layer_id = f"{layer_type.value}_{len(self.layers)}"
        self.layers[layer_id] = layer
        
        return layer_id
        
    def get_layer(self, layer_id: str) -> Optional[OverlayLayer]:
        """Get layer by ID."""
        return self.layers.get(layer_id)
        
    def remove_layer(self, layer_id: str) -> bool:
        """
        Remove a layer completely.
        
        Args:
            layer_id: ID of layer to remove
            
        Returns:
            True if layer was found and removed
        """
        layer = self.layers.get(layer_id)
        if layer:
            # Remove from scene
            self.scene.removeItem(layer.group)
            # Remove from manager
            del self.layers[layer_id]
            return True
        return False
        
    def clear_layer(self, layer_id: str) -> bool:
        """
        Clear all items from a layer without removing the layer.
        
        Args:
            layer_id: ID of layer to clear
            
        Returns:
            True if layer was found and cleared
        """
        layer = self.layers.get(layer_id)
        if layer:
            layer.clear()
            return True
        return False
        
    def set_layer_visibility(self, layer_id: str, visible: bool) -> bool:
        """Set layer visibility."""
        layer = self.layers.get(layer_id)
        if layer:
            layer.set_visible(visible)
            return True
        return False
        
    def set_layer_opacity(self, layer_id: str, opacity: float) -> bool:
        """Set layer opacity."""
        layer = self.layers.get(layer_id)
        if layer:
            layer.set_opacity(opacity)
            return True
        return False
        
    def get_layers_by_type(self, layer_type: LayerType) -> List[str]:
        """Get all layer IDs of a specific type."""
        return [
            layer_id for layer_id, layer in self.layers.items()
            if layer.layer_type == layer_type
        ]
        
    def clear_all_layers(self):
        """Clear and remove all overlay layers from the scene, safely."""
        # Create a static list of (layer_id, layer) to avoid mutating during iteration
        for layer_id, layer in list(self.layers.items()):
            try:
                # Remove all items from layer first
                layer.clear()
                # Safely remove graphics group from scene if it still exists
                if layer.group is not None:
                    try:
                        self.scene.removeItem(layer.group)
                    except RuntimeError:
                        # The underlying C++ object might already be deleted; ignore
                        pass
            except RuntimeError:
                # Handle cases where group or items were already deleted
                pass
            # Remove reference from manager regardless
            self.layers.pop(layer_id, None)
        self.layer_counter = 0 