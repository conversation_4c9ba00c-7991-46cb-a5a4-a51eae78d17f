"""
Polygon annotation tool implementation.
"""

from typing import Optional, List
import uuid
from PySide6.QtCore import QPoint, Qt
from PySide6.QtGui import QPen, QBrush, QPolygonF
from PySide6.QtWidgets import QGraphicsPolygonItem, QGraphicsScene

from .base import AnnotationToolBase
from ..types import Annota<PERSON>Tool, ManualAnnotation


class PolygonTool(AnnotationToolBase):
    """
    Tool for drawing polygon annotations on medical images.
    Supports click-to-add-point workflow with double-click to finish.
    """
    
    def __init__(self):
        """Initialize polygon tool."""
        super().__init__(AnnotationTool.POLYGON)
        self.points: List[QPoint] = []
        self.min_points = 3
        
    def _start_drawing_impl(self, scene_pos: QPoint, scene: QGraphicsScene) -> bool:
        """Start drawing a polygon by adding the first point."""
        self.points = [scene_pos]
        
        # Create preview polygon
        self.preview_item = QGraphicsPolygonItem()
        pen = QPen(self.pen_color)
        pen.setWidth(self.pen_width)
        pen.setStyle(Qt.DashLine)  # Dashed line for preview
        self.preview_item.setPen(pen)
        
        brush = QBrush(self.brush_color)
        brush.setStyle(Qt.NoBrush)
        self.preview_item.setBrush(brush)
        
        scene.addItem(self.preview_item)
        self._update_preview()
        return True
        
    def add_point(self, scene_pos: QPoint) -> bool:
        """
        Add a point to the polygon.
        
        Args:
            scene_pos: New point to add
            
        Returns:
            bool: True if point was added successfully
        """
        if not self.is_drawing:
            return False
            
        self.points.append(scene_pos)
        self._update_preview()
        return True
        
    def _continue_drawing_impl(self, scene_pos: QPoint) -> bool:
        """Update preview with current mouse position."""
        if not self.preview_item:
            return False
            
        # Update preview to show potential next point
        self._update_preview_with_points(self.points + [scene_pos])
        return True
        
    def _finish_drawing_impl(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """Finish drawing and create polygon annotation."""
        if len(self.points) < self.min_points:
            return None
            
        # Create annotation data
        geometry_data = {
            'points': [{'x': p.x(), 'y': p.y()} for p in self.points]
        }
        
        # Create annotation
        annotation = ManualAnnotation(
            annotation_id=str(uuid.uuid4()),
            tool_type=self.tool_type,
            geometry_data=geometry_data,
            metadata={
                'pen_color': self.pen_color.name(),
                'pen_width': self.pen_width,
                'brush_color': None,
                'point_count': len(self.points)
            }
        )
        
        return annotation
        
    def _update_preview(self):
        """Update preview polygon with current points."""
        self._update_preview_with_points(self.points)
        
    def _update_preview_with_points(self, points: List[QPoint]):
        """Update preview polygon with given points."""
        if not self.preview_item or not points:
            return
            
        # Create QPolygonF from points
        polygon = QPolygonF([p for p in points])
        self.preview_item.setPolygon(polygon)
        
    def can_finish(self) -> bool:
        """Check if polygon can be finished (has enough points)."""
        return len(self.points) >= self.min_points
        
    def _cancel_drawing_impl(self):
        """Cancel drawing and clear points."""
        self.points.clear() 