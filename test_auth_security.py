#!/usr/bin/env python3
"""
Quick test script for authentication security testing
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from medscan_ai.security.testing.auth_security_tests import run_authentication_security_tests
    print("✅ Import successful")
    
    print("🔄 Running authentication security tests...")
    results = run_authentication_security_tests()
    
    print("✅ Test completed")
    
    # Print summary
    summary = results.get('summary', {})
    print(f"\n📊 SUMMARY:")
    print(f"   Total tests: {summary.get('total_tests', 0)}")
    print(f"   Passed tests: {summary.get('passed_tests', 0)}")
    print(f"   Failed tests: {summary.get('failed_tests', 0)}")
    print(f"   Security score: {summary.get('security_score', 0):.1f}%")
    print(f"   Readiness level: {summary.get('readiness_level', 'Unknown')}")
    
    # Print issues
    issues = results.get('issues_by_severity', {})
    print(f"\n⚠️  ISSUES:")
    print(f"   Critical: {issues.get('critical', 0)}")
    print(f"   High: {issues.get('high', 0)}")
    print(f"   Medium: {issues.get('medium', 0)}")
    print(f"   Low: {issues.get('low', 0)}")
    
    # Show failed tests
    failed_tests = [t for t in results.get('test_results', []) if not t.get('passed', False)]
    if failed_tests:
        print(f"\n❌ FAILED TESTS:")
        for i, test in enumerate(failed_tests, 1):
            print(f"   {i}. {test.get('test_name', 'Unknown')}")
            print(f"      Details: {test.get('details', 'No details')}")
            if test.get('recommendations'):
                print(f"      Recommendations: {', '.join(test['recommendations'])}")
    
    # Save results
    with open('.taskmaster/reports/auth_security_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved to .taskmaster/reports/auth_security_test_results.json")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc() 