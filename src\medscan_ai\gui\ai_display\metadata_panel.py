"""
AI metadata panel for displaying detailed findings information.
"""

from typing import Optional, List
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import (
    QDockWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QTextEdit, QProgressBar, QPushButton,
    QGroupBox, QScrollArea, QFrame
)

# Import AI postprocessing results
try:
    from ...ai.postprocessing.result_interpreter import (
        InterpretationResult, AnomalyDetection
    )
except ImportError:
    InterpretationResult = None
    AnomalyDetection = None


class DifferentialDiagnosis:
    """Differential diagnosis for metadata display."""
    def __init__(self, condition: str, probability: float, supporting_evidence=None):
        self.condition = condition
        self.probability = probability
        self.supporting_evidence = supporting_evidence or []


class AIMetadataPanel(QDockWidget):
    """
    Dockable panel for displaying detailed AI metadata and findings information.
    
    Features:
    - Persistent metadata display while viewing images
    - Clinical significance breakdown
    - Confidence score visualization  
    - Differential diagnoses ranking
    - Processing metadata and model information
    - Interactive findings selection
    """
    
    # Signal emitted when user wants to highlight a specific finding
    finding_highlight_requested = Signal(str)  # finding_id
    
    def __init__(self, parent=None):
        """Initialize AI metadata panel."""
        super().__init__("AI Analysis Metadata", parent)
        
        # Store current interpretation result
        self.current_result: Optional[InterpretationResult] = None
        self.selected_finding_id: Optional[str] = None
        
        # Setup UI
        self._setup_ui()
        
        # Configure dock widget
        self.setFeatures(
            QDockWidget.DockWidgetMovable | 
            QDockWidget.DockWidgetFloatable |
            QDockWidget.DockWidgetClosable
        )
        self.setMinimumWidth(300)
        
    def _setup_ui(self):
        """Setup the user interface components."""
        # Main widget and layout
        main_widget = QWidget()
        self.setWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Overall confidence section
        confidence_group = QGroupBox("Overall Confidence")
        confidence_layout = QVBoxLayout()
        
        self.confidence_bar = QProgressBar()
        self.confidence_bar.setMinimum(0)
        self.confidence_bar.setMaximum(100)
        self.confidence_bar.setTextVisible(True)
        confidence_layout.addWidget(self.confidence_bar)
        
        self.confidence_label = QLabel("No analysis available")
        confidence_layout.addWidget(self.confidence_label)
        
        confidence_group.setLayout(confidence_layout)
        layout.addWidget(confidence_group)
        
        # Findings summary section
        findings_group = QGroupBox("Findings Summary")
        findings_layout = QVBoxLayout()
        
        self.findings_text = QTextEdit()
        self.findings_text.setReadOnly(True)
        self.findings_text.setMaximumHeight(150)
        findings_layout.addWidget(self.findings_text)
        
        findings_group.setLayout(findings_layout)
        layout.addWidget(findings_group)
        
        # Selected finding details
        selected_group = QGroupBox("Selected Finding Details")
        selected_layout = QVBoxLayout()
        
        # Scrollable area for finding details
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        
        self.finding_type_label = QLabel("No finding selected")
        self.details_layout.addWidget(self.finding_type_label)
        
        self.finding_confidence_label = QLabel("")
        self.details_layout.addWidget(self.finding_confidence_label)
        
        self.finding_severity_label = QLabel("")
        self.details_layout.addWidget(self.finding_severity_label)
        
        self.finding_significance_label = QLabel("")
        self.finding_significance_label.setWordWrap(True)
        self.details_layout.addWidget(self.finding_significance_label)
        
        # Differential diagnoses
        self.diff_diagnosis_label = QLabel("Differential Diagnoses:")
        self.diff_diagnosis_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.details_layout.addWidget(self.diff_diagnosis_label)
        
        self.diff_diagnosis_text = QTextEdit()
        self.diff_diagnosis_text.setReadOnly(True)
        self.diff_diagnosis_text.setMaximumHeight(100)
        self.details_layout.addWidget(self.diff_diagnosis_text)
        
        self.details_layout.addStretch()
        scroll_area.setWidget(self.details_widget)
        
        selected_layout.addWidget(scroll_area)
        selected_group.setLayout(selected_layout)
        layout.addWidget(selected_group)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.details_button = QPushButton("Detailed View")
        self.details_button.clicked.connect(self._show_detailed_dialog)
        button_layout.addWidget(self.details_button)
        
        self.export_button = QPushButton("Export Findings")
        self.export_button.clicked.connect(self._export_findings)
        button_layout.addWidget(self.export_button)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
    def update_interpretation_result(self, result: InterpretationResult):
        """
        Update the panel with new interpretation results.
        
        Args:
            result: InterpretationResult from AI analysis
        """
        self.current_result = result
        
        if not result:
            self._clear_display()
            return
            
        # Update overall confidence
        confidence_percent = int(result.overall_confidence * 100)
        self.confidence_bar.setValue(confidence_percent)
        self.confidence_label.setText(f"Overall: {confidence_percent}%")
        
        # Update findings summary
        summary_lines = [
            f"Total detections: {len(result.detections)}",
            f"High confidence: {len(result.high_confidence_detections)}",
            f"Critical findings: {len(result.critical_findings)}"
        ]
        
        if result.requires_immediate_attention:
            summary_lines.append("⚠️ REQUIRES IMMEDIATE ATTENTION")
            
        self.findings_text.setText("\n".join(summary_lines))
        
        # Select first critical or high-confidence finding if available
        if result.critical_findings:
            self.select_finding(result.critical_findings[0].id)
        elif result.high_confidence_detections:
            self.select_finding(result.high_confidence_detections[0].id)
        elif result.detections:
            self.select_finding(result.detections[0].id)
            
    def select_finding(self, finding_id: str):
        """
        Select and display details for a specific finding.
        
        Args:
            finding_id: ID of the finding to display
        """
        if not self.current_result:
            return
            
        # Find the detection
        detection = None
        for det in self.current_result.detections:
            if det.id == finding_id:
                detection = det
                break
                
        if not detection:
            return
            
        self.selected_finding_id = finding_id
        
        # Update finding details
        anomaly_name = detection.anomaly_type.value.replace('_', ' ').title()
        self.finding_type_label.setText(f"Type: {anomaly_name}")
        
        confidence_percent = int(detection.confidence_score * 100)
        self.finding_confidence_label.setText(
            f"Confidence: {confidence_percent}% ({detection.confidence_level.value})"
        )
        
        if detection.severity:
            self.finding_severity_label.setText(
                f"Severity: {detection.severity.value.title()}"
            )
            self.finding_severity_label.setVisible(True)
        else:
            self.finding_severity_label.setVisible(False)
            
        if detection.clinical_significance:
            self.finding_significance_label.setText(
                f"Clinical Significance: {detection.clinical_significance}"
            )
            self.finding_significance_label.setVisible(True)
        else:
            self.finding_significance_label.setVisible(False)
            
        # Update differential diagnoses
        self._update_differential_diagnoses(detection)
        
        # Emit signal to highlight in viewer
        self.finding_highlight_requested.emit(finding_id)
        
    def _update_differential_diagnoses(self, detection: AnomalyDetection):
        """Update differential diagnoses display for selected detection."""
        # In real implementation, this would come from the detection object
        # For now, create mock differential diagnoses based on anomaly type
        diagnoses = self._create_mock_differential_diagnoses(detection)
        
        if not diagnoses:
            self.diff_diagnosis_text.setText("No differential diagnoses available")
            return
            
        # Format differential diagnoses
        lines = []
        for diag in diagnoses:
            line = f"• {diag.condition}: {diag.probability:.1%}"
            if diag.supporting_evidence:
                evidence = ", ".join(diag.supporting_evidence)
                line += f"\n  Evidence: {evidence}"
            lines.append(line)
            
        self.diff_diagnosis_text.setText("\n".join(lines))
        
    def _create_mock_differential_diagnoses(self, detection: AnomalyDetection):
        """Create mock differential diagnoses for demonstration."""
        # This would be replaced with actual differential diagnoses from the AI model
        anomaly_type = detection.anomaly_type.value
        
        mock_diagnoses = {
            "fracture": [
                DifferentialDiagnosis("Acute fracture", 0.7, ["Cortical disruption", "Soft tissue swelling"]),
                DifferentialDiagnosis("Stress fracture", 0.2, ["Periosteal reaction"]),
                DifferentialDiagnosis("Pathologic fracture", 0.1, ["Underlying lesion"])
            ],
            "pneumonia": [
                DifferentialDiagnosis("Bacterial pneumonia", 0.6, ["Consolidation pattern", "Air bronchograms"]),
                DifferentialDiagnosis("Viral pneumonia", 0.3, ["Ground glass opacities"]),
                DifferentialDiagnosis("Aspiration pneumonia", 0.1, ["Dependent location"])
            ],
            "nodule": [
                DifferentialDiagnosis("Benign nodule", 0.5, ["Small size", "Smooth borders"]),
                DifferentialDiagnosis("Primary malignancy", 0.3, ["Spiculated margins"]),
                DifferentialDiagnosis("Metastasis", 0.2, ["Multiple nodules"])
            ]
        }
        
        return mock_diagnoses.get(anomaly_type, [])
        
    def _clear_display(self):
        """Clear all display fields."""
        self.confidence_bar.setValue(0)
        self.confidence_label.setText("No analysis available")
        self.findings_text.clear()
        self.finding_type_label.setText("No finding selected")
        self.finding_confidence_label.setText("")
        self.finding_severity_label.setText("")
        self.finding_significance_label.setText("")
        self.diff_diagnosis_text.clear()
        self.selected_finding_id = None
        
    def _show_detailed_dialog(self):
        """Show detailed findings dialog."""
        # This would open the AIFindingDialog
        # Implementation would be in the main viewer
        pass
        
    def _export_findings(self):
        """Export findings to file."""
        # This would implement export functionality
        # Could export to JSON, PDF report, etc.
        pass 