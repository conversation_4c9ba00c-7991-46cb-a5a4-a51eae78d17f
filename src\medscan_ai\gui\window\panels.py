"""
Panel creation for MedScan AI GUI.

This module provides creation of main UI panels: patient information panel,
image viewer panel, and analysis results panel with responsive design.
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import (
    QFrame, QHBoxLayout, QLabel, QSizePolicy, QSplitter, 
    QTextEdit, QVBoxLayout, QWidget
)

from ..utils import InteractiveImageViewer


class PanelManagerMixin:
    """
    Mixin class providing panel creation capabilities for the main window.
    
    This mixin creates the main three-panel layout: left (patient info),
    center (image viewer), and right (analysis results).
    """

    def _create_central_widget(self):
        """Create the central widget with medical workflow layout."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(8)
        main_layout.setContentsMargins(8, 8, 8, 8)

        # Create splitter for resizable panels
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setHandleWidth(6)
        self.main_splitter.setChildrenCollapsible(True)

        # Left panel - Patient/Study information
        left_panel = self._create_left_panel()
        self.main_splitter.addWidget(left_panel)

        # Center panel - Image viewer
        center_panel = self._create_center_panel()
        self.main_splitter.addWidget(center_panel)

        # Right panel - Analysis results
        right_panel = self._create_right_panel()
        self.main_splitter.addWidget(right_panel)

        # Set initial proportional sizes (left: 250px, center: flexible, right: 350px)
        self.main_splitter.setSizes([250, 600, 350])

        # Set stretch factors for responsive behavior
        self.main_splitter.setStretchFactor(0, 0)  # Left panel - fixed width
        self.main_splitter.setStretchFactor(1, 1)  # Center panel - stretches
        self.main_splitter.setStretchFactor(2, 0)  # Right panel - fixed width

        # Connect resize event to handle responsive behavior
        if hasattr(self, '_on_splitter_moved'):
            self.main_splitter.splitterMoved.connect(self._on_splitter_moved)

        main_layout.addWidget(self.main_splitter)

    def _create_left_panel(self):
        """Create left panel for patient and study information."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(200)
        panel.setMaximumWidth(400)

        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # Panel title
        title = QLabel("📋 Patient Information")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet(
            "color: #2E7D32; padding: 8px; background-color: #E8F5E8; border-radius: 4px;"
        )
        layout.addWidget(title)

        # Patient info with responsive sizing
        self._patient_info_widget = QTextEdit()
        self._patient_info_widget.setPlainText(
            """Patient: [No patient selected]
ID: ---
DOB: ---
Gender: ---
Study Date: ---
Modality: ---
Series: ---

Status: Ready for new patient
"""
        )
        self._patient_info_widget.setMinimumHeight(150)
        self._patient_info_widget.setMaximumHeight(250)
        self._patient_info_widget.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Preferred
        )
        layout.addWidget(self._patient_info_widget)

        # Study list with responsive sizing
        study_title = QLabel("📁 Studies")
        study_title.setFont(QFont("Arial", 10, QFont.Bold))
        study_title.setStyleSheet("color: #1976D2; padding: 4px;")
        layout.addWidget(study_title)

        study_list = QTextEdit()
        study_list.setPlainText(
            "No studies loaded.\n\nUse File > Open DICOM Files\nto load medical images."
        )
        study_list.setMinimumHeight(100)
        study_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(study_list)

        # Add responsive spacing
        layout.addStretch()

        return panel

    def _create_center_panel(self):
        """Create center panel for image viewing with responsive sizing."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(300)

        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # Panel title with responsive styling
        title = QLabel("🖼️ Medical Image Viewer")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet(
            "color: #1976D2; padding: 8px; background-color: #E3F2FD; border-radius: 4px;"
        )
        layout.addWidget(title)

        # Interactive image viewer with zoom and pan capabilities
        self.image_viewer = InteractiveImageViewer()
        self.image_viewer.setMinimumSize(300, 300)
        self.image_viewer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Style the viewer
        self.image_viewer.setStyleSheet(
            """
            QGraphicsView {
                border: 2px solid #CCCCCC;
                border-radius: 10px;
                background-color: #000000;
            }
        """
        )

        layout.addWidget(self.image_viewer)

        # Create placeholder label for when no image is loaded
        self.image_placeholder = QLabel()
        self.image_placeholder.setMinimumSize(300, 300)
        self.image_placeholder.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Expanding
        )
        self.image_placeholder.setStyleSheet(
            """
            QLabel {
                border: 2px dashed #CCCCCC;
                border-radius: 10px;
                background-color: #F5F5F5;
                color: #666666;
                font-size: 14px;
                text-align: center;
            }
        """
        )
        self.image_placeholder.setText(
            """
🏥 MedScan AI - Interactive Image Viewer

No medical images loaded

Supported formats:
• DICOM (.dcm)
• JPEG, PNG
• TIFF, BMP

Features:
• Mouse wheel zoom at cursor
• Left-click drag to pan
• Keyboard shortcuts (+, -, 0, 1)

Drag & drop files here
or use File > Open DICOM Files
        """
        )
        self.image_placeholder.setAlignment(Qt.AlignCenter)
        self.image_placeholder.setWordWrap(True)

        # Initially show placeholder, hide viewer
        layout.addWidget(self.image_placeholder)
        self.image_viewer.hide()

        # Image controls with responsive layout
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(4)

        # Create responsive control buttons
        from PySide6.QtWidgets import QPushButton

        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.setMaximumWidth(40)
        zoom_in_btn.setToolTip("Zoom In")
        if hasattr(self, '_zoom_in'):
            zoom_in_btn.clicked.connect(self._zoom_in)

        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.setMaximumWidth(40)
        zoom_out_btn.setToolTip("Zoom Out")
        if hasattr(self, '_zoom_out'):
            zoom_out_btn.clicked.connect(self._zoom_out)

        reset_btn = QPushButton("🔄")
        reset_btn.setMaximumWidth(40)
        reset_btn.setToolTip("Reset View")
        if hasattr(self, '_reset_view'):
            reset_btn.clicked.connect(self._reset_view)

        # Add buttons to layout
        controls_layout.addWidget(zoom_in_btn)
        controls_layout.addWidget(zoom_out_btn)
        controls_layout.addWidget(reset_btn)
        controls_layout.addStretch()  # Push buttons to left

        layout.addLayout(controls_layout)

        return panel

    def _create_right_panel(self):
        """Create right panel for analysis results and tools."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(250)
        panel.setMaximumWidth(500)

        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # Analysis Results Section
        analysis_title = QLabel("🔍 Analysis Results")
        analysis_title.setFont(QFont("Arial", 12, QFont.Bold))
        analysis_title.setStyleSheet(
            "color: #FF6B35; padding: 8px; background-color: #FFF3E0; border-radius: 4px;"
        )
        layout.addWidget(analysis_title)

        # Analysis results display
        self._analysis_results_widget = QTextEdit()
        self._analysis_results_widget.setPlainText(
            """🤖 AI Analysis Results

No analysis performed yet.

Available Analysis:
• Pathology Detection
• Measurement Tools  
• Statistical Analysis
• Comparison Studies

Click "Run Analysis" to begin
automated medical assessment.

Status: Ready for analysis
"""
        )
        self._analysis_results_widget.setMinimumHeight(200)
        self._analysis_results_widget.setMaximumHeight(300)
        self._analysis_results_widget.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Preferred
        )
        layout.addWidget(self._analysis_results_widget)

        # Tools Section
        tools_title = QLabel("🛠️ Medical Tools")
        tools_title.setFont(QFont("Arial", 10, QFont.Bold))
        tools_title.setStyleSheet("color: #4CAF50; padding: 4px;")
        layout.addWidget(tools_title)

        # Tools list
        tools_list = QTextEdit()
        tools_list.setPlainText(
            """📏 Measurement Tools
📐 Angle Measurement
🎯 Distance Calculation
📊 Area/Volume Analysis
🔍 Magnification Tool

📝 Annotation Tools
✏️ Text Annotations
🖊️ Drawing Tools
📌 Markers & Labels
🎨 Highlighting

🔧 Image Processing
🌈 Color Adjustment
🔄 Rotation & Flip
✂️ Crop & Resize
🎚️ Brightness/Contrast

Click tools to activate...
"""
        )
        tools_list.setMinimumHeight(150)
        tools_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(tools_list)

        # Workflow Section
        workflow_title = QLabel("📋 Workflow Status")
        workflow_title.setFont(QFont("Arial", 10, QFont.Bold))
        workflow_title.setStyleSheet("color: #9C27B0; padding: 4px;")
        layout.addWidget(workflow_title)

        # Workflow status
        self._workflow_status_widget = QTextEdit()
        self._workflow_status_widget.setPlainText(
            """Current Workflow:

1. ✅ Application Started
2. ⏳ Load Patient Study
3. ⏳ Review Images  
4. ⏳ Perform Analysis
5. ⏳ Generate Report
6. ⏳ Archive Results

Next Step: Load DICOM files
to begin medical analysis.
"""
        )
        self._workflow_status_widget.setMaximumHeight(120)
        self._workflow_status_widget.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Preferred
        )
        layout.addWidget(self._workflow_status_widget)

        # Add responsive spacing
        layout.addStretch()

        return panel

    def _update_analysis_results(self, results: str):
        """
        Update analysis results display.
        
        Args:
            results: Analysis results text to display
        """
        if hasattr(self, '_analysis_results_widget'):
            self._analysis_results_widget.setPlainText(results)

    def _update_workflow_status(self, status: str):
        """
        Update workflow status display.
        
        Args:
            status: Workflow status text to display
        """
        if hasattr(self, '_workflow_status_widget'):
            self._workflow_status_widget.setPlainText(status)

    def _show_tools_panel(self, visible: bool = True):
        """
        Show or hide tools panel.
        
        Args:
            visible: Whether to show the tools panel
        """
        try:
            if hasattr(self, 'main_splitter') and len(self.main_splitter.sizes()) >= 3:
                sizes = self.main_splitter.sizes()
                if visible:
                    # Ensure right panel is visible
                    if sizes[2] < 200:
                        sizes[2] = 300
                        sizes[1] = max(300, sizes[1] - 100)
                        self.main_splitter.setSizes(sizes)
                else:
                    # Hide right panel
                    sizes[1] += sizes[2] - 50
                    sizes[2] = 50
                    self.main_splitter.setSizes(sizes)
        except Exception:
            pass

    def _toggle_panels_visibility(self):
        """Toggle visibility of side panels."""
        try:
            if hasattr(self, 'main_splitter'):
                sizes = self.main_splitter.sizes()
                total = sum(sizes)
                
                # Check if panels are minimized
                left_minimized = sizes[0] < 100
                right_minimized = sizes[2] < 100
                
                if left_minimized or right_minimized:
                    # Restore panels
                    self.main_splitter.setSizes([250, total - 600, 350])
                else:
                    # Minimize panels
                    self.main_splitter.setSizes([50, total - 100, 50])
        except Exception:
            pass 
