"""
Eraser annotation tool implementation.
"""

from typing import Optional, List, Set
import uuid
from PySide6.QtCore import QPoint, Qt, QRectF
from PySide6.QtGui import QPen, QBrush, QColor
from PySide6.QtWidgets import QGraphicsEllipseItem, QGraphicsScene, QGraphicsItem, QGraphicsPixmapItem

from .base import AnnotationToolBase
from ..types import AnnotationTool, ManualAnnotation


class EraserTool(AnnotationToolBase):
    """
    Tool for erasing existing annotations on medical images.
    Shows a circular eraser cursor and removes intersecting items.
    """
    
    def __init__(self):
        """Initialize eraser tool."""
        super().__init__(AnnotationTool.ERASER)
        self.eraser_radius = 10.0
        self.erased_items: Set[QGraphicsItem] = set()
        
    def _start_drawing_impl(self, scene_pos: QPoint, scene: QGraphicsScene) -> bool:
        """Start erasing at the given position."""
        self.erased_items.clear()
        
        # Create preview eraser cursor
        self.preview_item = QGraphicsEllipseItem()
        pen = QPen(self.pen_color)
        pen.setWidth(2)
        pen.setStyle(Qt.DashLine)
        self.preview_item.setPen(pen)
        
        # Semi-transparent fill
        brush = QBrush(QColor(255, 0, 0, 50))
        self.preview_item.setBrush(brush)
        
        # Position eraser cursor
        self._update_cursor_position(scene_pos)
        scene.addItem(self.preview_item)
        
        # Start erasing at this position
        self._erase_at_position(scene_pos, scene)
        return True
        
    def _continue_drawing_impl(self, scene_pos: QPoint) -> bool:
        """Continue erasing as mouse moves."""
        if not self.preview_item or not self.current_scene:
            return False
            
        # Update cursor position
        self._update_cursor_position(scene_pos)
        
        # Continue erasing
        self._erase_at_position(scene_pos, self.current_scene)
        return True
        
    def _erase_at_position(self, scene_pos: QPoint, scene: QGraphicsScene):
        """
        Erase annotations at the given position.
        
        Args:
            scene_pos: Position to erase at
            scene: Graphics scene containing items
        """
        # Create eraser area
        eraser_rect = QRectF(
            scene_pos.x() - self.eraser_radius,
            scene_pos.y() - self.eraser_radius,
            self.eraser_radius * 2,
            self.eraser_radius * 2
        )
        
        # Find items in eraser area
        items_to_erase = scene.items(eraser_rect)
        
        for item in items_to_erase:
            # Skip our own preview item
            if item == self.preview_item:
                continue
                
            # Only erase annotation items (could check for specific types)
            # In a full implementation, you'd identify annotation items by type or metadata
            if hasattr(item, 'annotation_id') or self._is_annotation_item(item):
                if item not in self.erased_items:
                    self.erased_items.add(item)
                    # Hide the item (don't remove yet, in case user cancels)
                    item.setVisible(False)
                    
    def _is_annotation_item(self, item: QGraphicsItem) -> bool:
        """
        Check if an item is an annotation that can be erased.
        
        Args:
            item: Graphics item to check
            
        Returns:
            bool: True if item can be erased
        """
        # Skip base image pixmap
        if isinstance(item, QGraphicsPixmapItem):
            return False
        # Accept only items explicitly tagged as annotation
        data = item.data(0)
        if data == 'annotation':
            return True
        return False
        
    def _update_cursor_position(self, scene_pos: QPoint):
        """Update the position of the eraser cursor."""
        if not self.preview_item:
            return
            
        # Center the circular cursor on the mouse position
        rect = QRectF(
            scene_pos.x() - self.eraser_radius,
            scene_pos.y() - self.eraser_radius,
            self.eraser_radius * 2,
            self.eraser_radius * 2
        )
        self.preview_item.setRect(rect)
        
    def _finish_drawing_impl(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """
        Finish erasing operation.
        
        Note: Eraser doesn't create new annotations, but records what was erased.
        Returns None as no new annotation is created.
        """
        if not self.erased_items:
            return None
            
        # Actually remove the erased items from the scene
        for item in self.erased_items:
            if item.scene():
                item.scene().removeItem(item)
                
        # Create a record of the erase operation (optional)
        # This could be used for undo functionality
        geometry_data = {
            'erased_items': len(self.erased_items),
            'eraser_radius': self.eraser_radius,
            'center_x': scene_pos.x(),
            'center_y': scene_pos.y()
        }
        
        # Return an annotation record of the erase operation
        annotation = ManualAnnotation(
            annotation_id=str(uuid.uuid4()),
            tool_type=self.tool_type,
            geometry_data=geometry_data,
            metadata={
                'operation': 'erase',
                'items_erased': len(self.erased_items)
            }
        )
        
        return annotation
        
    def set_eraser_radius(self, radius: float):
        """Set the radius of the eraser tool."""
        self.eraser_radius = max(5.0, radius)
        
    def _cancel_drawing_impl(self):
        """Cancel erasing and restore hidden items."""
        # Restore visibility of items that were marked for erasing
        for item in self.erased_items:
            item.setVisible(True)
        self.erased_items.clear() 