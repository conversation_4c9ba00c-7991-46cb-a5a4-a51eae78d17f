"""
RBAC (Role-Based Access Control) Module for MedScan AI
Modularized RBAC components split from the original monolithic rbac_service.py

This module provides comprehensive role and permission management with medical-specific
operations, CRUD functionality, and HIPAA/GDPR compliance features.
"""

from .manager import RBACManager
from .service import RBACSer<PERSON>  
from .permissions import PermissionManager
from .roles import RoleManager

# Backward compatibility - import the original service class
try:
    from ..rbac_service import RBACService as LegacyRBACService
except ImportError:
    LegacyRBACService = None

__all__ = [
    'RBACManager',
    'RBACService', 
    'PermissionManager',
    'RoleManager',
    'LegacyRBACService'
] 