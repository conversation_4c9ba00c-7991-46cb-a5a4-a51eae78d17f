#!/usr/bin/env python3
"""
Test script for InteractiveImageViewer overlay layer system and AI metadata display.
Tests the functionality of overlay management, layer operations, and AI metadata integration.
"""

import sys
import numpy as np
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, 
    QHBoxLayout, QSplitter, QGroupBox, QLabel
)
from PySide6.QtGui import QPixmap, QPen, QBrush, QColor
from PySide6.QtCore import Qt

# Import our overlay and metadata system
from src.medscan_ai.gui.core import (
    InteractiveImageViewer, LayerType, numpy_to_qpixmap
)
from src.medscan_ai.gui.ai_display import (
    AIMetadataPanel, AIFindingDialog
)
from PySide6.QtWidgets import QGraphicsRectItem, QGraphicsEllipseItem

# Import AI interpretation classes for testing
try:
    from src.medscan_ai.ai.postprocessing.result_interpreter import (
        InterpretationResult,
        AnomalyDetection,
        BoundingBox,
        AnomalyType,
        ConfidenceLevel,
        DifferentialDiagnosis
    )
    AI_AVAILABLE = True
except ImportError:
    # Create mock classes for testing
    print("AI modules not available, using mock classes for testing")
    
    class BoundingBox:
        def __init__(self, x1, y1, x2, y2):
            self.x1 = x1
            self.y1 = y1
            self.x2 = x2
            self.y2 = y2
            
        @property
        def width(self):
            return self.x2 - self.x1
        
        @property
        def height(self):
            return self.y2 - self.y1
        
        def to_pixel_coordinates(self, img_w, img_h):
            return BoundingBox(self.x1 * img_w, self.y1 * img_h, self.x2 * img_w, self.y2 * img_h)
        
    class _EnumValue:
        def __init__(self, value):
            self.value = value
        def __str__(self):
            return self.value
        def __eq__(self, other):
            if isinstance(other, _EnumValue):
                return self.value == other.value
            return self.value == other
        def __hash__(self):
            return hash(self.value)
    
    class AnomalyType:
        FRACTURE = _EnumValue("fracture")
        PNEUMONIA = _EnumValue("pneumonia")
        MASS = _EnumValue("mass")
        NODULE = _EnumValue("nodule")
    
    class ConfidenceLevel:
        VERY_HIGH = _EnumValue("very_high")
        HIGH = _EnumValue("high")
        MODERATE = _EnumValue("moderate")
        LOW = _EnumValue("low")
        VERY_LOW = _EnumValue("very_low")
    
    class AnomalyDetection:
        _id_counter = 1
        def __init__(self, anomaly_type, confidence_score, bounding_box=None, clinical_significance=""):
            self.anomaly_id = AnomalyDetection._id_counter
            AnomalyDetection._id_counter += 1
            self.anomaly_type = anomaly_type
            self.confidence_score = confidence_score
            self.bounding_box = bounding_box
            self.clinical_significance = clinical_significance
    
    class DifferentialDiagnosis:
        def __init__(self, condition, probability, supporting_evidence=None):
            self.condition = condition
            self.probability = probability
            self.supporting_evidence = supporting_evidence or []
    
    class InterpretationResult:
        def __init__(self, overall_anomaly_detected=False, overall_confidence=0.0, detections=None,
                     processing_metadata=None, clinical_summary="", recommendations=None, timestamp=None):
            self.overall_anomaly_detected = overall_anomaly_detected
            self.overall_confidence = overall_confidence
            self.detections = detections or []
            self.processing_metadata = processing_metadata or {}
            self.clinical_summary = clinical_summary
            self.recommendations = recommendations or []
            from datetime import datetime
            self.timestamp = timestamp or datetime.now().isoformat()
    
    AI_AVAILABLE = False


class ComprehensiveTestWindow(QMainWindow):
    """Comprehensive test window for overlay layer system and AI metadata display."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MedScan AI - Comprehensive Overlay & Metadata Test")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize tracking variables
        self.current_layers = []
        
        self._setup_ui()
        
    def _setup_ui(self):
        """Setup the test window UI with metadata panel integration."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(main_splitter)
        
        # Left side: Image viewer and controls
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Create image viewer
        self.image_viewer = InteractiveImageViewer()
        left_layout.addWidget(self.image_viewer)
        
        # Controls section
        controls_group = QGroupBox("Test Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Basic controls row 1
        basic_row1 = QHBoxLayout()
        
        self.create_image_btn = QPushButton("Create Test Image")
        self.create_image_btn.clicked.connect(self.create_test_image)
        
        self.add_basic_layer_btn = QPushButton("Add Basic Layer")
        self.add_basic_layer_btn.clicked.connect(self.add_basic_layer)
        
        self.toggle_visibility_btn = QPushButton("Toggle Visibility")
        self.toggle_visibility_btn.clicked.connect(self.toggle_layer_visibility)
        
        basic_row1.addWidget(self.create_image_btn)
        basic_row1.addWidget(self.add_basic_layer_btn)
        basic_row1.addWidget(self.toggle_visibility_btn)
        
        # Basic controls row 2
        basic_row2 = QHBoxLayout()
        
        self.clear_layer_btn = QPushButton("Clear Layer")
        self.clear_layer_btn.clicked.connect(self.clear_last_layer)
        
        self.clear_all_btn = QPushButton("Clear All Layers")
        self.clear_all_btn.clicked.connect(self.clear_all_layers)
        
        basic_row2.addWidget(self.clear_layer_btn)
        basic_row2.addWidget(self.clear_all_btn)
        
        # AI metadata controls row 1
        ai_row1 = QHBoxLayout()
        
        self.add_ai_layer_btn = QPushButton("Add AI Findings Layer")
        self.add_ai_layer_btn.clicked.connect(self.add_ai_findings_layer)
        
        self.test_ai_integration_btn = QPushButton("Test AI Metadata Integration")
        self.test_ai_integration_btn.clicked.connect(self.test_ai_metadata_integration)
        
        self.show_metadata_dialog_btn = QPushButton("Show Metadata Dialog")
        self.show_metadata_dialog_btn.clicked.connect(self.show_metadata_dialog)
        
        ai_row1.addWidget(self.add_ai_layer_btn)
        ai_row1.addWidget(self.test_ai_integration_btn)
        ai_row1.addWidget(self.show_metadata_dialog_btn)
        
        # AI metadata controls row 2
        ai_row2 = QHBoxLayout()
        
        self.clear_ai_findings_btn = QPushButton("Clear AI Findings")
        self.clear_ai_findings_btn.clicked.connect(self.clear_ai_findings)
        
        self.test_interaction_btn = QPushButton("Test Finding Interaction")
        self.test_interaction_btn.clicked.connect(self.test_finding_interaction)
        
        ai_row2.addWidget(self.clear_ai_findings_btn)
        ai_row2.addWidget(self.test_interaction_btn)
        
        # Manual Annotation Tools Section
        annotation_group = QGroupBox("--- Manual Annotation Tools ---")
        annotation_layout = QVBoxLayout(annotation_group)
        
        # Annotation tools row 1
        tool_row1 = QHBoxLayout()
        
        self.rectangle_tool_btn = QPushButton("Rectangle Tool")
        self.rectangle_tool_btn.clicked.connect(lambda: self.set_annotation_tool("rectangle"))
        
        self.polygon_tool_btn = QPushButton("Polygon Tool")
        self.polygon_tool_btn.clicked.connect(lambda: self.set_annotation_tool("polygon"))
        
        self.freehand_tool_btn = QPushButton("Freehand Tool")
        self.freehand_tool_btn.clicked.connect(lambda: self.set_annotation_tool("freehand"))
        
        tool_row1.addWidget(self.rectangle_tool_btn)
        tool_row1.addWidget(self.polygon_tool_btn)
        tool_row1.addWidget(self.freehand_tool_btn)
        
        # Annotation tools row 2
        tool_row2 = QHBoxLayout()
        
        self.eraser_tool_btn = QPushButton("Eraser Tool")
        self.eraser_tool_btn.clicked.connect(lambda: self.set_annotation_tool("eraser"))
        
        self.select_tool_btn = QPushButton("Select Tool")
        self.select_tool_btn.clicked.connect(lambda: self.set_annotation_tool("select"))
        
        self.clear_annotations_btn = QPushButton("Clear Annotations")
        self.clear_annotations_btn.clicked.connect(self.clear_annotations)
        
        tool_row2.addWidget(self.eraser_tool_btn)
        tool_row2.addWidget(self.select_tool_btn)
        tool_row2.addWidget(self.clear_annotations_btn)
        
        annotation_layout.addLayout(tool_row1)
        annotation_layout.addLayout(tool_row2)
        
        # Add all control rows and sections
        controls_layout.addLayout(basic_row1)
        controls_layout.addLayout(basic_row2)
        controls_layout.addWidget(QLabel("--- AI Metadata Features ---"))
        controls_layout.addLayout(ai_row1)
        controls_layout.addLayout(ai_row2)
        controls_layout.addWidget(annotation_group)
        
        left_layout.addWidget(controls_group)
        
        # Right side: AI Metadata Panel
        self.metadata_panel = AIMetadataPanel()
        
        # Connect metadata panel to image viewer
        self.image_viewer.set_ai_metadata_panel(self.metadata_panel)
        
        # Add to splitter
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(self.metadata_panel)
        
        # Set splitter proportions (70% image viewer, 30% metadata panel)
        main_splitter.setSizes([1000, 400])
        
        # Status information
        self.status_label = QLabel("Ready - Create test image to begin")
        central_widget_layout.addWidget(self.status_label)
        
    def create_test_image(self):
        """Create a test medical image for testing overlays."""
        # Create a test image (simulating X-ray)
        image_array = np.zeros((512, 512), dtype=np.uint8)
        
        # Add some simulated anatomical structures
        # Chest cavity simulation
        y_center, x_center = 256, 256
        for y in range(512):
            for x in range(512):
                distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                if distance < 200:
                    image_array[y, x] = 30 + int(20 * np.sin(distance / 20))
                    
        # Add some noise for realism
        noise = np.random.normal(0, 10, (512, 512))
        image_array = np.clip(image_array + noise, 0, 255).astype(np.uint8)
        
        # Convert to QPixmap and display
        pixmap = numpy_to_qpixmap(image_array)
        self.image_viewer.set_pixmap(pixmap)
        
        self.status_label.setText("Test image created - 512x512 simulated medical image")
        print("Test image created successfully")
        
    def add_basic_layer(self):
        """Add a basic overlay layer with some shapes."""
        layer_id = self.image_viewer.create_overlay_layer(LayerType.MANUAL_ANNOTATIONS, "test-basic")
        
        # Add some test shapes
        rect = QGraphicsRectItem(100, 100, 150, 100)
        rect.setPen(QPen(QColor(0, 255, 0), 2))
        rect.setBrush(QBrush(QColor(0, 255, 0, 50)))
        
        circle = QGraphicsEllipseItem(300, 200, 80, 80)
        circle.setPen(QPen(QColor(0, 0, 255), 2))
        circle.setBrush(QBrush(QColor(0, 0, 255, 50)))
        
        self.image_viewer.add_graphics_item_to_layer(layer_id, rect)
        self.image_viewer.add_graphics_item_to_layer(layer_id, circle)
        
        self.current_layers.append(layer_id)
        
        self.status_label.setText(f"Basic overlay layer created: {layer_id}")
        print(f"Basic overlay layer '{layer_id}' added with rectangle and circle")
        
    def add_ai_findings_layer(self):
        """Add AI findings layer (without metadata integration)."""
        # Create mock interpretation result
        result = self._create_mock_interpretation_result()
        
        # Display AI findings without metadata integration
        layer_id = self.image_viewer.display_ai_findings(result)
        self.current_layers.append(layer_id)
        
        self.status_label.setText(f"AI findings layer created: {layer_id}")
        print(f"AI findings layer '{layer_id}' added with mock detections")
        
    def test_ai_metadata_integration(self):
        """Test comprehensive AI findings integration with metadata display."""
        # Create comprehensive mock interpretation result
        result = self._create_comprehensive_interpretation_result()
        
        # Display AI findings WITH metadata integration
        layer_id = self.image_viewer.display_ai_findings_with_metadata(result)
        self.current_layers.append(layer_id)
        
        self.status_label.setText(f"AI metadata integration tested: {layer_id}")
        print(f"Comprehensive AI findings with metadata integration: '{layer_id}'")
        print("✓ Metadata panel should now show detailed analysis")
        print("✓ Try clicking on AI findings for selection")
        print("✓ Try hovering over findings for enhanced tooltips")
        
    def _create_mock_interpretation_result(self):
        """Create a mock interpretation result for testing."""
        # Create mock anomaly detections
        detections = [
            AnomalyDetection(
                anomaly_type=AnomalyType.FRACTURE,
                confidence_score=0.87,
                bounding_box=BoundingBox(x1=0.2, y1=0.3, x2=0.4, y2=0.5),
                clinical_significance="Suspected fracture requiring attention"
            ),
            AnomalyDetection(
                anomaly_type=AnomalyType.PNEUMONIA,
                confidence_score=0.72,
                bounding_box=BoundingBox(x1=0.6, y1=0.1, x2=0.8, y2=0.3),
                clinical_significance="Possible pneumonia, recommend follow-up"
            )
        ]
        
        result = InterpretationResult(
            overall_anomaly_detected=True,
            overall_confidence=0.87,
            detections=detections,
            processing_metadata={
                'model_name': 'Test-Model-v1.0',
                'processing_time': '234ms',
                'output_type': 'detection'
            },
            clinical_summary="Multiple findings detected requiring clinical correlation",
            recommendations=[
                "Consider further imaging for suspected fracture",
                "Monitor pneumonia progression"
            ]
        )
        
        return result
        
    def _create_comprehensive_interpretation_result(self):
        """Create a comprehensive mock interpretation result for metadata testing."""
        # Create diverse anomaly detections with clinical significance
        detections = [
            AnomalyDetection(
                anomaly_type=AnomalyType.FRACTURE,
                confidence_score=0.92,
                bounding_box=BoundingBox(x1=0.15, y1=0.25, x2=0.35, y2=0.45),
                clinical_significance="Suspected rib fracture requiring immediate orthopedic consultation. Consider CT scan for detailed assessment."
            ),
            AnomalyDetection(
                anomaly_type=AnomalyType.PNEUMONIA,
                confidence_score=0.78,
                bounding_box=BoundingBox(x1=0.55, y1=0.1, x2=0.75, y2=0.35),
                clinical_significance="Consolidation pattern consistent with pneumonia. Recommend antibiotic therapy and follow-up imaging in 48-72 hours."
            ),
            AnomalyDetection(
                anomaly_type=AnomalyType.MASS,
                confidence_score=0.65,
                bounding_box=BoundingBox(x1=0.7, y1=0.6, x2=0.85, y2=0.8),
                clinical_significance="Pulmonary nodule requiring further evaluation. Consider PET scan or biopsy for characterization."
            ),
            AnomalyDetection(
                anomaly_type=AnomalyType.NODULE,
                confidence_score=0.58,
                bounding_box=BoundingBox(x1=0.1, y1=0.7, x2=0.25, y2=0.85),
                clinical_significance="Small pulmonary nodule. Recommend 6-month follow-up imaging for stability assessment."
            )
        ]
        
        result = InterpretationResult(
            overall_anomaly_detected=True,
            overall_confidence=0.92,
            detections=detections,
            processing_metadata={
                "model_name": "MedScan-AI-v2.1",
                "model_version": "2.1.0",
                "output_type": "detection",
                "inference_time_ms": 1247.3,
                "preprocessing_time_ms": 156.8,
                "postprocessing_time_ms": 89.2,
                "total_time_ms": 1493.3,
                "gpu_utilization": "78%",
                "memory_usage_mb": 3421,
                "training_date": "2024-01-15",
                "accuracy": "94.2%",
                "specialty": "Chest X-ray Analysis"
            },
            clinical_summary="Multiple significant findings detected including fracture and pneumonia requiring urgent clinical correlation.",
            recommendations=[
                "Immediate orthopedic consultation for suspected rib fracture",
                "Start empirical antibiotic therapy for pneumonia",
                "Follow-up chest CT in 6 months for pulmonary nodules",
                "Consider pulmonology referral for suspicious mass lesion",
                "Patient education on smoking cessation if applicable"
            ]
        )
        
        return result
        
    def show_metadata_dialog(self):
        """Show standalone metadata dialog for testing."""
        if hasattr(self.image_viewer, '_current_ai_result') and self.image_viewer._current_ai_result:
            dialog = AIFindingDialog(self.image_viewer._current_ai_result, None, self)
            dialog.exec()
        else:
            # Create test data
            result = self._create_comprehensive_interpretation_result()
            dialog = AIFindingDialog(result, None, self)
            dialog.exec()
            
    def test_finding_interaction(self):
        """Test finding interaction features."""
        if hasattr(self.image_viewer, '_current_ai_result') and self.image_viewer._current_ai_result:
            # Simulate finding selection
            first_detection = self.image_viewer._current_ai_result.detections[0]
            finding_id = str(1)  # Use index-based ID
            
            # Select in metadata panel
            self.metadata_panel.select_finding(finding_id)
            
            # Highlight on image
            self.image_viewer._highlight_finding(finding_id)
            
            self.status_label.setText(f"Tested interaction with finding #{finding_id}")
            print(f"✓ Finding #{finding_id} selected and highlighted")
            print("✓ Check metadata panel for detailed information")
        else:
            self.status_label.setText("No AI findings available - run metadata integration test first")
            
    def toggle_layer_visibility(self):
        """Toggle visibility of the last created layer."""
        if self.current_layers:
            layer_id = self.current_layers[-1]
            # Get current visibility and toggle
            current_layer = self.image_viewer.get_overlay_layer(layer_id)
            if current_layer:
                new_visibility = not current_layer.visible
                self.image_viewer.set_overlay_layer_visibility(layer_id, new_visibility)
                self.status_label.setText(f"Layer {layer_id} visibility: {new_visibility}")
                print(f"Toggled layer '{layer_id}' visibility to {new_visibility}")
        else:
            self.status_label.setText("No layers available to toggle")
            
    def clear_last_layer(self):
        """Clear the last created layer."""
        if self.current_layers:
            layer_id = self.current_layers.pop()
            self.image_viewer.clear_overlay_layer(layer_id)
            self.status_label.setText(f"Cleared layer: {layer_id}")
            print(f"Cleared layer: {layer_id}")
        else:
            self.status_label.setText("No layers to clear")
            
    def clear_all_layers(self):
        """Clear all overlay layers."""
        # Clear all overlay layers from the image viewer
        self.image_viewer.clear_all_overlay_layers()
        # Clear our local tracking list
        self.current_layers.clear()
        self.status_label.setText("All overlay layers cleared")
        print("All overlay layers cleared")
        
    def clear_ai_findings(self):
        """Clear AI findings specifically."""
        success = self.image_viewer.clear_ai_findings()
        if success:
            self.status_label.setText("AI findings cleared")
            print("AI findings cleared successfully")
        else:
            self.status_label.setText("No AI findings to clear")

    # Manual Annotation Tool Methods
    def set_annotation_tool(self, tool_name: str):
        """Set the active annotation tool."""
        from src.medscan_ai.gui.annotations import AnnotationTool
        
        tool_mapping = {
            "rectangle": AnnotationTool.RECTANGLE,
            "polygon": AnnotationTool.POLYGON,
            "freehand": AnnotationTool.FREEHAND,
            "eraser": AnnotationTool.ERASER,
            "select": AnnotationTool.SELECT,
            "none": AnnotationTool.NONE
        }
        
        if tool_name in tool_mapping:
            self.image_viewer.set_annotation_tool(tool_mapping[tool_name])
            self.status_label.setText(f"Annotation tool set to: {tool_name}")
            print(f"✓ Annotation tool switched to: {tool_name}")
            
            # Update button states to show active tool
            self._update_tool_button_states(tool_name)
        else:
            self.status_label.setText(f"Unknown tool: {tool_name}")
            
    def _update_tool_button_states(self, active_tool: str):
        """Update visual state of tool buttons to show which is active."""
        tool_buttons = {
            "rectangle": self.rectangle_tool_btn,
            "polygon": self.polygon_tool_btn,
            "freehand": self.freehand_tool_btn,
            "eraser": self.eraser_tool_btn,
            "select": self.select_tool_btn
        }
        
        # Reset all buttons to normal state
        for btn in tool_buttons.values():
            btn.setStyleSheet("")
            
        # Highlight active tool button
        if active_tool in tool_buttons:
            tool_buttons[active_tool].setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
            
    def clear_annotations(self):
        """Clear all manual annotations."""
        self.image_viewer.clear_all_annotations()
        self.status_label.setText("All annotations cleared")
        print("✓ All manual annotations cleared")


def main():
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("MedScan AI Test")
    app.setApplicationVersion("1.0")
    
    # Create and show test window
    window = ComprehensiveTestWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main()) 