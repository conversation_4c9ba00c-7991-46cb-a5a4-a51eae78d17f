"""
Test suite for MFA (Multi-Factor Authentication) Integration.

Tests MFA enrollment, TOTP verification, backup codes, and complete MFA flow
for medical-grade security compliance.
"""

import json
import os
import sys
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import base64

import pytest
import pyotp
from flask import Flask

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", ".."))

from src.medscan_ai.api.auth_endpoints import create_auth_app
from src.medscan_ai.security.authentication import AuthenticationResult


class TestMFAIntegration:
    """Test suite for MFA Integration and TOTP functionality."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        app = create_auth_app()
        app.config["TESTING"] = True
        app.config["SECRET_KEY"] = "test-secret-key"
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def mock_user_token(self):
        """Mock valid user JWT token."""
        return "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token"
    
    @pytest.fixture
    def mock_user_payload(self):
        """Mock user payload for token validation."""
        return {
            "user_id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "username": "testuser",
            "roles": ["radiologist"],
            "permissions": ["patient:read", "dicom:read"],
            "department": "Radiology",
            "iat": datetime.utcnow().timestamp(),
            "exp": (datetime.utcnow() + timedelta(hours=1)).timestamp(),
        }
    
    @pytest.fixture
    def mock_mfa_secret(self):
        """Mock MFA secret for testing."""
        return pyotp.random_base32()
    
    @pytest.fixture
    def mock_mfa_enrollment_response(self, mock_mfa_secret):
        """Mock MFA enrollment response."""
        totp = pyotp.TOTP(mock_mfa_secret)
        qr_provisioning_uri = totp.provisioning_uri(
            name="<EMAIL>",
            issuer_name="MedScan AI"
        )
        
        return {
            "secret": mock_mfa_secret,
            "qr_code_data": qr_provisioning_uri,
            "backup_codes": [
                "ABC123DEF", "GHI456JKL", "MNO789PQR", 
                "STU012VWX", "YZ3456ABC", "DEF789GHI",
                "JKL012MNO", "PQR345STU", "VWX678YZ1", "234567890"
            ]
        }

    # MFA Enrollment Tests
    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_enrollment_success(self, mock_auth_service, client, mock_user_token, 
                                  mock_user_payload, mock_mfa_enrollment_response):
        """Test successful MFA enrollment."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA enrollment
        mock_auth_service.enroll_mfa.return_value = (
            True, mock_mfa_enrollment_response, ""
        )
        
        response = client.post(
            "/api/auth/mfa/enroll",
            headers={"Authorization": mock_user_token}
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "success"
        assert "secret" in data["data"]
        assert "qr_code_data" in data["data"]
        assert "backup_codes" in data["data"]
        assert len(data["data"]["backup_codes"]) == 10

    def test_mfa_enrollment_missing_authorization(self, client):
        """Test MFA enrollment without authorization header."""
        response = client.post("/api/auth/mfa/enroll")
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "Authorization header required" in data["message"]

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_enrollment_invalid_token(self, mock_auth_service, client):
        """Test MFA enrollment with invalid token."""
        mock_auth_service.validate_request_token.return_value = (
            False, None, "Invalid token"
        )
        
        response = client.post(
            "/api/auth/mfa/enroll",
            headers={"Authorization": "Bearer invalid.token"}
        )
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "Invalid token" in data["message"]

    # MFA Verification Setup Tests
    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_verify_setup_success(self, mock_auth_service, client, 
                                    mock_user_token, mock_user_payload, mock_mfa_secret):
        """Test successful MFA setup verification."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA verification
        mock_auth_service.verify_mfa_setup.return_value = (
            True, "MFA enabled successfully"
        )
        
        # Generate valid TOTP code
        totp = pyotp.TOTP(mock_mfa_secret)
        valid_code = totp.now()
        
        response = client.post(
            "/api/auth/mfa/verify-setup",
            headers={"Authorization": mock_user_token},
            json={"totp_code": valid_code}
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "success"
        assert "MFA enabled successfully" in data["message"]

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_verify_setup_invalid_code(self, mock_auth_service, client,
                                         mock_user_token, mock_user_payload):
        """Test MFA setup verification with invalid code."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA verification failure
        mock_auth_service.verify_mfa_setup.return_value = (
            False, "Invalid TOTP code"
        )
        
        response = client.post(
            "/api/auth/mfa/verify-setup",
            headers={"Authorization": mock_user_token},
            json={"totp_code": "123456"}
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        
        assert data["status"] == "error"
        assert "Invalid TOTP code" in data["message"]

    def test_mfa_verify_setup_missing_code(self, client, mock_user_token):
        """Test MFA setup verification without TOTP code."""
        response = client.post(
            "/api/auth/mfa/verify-setup",
            headers={"Authorization": mock_user_token},
            json={}
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "TOTP code is required" in data["message"]

    # MFA Login Integration Tests
    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_login_with_mfa_required(self, mock_auth_service, client):
        """Test login that requires MFA code."""
        mock_auth_service.authenticate_user.return_value = (
            AuthenticationResult.MFA_REQUIRED,
            {"user_id": str(uuid.uuid4()), "email": "<EMAIL>"}
        )
        
        response = client.post(
            "/api/auth/login",
            json={"email": "<EMAIL>", "password": "SecurePass123!"}
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "mfa_required"
        assert "Multi-factor authentication code required" in data["message"]
        assert "user_id" in data["data"]

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_login_with_mfa_success(self, mock_auth_service, client):
        """Test successful login with MFA code."""
        mock_jwt_data = {
            "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token",
            "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.refresh.token",
            "token_type": "Bearer",
            "expires_in": 1800,
            "user": {
                "user_id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "roles": ["radiologist"]
            }
        }
        
        mock_auth_service.authenticate_user.return_value = (
            AuthenticationResult.SUCCESS,
            mock_jwt_data
        )
        
        response = client.post(
            "/api/auth/login",
            json={
                "email": "<EMAIL>", 
                "password": "SecurePass123!",
                "mfa_code": "123456"
            }
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "success"
        assert "access_token" in data["data"]

    # MFA Disable Tests
    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_disable_success(self, mock_auth_service, client,
                               mock_user_token, mock_user_payload):
        """Test successful MFA disable."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA disable
        mock_auth_service.disable_mfa.return_value = (
            True, "MFA disabled successfully"
        )
        
        response = client.post(
            "/api/auth/mfa/disable",
            headers={"Authorization": mock_user_token},
            json={"password": "SecurePass123!"}
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "success"
        assert "MFA disabled successfully" in data["message"]

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_disable_wrong_password(self, mock_auth_service, client,
                                      mock_user_token, mock_user_payload):
        """Test MFA disable with wrong password."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA disable failure
        mock_auth_service.disable_mfa.return_value = (
            False, "Invalid password"
        )
        
        response = client.post(
            "/api/auth/mfa/disable",
            headers={"Authorization": mock_user_token},
            json={"password": "wrongpassword"}
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        
        assert data["status"] == "error"
        assert "Invalid password" in data["message"]

    # MFA Status Tests
    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_status_enabled(self, mock_auth_service, client,
                              mock_user_token, mock_user_payload):
        """Test MFA status check for enabled user."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA status
        mock_auth_service.get_mfa_status.return_value = {
            "mfa_enabled": True,
            "backup_codes_remaining": 8,
            "enrollment_date": datetime.utcnow().isoformat()
        }
        
        response = client.get(
            "/api/auth/mfa/status",
            headers={"Authorization": mock_user_token}
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "success"
        assert data["data"]["mfa_enabled"] is True
        assert data["data"]["backup_codes_remaining"] == 8

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_mfa_status_disabled(self, mock_auth_service, client,
                               mock_user_token, mock_user_payload):
        """Test MFA status check for disabled user."""
        # Mock token validation
        mock_auth_service.validate_request_token.return_value = (
            True, mock_user_payload, ""
        )
        
        # Mock MFA status
        mock_auth_service.get_mfa_status.return_value = {
            "mfa_enabled": False,
            "backup_codes_remaining": 0,
            "enrollment_date": None
        }
        
        response = client.get(
            "/api/auth/mfa/status",
            headers={"Authorization": mock_user_token}
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        assert data["status"] == "success"
        assert data["data"]["mfa_enabled"] is False
        assert data["data"]["backup_codes_remaining"] == 0

    # TOTP Edge Cases
    def test_totp_time_window_validation(self, mock_mfa_secret):
        """Test TOTP code validation within time window."""
        totp = pyotp.TOTP(mock_mfa_secret)
        
        # Current code should be valid
        current_code = totp.now()
        assert totp.verify(current_code)
        
        # Code from 30 seconds ago should be valid (±1 step tolerance)
        previous_code = totp.at(datetime.now().timestamp() - 30)
        assert totp.verify(previous_code, valid_window=1)
        
        # Code from 60 seconds ago should be invalid
        old_code = totp.at(datetime.now().timestamp() - 60)
        assert not totp.verify(old_code, valid_window=1)

    # Backup Codes Tests  
    def test_backup_codes_format(self, mock_mfa_enrollment_response):
        """Test backup codes format and uniqueness."""
        backup_codes = mock_mfa_enrollment_response["backup_codes"]
        
        # Should have exactly 10 codes
        assert len(backup_codes) == 10
        
        # All codes should be unique
        assert len(set(backup_codes)) == 10
        
        # Each code should be 9 characters
        for code in backup_codes:
            assert len(code) == 9
            assert code.isalnum()


def run_mfa_tests():
    """Run all MFA integration tests."""
    print("🧪 Starting MFA Integration Tests...")
    
    import subprocess
    import sys
    
    result = subprocess.run(
        [sys.executable, "-m", "pytest", __file__, "-v"], 
        capture_output=True, text=True
    )
    
    print("📊 MFA Test Results:")
    print(result.stdout)
    if result.stderr:
        print("❌ Errors:")
        print(result.stderr)
    
    return result.returncode == 0


if __name__ == "__main__":
    """Run tests when script is executed directly."""
    success = run_mfa_tests()
    if success:
        print("\n✅ All MFA integration tests passed!")
    else:
        print("\n❌ Some MFA tests failed. Check output above.")
        exit(1) 