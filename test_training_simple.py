"""
Simple Training Test for Medical CNN

This script tests our medical CNN with extracted datasets
to verify the training pipeline works before full implementation.
"""

import os
import sys
import tensorflow as tf
from pathlib import Path
import numpy as np

# Add src to path
sys.path.append('src')

from medscan_ai.ai.models.medical_cnn import create_medical_cnn, ModelConfig

def check_gpu():
    """Check GPU availability."""
    gpus = tf.config.experimental.list_physical_devices('GPU')
    print(f"🔍 GPU Check: {len(gpus)} GPU(s) found")
    if gpus:
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu.name}")
        
        # Enable memory growth
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("✅ GPU memory growth enabled")
        except RuntimeError as e:
            print(f"❌ GPU configuration error: {e}")
    return len(gpus) > 0

def check_datasets():
    """Check extracted datasets."""
    dataset_path = Path("./data/datasets/extracted")
    print(f"🔍 Dataset Check: {dataset_path}")
    
    if not dataset_path.exists():
        print("❌ No extracted datasets found")
        return False
    
    # Check for lung segmentation dataset
    lung_path = dataset_path / "lung_segmentation" / "Lung Segmentation" / "CXR_png"
    lung_count = 0
    if lung_path.exists():
        lung_count = len(list(lung_path.glob("*.png")))
        print(f"✅ Lung dataset: {lung_count} PNG images")
    
    return lung_count > 0

def create_simple_dataset():
    """Create a simple dataset for testing."""
    print("🔄 Creating simple test dataset...")
    
    # Find images
    dataset_path = Path("./data/datasets/extracted")
    lung_path = dataset_path / "lung_segmentation" / "Lung Segmentation" / "CXR_png"
    
    image_paths = list(lung_path.glob("*.png"))[:100]  # Use first 100 images
    print(f"📊 Using {len(image_paths)} images for testing")
    
    def load_and_preprocess_image(path):
        image = tf.io.read_file(str(path))
        image = tf.image.decode_image(image, channels=3)
        image = tf.image.resize(image, [224, 224])
        image = tf.cast(image, tf.float32) / 255.0
        return image
    
    # Create dataset
    images = []
    labels = []
    
    for i, path in enumerate(image_paths):
        try:
            image = load_and_preprocess_image(path)
            images.append(image)
            # Simple binary labels (0 for all in this test)
            labels.append(0)
            
            if i % 20 == 0:
                print(f"  Processed {i+1}/{len(image_paths)} images")
                
        except Exception as e:
            print(f"  Error processing {path}: {e}")
            continue
    
    print(f"✅ Processed {len(images)} images successfully")
    
    # Convert to tensors
    images_tensor = tf.stack(images)
    labels_tensor = tf.constant(labels)
    
    # Create tf.data.Dataset
    dataset = tf.data.Dataset.from_tensor_slices((images_tensor, labels_tensor))
    dataset = dataset.batch(8)  # Small batch size for testing
    dataset = dataset.prefetch(tf.data.AUTOTUNE)
    
    return dataset

def test_model_creation():
    """Test medical CNN model creation."""
    print("🔄 Testing Medical CNN model creation...")
    
    try:
        # Create model
        medical_cnn = create_medical_cnn(
            base_model="ResNet50",
            input_shape=(224, 224, 3),
            num_classes=2,
            dense_layers=[256, 128],
            dropout_rate=0.5
        )
        
        # Build model
        model = medical_cnn.build_model()
        
        print("✅ Medical CNN model created successfully")
        print(f"📊 Model parameters: {model.count_params():,}")
        
        return medical_cnn
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return None

def test_simple_training(medical_cnn, dataset):
    """Test simple training with the dataset."""
    print("🔄 Testing simple training...")
    
    try:
        # Split dataset into train/val (simple split)
        dataset_size = 100  # We know we have 100 images
        train_size = int(0.8 * dataset_size / 8)  # 8 is batch size
        
        train_dataset = dataset.take(train_size)
        val_dataset = dataset.skip(train_size)
        
        print(f"📊 Training batches: {train_size}")
        print(f"📊 Validation batches: {dataset_size//8 - train_size}")
        
        # Train for just 2 epochs to test
        print("🚀 Starting training test (2 epochs)...")
        
        history = medical_cnn.model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=2,
            verbose=1
        )
        
        print("✅ Training test completed successfully!")
        print(f"📊 Final training accuracy: {history.history['accuracy'][-1]:.4f}")
        print(f"📊 Final validation accuracy: {history.history['val_accuracy'][-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Medical CNN Training Test")
    print("=" * 50)
    
    # Check GPU
    gpu_available = check_gpu()
    print()
    
    # Check datasets
    datasets_available = check_datasets()
    print()
    
    if not datasets_available:
        print("❌ Cannot proceed without datasets")
        return
    
    # Create simple dataset
    dataset = create_simple_dataset()
    print()
    
    # Test model creation
    medical_cnn = test_model_creation()
    print()
    
    if medical_cnn is None:
        print("❌ Cannot proceed without model")
        return
    
    # Test training
    training_success = test_simple_training(medical_cnn, dataset)
    print()
    
    # Summary
    print("📋 Test Summary:")
    print(f"  GPU Available: {'✅' if gpu_available else '❌'}")
    print(f"  Datasets Ready: {'✅' if datasets_available else '❌'}")
    print(f"  Model Creation: {'✅' if medical_cnn else '❌'}")
    print(f"  Training Test: {'✅' if training_success else '❌'}")
    
    if training_success:
        print("\n🎉 All tests passed! Ready for full training pipeline.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
