"""
Authorization and RBAC Security Tests for MedScan AI
Tests focused on role-based access control and authorization mechanisms

Extracted from the original auth_security_tests.py file for better modularity.
"""

import time
from typing import Dict, List, Any
from . import (
    SecurityTestResult, MockDatabaseSession, logger,
    RBAC_MANAGER_AVAILABLE, RBAC_SERVICE_AVAILABLE, AUTHORIZATION_AVAILABLE
)

if RBAC_MANAGER_AVAILABLE:
    from medscan_ai.security.access_control.rbac_manager import RBACManager

if RBAC_SERVICE_AVAILABLE:
    from medscan_ai.security.access_control.rbac_service import RBACService

if AUTHORIZATION_AVAILABLE:
    from medscan_ai.security.authentication.authorization import AuthorizationMiddleware


class AuthorizationSecurityTests:
    """
    Authorization and RBAC-specific security tests
    """

    def __init__(self):
        """Initialize authorization security testing"""
        self.test_results: List[SecurityTestResult] = []
        self.available_services = []
        
        # Mock database session
        mock_session = MockDatabaseSession()
        
        # Initialize services with error handling
        self.rbac_manager = None
        self.rbac_service = None
        self.authorization = None
        
        # Try RBAC manager
        if RBAC_MANAGER_AVAILABLE:
            try:
                self.rbac_manager = RBACManager(db_session=mock_session)
                self.available_services.append('rbac_manager')
                logger.info("✅ RBACManager initialized")
            except Exception as e:
                logger.warning(f"❌ RBACManager failed: {e}")
        
        # Try RBAC service
        if RBAC_SERVICE_AVAILABLE:
            try:
                self.rbac_service = RBACService(db_session=mock_session)
                self.available_services.append('rbac_service')
                logger.info("✅ RBACService initialized")
            except Exception as e:
                logger.warning(f"❌ RBACService failed: {e}")
        
        # Try authorization middleware
        if AUTHORIZATION_AVAILABLE:
            try:
                self.authorization = AuthorizationMiddleware()
                self.available_services.append('authorization')
                logger.info("✅ AuthorizationMiddleware initialized")
            except Exception as e:
                logger.warning(f"❌ AuthorizationMiddleware failed: {e}")

    def run_authorization_tests(self) -> List[SecurityTestResult]:
        """
        Run all authorization security tests
        
        Returns:
            List of test results
        """
        logger.info("Starting authorization security testing")
        
        # Clear previous results
        self.test_results = []
        
        # Run tests based on available services
        if 'rbac_manager' in self.available_services or 'rbac_service' in self.available_services:
            self._test_rbac_enforcement()
        
        if 'authorization' in self.available_services:
            self._test_authorization_bypass()
        
        # General authorization tests
        self._test_privilege_escalation()
        self._test_role_validation()
        
        return self.test_results

    def _test_rbac_enforcement(self):
        """Test Role-Based Access Control enforcement"""
        logger.info("Testing RBAC enforcement")
        
        # Use either RBAC manager or service, whichever is available
        rbac_system = self.rbac_manager or self.rbac_service
        
        if not rbac_system:
            self.test_results.append(
                SecurityTestResult(
                    test_name="RBAC System Availability",
                    passed=False,
                    details="No RBAC system available for testing",
                    severity="high",
                    recommendations=["Initialize RBAC manager or service"]
                )
            )
            return
        
        # Test 1: Role assignment validation
        try:
            test_user_id = f"test_user_{int(time.time())}"
            test_roles = ["admin", "doctor", "patient", "invalid_role"]
            
            valid_assignments = 0
            invalid_rejections = 0
            
            for role in test_roles:
                try:
                    if hasattr(rbac_system, 'assign_role'):
                        result = rbac_system.assign_role(test_user_id, role)
                        
                        if role == "invalid_role" and not result:
                            invalid_rejections += 1
                        elif role != "invalid_role" and result:
                            valid_assignments += 1
                            
                except Exception as e:
                    if role == "invalid_role" and "invalid" in str(e).lower():
                        invalid_rejections += 1
                    else:
                        logger.warning(f"Role assignment test error: {e}")
            
            if invalid_rejections > 0 and valid_assignments > 0:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="RBAC Role Assignment Validation",
                        passed=True,
                        details=f"Role validation working: {valid_assignments} valid assignments, {invalid_rejections} invalid rejections",
                        severity="high"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="RBAC Role Assignment Validation",
                        passed=False,
                        details=f"Role validation issues: {valid_assignments} valid assignments, {invalid_rejections} invalid rejections",
                        severity="high",
                        recommendations=["Improve role validation logic"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="RBAC Role Assignment Validation",
                    passed=False,
                    details=f"RBAC role assignment test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix RBAC role assignment functionality"]
                )
            )
        
        # Test 2: Permission enforcement
        try:
            test_permissions = [
                ("read_patient_data", "doctor", True),
                ("read_patient_data", "patient", False),
                ("admin_access", "admin", True),
                ("admin_access", "doctor", False),
                ("view_reports", "doctor", True)
            ]
            
            permission_tests_passed = 0
            
            for permission, role, should_have_access in test_permissions:
                try:
                    if hasattr(rbac_system, 'check_permission'):
                        has_access = rbac_system.check_permission(test_user_id, permission, role)
                        
                        if has_access == should_have_access:
                            permission_tests_passed += 1
                            
                except Exception as e:
                    logger.warning(f"Permission test error for {permission}/{role}: {e}")
            
            if permission_tests_passed >= len(test_permissions) * 0.6:  # At least 60% pass
                self.test_results.append(
                    SecurityTestResult(
                        test_name="RBAC Permission Enforcement",
                        passed=True,
                        details=f"Permission enforcement working: {permission_tests_passed}/{len(test_permissions)} tests passed",
                        severity="high"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="RBAC Permission Enforcement",
                        passed=False,
                        details=f"Permission enforcement issues: {permission_tests_passed}/{len(test_permissions)} tests passed",
                        severity="critical",
                        recommendations=["Fix permission enforcement logic"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="RBAC Permission Enforcement",
                    passed=False,
                    details=f"RBAC permission enforcement test failed: {str(e)}",
                    severity="critical",
                    recommendations=["Implement proper permission enforcement"]
                )
            )

    def _test_authorization_bypass(self):
        """Test for authorization bypass vulnerabilities"""
        logger.info("Testing authorization bypass protection")
        
        if not self.authorization:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Authorization Bypass Protection",
                    passed=False,
                    details="Authorization middleware not available for testing",
                    severity="high",
                    recommendations=["Initialize authorization middleware"]
                )
            )
            return
        
        # Test common authorization bypass patterns
        bypass_attempts = [
            {"user_id": "admin", "requested_resource": "/admin/users"},
            {"user_id": "../admin", "requested_resource": "/sensitive_data"},
            {"user_id": "user1", "requested_resource": "/admin/../sensitive"},
            {"user_id": "user1' OR '1'='1", "requested_resource": "/admin"},
            {"user_id": "null", "requested_resource": "/admin"},
            {"user_id": "", "requested_resource": "/admin"}
        ]
        
        bypass_blocked = 0
        
        for attempt in bypass_attempts:
            try:
                if hasattr(self.authorization, 'check_access'):
                    result = self.authorization.check_access(
                        attempt["user_id"], 
                        attempt["requested_resource"]
                    )
                    
                    # Most of these should be blocked (return False)
                    if not result:
                        bypass_blocked += 1
                        
            except Exception as e:
                # Exceptions are generally good - they indicate the bypass was caught
                bypass_blocked += 1
        
        if bypass_blocked >= len(bypass_attempts) * 0.8:  # At least 80% blocked
            self.test_results.append(
                SecurityTestResult(
                    test_name="Authorization Bypass Protection",
                    passed=True,
                    details=f"Authorization bypass protection working: {bypass_blocked}/{len(bypass_attempts)} attempts blocked",
                    severity="high"
                )
            )
        else:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Authorization Bypass Protection",
                    passed=False,
                    details=f"Authorization bypass vulnerabilities detected: {bypass_blocked}/{len(bypass_attempts)} attempts blocked",
                    severity="critical",
                    recommendations=["Strengthen authorization bypass protection"]
                )
            )

    def _test_privilege_escalation(self):
        """Test for privilege escalation vulnerabilities"""
        logger.info("Testing privilege escalation protection")
        
        escalation_attempts = [
            ("patient", "admin"),
            ("doctor", "super_admin"),
            ("user", "root"),
            ("guest", "admin"),
            ("", "admin")
        ]
        
        escalations_blocked = 0
        
        for from_role, to_role in escalation_attempts:
            try:
                # Test if a user can escalate from from_role to to_role
                # This should generally be blocked unless there's a legitimate upgrade path
                
                rbac_system = self.rbac_manager or self.rbac_service
                if rbac_system and hasattr(rbac_system, 'can_escalate_role'):
                    can_escalate = rbac_system.can_escalate_role(from_role, to_role)
                    
                    if not can_escalate:
                        escalations_blocked += 1
                else:
                    # If no escalation method exists, that's also good
                    escalations_blocked += 1
                    
            except Exception as e:
                # Exceptions during escalation attempts are generally good
                escalations_blocked += 1
        
        if escalations_blocked >= len(escalation_attempts) * 0.9:  # At least 90% blocked
            self.test_results.append(
                SecurityTestResult(
                    test_name="Privilege Escalation Protection",
                    passed=True,
                    details=f"Privilege escalation protection working: {escalations_blocked}/{len(escalation_attempts)} attempts blocked",
                    severity="high"
                )
            )
        else:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Privilege Escalation Protection",
                    passed=False,
                    details=f"Privilege escalation vulnerabilities detected: {escalations_blocked}/{len(escalation_attempts)} attempts blocked",
                    severity="critical",
                    recommendations=["Implement stronger privilege escalation protection"]
                )
            )

    def _test_role_validation(self):
        """Test role validation and consistency"""
        logger.info("Testing role validation")
        
        # Test role hierarchy consistency
        try:
            test_roles = [
                "super_admin",
                "admin", 
                "doctor",
                "nurse",
                "patient",
                "guest"
            ]
            
            rbac_system = self.rbac_manager or self.rbac_service
            
            if rbac_system:
                valid_roles = 0
                
                for role in test_roles:
                    try:
                        if hasattr(rbac_system, 'validate_role'):
                            is_valid = rbac_system.validate_role(role)
                            if is_valid:
                                valid_roles += 1
                        elif hasattr(rbac_system, 'get_role_permissions'):
                            # If we can get permissions, the role is probably valid
                            permissions = rbac_system.get_role_permissions(role)
                            if permissions is not None:
                                valid_roles += 1
                    except Exception:
                        # Role validation failed, which might be expected for some roles
                        pass
                
                if valid_roles >= 3:  # At least some roles should be valid
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Role Validation System",
                            passed=True,
                            details=f"Role validation working: {valid_roles}/{len(test_roles)} roles validated",
                            severity="medium"
                        )
                    )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Role Validation System",
                            passed=False,
                            details=f"Role validation issues: {valid_roles}/{len(test_roles)} roles validated",
                            severity="medium",
                            recommendations=["Improve role validation system"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Role Validation System",
                        passed=False,
                        details="No RBAC system available for role validation testing",
                        severity="medium",
                        recommendations=["Implement role validation system"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Role Validation System",
                    passed=False,
                    details=f"Role validation test failed: {str(e)}",
                    severity="medium",
                    recommendations=["Fix role validation implementation"]
                )
            )


def run_authorization_security_tests() -> List[SecurityTestResult]:
    """
    Convenience function to run authorization security tests
    
    Returns:
        List of test results
    """
    auth_tests = AuthorizationSecurityTests()
    return auth_tests.run_authorization_tests() 