"""User Registration API Endpoints for MedScan AI.

Provides REST API endpoints for new user registration operations:
- User account registration
- Medical staff registration with license validation
- Role-based registration with approval workflows
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from flask import Blueprint, g, jsonify, request
from werkzeug.exceptions import BadRequest, Forbidden, Unauthorized

from ...database.engine import get_session
from ...security.access_control.rbac_manager import RBACManager
from ...security.authentication import (
    AuthenticationResult,
    AuthenticationService, 
    RegistrationResult,
    UserCredentialService,
)
from ...security.authentication.authorization import (
    AuthorizationMiddleware,
    admin_only,
    medical_staff_only,
    physician_only,
    require_authentication,
    require_role,
    require_permission,
    audit_access,
)
from ...security.authentication.session_manager import SessionMiddleware
from ...security.audit import get_audit_service, AuditEvent, AuditContext, ActionType, ActionCategory

logger = logging.getLogger(__name__)

def create_registration_blueprint(
    auth_service: AuthenticationService,
    session_middleware: SessionMiddleware,
    auth_middleware: AuthorizationMiddleware
) -> Blueprint:
    """Create registration endpoints blueprint."""
    
    registration_bp = Blueprint("registration", __name__, url_prefix="/api/auth")

    @registration_bp.route("/register", methods=["POST"])
    def register():
        """
        Register new user account.

        Request body:
            email: User email
            username: Unique username
            password: User password
            first_name: User's first name
            last_name: User's last name
            role: Role name to assign
            department: Medical department (optional)
            facility: Healthcare facility (optional)
            medical_license: Medical license number (optional)
            medical_specialties: List of specialties (optional)

        Returns:
            Registration result and user information
        """
        audit_service = get_audit_service()
        client_ip = request.environ.get("REMOTE_ADDR", "unknown")
        user_agent = request.headers.get("User-Agent", "unknown")
        
        try:
            data = request.get_json()
            if not data:
                return (
                    jsonify({"status": "error", "message": "Request body is required"}),
                    400,
                )

            # Extract required fields
            required_fields = [
                "email",
                "username",
                "password",
                "first_name",
                "last_name",
                "role",
            ]
            for field in required_fields:
                if not data.get(field):
                    return (
                        jsonify({"status": "error", "message": f"{field} is required"}),
                        400,
                    )

            email = data["email"]
            
            # Create audit context
            audit_context = AuditContext(
                user_id=email,
                ip_address=client_ip,
                user_agent=user_agent
            )

            # Register user
            result, user_data = auth_service.register_user(
                email=email,
                username=data["username"],
                password=data["password"],
                first_name=data["first_name"],
                last_name=data["last_name"],
                role_name=data["role"],
                department=data.get("department"),
                facility=data.get("facility"),
                medical_license=data.get("medical_license"),
                medical_specialties=data.get("medical_specialties"),
            )

            if result == RegistrationResult.SUCCESS:
                # Log successful registration
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=True,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    resource_id=user_data.get("user_id"),
                    details={
                        "registration_type": "self_registration",
                        "role": data["role"],
                        "department": data.get("department"),
                        "facility": data.get("facility"),
                        "has_medical_license": bool(data.get("medical_license")),
                        "username": data["username"]
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "success",
                    "message": "User registered successfully",
                    "data": user_data,
                }
                logger.info(f"User registration successful: {email}")
                return jsonify(response_data), 201

            elif result == RegistrationResult.APPROVAL_REQUIRED:
                # Log registration pending approval
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=True,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    resource_id=user_data.get("user_id") if user_data else None,
                    details={
                        "registration_type": "pending_approval",
                        "role": data["role"],
                        "department": data.get("department"),
                        "requires_admin_approval": True
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "pending_approval",
                    "message": "Registration successful but requires administrative approval",
                    "data": user_data,
                }
                return jsonify(response_data), 202

            elif result == RegistrationResult.EMAIL_EXISTS:
                # Log duplicate email attempt
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "email_already_exists",
                        "attempted_email": email,
                        "security_note": "duplicate_registration_attempt"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Email address is already registered",
                }
                return jsonify(response_data), 409

            elif result == RegistrationResult.USERNAME_EXISTS:
                # Log duplicate username attempt
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "username_already_exists",
                        "attempted_username": data["username"]
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {"status": "error", "message": "Username is already taken"}
                return jsonify(response_data), 409

            elif result == RegistrationResult.WEAK_PASSWORD:
                # Log weak password attempt
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "weak_password",
                        "security_policy_violation": True
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Password does not meet security requirements",
                }
                return jsonify(response_data), 400

            elif result == RegistrationResult.INVALID_LICENSE:
                # Log invalid medical license
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "invalid_medical_license",
                        "role": data["role"],
                        "compliance_issue": "medical_license_validation_failed"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Invalid or missing medical license for medical role",
                }
                return jsonify(response_data), 400

            elif result == RegistrationResult.INVALID_DEPARTMENT:
                # Log missing department for medical role
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "missing_department",
                        "role": data["role"],
                        "policy_violation": "medical_role_requires_department"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Department is required for medical roles",
                }
                return jsonify(response_data), 400

            elif result == RegistrationResult.ROLE_NOT_ALLOWED:
                # Log invalid role assignment
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "invalid_role",
                        "attempted_role": data["role"],
                        "security_violation": "unauthorized_role_assignment"
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {
                    "status": "error",
                    "message": "Invalid role or role assignment failed",
                }
                return jsonify(response_data), 400

            else:
                # Log general registration failure
                audit_event = AuditEvent(
                    action=ActionType.CREATE,
                    action_category=ActionCategory.USER_MANAGEMENT,
                    success=False,
                    context=audit_context,
                    resource_type="USER_ACCOUNT",
                    details={
                        "failure_reason": "general_registration_failure",
                        "result": str(result)
                    },
                    phi_accessed=False
                )
                audit_service.log_event(audit_event)
                
                response_data = {"status": "error", "message": "Registration failed"}
                return jsonify(response_data), 400
                
        except Exception as e:
            # Log system error during registration
            audit_event = AuditEvent(
                action=ActionType.CREATE,
                action_category=ActionCategory.USER_MANAGEMENT,
                success=False,
                context=AuditContext(
                    user_id=data.get("email") if data else "unknown",
                    ip_address=client_ip,
                    user_agent=user_agent
                ),
                resource_type="USER_ACCOUNT",
                details={
                    "failure_reason": "system_error",
                    "error_message": str(e)
                },
                phi_accessed=False
            )
            audit_service.log_event(audit_event)
            
            logger.error(f"Registration endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "Registration service temporarily unavailable",
            }
            return jsonify(response_data), 500

    return registration_bp
