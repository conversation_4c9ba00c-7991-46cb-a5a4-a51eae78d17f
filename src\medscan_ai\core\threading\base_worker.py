"""
Base Worker Class for MedScan AI Threading Infrastructure

Provides common functionality for all background worker threads including:
- Progress reporting and cancellation support
- Thread-safe communication with UI
- Error handling and resource cleanup
- Performance monitoring
"""

import time
import traceback
from typing import Any, Dict, Optional, Callable
from PySide6.QtCore import QThread, Signal, QMutex, QMutexLocker
import logging

logger = logging.getLogger(__name__)


class WorkerError(Exception):
    """Custom exception for worker thread errors."""
    pass


class MedScanWorker(QThread):
    """
    Base worker thread class for MedScan AI background operations.
    
    Provides common functionality including:
    - Progress reporting with percentage and message updates
    - Cancellation support for long-running operations
    - Thread-safe error handling and result communication
    - Performance monitoring and statistics
    - Resource cleanup on completion or cancellation
    """
    
    # Signals for thread-safe communication with UI
    progress_updated = Signal(int, str)  # (percentage, message)
    finished_with_result = Signal(object)  # (result)
    error_occurred = Signal(str, str)  # (error_message, error_details)
    status_changed = Signal(str)  # (status_message)
    performance_stats = Signal(dict)  # (stats_dict)
    
    def __init__(self, operation_name: str = "Background Operation", parent=None):
        """
        Initialize the base worker.
        
        Args:
            operation_name: Human-readable name for this operation
            parent: Parent QObject (typically the main window)
        """
        super().__init__(parent)
        
        self.operation_name = operation_name
        self._cancellation_requested = False
        self._is_running = False
        self._progress_percentage = 0
        self._progress_message = ""
        
        # Thread-safe access control
        self._mutex = QMutex()
        
        # Performance tracking
        self._start_time = None
        self._end_time = None
        self._stats = {
            'operation_name': operation_name,
            'start_time': None,
            'end_time': None,
            'duration_seconds': 0.0,
            'status': 'initialized',
            'items_processed': 0,
            'errors_count': 0,
            'memory_peak_mb': 0.0
        }
        
        # Optional callbacks for custom handling
        self._progress_callback: Optional[Callable[[int, str], None]] = None
        self._completion_callback: Optional[Callable[[Any], None]] = None
        self._error_callback: Optional[Callable[[str, str], None]] = None
        
        logger.info(f"Worker '{operation_name}' initialized")
    
    def set_progress_callback(self, callback: Callable[[int, str], None]) -> None:
        """Set custom progress callback (in addition to signals)."""
        self._progress_callback = callback
    
    def set_completion_callback(self, callback: Callable[[Any], None]) -> None:
        """Set custom completion callback (in addition to signals)."""
        self._completion_callback = callback
    
    def set_error_callback(self, callback: Callable[[str, str], None]) -> None:
        """Set custom error callback (in addition to signals)."""
        self._error_callback = callback
    
    def request_cancellation(self) -> None:
        """
        Request cancellation of the operation.
        Worker should check is_cancellation_requested() periodically.
        """
        with QMutexLocker(self._mutex):
            self._cancellation_requested = True
            
        logger.info(f"Cancellation requested for worker '{self.operation_name}'")
        self.update_status("Cancellation requested...")
    
    def is_cancellation_requested(self) -> bool:
        """Check if cancellation has been requested (thread-safe)."""
        with QMutexLocker(self._mutex):
            return self._cancellation_requested
    
    def is_running(self) -> bool:
        """Check if worker is currently running (thread-safe)."""
        with QMutexLocker(self._mutex):
            return self._is_running
    
    def update_progress(self, percentage: int, message: str = "") -> None:
        """
        Update progress information (thread-safe).
        
        Args:
            percentage: Progress percentage (0-100)
            message: Progress message for user display
        """
        with QMutexLocker(self._mutex):
            self._progress_percentage = max(0, min(100, percentage))
            self._progress_message = message
        
        # Emit signal for UI updates
        self.progress_updated.emit(self._progress_percentage, message)
        
        # Call custom callback if set
        if self._progress_callback:
            try:
                self._progress_callback(self._progress_percentage, message)
            except Exception as e:
                logger.warning(f"Progress callback error: {e}")
        
        # Check for cancellation during progress updates
        if self.is_cancellation_requested():
            raise WorkerError("Operation cancelled by user")
    
    def update_status(self, status: str) -> None:
        """Update status message (thread-safe)."""
        self.status_changed.emit(status)
        logger.debug(f"Worker '{self.operation_name}' status: {status}")
    
    def increment_processed_count(self, count: int = 1) -> None:
        """Increment the count of processed items (thread-safe)."""
        with QMutexLocker(self._mutex):
            self._stats['items_processed'] += count
    
    def increment_error_count(self, count: int = 1) -> None:
        """Increment the error count (thread-safe)."""
        with QMutexLocker(self._mutex):
            self._stats['errors_count'] += count
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current performance statistics (thread-safe)."""
        with QMutexLocker(self._mutex):
            stats = self._stats.copy()
            stats['is_running'] = self._is_running
            stats['cancellation_requested'] = self._cancellation_requested
            stats['progress_percentage'] = self._progress_percentage
            stats['progress_message'] = self._progress_message
            
            # Calculate current duration if running
            if self._is_running and self._start_time:
                stats['current_duration'] = time.time() - self._start_time
            
            return stats
    
    def run(self) -> None:
        """
        Main thread execution method.
        Calls do_work() with proper error handling and performance tracking.
        """
        try:
            # Initialize run state
            with QMutexLocker(self._mutex):
                self._is_running = True
                self._cancellation_requested = False
                self._start_time = time.time()
                self._stats['start_time'] = self._start_time
                self._stats['status'] = 'running'
            
            self.update_status(f"Starting {self.operation_name}...")
            logger.info(f"Worker '{self.operation_name}' started")
            
            # Perform the actual work (implemented by subclasses)
            result = self.do_work()
            
            # Check if cancelled during execution
            if self.is_cancellation_requested():
                self._handle_cancellation()
                return
            
            # Successful completion
            self._handle_success(result)
            
        except WorkerError as e:
            # Handle expected worker errors (e.g., cancellation)
            self._handle_worker_error(str(e))
            
        except Exception as e:
            # Handle unexpected errors
            error_details = traceback.format_exc()
            self._handle_unexpected_error(str(e), error_details)
            
        finally:
            # Cleanup and finalize stats
            self._finalize_execution()
    
    def do_work(self) -> Any:
        """
        Perform the actual work (to be implemented by subclasses).
        
        Should:
        - Check is_cancellation_requested() periodically
        - Call update_progress() to report progress
        - Call increment_processed_count() for tracking
        - Return result object or None
        
        Returns:
            Result object (type depends on operation)
            
        Raises:
            WorkerError: For expected errors (cancellation, validation, etc.)
            Exception: For unexpected errors
        """
        raise NotImplementedError("Subclasses must implement do_work()")
    
    def _handle_success(self, result: Any) -> None:
        """Handle successful completion."""
        with QMutexLocker(self._mutex):
            self._stats['status'] = 'completed'
        
        self.update_progress(100, "Operation completed successfully")
        self.update_status(f"{self.operation_name} completed")
        
        # Emit success signals
        self.finished_with_result.emit(result)
        
        # Call custom callback if set
        if self._completion_callback:
            try:
                self._completion_callback(result)
            except Exception as e:
                logger.warning(f"Completion callback error: {e}")
        
        logger.info(f"Worker '{self.operation_name}' completed successfully")
    
    def _handle_cancellation(self) -> None:
        """Handle cancellation."""
        with QMutexLocker(self._mutex):
            self._stats['status'] = 'cancelled'
        
        self.update_status(f"{self.operation_name} cancelled")
        self.finished_with_result.emit(None)  # Emit None result for cancellation
        
        logger.info(f"Worker '{self.operation_name}' cancelled")
    
    def _handle_worker_error(self, error_message: str) -> None:
        """Handle expected worker errors."""
        with QMutexLocker(self._mutex):
            self._stats['status'] = 'error'
            self._stats['errors_count'] += 1
        
        self.update_status(f"{self.operation_name} error: {error_message}")
        self.error_occurred.emit(error_message, "Worker error")
        
        # Call custom error callback if set
        if self._error_callback:
            try:
                self._error_callback(error_message, "Worker error")
            except Exception as e:
                logger.warning(f"Error callback error: {e}")
        
        logger.error(f"Worker '{self.operation_name}' error: {error_message}")
    
    def _handle_unexpected_error(self, error_message: str, error_details: str) -> None:
        """Handle unexpected errors."""
        with QMutexLocker(self._mutex):
            self._stats['status'] = 'error'
            self._stats['errors_count'] += 1
        
        self.update_status(f"{self.operation_name} failed: {error_message}")
        self.error_occurred.emit(error_message, error_details)
        
        # Call custom error callback if set
        if self._error_callback:
            try:
                self._error_callback(error_message, error_details)
            except Exception as e:
                logger.warning(f"Error callback error: {e}")
        
        logger.error(f"Worker '{self.operation_name}' unexpected error: {error_message}")
        logger.debug(f"Error details: {error_details}")
    
    def _finalize_execution(self) -> None:
        """Finalize execution and emit performance stats."""
        try:
            # Update final timing and state
            with QMutexLocker(self._mutex):
                self._is_running = False
                self._end_time = time.time()
                self._stats['end_time'] = self._end_time
                
                if self._start_time:
                    self._stats['duration_seconds'] = self._end_time - self._start_time
            
            # Emit final performance statistics
            final_stats = self.get_current_stats()
            self.performance_stats.emit(final_stats)
            
            # Log performance summary
            logger.info(
                f"Worker '{self.operation_name}' finished: "
                f"Status={final_stats['status']}, "
                f"Duration={final_stats['duration_seconds']:.2f}s, "
                f"Processed={final_stats['items_processed']}, "
                f"Errors={final_stats['errors_count']}"
            )
            
        except Exception as e:
            logger.error(f"Error finalizing worker execution: {e}")
    
    def cleanup_resources(self) -> None:
        """
        Cleanup worker resources (to be overridden by subclasses if needed).
        Called automatically when thread finishes.
        """
        pass
    
    def __del__(self):
        """Ensure proper cleanup on destruction."""
        try:
            if self.isRunning():
                self.request_cancellation()
                if not self.wait(3000):  # Wait up to 3 seconds
                    self.terminate()  # Force termination if needed
                    logger.warning(f"Worker '{self.operation_name}' terminated forcefully")
            
            self.cleanup_resources()
            
        except Exception as e:
            logger.error(f"Error during worker cleanup: {e}") 