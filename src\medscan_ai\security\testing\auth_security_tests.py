﻿"""
Authentication Security Testing Module for MedScan AI
Specialized security tests for authentication, RBAC, and session management

REFACTORED: This module now delegates to specialized test modules for better organization.
The original monolithic implementation has been split into focused test modules:
- auth/authentication_tests.py - Authentication flow tests
- auth/authorization_tests.py - RBAC and authorization tests  
- auth/mfa_tests.py - Multi-factor authentication tests
- auth/session_tests.py - Session management tests

This provides backward compatibility while improving maintainability.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Import modular test components
from .auth import (
    run_authentication_security_tests,
    run_authorization_security_tests, 
    run_mfa_security_tests,
    run_session_security_tests,
    SecurityTestResult
)

logger = logging.getLogger(__name__)


class AuthenticationSecurityTests:
    """
    Comprehensive authentication security testing (REFACTORED)
    
    This class now orchestrates specialized test modules instead of implementing
    all tests directly. This improves separation of concerns and maintainability.
    """

    def __init__(self):
        """Initialize authentication security testing orchestrator"""
        self.test_results: List[SecurityTestResult] = []
        logger.info("Authentication security testing orchestrator initialized")

    def run_comprehensive_auth_tests(self) -> Dict[str, Any]:
        """
        Run all authentication security tests using modular components
        
        Returns:
            Dictionary containing all test results
        """
        logger.info("Starting comprehensive authentication security testing")
        
        # Clear previous results
        self.test_results = []
        
        # Delegate to specialized test modules
        auth_results = run_authentication_security_tests()
        authorization_results = run_authorization_security_tests()
        mfa_results = run_mfa_security_tests()
        session_results = run_session_security_tests()
        
        # Combine all test results
        all_test_results = []
        all_test_results.extend(auth_results)
        all_test_results.extend(authorization_results)
        all_test_results.extend(mfa_results)
        all_test_results.extend(session_results)
        
        # Convert to SecurityTestResult objects if needed
        for result in all_test_results:
            if isinstance(result, SecurityTestResult):
                self.test_results.append(result)
            elif isinstance(result, dict):
                # Convert dict to SecurityTestResult
                test_result = SecurityTestResult(
                    test_name=result.get('test_name', 'Unknown Test'),
                    passed=result.get('passed', False),
                    details=result.get('details', ''),
                    severity=result.get('severity', 'medium'),
                    recommendations=result.get('recommendations', [])
                )
                self.test_results.append(test_result)
        
        return self._generate_comprehensive_report()
    
    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive authentication security report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.passed)
        
        # Categorize by severity
        critical_issues = [r for r in self.test_results if not r.passed and r.severity == "critical"]
        high_issues = [r for r in self.test_results if not r.passed and r.severity == "high"]
        medium_issues = [r for r in self.test_results if not r.passed and r.severity == "medium"]
        low_issues = [r for r in self.test_results if not r.passed and r.severity == "low"]
        
        # Calculate security score
        security_score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # Determine readiness level
        if len(critical_issues) == 0 and len(high_issues) == 0:
            readiness = "Production Ready"
        elif len(critical_issues) == 0:
            readiness = "Staging Ready"
        else:
            readiness = "Development Only"
        
        report = {
            "test_category": "Comprehensive Authentication Security Testing",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "security_score": round(security_score, 2),
                "readiness_level": readiness
            },
            "issues_by_severity": {
                "critical": len(critical_issues),
                "high": len(high_issues), 
                "medium": len(medium_issues),
                "low": len(low_issues)
            },
            "test_modules": {
                "authentication": "auth.authentication_tests",
                "authorization": "auth.authorization_tests",
                "mfa": "auth.mfa_tests", 
                "session": "auth.session_tests"
            },
            "test_results": [result.to_dict() for result in self.test_results],
            "recommendations": self._get_priority_recommendations()
        }
        
        logger.info(f"Comprehensive authentication security testing completed: {passed_tests}/{total_tests} tests passed")
        return report
    
    def _get_priority_recommendations(self) -> List[str]:
        """Get prioritized security recommendations"""
        recommendations = []
        
        # Collect all recommendations
        all_recommendations = []
        for result in self.test_results:
            if not result.passed:
                all_recommendations.extend(result.recommendations)
        
        # Remove duplicates and prioritize
        unique_recommendations = list(set(all_recommendations))
        
        # Priority order based on security impact
        priority_keywords = [
            "critical", "authentication", "authorization", "injection", 
            "brute force", "session", "password", "rbac", "mfa"
        ]
        
        for keyword in priority_keywords:
            for rec in unique_recommendations:
                if keyword.lower() in rec.lower() and rec not in recommendations:
                    recommendations.append(rec)
        
        # Add remaining recommendations
        for rec in unique_recommendations:
            if rec not in recommendations:
                recommendations.append(rec)
        
        return recommendations[:10]  # Top 10 recommendations

    # ===================
    # Legacy Compatibility Methods - Delegate to modular components
    # ===================

    def _test_password_security(self):
        """Legacy method - now delegates to authentication module"""
        return run_authentication_security_tests()
    
    def _test_authentication_flow(self):
        """Legacy method - now delegates to authentication module"""
        return run_authentication_security_tests()
    
    def _test_rbac_enforcement(self):
        """Legacy method - now delegates to authorization module"""
        return run_authorization_security_tests()
    
    def _test_session_security(self):
        """Legacy method - now delegates to session module"""
        return run_session_security_tests()
    
    def _test_mfa_security(self):
        """Legacy method - now delegates to MFA module"""
        return run_mfa_security_tests()


def run_authentication_security_tests() -> Dict[str, Any]:
    """
    Main function to run authentication security tests
    
    Returns:
        Dictionary containing test results
    """
    auth_tester = AuthenticationSecurityTests()
    return auth_tester.run_comprehensive_auth_tests()


# Backward compatibility function
def run_comprehensive_auth_tests() -> Dict[str, Any]:
    """Backward compatibility function"""
    return run_authentication_security_tests()


if __name__ == "__main__":
    # Run tests if module is executed directly
    results = run_authentication_security_tests()
    print(json.dumps(results, indent=2))
