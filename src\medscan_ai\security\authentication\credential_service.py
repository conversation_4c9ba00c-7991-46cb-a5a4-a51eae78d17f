"""User Credential Service for MedScan AI Authentication.

Provides high-level user credential management with secure operations,
audit logging, and integration with the password manager and database.
"""

import json
import logging
import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import uuid4

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from ...database.models.audit_log import AuditLog
from ...database.models.user import User, UserSession
from .password_manager import PasswordManager, PasswordPolicy

logger = logging.getLogger(__name__)


class UserCredentialService:
    """Service for managing user credentials and authentication.

    Provides secure user operations with comprehensive audit logging,
    password management, and session handling for medical environments.
    """

    def __init__(self, db_session: Session):
        """Initialize credential service.

        Args:
            db_session: Database session for operations
        """
        self.db = db_session
        self.password_manager = PasswordManager()
        self.password_policy = PasswordPolicy()

        logger.info("UserCredentialService initialized")

    def create_user(
        self,
        username: str,
        email: str,
        password: str,
        role: str = "Technician",
        license_number: Optional[str] = None,
        department: Optional[str] = None,
        facility_id: Optional[str] = None,
        created_by: Optional[str] = None,
    ) -> User:
        """Create a new user with secure credential storage.

        Args:
            username: Unique username for the user
            email: User's email address
            password: Plain text password (will be hashed)
            role: User's role for RBAC
            license_number: Medical license number (optional)
            department: Medical department (optional)
            facility_id: Healthcare facility ID (optional)
            created_by: User who created this account

        Returns:
            Created User object

        Raises:
            ValueError: If input validation fails
            IntegrityError: If username/email already exists
        """
        try:
            # Validate inputs
            self._validate_user_input(username, email, role)

            # Hash password securely
            password_hash, salt = self.password_manager.hash_password(password)

            # Create user object
            user = User(
                username=username,
                email=email.lower(),  # Normalize email
                password_hash=password_hash,
                password_salt=salt,
                role=role,
                license_number=license_number,
                department=department,
                facility_id=facility_id,
                created_by=created_by,
                password_changed_at=datetime.utcnow(),
            )

            # Save to database
            self.db.add(user)
            self.db.flush()  # Get user.id without committing

            # Log user creation
            self._log_audit_event(
                user_id=user.id,
                action="USER_CREATED",
                details={
                    "username": username,
                    "email": email,
                    "role": role,
                    "created_by": created_by,
                },
                performed_by=created_by,
            )

            self.db.commit()
            logger.info(f"User created successfully: {username} ({user.id})")

            return user

        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"User creation failed - duplicate: {e}")
            raise ValueError("Username or email already exists")

        except Exception as e:
            self.db.rollback()
            logger.error(f"User creation failed: {e}")
            raise

    def authenticate_user(
        self,
        username: str,
        password: str,
        ip_address: str,
        user_agent: Optional[str] = None,
    ) -> Optional[User]:
        """Authenticate user credentials.

        Args:
            username: Username or email
            password: Plain text password
            ip_address: Client IP address
            user_agent: Client user agent string

        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            # Find user by username or email
            user = self._find_user_by_login(username)

            if not user:
                self._log_audit_event(
                    action="LOGIN_FAILED",
                    details={
                        "username": username,
                        "reason": "user_not_found",
                        "ip_address": ip_address,
                    },
                )
                logger.warning(f"Login failed - user not found: {username}")
                return None

            # Check if account can login
            if not user.can_login():
                reason = self._get_login_failure_reason(user)
                self._log_audit_event(
                    user_id=user.id,
                    action="LOGIN_FAILED",
                    details={
                        "username": username,
                        "reason": reason,
                        "ip_address": ip_address,
                    },
                )
                logger.warning(
                    f"Login failed - account issue: {username}, reason: {reason}"
                )
                return None

            # Verify password
            if not self.password_manager.verify_password(
                password, user.password_hash, user.password_salt
            ):
                # Increment failed attempts
                user.failed_login_attempts += 1

                # Check if account should be locked
                if self.password_policy.should_lock_account(user.failed_login_attempts):
                    lockout_duration = self.password_policy.get_lockout_duration()
                    user.locked_until = datetime.utcnow() + lockout_duration

                    self._log_audit_event(
                        user_id=user.id,
                        action="ACCOUNT_LOCKED",
                        details={
                            "username": username,
                            "failed_attempts": user.failed_login_attempts,
                            "locked_until": user.locked_until.isoformat(),
                            "ip_address": ip_address,
                        },
                    )
                    logger.warning(f"Account locked due to failed attempts: {username}")

                self._log_audit_event(
                    user_id=user.id,
                    action="LOGIN_FAILED",
                    details={
                        "username": username,
                        "reason": "invalid_password",
                        "failed_attempts": user.failed_login_attempts,
                        "ip_address": ip_address,
                    },
                )

                self.db.commit()
                logger.warning(f"Login failed - invalid password: {username}")
                return None

            # Successful authentication
            user.failed_login_attempts = 0
            user.last_login = datetime.utcnow()
            user.last_login_ip = ip_address
            user.locked_until = None  # Clear any lock

            self._log_audit_event(
                user_id=user.id,
                action="LOGIN_SUCCESS",
                details={
                    "username": username,
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                },
            )

            self.db.commit()
            logger.info(f"User authenticated successfully: {username}")

            return user

        except Exception as e:
            self.db.rollback()
            logger.error(f"Authentication error: {e}")
            return None

    def change_password(
        self,
        user_id: str,
        current_password: str,
        new_password: str,
        performed_by: Optional[str] = None,
    ) -> bool:
        """Change user password with validation.

        Args:
            user_id: User ID
            current_password: Current password for verification
            new_password: New password to set
            performed_by: User performing the change

        Returns:
            True if password changed successfully

        Raises:
            ValueError: If validation fails
        """
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError("User not found")

            # Verify current password (unless admin override)
            if performed_by != user_id:  # Admin changing password
                logger.info(
                    f"Admin password change for user {user_id} by {performed_by}"
                )
            else:
                # User changing own password - verify current
                if not self.password_manager.verify_password(
                    current_password, user.password_hash, user.password_salt
                ):
                    self._log_audit_event(
                        user_id=user_id,
                        action="PASSWORD_CHANGE_FAILED",
                        details={"reason": "invalid_current_password"},
                        performed_by=performed_by,
                    )
                    raise ValueError("Current password is incorrect")

            # Hash new password
            password_hash, salt = self.password_manager.hash_password(new_password)

            # Update user
            user.password_hash = password_hash
            user.password_salt = salt
            user.password_changed_at = datetime.utcnow()
            user.updated_by = performed_by

            # Set password expiration if policy requires
            if self.password_policy.max_age_days > 0:
                user.password_expires_at = datetime.utcnow() + timedelta(
                    days=self.password_policy.max_age_days
                )

            self._log_audit_event(
                user_id=user_id,
                action="PASSWORD_CHANGED",
                details={"changed_by": performed_by or user_id},
                performed_by=performed_by,
            )

            self.db.commit()
            logger.info(f"Password changed successfully for user {user_id}")

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Password change failed: {e}")
            raise

    def update_user_role(self, user_id: str, new_role: str, performed_by: str) -> bool:
        """Update user role with audit logging.

        Args:
            user_id: User ID to update
            new_role: New role to assign
            performed_by: User performing the change

        Returns:
            True if role updated successfully
        """
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError("User not found")

            old_role = user.role
            user.role = new_role
            user.updated_by = performed_by

            self._log_audit_event(
                user_id=user_id,
                action="ROLE_CHANGED",
                details={
                    "old_role": old_role,
                    "new_role": new_role,
                    "changed_by": performed_by,
                },
                performed_by=performed_by,
            )

            self.db.commit()
            logger.info(f"Role updated for user {user_id}: {old_role} -> {new_role}")

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Role update failed: {e}")
            raise

    def deactivate_user(
        self, user_id: str, performed_by: str, reason: Optional[str] = None
    ) -> bool:
        """Deactivate user account.

        Args:
            user_id: User ID to deactivate
            performed_by: User performing the action
            reason: Reason for deactivation

        Returns:
            True if user deactivated successfully
        """
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError("User not found")

            user.is_active = False
            user.updated_by = performed_by

            # Revoke all active sessions
            active_sessions = (
                self.db.query(UserSession)
                .filter(UserSession.user_id == user_id, UserSession.is_active == True)
                .all()
            )

            for session in active_sessions:
                session.is_active = False
                session.revoked_at = datetime.utcnow()
                session.revoked_reason = "user_deactivated"

            self._log_audit_event(
                user_id=user_id,
                action="USER_DEACTIVATED",
                details={
                    "reason": reason,
                    "deactivated_by": performed_by,
                    "revoked_sessions": len(active_sessions),
                },
                performed_by=performed_by,
            )

            self.db.commit()
            logger.info(f"User deactivated: {user_id}")

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"User deactivation failed: {e}")
            raise

    def _find_user_by_login(self, login: str) -> Optional[User]:
        """Find user by username or email.

        Args:
            login: Username or email

        Returns:
            User object if found, None otherwise
        """
        return (
            self.db.query(User)
            .filter((User.username == login) | (User.email == login.lower()))
            .first()
        )

    def _validate_user_input(self, username: str, email: str, role: str) -> None:
        """Validate user input data.

        Args:
            username: Username to validate
            email: Email to validate
            role: Role to validate

        Raises:
            ValueError: If validation fails
        """
        if not username or len(username) < 3:
            raise ValueError("Username must be at least 3 characters")

        if not email or "@" not in email:
            raise ValueError("Valid email address is required")

        valid_roles = {"Admin", "Radiologist", "GP", "Technician", "Viewer", "Auditor"}
        if role not in valid_roles:
            raise ValueError(f"Role must be one of: {', '.join(valid_roles)}")

    def _get_login_failure_reason(self, user: User) -> str:
        """Get reason for login failure.

        Args:
            user: User object

        Returns:
            Reason string
        """
        if not user.is_active:
            return "account_inactive"
        elif not user.is_verified:
            return "account_unverified"
        elif user.is_account_locked():
            return "account_locked"
        elif user.is_password_expired():
            return "password_expired"
        else:
            return "unknown"

    def _log_audit_event(
        self,
        action: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None,
        performed_by: Optional[str] = None,
    ) -> None:
        """Log audit event to database.

        Args:
            action: Action performed
            details: Event details
            user_id: User ID involved in action
            performed_by: User who performed the action
        """
        try:
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource_type="User",
                resource_id=user_id,
                details=json.dumps(details),
                ip_address=details.get("ip_address"),
                user_agent=details.get("user_agent"),
                performed_by=performed_by or user_id,
            )

            self.db.add(audit_log)
            # Note: Not committing here - let caller handle transaction

        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            # Don't fail the main operation due to audit logging issues
