# MedScan AI - GUI Development Guide

## Overview

This guide documents the modularized GUI architecture of MedScan AI, following the refactoring of the monolithic `gui/utils.py` file (originally 3,448 lines) into organized, reusable components. The new structure promotes separation of concerns, better maintainability, and easier testing.

## Architecture Overview

### Modular Structure

The GUI module is organized into four main categories:

```
src/medscan_ai/gui/
├── main.py                 # Main application window (1,251 lines)
├── core/                   # Core UI components and utilities
│   ├── utils.py           # Basic utility functions
│   ├── display_helper.py  # ImageDisplayHelper class
│   ├── overlay_manager.py # Layer management system
│   └── image_viewer.py    # Main interactive viewer (~1080 lines)
├── ai_display/            # AI-related UI components
│   ├── findings_visualizer.py  # AI findings visualization
│   ├── metadata_panel.py       # AI metadata display
│   └── findings_dialog.py      # AI findings dialog
├── annotations/           # Annotation tools and management
│   ├── types.py          # Data structures and enums
│   ├── manager.py        # Annotation coordination
│   └── tools/            # Individual annotation tools
│       ├── base.py       # Abstract base class
│       ├── rectangle.py  # Rectangle drawing tool
│       ├── polygon.py    # Polygon drawing tool
│       ├── freehand.py   # Freehand drawing
│       └── eraser.py     # Annotation eraser tool
└── helpers/              # Helper utilities
    └── imaging.py        # Image processing helpers
```

## Core Module (`gui.core`)

### Key Components

#### InteractiveImageViewer
Main medical image display widget with zoom, pan, and overlay support.

```python
from medscan_ai.gui.core import InteractiveImageViewer

viewer = InteractiveImageViewer()
viewer.load_dicom_image("path/to/image.dcm")
```

#### OverlayManager
Layer management system for AI findings and annotations.

```python
from medscan_ai.gui.core import OverlayManager, LayerType

overlay_manager = OverlayManager()
overlay_manager.add_layer("ai_findings", overlay, LayerType.AI_OVERLAY)
```

## AI Display Module (`gui.ai_display`)

### Key Components

#### AIFindingsVisualizer
Converts AI analysis results into visual overlays.

#### AIMetadataPanel
Displays AI metadata and confidence scores.

#### AIFindingDialog
Comprehensive dialog for detailed AI findings review.

## Annotations Module (`gui.annotations`)

### Key Components

#### AnnotationManager
Coordinates annotation tools and manages annotation state.

#### Annotation Tools
- Rectangle Tool: Rectangle drawing
- Polygon Tool: Multi-point polygon drawing
- Freehand Tool: Smooth freehand drawing
- Eraser Tool: Annotation removal

## Development Guidelines

### Creating New Components
1. Choose appropriate module based on functionality
2. Follow established naming conventions
3. Maintain separation of concerns
4. Include comprehensive tests

### Import Patterns
```python
# Recommended imports
from medscan_ai.gui.core import InteractiveImageViewer
from medscan_ai.gui.ai_display import AIFindingsVisualizer
from medscan_ai.gui.annotations import AnnotationManager
```

### Testing Structure
- Unit tests for individual components
- Integration tests for component interaction
- GUI tests for user interface behavior

## Migration from Legacy Code

### Backward Compatibility
The old `gui.utils` imports continue to work with deprecation warnings:

```python
# Old (deprecated but functional)
from medscan_ai.gui.utils import InteractiveImageViewer

# New (recommended)
from medscan_ai.gui.core import InteractiveImageViewer
```

## Future Roadmap

### Planned Improvements
1. Refactor `gui/main.py` into smaller components
2. Create dedicated dialogs module
3. Develop reusable medical UI widgets
4. Implement professional theming system

---

**Document Version**: 1.0  
**Created**: January 2025  
**Authors**: Development Team 