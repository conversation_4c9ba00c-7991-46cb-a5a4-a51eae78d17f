"""
Event handling for annotation interactions.
Separated from AnnotationManager for better modularity.
"""

from typing import Optional, Callable
from PySide6.QtCore import QObject, QPoint, Signal

from .types import AnnotationTool, ManualAnnotation  
from .tools import AnnotationToolBase, PolygonTool


class AnnotationEventHandler(QObject):
    """Handles mouse events for annotation drawing and interaction."""
    
    # Signals
    annotation_created = Signal(ManualAnnotation)  # Emitted when annotation is completed
    annotation_selected = Signal(str)  # Emitted when annotation is selected (annotation_id)
    context_menu_requested = Signal(QPoint, QPoint, ManualAnnotation)  # scene_pos, global_pos, annotation
    
    def __init__(self, annotation_manager):
        """Initialize event handler."""
        # Use None as parent since annotation_manager is not a QObject
        super().__init__(None)
        self.annotation_manager = annotation_manager
        self.active_tool: Optional[AnnotationToolBase] = None
        self.find_annotation_callback: Optional[Callable] = None
    
    def set_active_tool(self, tool: Optional[AnnotationToolBase]):
        """Set the currently active annotation tool."""
        self.active_tool = tool
    
    def set_find_annotation_callback(self, callback: Callable):
        """Set callback function for finding annotations at positions."""
        self.find_annotation_callback = callback
    
    def handle_mouse_press(self, scene_pos: QPoint) -> bool:
        """Handle mouse press event for annotation drawing.
        
        Args:
            scene_pos: Position in scene coordinates
            
        Returns:
            True if event was handled by annotation tool
        """
        if not self.active_tool or not self.active_tool.is_active:
            return False
            
        # Special handling for polygon tool (click-to-add-point)
        if isinstance(self.active_tool, PolygonTool) and self.active_tool.is_drawing:
            return self.active_tool.add_point(scene_pos)
            
        # Start drawing with scene reference
        scene = self.annotation_manager.scene if self.annotation_manager else None
        return self.active_tool.start_drawing(scene_pos, scene)
    
    def handle_mouse_move(self, scene_pos: QPoint) -> bool:
        """Handle mouse move event for annotation drawing.
        
        Args:
            scene_pos: Position in scene coordinates
            
        Returns:
            True if event was handled by annotation tool
        """
        if not self.active_tool or not self.active_tool.is_active:
            return False
            
        return self.active_tool.continue_drawing(scene_pos)
    
    def handle_mouse_release(self, scene_pos: QPoint) -> bool:
        """Handle mouse release event for annotation drawing.
        
        Args:
            scene_pos: Position in scene coordinates
            
        Returns:
            True if event was handled and annotation was created
        """
        if not self.active_tool or not self.active_tool.is_active:
            return False
            
        # Don't finish on release for polygon tool (uses double-click)
        if isinstance(self.active_tool, PolygonTool):
            return True
            
        annotation = self.active_tool.finish_drawing(scene_pos)
        if annotation:
            self.annotation_created.emit(annotation)
            return True
            
        return False
    
    def handle_double_click(self, scene_pos: QPoint) -> bool:
        """Handle double-click event for annotation drawing.
        
        Args:
            scene_pos: Position in scene coordinates
            
        Returns:
            True if event was handled and annotation was created
        """
        if not self.active_tool or not self.active_tool.is_active:
            return False
            
        # Double-click finishes polygon drawing
        if isinstance(self.active_tool, PolygonTool) and self.active_tool.can_finish():
            annotation = self.active_tool.finish_drawing(scene_pos)
            if annotation:
                self.annotation_created.emit(annotation)
                return True
                
        return False
    
    def handle_right_click(self, scene_pos: QPoint, global_pos: QPoint) -> bool:
        """Handle right-click for context menu.
        
        Args:
            scene_pos: Position in scene coordinates
            global_pos: Global position for menu display
            
        Returns:
            True if context menu was requested
        """
        if self.find_annotation_callback:
            annotation = self.find_annotation_callback(scene_pos)
            if annotation:
                self.context_menu_requested.emit(scene_pos, global_pos, annotation)
                return True
        
        return False
    
    def cancel_current_drawing(self):
        """Cancel the current drawing operation."""
        if self.active_tool and self.active_tool.is_drawing:
            self.active_tool.cancel_drawing() 