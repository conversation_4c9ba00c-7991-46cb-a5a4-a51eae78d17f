"""
Handler mixins for MedScan AI main window.

Provides specialized functionality through composition using mixins:
- File handling for DICOM loading and management
- Image processing and display operations
- View controls for zoom, pan, and navigation
- Windowing controls for medical image display
- Threading management for background operations
"""

from .file_handlers import FileHandlerMixin
from .image_handlers import ImageHandlerMixin
from .view_handlers import ViewHandlerMixin
from .windowing_handlers import WindowingHandlerMixin
from .threading_handler import ThreadingHandlerMixin

__all__ = [
    'FileHandlerMixin',
    'ImageHandlerMixin', 
    'ViewHandlerMixin',
    'WindowingHandlerMixin',
    'ThreadingHandlerMixin'
]

# Module version
__version__ = "0.1.0" 