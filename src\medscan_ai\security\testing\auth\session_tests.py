"""
Session Management Security Tests for MedScan AI
Tests focused on session security and JWT token management

Extracted from the original auth_security_tests.py file for better modularity.
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from . import (
    SecurityTestResult, MockDatabaseSession, logger,
    SESSION_MANAGER_AVAILABLE
)

if SESSION_MANAGER_AVAILABLE:
    from medscan_ai.security.authentication.session_manager import JWTSessionManager


class SessionSecurityTests:
    """
    Session management security tests
    """

    def __init__(self):
        """Initialize session security testing"""
        self.test_results: List[SecurityTestResult] = []
        self.available_services = []
        
        # Initialize session manager with error handling
        self.session_manager = None
        
        if SESSION_MANAGER_AVAILABLE:
            try:
                self.session_manager = JWTSessionManager(
                    secret_key="test_secret_key_for_security_testing",
                    algorithm="HS256"
                )
                self.available_services.append('session_manager')
                logger.info("✅ SessionManager initialized")
            except Exception as e:
                logger.warning(f"❌ SessionManager failed: {e}")

    def run_session_tests(self) -> List[SecurityTestResult]:
        """
        Run all session security tests
        
        Returns:
            List of test results
        """
        logger.info("Starting session security testing")
        
        # Clear previous results
        self.test_results = []
        
        # Run tests based on available services
        if 'session_manager' in self.available_services:
            self._test_session_security()
            self._test_jwt_security()
            self._test_session_expiration()
            self._test_session_hijacking_protection()
        else:
            self._test_session_service_availability()
        
        return self.test_results

    def _test_session_service_availability(self):
        """Test session service availability"""
        logger.info("Testing session service availability")
        
        self.test_results.append(
            SecurityTestResult(
                test_name="Session Manager Availability",
                passed=False,
                details="Session manager not available for testing",
                severity="high",
                recommendations=["Initialize session manager for secure session handling"]
            )
        )

    def _test_session_security(self):
        """Test session management security"""
        logger.info("Testing session security")
        
        if not self.session_manager:
            return
        
        # Test 1: Session creation
        try:
            test_user_id = f"test_user_{int(time.time())}"
            test_user_data = {
                "user_id": test_user_id,
                "username": "test_user",
                "role": "patient"
            }
            
            if hasattr(self.session_manager, 'create_session'):
                session_token = self.session_manager.create_session(test_user_data)
                
                if session_token and len(session_token) > 20:  # JWT tokens should be reasonably long
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Session Creation",
                            passed=True,
                            details="Session creation working correctly",
                            severity="high"
                        )
                    )
                    
                    # Test session validation
                    if hasattr(self.session_manager, 'validate_session'):
                        is_valid = self.session_manager.validate_session(session_token)
                        
                        if is_valid:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="Session Validation",
                                    passed=True,
                                    details="Session validation working correctly",
                                    severity="high"
                                )
                            )
                        else:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="Session Validation",
                                    passed=False,
                                    details="Valid session token was rejected",
                                    severity="high",
                                    recommendations=["Fix session validation logic"]
                                )
                            )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Session Creation",
                            passed=False,
                            details="Session creation returned invalid token",
                            severity="critical",
                            recommendations=["Fix session token generation"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Session Creation",
                        passed=False,
                        details="Session creation method not available",
                        severity="high",
                        recommendations=["Implement session creation functionality"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Session Creation",
                    passed=False,
                    details=f"Session creation test failed: {str(e)}",
                    severity="critical",
                    recommendations=["Fix session manager implementation"]
                )
            )
        
        # Test 2: Invalid session rejection
        try:
            invalid_tokens = [
                "invalid.token.here",
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
                "",
                None,
                "malformed_token",
                "Bearer invalid_token"
            ]
            
            invalid_rejected = 0
            
            for token in invalid_tokens:
                try:
                    if hasattr(self.session_manager, 'validate_session'):
                        is_valid = self.session_manager.validate_session(token)
                        
                        if not is_valid:
                            invalid_rejected += 1
                except Exception:
                    # Exceptions for invalid tokens are good
                    invalid_rejected += 1
            
            if invalid_rejected == len(invalid_tokens):
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Invalid Session Rejection",
                        passed=True,
                        details="All invalid sessions correctly rejected",
                        severity="high"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Invalid Session Rejection",
                        passed=False,
                        details=f"Some invalid sessions were accepted: {invalid_rejected}/{len(invalid_tokens)} rejected",
                        severity="critical",
                        recommendations=["Strengthen session validation"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Invalid Session Rejection",
                    passed=False,
                    details=f"Invalid session test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix session validation implementation"]
                )
            )

    def _test_jwt_security(self):
        """Test JWT token security"""
        logger.info("Testing JWT security")
        
        if not self.session_manager:
            return
        
        # Test JWT signature verification
        try:
            test_user_data = {
                "user_id": "test_jwt_user",
                "username": "jwt_test",
                "role": "doctor"
            }
            
            if hasattr(self.session_manager, 'create_session'):
                token = self.session_manager.create_session(test_user_data)
                
                if token:
                    # Try to tamper with the token
                    parts = token.split('.')
                    if len(parts) == 3:
                        # Tamper with the payload
                        try:
                            import base64
                            payload = json.loads(base64.b64decode(parts[1] + '=='))
                            payload['role'] = 'admin'  # Privilege escalation attempt
                            
                            tampered_payload = base64.b64encode(
                                json.dumps(payload).encode()
                            ).decode().rstrip('=')
                            
                            tampered_token = f"{parts[0]}.{tampered_payload}.{parts[2]}"
                            
                            # Test if tampered token is accepted
                            if hasattr(self.session_manager, 'validate_session'):
                                is_valid = self.session_manager.validate_session(tampered_token)
                                
                                if not is_valid:
                                    self.test_results.append(
                                        SecurityTestResult(
                                            test_name="JWT Signature Verification",
                                            passed=True,
                                            details="JWT signature verification correctly rejected tampered token",
                                            severity="critical"
                                        )
                                    )
                                else:
                                    self.test_results.append(
                                        SecurityTestResult(
                                            test_name="JWT Signature Verification",
                                            passed=False,
                                            details="JWT accepted tampered token (critical vulnerability)",
                                            severity="critical",
                                            recommendations=["Fix JWT signature verification immediately"]
                                        )
                                    )
                        except Exception as e:
                            # Errors during tampering or validation are generally good
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="JWT Signature Verification",
                                    passed=True,
                                    details="JWT tampering attempt properly handled with error",
                                    severity="high"
                                )
                            )
                    else:
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="JWT Signature Verification",
                                passed=False,
                                details="JWT token format is invalid",
                                severity="high",
                                recommendations=["Fix JWT token format"]
                            )
                        )
                        
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="JWT Signature Verification",
                    passed=False,
                    details=f"JWT security test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix JWT implementation"]
                )
            )

    def _test_session_expiration(self):
        """Test session expiration security"""
        logger.info("Testing session expiration")
        
        if not self.session_manager:
            return
        
        # Test session expiration
        try:
            test_user_data = {
                "user_id": "test_expiry_user",
                "username": "expiry_test",
                "role": "nurse"
            }
            
            if hasattr(self.session_manager, 'create_session_with_expiry'):
                # Create session with very short expiry (1 second)
                token = self.session_manager.create_session_with_expiry(
                    test_user_data, 
                    expires_in=1
                )
                
                if token:
                    # Immediately test - should be valid
                    if hasattr(self.session_manager, 'validate_session'):
                        is_valid_now = self.session_manager.validate_session(token)
                        
                        # Wait for expiration
                        time.sleep(2)
                        
                        # Test after expiration - should be invalid
                        is_valid_after = self.session_manager.validate_session(token)
                        
                        if is_valid_now and not is_valid_after:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="Session Expiration",
                                    passed=True,
                                    details="Session expiration working correctly",
                                    severity="high"
                                )
                            )
                        elif is_valid_now and is_valid_after:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="Session Expiration",
                                    passed=False,
                                    details="Session did not expire as expected",
                                    severity="high",
                                    recommendations=["Fix session expiration logic"]
                                )
                            )
                        else:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="Session Expiration",
                                    passed=False,
                                    details="Session was invalid immediately after creation",
                                    severity="high",
                                    recommendations=["Fix session creation with expiry"]
                                )
                            )
            elif hasattr(self.session_manager, 'create_session'):
                # Fallback test with standard session creation
                token = self.session_manager.create_session(test_user_data)
                
                if token and hasattr(self.session_manager, 'validate_session'):
                    is_valid = self.session_manager.validate_session(token)
                    
                    if is_valid:
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="Session Expiration",
                                passed=True,
                                details="Basic session validation working (expiry method not available)",
                                severity="medium",
                                recommendations=["Implement session expiration functionality"]
                            )
                        )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Session Expiration",
                        passed=False,
                        details="Session expiration methods not available",
                        severity="medium",
                        recommendations=["Implement session expiration functionality"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Session Expiration",
                    passed=False,
                    details=f"Session expiration test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix session expiration implementation"]
                )
            )

    def _test_session_hijacking_protection(self):
        """Test session hijacking protection"""
        logger.info("Testing session hijacking protection")
        
        if not self.session_manager:
            return
        
        # Test concurrent session limits
        try:
            test_user_id = f"test_concurrent_user_{int(time.time())}"
            test_user_data = {
                "user_id": test_user_id,
                "username": "concurrent_test",
                "role": "patient"
            }
            
            sessions = []
            
            # Try to create multiple sessions for the same user
            for i in range(5):
                try:
                    if hasattr(self.session_manager, 'create_session'):
                        token = self.session_manager.create_session(test_user_data)
                        if token:
                            sessions.append(token)
                except Exception:
                    break
            
            # Check if there's session limiting
            if len(sessions) <= 3:  # Reasonable limit
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Concurrent Session Limiting",
                        passed=True,
                        details=f"Concurrent sessions limited to {len(sessions)}",
                        severity="medium"
                    )
                )
            elif len(sessions) > 10:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Concurrent Session Limiting",
                        passed=False,
                        details=f"Too many concurrent sessions allowed: {len(sessions)}",
                        severity="medium",
                        recommendations=["Implement concurrent session limits"]
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Concurrent Session Limiting",
                        passed=True,
                        details=f"Reasonable concurrent session limit: {len(sessions)}",
                        severity="low"
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Concurrent Session Limiting",
                    passed=False,
                    details=f"Concurrent session test failed: {str(e)}",
                    severity="medium",
                    recommendations=["Implement session management"]
                )
            )
        
        # Test session invalidation
        try:
            test_user_data = {
                "user_id": "test_invalidation_user",
                "username": "invalidation_test",
                "role": "admin"
            }
            
            if hasattr(self.session_manager, 'create_session'):
                token = self.session_manager.create_session(test_user_data)
                
                if token:
                    # Test session invalidation
                    if hasattr(self.session_manager, 'invalidate_session'):
                        invalidation_result = self.session_manager.invalidate_session(token)
                        
                        if invalidation_result:
                            # Test that invalidated session is no longer valid
                            if hasattr(self.session_manager, 'validate_session'):
                                is_still_valid = self.session_manager.validate_session(token)
                                
                                if not is_still_valid:
                                    self.test_results.append(
                                        SecurityTestResult(
                                            test_name="Session Invalidation",
                                            passed=True,
                                            details="Session invalidation working correctly",
                                            severity="high"
                                        )
                                    )
                                else:
                                    self.test_results.append(
                                        SecurityTestResult(
                                            test_name="Session Invalidation",
                                            passed=False,
                                            details="Session still valid after invalidation",
                                            severity="critical",
                                            recommendations=["Fix session invalidation logic"]
                                        )
                                    )
                    else:
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="Session Invalidation",
                                passed=False,
                                details="Session invalidation method not available",
                                severity="medium",
                                recommendations=["Implement session invalidation functionality"]
                            )
                        )
                        
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Session Invalidation",
                    passed=False,
                    details=f"Session invalidation test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix session invalidation implementation"]
                )
            )


def run_session_security_tests() -> List[SecurityTestResult]:
    """
    Convenience function to run session security tests
    
    Returns:
        List of test results
    """
    session_tests = SessionSecurityTests()
    return session_tests.run_session_tests() 