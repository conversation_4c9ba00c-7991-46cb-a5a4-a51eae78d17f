"""
GUI utility functions and classes for the MedScan AI application.

DEPRECATED: This module has been modularized for better maintainability.

New import paths:
- Core components: from medscan_ai.gui.core import ...
- AI display: from medscan_ai.gui.ai_display import ...
- Annotations: from medscan_ai.gui.annotations import ...

This file now provides backward compatibility imports only.
"""

import warnings

# Issue deprecation warning
warnings.warn(
    "Importing from medscan_ai.gui.utils is deprecated. "
    "Use specific submodules: gui.core, gui.ai_display, gui.annotations",
    DeprecationWarning,
    stacklevel=2
)

# Backward compatibility imports from modular structure
from .core import (
    InteractiveImageViewer,
    OverlayManager,
    LayerType,
    OverlayLayer,
    ImageDisplayHelper,
    numpy_to_qimage,
    numpy_to_qpixmap,
    scale_pixmap_to_fit,
    create_error_pixmap,
    get_image_info_text,
    apply_window_level_to_display
)

from .ai_display import (
    AIFindingsVisualizer,
    AIMetadataPanel,
    DifferentialDiagnosis,
    AIFindingDialog
)

from .annotations import (
    AnnotationTool,
    ManualAnnotation,
    AnnotationToolBase,
    RectangleTool,
    PolygonTool,
    FreehandTool,
    EraserTool,
    AnnotationManager
)

# Preserve original __all__ for backward compatibility
__all__ = [
    # Core image utilities
    'numpy_to_qimage',
    'numpy_to_qpixmap',
    'scale_pixmap_to_fit',
    'create_error_pixmap',
    'get_image_info_text',
    'apply_window_level_to_display',
    
    # Core classes
    'ImageDisplayHelper',
    'InteractiveImageViewer',
    
    # Overlay system
    'LayerType',
    'OverlayLayer',
    'OverlayManager',
    
    # AI display components
    'AIFindingsVisualizer',
    'AIMetadataPanel',
    'DifferentialDiagnosis',
    'AIFindingDialog',
    
    # Annotation system
    'AnnotationTool',
    'ManualAnnotation',
    'AnnotationToolBase',
    'RectangleTool',
    'PolygonTool',
    'FreehandTool',
    'EraserTool',
    'AnnotationManager'
] 