"""
Rectangle annotation tool implementation.
"""

from typing import Optional
import uuid
from PySide6.QtCore import QPoint, QRectF, Qt
from PySide6.QtGui import QPen, QBrush
from PySide6.QtWidgets import QGraphicsRectItem, QGraphicsScene

from .base import AnnotationToolBase
from ..types import AnnotationTool, ManualAnnotation


class RectangleTool(AnnotationToolBase):
    """
    Tool for drawing rectangular annotations on medical images.
    Supports click-and-drag to create rectangles.
    """
    
    def __init__(self):
        """Initialize rectangle tool."""
        super().__init__(AnnotationTool.RECTANGLE)
        self.start_pos = None
        
    def _start_drawing_impl(self, scene_pos: QPoint, scene: QGraphicsScene) -> bool:
        """Start drawing a rectangle."""
        self.start_pos = scene_pos
        
        # Create preview rectangle
        self.preview_item = QGraphicsRectItem()
        pen = QPen(self.pen_color)
        pen.setWidth(self.pen_width)
        # Use solid line to match other annotation outlines
        self.preview_item.setPen(pen)
        
        brush = QBrush(self.brush_color)
        # Ensure preview brush is transparent to avoid masking image
        brush.setStyle(Qt.NoBrush)
        self.preview_item.setBrush(brush)
        
        # Set initial position (zero-sized rectangle)
        self.preview_item.setRect(QRectF(scene_pos, scene_pos))
        
        scene.addItem(self.preview_item)
        return True
        
    def _continue_drawing_impl(self, scene_pos: QPoint) -> bool:
        """Update rectangle size during drawing."""
        if not self.preview_item or not self.start_pos:
            return False
            
        # Calculate rectangle from start position to current position
        x = min(self.start_pos.x(), scene_pos.x())
        y = min(self.start_pos.y(), scene_pos.y())
        width = abs(scene_pos.x() - self.start_pos.x())
        height = abs(scene_pos.y() - self.start_pos.y())
        
        rect = QRectF(x, y, width, height)
        self.preview_item.setRect(rect)
        
        return True
        
    def _finish_drawing_impl(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """Finish drawing and create rectangle annotation."""
        if not self.preview_item or not self.start_pos:
            return None
            
        # Get final rectangle
        rect = self.preview_item.rect()
        
        # Don't create annotation for very small rectangles
        if rect.width() < 5 or rect.height() < 5:
            return None
            
        # Create annotation data
        geometry_data = {
            'x': rect.x(),
            'y': rect.y(),
            'width': rect.width(),
            'height': rect.height()
        }
        
        # Create annotation
        annotation = ManualAnnotation(
            annotation_id=str(uuid.uuid4()),
            tool_type=self.tool_type,
            geometry_data=geometry_data,
            metadata={
                'pen_color': self.pen_color.name(),
                'pen_width': self.pen_width,
                # Default: no brush fill so underlying image visible
                'brush_color': None
            }
        )
        
        return annotation 