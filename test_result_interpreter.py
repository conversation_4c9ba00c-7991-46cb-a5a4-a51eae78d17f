#!/usr/bin/env python3
"""
Test script for AI Result Interpretation System

This script tests the complete result interpretation pipeline including:
- Binary and multi-class classification interpretation
- Object detection output processing
- Non-Maximum Suppression (NMS)
- Medical anomaly type classification
- Clinical significance assessment
- Bounding box processing and IoU calculations
- Comprehensive medical reporting

Author: MedScan AI Team
Date: 2025-01-27
"""

import sys
import os
import numpy as np
import unittest
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Add src to path for imports
sys.path.insert(0, 'src')

try:
    from medscan_ai.ai.postprocessing import (
        ResultInterpreter, InterpretationResult, AnomalyDetection, BoundingBox,
        AnomalyType, ConfidenceLevel, SeverityLevel, ResultInterpreterError
    )
    print("✅ Successfully imported MedScan AI postprocessing components")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_mock_classification_output() -> np.ndarray:
    """Create mock binary classification model output."""
    # Simulate a confident anomaly detection (0.75 confidence)
    return np.array([0.75])


def create_mock_multiclass_output() -> np.ndarray:
    """Create mock multi-class classification output."""
    # Simulate probabilities for different anomaly types
    # [normal, pneumonia, fracture, nodule, mass]
    return np.array([0.1, 0.7, 0.05, 0.1, 0.05])


def create_mock_detection_output() -> Dict[str, np.ndarray]:
    """Create mock object detection model output with bounding boxes."""
    # Simulate detection of multiple anomalies
    return {
        'boxes': np.array([
            [100, 150, 200, 250],  # Pneumonia detection
            [300, 100, 350, 180],  # Fracture detection  
            [150, 300, 220, 380],  # Nodule detection
            [180, 200, 250, 270],  # Overlapping detection (should be suppressed)
        ], dtype=np.float32),
        'scores': np.array([0.85, 0.72, 0.68, 0.45], dtype=np.float32),
        'classes': np.array([1, 2, 3, 1], dtype=np.int32)  # pneumonia, fracture, nodule, pneumonia
    }


def test_bounding_box_operations():
    """Test BoundingBox class operations and IoU calculations."""
    print("\n🧪 Testing BoundingBox operations...")
    
    try:
        # Create test bounding boxes
        bbox1 = BoundingBox(0.1, 0.1, 0.5, 0.5)  # Normalized coordinates
        bbox2 = BoundingBox(0.3, 0.3, 0.7, 0.7)  # Overlapping box
        bbox3 = BoundingBox(0.8, 0.8, 0.9, 0.9)  # Non-overlapping box
        
        print(f"✅ BoundingBox creation successful")
        print(f"   Box1: width={bbox1.width:.2f}, height={bbox1.height:.2f}, area={bbox1.area:.3f}")
        print(f"   Box1 center: {bbox1.center}")
        
        # Test IoU calculations
        iou_overlap = bbox1.iou(bbox2)
        iou_separate = bbox1.iou(bbox3)
        
        print(f"✅ IoU calculations:")
        print(f"   Overlapping boxes IoU: {iou_overlap:.3f}")
        print(f"   Separate boxes IoU: {iou_separate:.3f}")
        
        # Test pixel coordinate conversion
        image_width, image_height = 512, 512
        pixel_bbox = bbox1.to_pixel_coordinates(image_width, image_height)
        print(f"✅ Pixel coordinate conversion:")
        print(f"   Normalized: ({bbox1.x1:.2f}, {bbox1.y1:.2f}) -> ({bbox1.x2:.2f}, {bbox1.y2:.2f})")
        print(f"   Pixels: ({pixel_bbox.x1:.0f}, {pixel_bbox.y1:.0f}) -> ({pixel_bbox.x2:.0f}, {pixel_bbox.y2:.0f})")
        
        # Verify IoU properties
        assert 0.0 <= iou_overlap <= 1.0, "IoU should be between 0 and 1"
        assert iou_separate == 0.0, "Non-overlapping boxes should have IoU of 0"
        assert iou_overlap > 0.0, "Overlapping boxes should have IoU > 0"
        
        return True
        
    except Exception as e:
        print(f"❌ BoundingBox test failed: {e}")
        return False


def test_anomaly_detection_classification():
    """Test AnomalyDetection dataclass and classification."""
    print("\n🧪 Testing AnomalyDetection classification...")
    
    try:
        # Test different confidence levels
        test_cases = [
            (0.95, ConfidenceLevel.VERY_HIGH, "Very high confidence case"),
            (0.75, ConfidenceLevel.HIGH, "High confidence case"),
            (0.55, ConfidenceLevel.MODERATE, "Moderate confidence case"),
            (0.35, ConfidenceLevel.LOW, "Low confidence case"),
            (0.15, ConfidenceLevel.VERY_LOW, "Very low confidence case")
        ]
        
        for confidence, expected_level, description in test_cases:
            detection = AnomalyDetection(
                anomaly_type=AnomalyType.PNEUMONIA,
                confidence_score=confidence,
                clinical_significance=f"Test case: {description}"
            )
            
            assert detection.confidence_level == expected_level, \
                f"Expected {expected_level}, got {detection.confidence_level}"
            
            print(f"✅ {description}: {confidence:.2f} -> {detection.confidence_level.value}")
        
        # Test with bounding box
        bbox = BoundingBox(0.2, 0.2, 0.6, 0.6)
        detection_with_bbox = AnomalyDetection(
            anomaly_type=AnomalyType.FRACTURE,
            confidence_score=0.82,
            bounding_box=bbox,
            severity=SeverityLevel.SEVERE,
            clinical_significance="High-confidence fracture detection"
        )
        
        # Test serialization
        detection_dict = detection_with_bbox.to_dict()
        assert 'bounding_box' in detection_dict, "Bounding box should be in serialized output"
        assert 'severity' in detection_dict, "Severity should be in serialized output"
        
        print(f"✅ AnomalyDetection with bounding box created successfully")
        print(f"   Type: {detection_with_bbox.anomaly_type.value}")
        print(f"   Confidence: {detection_with_bbox.confidence_score:.3f}")
        print(f"   Severity: {detection_with_bbox.severity.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ AnomalyDetection test failed: {e}")
        return False


def test_binary_classification_interpretation():
    """Test binary classification output interpretation."""
    print("\n🧪 Testing binary classification interpretation...")
    
    try:
        interpreter = ResultInterpreter(confidence_threshold=0.5)
        
        # Test positive case (anomaly detected)
        positive_output = create_mock_classification_output()
        result = interpreter.interpret_classification_output(
            raw_output=positive_output,
            image_metadata={'modality': 'CR', 'body_part': 'chest'}
        )
        
        print(f"✅ Binary classification interpretation completed:")
        print(f"   Overall anomaly detected: {result.overall_anomaly_detected}")
        print(f"   Overall confidence: {result.overall_confidence:.3f}")
        print(f"   Number of detections: {result.detection_count}")
        print(f"   Clinical summary: {result.clinical_summary}")
        
        assert result.overall_anomaly_detected == True, "Should detect anomaly with 0.75 confidence"
        assert result.detection_count == 1, "Should have one detection"
        assert len(result.recommendations) > 0, "Should have recommendations"
        
        # Test negative case (no anomaly)
        negative_output = np.array([0.3])  # Below threshold
        negative_result = interpreter.interpret_classification_output(
            raw_output=negative_output
        )
        
        print(f"✅ Negative case:")
        print(f"   Overall anomaly detected: {negative_result.overall_anomaly_detected}")
        print(f"   Number of detections: {negative_result.detection_count}")
        
        assert negative_result.overall_anomaly_detected == False, "Should not detect anomaly with 0.3 confidence"
        assert negative_result.detection_count == 0, "Should have no detections"
        
        return True
        
    except Exception as e:
        print(f"❌ Binary classification test failed: {e}")
        return False


def test_multiclass_classification_interpretation():
    """Test multi-class classification output interpretation."""
    print("\n🧪 Testing multi-class classification interpretation...")
    
    try:
        interpreter = ResultInterpreter(confidence_threshold=0.4)
        
        # Test multi-class output
        multiclass_output = create_mock_multiclass_output()
        class_names = ['normal', 'pneumonia', 'fracture', 'nodule', 'mass']
        
        result = interpreter.interpret_classification_output(
            raw_output=multiclass_output,
            class_names=class_names,
            image_metadata={'modality': 'CR', 'patient_age': '45', 'body_part': 'chest'}
        )
        
        print(f"✅ Multi-class classification interpretation completed:")
        print(f"   Overall anomaly detected: {result.overall_anomaly_detected}")
        print(f"   Overall confidence: {result.overall_confidence:.3f}")
        print(f"   Number of detections: {result.detection_count}")
        
        # Check individual detections
        for i, detection in enumerate(result.detections):
            print(f"   Detection {i+1}: {detection.anomaly_type.value} "
                  f"({detection.confidence_score:.3f}, {detection.confidence_level.value})")
            if detection.severity:
                print(f"     Severity: {detection.severity.value}")
        
        print(f"   Clinical summary: {result.clinical_summary}")
        print(f"   Recommendations: {len(result.recommendations)} item(s)")
        
        # Verify results
        assert result.overall_anomaly_detected == True, "Should detect anomaly"
        assert result.detection_count >= 1, "Should have at least one detection"
        assert any(d.anomaly_type == AnomalyType.PNEUMONIA for d in result.detections), \
            "Should detect pneumonia (highest confidence)"
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-class classification test failed: {e}")
        return False


def test_object_detection_interpretation():
    """Test object detection output interpretation with NMS."""
    print("\n🧪 Testing object detection interpretation...")
    
    try:
        interpreter = ResultInterpreter(
            confidence_threshold=0.4, 
            nms_threshold=0.5,
            max_detections=10
        )
        
        # Test detection output
        detection_output = create_mock_detection_output()
        image_shape = (512, 512)  # Height, Width
        class_names = ['background', 'pneumonia', 'fracture', 'nodule']
        
        result = interpreter.interpret_detection_output(
            raw_output=detection_output,
            image_shape=image_shape,
            class_names=class_names,
            image_metadata={'modality': 'CR', 'study_date': '20250127'}
        )
        
        print(f"✅ Object detection interpretation completed:")
        print(f"   Overall anomaly detected: {result.overall_anomaly_detected}")
        print(f"   Overall confidence: {result.overall_confidence:.3f}")
        print(f"   Number of detections: {result.detection_count}")
        print(f"   High confidence detections: {len(result.high_confidence_detections)}")
        print(f"   Critical findings: {len(result.critical_findings)}")
        
        # Check individual detections
        for i, detection in enumerate(result.detections):
            print(f"   Detection {i+1}: {detection.anomaly_type.value}")
            print(f"     Confidence: {detection.confidence_score:.3f} ({detection.confidence_level.value})")
            if detection.severity:
                print(f"     Severity: {detection.severity.value}")
            if detection.bounding_box:
                bbox = detection.bounding_box
                print(f"     Bounding box: ({bbox.x1:.3f}, {bbox.y1:.3f}) -> ({bbox.x2:.3f}, {bbox.y2:.3f})")
                print(f"     Box area: {bbox.area:.3f}")
        
        print(f"   Processing metadata: {result.processing_metadata['output_type']}")
        print(f"   Clinical summary: {result.clinical_summary[:100]}...")
        
        # Verify results
        assert result.overall_anomaly_detected == True, "Should detect anomalies"
        assert result.detection_count >= 1, "Should have detections after filtering"
        assert all(d.bounding_box is not None for d in result.detections), \
            "All detections should have bounding boxes"
        
        # Test NMS effectiveness (should filter overlapping detections)
        original_count = len(detection_output['scores'][detection_output['scores'] > 0.4])
        final_count = result.detection_count
        print(f"   NMS filtering: {original_count} -> {final_count} detections")
        
        return True
        
    except Exception as e:
        print(f"❌ Object detection test failed: {e}")
        return False


def test_clinical_assessment_features():
    """Test clinical assessment and medical interpretation features."""
    print("\n🧪 Testing clinical assessment features...")
    
    try:
        interpreter = ResultInterpreter()
        
        # Create test detections with different severity levels
        test_detections = [
            AnomalyDetection(
                anomaly_type=AnomalyType.FRACTURE,
                confidence_score=0.92,
                severity=SeverityLevel.CRITICAL,
                clinical_significance="High-confidence rib fracture requiring immediate attention"
            ),
            AnomalyDetection(
                anomaly_type=AnomalyType.PNEUMONIA,
                confidence_score=0.78,
                severity=SeverityLevel.SEVERE,
                clinical_significance="Pneumonia with consolidation"
            ),
            AnomalyDetection(
                anomaly_type=AnomalyType.NODULE,
                confidence_score=0.45,
                severity=SeverityLevel.MILD,
                clinical_significance="Small pulmonary nodule - follow-up recommended"
            )
        ]
        
        # Create interpretation result
        result = InterpretationResult(
            overall_anomaly_detected=True,
            overall_confidence=0.92,
            detections=test_detections,
            processing_metadata={'test': 'clinical_assessment'},
            clinical_summary="Multiple abnormalities detected requiring urgent attention",
            recommendations=[
                "Immediate clinical correlation recommended",
                "Consider orthopedic consultation for fracture",
                "Pulmonary evaluation for pneumonia"
            ]
        )
        
        print(f"✅ Clinical assessment features:")
        print(f"   Total detections: {result.detection_count}")
        print(f"   High confidence detections: {len(result.high_confidence_detections)}")
        print(f"   Critical findings: {len(result.critical_findings)}")
        
        # Test serialization
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict), "Result should serialize to dictionary"
        assert 'detections' in result_dict, "Serialized result should include detections"
        assert 'clinical_summary' in result_dict, "Serialized result should include clinical summary"
        
        print(f"✅ Serialization successful with {len(result_dict)} fields")
        
        # Test medical knowledge integration
        severity_assessment = interpreter._assess_severity(AnomalyType.FRACTURE, 0.85)
        print(f"✅ Severity assessment: FRACTURE @ 0.85 confidence -> {severity_assessment.value}")
        
        clinical_significance = interpreter._assess_clinical_significance(
            AnomalyType.PNEUMONIA, 0.72, {'modality': 'CR', 'patient_age': '65'}
        )
        print(f"✅ Clinical significance assessment generated: {len(clinical_significance)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Clinical assessment test failed: {e}")
        return False


def test_error_handling_and_edge_cases():
    """Test error handling and edge cases."""
    print("\n🧪 Testing error handling and edge cases...")
    
    try:
        interpreter = ResultInterpreter()
        
        # Test invalid confidence threshold
        try:
            interpreter.set_confidence_threshold(1.5)
            print("❌ Should have failed with invalid threshold")
            return False
        except ValueError:
            print("✅ Invalid confidence threshold correctly rejected")
        
        # Test invalid NMS threshold
        try:
            interpreter.set_nms_threshold(-0.1)
            print("❌ Should have failed with invalid NMS threshold")
            return False
        except ValueError:
            print("✅ Invalid NMS threshold correctly rejected")
        
        # Test empty detection output
        empty_output = {
            'boxes': np.array([]),
            'scores': np.array([]),
            'classes': np.array([])
        }
        
        empty_result = interpreter.interpret_detection_output(
            raw_output=empty_output,
            image_shape=(512, 512)
        )
        
        assert empty_result.detection_count == 0, "Empty output should have no detections"
        assert not empty_result.overall_anomaly_detected, "Empty output should not detect anomalies"
        print("✅ Empty detection output handled correctly")
        
        # Test malformed output
        try:
            malformed_output = np.array([1, 2, 3])  # Wrong shape
            interpreter.interpret_detection_output(
                raw_output=malformed_output,
                image_shape=(512, 512)
            )
            print("❌ Should have failed with malformed output")
            return False
        except ResultInterpreterError:
            print("✅ Malformed detection output correctly rejected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


def test_performance_and_nms():
    """Test NMS performance and effectiveness."""
    print("\n🧪 Testing NMS performance and effectiveness...")
    
    try:
        interpreter = ResultInterpreter(nms_threshold=0.3)
        
        # Create overlapping detections for NMS testing
        overlapping_boxes = np.array([
            [100, 100, 200, 200],  # Base box
            [110, 110, 210, 210],  # Slightly overlapping
            [120, 120, 220, 220],  # More overlapping
            [300, 300, 400, 400],  # Separate box
        ], dtype=np.float32)
        
        scores = np.array([0.9, 0.8, 0.7, 0.85], dtype=np.float32)
        
        # Apply NMS manually
        keep_indices = interpreter._apply_nms(overlapping_boxes, scores, 0.3)
        
        print(f"✅ NMS test:")
        print(f"   Original detections: {len(overlapping_boxes)}")
        print(f"   After NMS: {len(keep_indices)}")
        print(f"   Kept indices: {keep_indices}")
        
        # Should keep the highest scoring box from overlapping group + separate box
        assert len(keep_indices) <= 2, "NMS should reduce overlapping detections"
        assert 0 in keep_indices, "Highest scoring overlapping box should be kept"
        assert 3 in keep_indices, "Separate box should be kept"
        
        # Test with no overlaps
        separate_boxes = np.array([
            [0, 0, 50, 50],
            [100, 100, 150, 150],
            [200, 200, 250, 250],
        ], dtype=np.float32)
        
        separate_scores = np.array([0.6, 0.7, 0.8], dtype=np.float32)
        keep_separate = interpreter._apply_nms(separate_boxes, separate_scores, 0.5)
        
        assert len(keep_separate) == 3, "Non-overlapping boxes should all be kept"
        print("✅ Non-overlapping detection preservation verified")
        
        return True
        
    except Exception as e:
        print(f"❌ NMS performance test failed: {e}")
        return False


def run_comprehensive_tests():
    """Run all tests and provide summary."""
    print("🚀 Starting comprehensive AI Result Interpretation System tests...\n")
    
    tests = [
        ("BoundingBox Operations", test_bounding_box_operations),
        ("AnomalyDetection Classification", test_anomaly_detection_classification),
        ("Binary Classification", test_binary_classification_interpretation),
        ("Multi-class Classification", test_multiclass_classification_interpretation),
        ("Object Detection", test_object_detection_interpretation),
        ("Clinical Assessment", test_clinical_assessment_features),
        ("Error Handling", test_error_handling_and_edge_cases),
        ("NMS Performance", test_performance_and_nms)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! AI Result Interpretation System is ready for integration.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1) 