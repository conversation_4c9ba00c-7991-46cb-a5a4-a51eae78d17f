"""
DICOM Loading Worker for Background File Processing

Provides background DICOM file loading capabilities with progress tracking,
allowing the UI to remain responsive during large file operations.
"""

import os
import time
import logging
from pathlib import Path
from typing import List, Union, Dict, Any, Optional
import pydicom
from pydicom.dataset import Dataset

from .base_worker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WorkerError
from ...dicom import <PERSON><PERSON><PERSON><PERSON><PERSON>, DicomError
from ...dicom.io.lazy_reader import LazyDicomReader

logger = logging.getLogger(__name__)


class DicomLoadingResult:
    """Container for DICOM loading results."""
    
    def __init__(self, 
                 dataset: Optional[Dataset] = None,
                 file_path: Optional[str] = None,
                 metadata: Optional[Dict[str, Any]] = None,
                 error: Optional[str] = None,
                 loading_time: float = 0.0):
        self.dataset = dataset
        self.file_path = file_path
        self.metadata = metadata
        self.error = error
        self.loading_time = loading_time
        self.success = dataset is not None and error is None


class BatchDicomLoadingResult:
    """Container for batch DICOM loading results."""
    
    def __init__(self):
        self.individual_results: List[DicomLoadingResult] = []
        self.total_files = 0
        self.successful_loads = 0
        self.failed_loads = 0
        self.total_loading_time = 0.0
        self.errors: List[str] = []
    
    def add_result(self, result: DicomLoadingResult) -> None:
        """Add an individual result to the batch."""
        self.individual_results.append(result)
        self.total_files += 1
        self.total_loading_time += result.loading_time
        
        if result.success:
            self.successful_loads += 1
        else:
            self.failed_loads += 1
            if result.error:
                self.errors.append(f"{result.file_path}: {result.error}")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_files == 0:
            return 0.0
        return (self.successful_loads / self.total_files) * 100.0


class DicomLoadingWorker(MedScanWorker):
    """
    Background worker for DICOM file loading operations.
    
    Supports both single file and batch loading with:
    - Progress tracking and UI updates
    - Lazy loading for memory efficiency
    - Error handling for corrupted files
    - Metadata extraction
    - Cancellation support
    """
    
    def __init__(self, 
                 file_paths: Union[str, Path, List[Union[str, Path]]],
                 use_lazy_loading: bool = True,
                 extract_metadata: bool = True,
                 validate_on_load: bool = True,
                 parent=None):
        """
        Initialize DICOM loading worker.
        
        Args:
            file_paths: Single file path or list of file paths to load
            use_lazy_loading: Whether to use lazy loading for large files
            extract_metadata: Whether to extract DICOM metadata
            validate_on_load: Whether to validate DICOM files during loading
            parent: Parent QObject
        """
        # Normalize file paths to list
        if isinstance(file_paths, (str, Path)):
            self.file_paths = [str(file_paths)]
        else:
            self.file_paths = [str(path) for path in file_paths]
        
        # Configuration
        self.use_lazy_loading = use_lazy_loading
        self.extract_metadata = extract_metadata
        self.validate_on_load = validate_on_load
        
        # Initialize base worker
        operation_name = f"DICOM Loading ({len(self.file_paths)} files)"
        super().__init__(operation_name, parent)
        
        # Initialize DICOM readers
        self.dicom_reader = DicomReader(validate_on_load=validate_on_load)
        if use_lazy_loading:
            self.lazy_reader = LazyDicomReader()
        
        # Result container
        if len(self.file_paths) == 1:
            self.result = None
        else:
            self.result = BatchDicomLoadingResult()
    
    def do_work(self) -> Union[DicomLoadingResult, BatchDicomLoadingResult]:
        """
        Perform DICOM loading work.
        
        Returns:
            DicomLoadingResult for single file or BatchDicomLoadingResult for multiple files
        """
        if len(self.file_paths) == 1:
            return self._load_single_file(self.file_paths[0])
        else:
            return self._load_multiple_files()
    
    def _load_single_file(self, file_path: str) -> DicomLoadingResult:
        """Load a single DICOM file with progress tracking."""
        self.update_progress(0, f"Loading {os.path.basename(file_path)}...")
        
        start_time = time.time()
        
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                raise WorkerError(f"File not found: {file_path}")
            
            # Check file size for progress estimation
            file_size = os.path.getsize(file_path)
            self.update_progress(10, f"Reading file ({file_size / 1024 / 1024:.1f} MB)...")
            
            # Load DICOM dataset
            if self.use_lazy_loading and file_size > 10 * 1024 * 1024:  # Use lazy loading for files > 10MB
                self.update_progress(30, "Using lazy loading for large file...")
                dataset = self.lazy_reader.read_dicom_lazy(file_path)
            else:
                self.update_progress(30, "Loading DICOM dataset...")
                dataset = self.dicom_reader.read_dicom(file_path)
            
            # Check for cancellation
            if self.is_cancellation_requested():
                raise WorkerError("Loading cancelled by user")
            
            self.update_progress(70, "Extracting metadata...")
            
            # Extract metadata if requested
            metadata = None
            if self.extract_metadata:
                metadata = self._extract_metadata(dataset, file_path)
            
            self.update_progress(90, "Finalizing...")
            
            loading_time = time.time() - start_time
            
            # Increment processed count
            self.increment_processed_count(1)
            
            self.update_progress(100, f"Loaded successfully in {loading_time:.2f}s")
            
            return DicomLoadingResult(
                dataset=dataset,
                file_path=file_path,
                metadata=metadata,
                loading_time=loading_time
            )
            
        except (DicomError, pydicom.errors.InvalidDicomError) as e:
            # Handle DICOM-specific errors
            loading_time = time.time() - start_time
            error_msg = f"DICOM error: {str(e)}"
            
            self.increment_error_count(1)
            
            return DicomLoadingResult(
                file_path=file_path,
                error=error_msg,
                loading_time=loading_time
            )
            
        except Exception as e:
            # Handle other errors
            loading_time = time.time() - start_time
            error_msg = f"Loading error: {str(e)}"
            
            self.increment_error_count(1)
            
            return DicomLoadingResult(
                file_path=file_path,
                error=error_msg,
                loading_time=loading_time
            )
    
    def _load_multiple_files(self) -> BatchDicomLoadingResult:
        """Load multiple DICOM files with batch progress tracking."""
        batch_result = BatchDicomLoadingResult()
        total_files = len(self.file_paths)
        
        self.update_progress(0, f"Starting batch load of {total_files} files...")
        
        for i, file_path in enumerate(self.file_paths):
            # Check for cancellation
            if self.is_cancellation_requested():
                raise WorkerError("Batch loading cancelled by user")
            
            # Update progress
            progress = int((i / total_files) * 100)
            self.update_progress(progress, f"Loading file {i+1}/{total_files}: {os.path.basename(file_path)}")
            
            # Load individual file
            try:
                # Use simplified loading for batch operations
                result = self._load_single_file_batch(file_path)
                batch_result.add_result(result)
                
            except Exception as e:
                # Handle errors gracefully in batch mode
                error_result = DicomLoadingResult(
                    file_path=file_path,
                    error=f"Batch loading error: {str(e)}",
                    loading_time=0.0
                )
                batch_result.add_result(error_result)
                self.increment_error_count(1)
        
        # Final progress update
        self.update_progress(100, f"Batch loading completed: {batch_result.successful_loads}/{total_files} successful")
        
        return batch_result
    
    def _load_single_file_batch(self, file_path: str) -> DicomLoadingResult:
        """Load a single file optimized for batch operations (simplified)."""
        start_time = time.time()
        
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Load DICOM dataset (simplified for batch)
            file_size = os.path.getsize(file_path)
            
            if self.use_lazy_loading and file_size > 50 * 1024 * 1024:  # Use lazy loading for files > 50MB in batch
                dataset = self.lazy_reader.read_dicom_lazy(file_path)
            else:
                dataset = self.dicom_reader.read_dicom(file_path)
            
            # Extract basic metadata for batch operations
            metadata = None
            if self.extract_metadata:
                metadata = self._extract_basic_metadata(dataset, file_path)
            
            loading_time = time.time() - start_time
            self.increment_processed_count(1)
            
            return DicomLoadingResult(
                dataset=dataset,
                file_path=file_path,
                metadata=metadata,
                loading_time=loading_time
            )
            
        except Exception as e:
            loading_time = time.time() - start_time
            return DicomLoadingResult(
                file_path=file_path,
                error=str(e),
                loading_time=loading_time
            )
    
    def _extract_metadata(self, dataset: Dataset, file_path: str) -> Dict[str, Any]:
        """Extract comprehensive metadata from DICOM dataset."""
        try:
            metadata = {
                'file_path': file_path,
                'file_size_bytes': os.path.getsize(file_path),
                'file_size_mb': os.path.getsize(file_path) / 1024 / 1024,
            }
            
            # Patient information
            metadata.update({
                'patient_name': getattr(dataset, 'PatientName', 'Unknown'),
                'patient_id': getattr(dataset, 'PatientID', 'Unknown'),
                'patient_birth_date': getattr(dataset, 'PatientBirthDate', 'Unknown'),
                'patient_sex': getattr(dataset, 'PatientSex', 'Unknown'),
            })
            
            # Study information
            metadata.update({
                'study_instance_uid': getattr(dataset, 'StudyInstanceUID', 'Unknown'),
                'study_date': getattr(dataset, 'StudyDate', 'Unknown'),
                'study_time': getattr(dataset, 'StudyTime', 'Unknown'),
                'study_description': getattr(dataset, 'StudyDescription', 'Unknown'),
                'accession_number': getattr(dataset, 'AccessionNumber', 'Unknown'),
            })
            
            # Series information
            metadata.update({
                'series_instance_uid': getattr(dataset, 'SeriesInstanceUID', 'Unknown'),
                'series_number': getattr(dataset, 'SeriesNumber', 'Unknown'),
                'series_description': getattr(dataset, 'SeriesDescription', 'Unknown'),
                'modality': getattr(dataset, 'Modality', 'Unknown'),
            })
            
            # Image information
            if hasattr(dataset, 'pixel_array'):
                try:
                    pixel_array = dataset.pixel_array
                    metadata.update({
                        'image_shape': pixel_array.shape,
                        'image_dtype': str(pixel_array.dtype),
                        'bits_allocated': getattr(dataset, 'BitsAllocated', 'Unknown'),
                        'bits_stored': getattr(dataset, 'BitsStored', 'Unknown'),
                        'photometric_interpretation': getattr(dataset, 'PhotometricInterpretation', 'Unknown'),
                        'rows': getattr(dataset, 'Rows', 'Unknown'),
                        'columns': getattr(dataset, 'Columns', 'Unknown'),
                    })
                except Exception:
                    metadata['image_info'] = 'Unable to extract pixel data info'
            
            return metadata
            
        except Exception as e:
            return {
                'file_path': file_path,
                'metadata_error': f"Failed to extract metadata: {str(e)}"
            }
    
    def _extract_basic_metadata(self, dataset: Dataset, file_path: str) -> Dict[str, Any]:
        """Extract basic metadata for batch operations (lightweight)."""
        try:
            return {
                'file_path': file_path,
                'file_size_mb': os.path.getsize(file_path) / 1024 / 1024,
                'patient_name': getattr(dataset, 'PatientName', 'Unknown'),
                'modality': getattr(dataset, 'Modality', 'Unknown'),
                'study_date': getattr(dataset, 'StudyDate', 'Unknown'),
                'series_description': getattr(dataset, 'SeriesDescription', 'Unknown'),
            }
        except Exception as e:
            return {
                'file_path': file_path,
                'metadata_error': str(e)
            }
    
    def cleanup_resources(self) -> None:
        """Cleanup DICOM reader resources."""
        try:
            # Clear any cached data
            if hasattr(self, 'dicom_reader'):
                # Clear any internal caches if the reader has them
                pass
            
            if hasattr(self, 'lazy_reader'):
                # Clear lazy reader caches
                pass
                
        except Exception as e:
            logger.warning(f"Error cleaning up DICOM worker resources: {e}") 