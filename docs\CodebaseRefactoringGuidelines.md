# MedScan AI - Codebase Refactoring Guidelines

## Overview

This document establishes guidelines and best practices for refactoring the MedScan AI codebase, based on lessons learned from the successful GUI module refactoring. These guidelines ensure consistency, maintainability, and quality across all refactoring efforts.

## Refactoring Principles

### 1. Separation of Concerns
**Principle**: Each module should have a single, well-defined responsibility.

**Example from GUI Refactoring**:
- `gui/core/` - Core UI utilities and image viewer
- `gui/ai_display/` - AI-specific UI components  
- `gui/annotations/` - Annotation tools and management
- `gui/helpers/` - General helper functions

### 2. Module Boundary Definition
**Module Types**:
- **Core Modules**: Fundamental utilities used across the application
- **Feature Modules**: Specific functionality (AI, annotations, reports)
- **Service Modules**: Cross-cutting concerns (security, database)
- **Helper Modules**: Utilities for backward compatibility

### 3. Dependency Management
**Rules**:
- Minimize dependencies between modules
- Eliminate circular references
- Use dependency injection for loose coupling
- Prefer composition over inheritance

## Refactoring Process

### Phase 1: Analysis and Planning
1. **File Size Analysis**: Identify files >500 lines as candidates
2. **Responsibility Mapping**: List distinct responsibilities within large files
3. **Dependency Analysis**: Map current dependencies and identify cycles
4. **Module Design**: Plan new module structure and interfaces

### Phase 2: Implementation Strategy
1. **Create Module Structure**: Establish directory hierarchy
2. **Extract Components**: Move related functionality to new modules
3. **Update Imports**: Modify import statements throughout codebase
4. **Maintain Compatibility**: Provide backward compatibility layer
5. **Update Tests**: Modify test files to use new module structure

### Phase 3: Validation and Cleanup
1. **Functional Testing**: Verify all functionality works
2. **Performance Testing**: Ensure no performance degradation
3. **Code Review**: Peer review of refactored components
4. **Documentation Update**: Update all relevant documentation

## Naming Conventions

### Module Naming
- **Directories**: `snake_case` (e.g., `ai_display`, `access_control`)
- **Files**: `snake_case` (e.g., `findings_visualizer.py`, `rbac_manager.py`)
- **Classes**: `PascalCase` (e.g., `AIFindingsVisualizer`)
- **Functions**: `snake_case` (e.g., `create_findings_overlay`)

### Import Organization
```python
# Standard library imports
import os
import sys

# Third-party imports
import numpy as np
from PySide6.QtWidgets import QWidget

# Local imports (grouped by module)
from medscan_ai.gui.core import InteractiveImageViewer
from medscan_ai.gui.ai_display import AIFindingsVisualizer
```

## Backward Compatibility Strategy

### Deprecation Process
1. **Phase 1**: New modular structure implemented, old imports work with warnings
2. **Phase 2**: Documentation updated to show new import paths  
3. **Phase 3**: Deprecated imports removed in next major version

### Compatibility Layer Implementation
```python
# gui/utils.py (backward compatibility)
import warnings
from .core.image_viewer import InteractiveImageViewer
from .ai_display.findings_visualizer import AIFindingsVisualizer

warnings.warn(
    "Importing from gui.utils is deprecated. "
    "Use specific modules instead.",
    DeprecationWarning,
    stacklevel=2
)
```

## Review Process

### Refactoring Proposal Process
1. **Create Refactoring Proposal**: Document target files and proposed structure
2. **Technical Review**: Architecture team reviews and approves
3. **Implementation Planning**: Create detailed implementation plan

### Code Review Requirements
**Pre-Review Checklist**:
- [ ] All new modules have clear documentation
- [ ] Backward compatibility maintained
- [ ] Unit tests cover new functionality
- [ ] Integration tests pass
- [ ] Documentation updated

## File Size Guidelines

### Refactoring Triggers
- **>1000 lines**: High priority for refactoring
- **>500 lines**: Medium priority, evaluate for logical boundaries
- **>300 lines**: Low priority, monitor for growth

### Target Sizes After Refactoring
- **Individual modules**: <300 lines preferred, <500 lines maximum
- **Core components**: May exceed 500 lines if cohesive
- **Utility modules**: <200 lines preferred

## Success Criteria

A refactoring effort is successful when:
1. **Functionality Preserved**: All existing features work without modification
2. **Improved Maintainability**: Code is easier to understand and modify
3. **Better Testability**: Individual components can be tested in isolation
4. **Clear Boundaries**: Module responsibilities are well-defined
5. **Performance Maintained**: No significant performance degradation
6. **Documentation Updated**: All documentation reflects new structure

## Lessons Learned from GUI Refactoring

### What Worked Well
- Clear separation of concerns between modules
- Backward compatibility layer allowed gradual migration
- Comprehensive testing ensured functionality preservation
- Documentation updates helped team adoption

### Key Takeaways
- Start with clear module boundaries before writing code
- Maintain backward compatibility to avoid breaking existing code
- Update tests systematically as modules are created
- Document the refactoring process for future reference

---

**Document Version**: 1.0  
**Created**: January 2025  
**Authors**: Development Team  
**Review Schedule**: Quarterly  
**Next Review**: April 2025 