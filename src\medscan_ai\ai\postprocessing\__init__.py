"""
AI Postprocessing module for medical imaging AI model results.

Provides comprehensive interpretation and analysis of AI model outputs,
including anomaly detection, bounding box processing, and clinical assessment.
"""

from .result_interpreter import (
    ResultInterpreter,
    InterpretationResult,
    AnomalyDetection,
    BoundingBox,
    AnomalyType,
    ConfidenceLevel,
    SeverityLevel,
    ResultInterpreterError
)

__all__ = [
    'ResultInterpreter',
    'InterpretationResult',
    'AnomalyDetection',
    'BoundingBox',
    'AnomalyType',
    'ConfidenceLevel',
    'SeverityLevel',
    'ResultInterpreterError'
]
