"""
Context menu for annotation management and editing operations.
"""

from typing import Optional, Callable, Dict, Any
from PySide6.QtWidgets import QMenu, QMessageBox
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QIcon, QPixmap, QColor, QAction

from .types import AnnotationTool, ManualAnnotation
from .properties_dialog import AnnotationPropertiesDialog


class AnnotationContextMenu(QObject):
    """Context menu for annotation operations."""
    
    # Signals for menu actions
    edit_properties_requested = Signal(str)  # annotation_id
    duplicate_annotation_requested = Signal(str)  # annotation_id
    delete_annotation_requested = Signal(str)  # annotation_id
    select_annotation_requested = Signal(str)  # annotation_id
    bring_to_front_requested = Signal(str)  # annotation_id
    send_to_back_requested = Signal(str)  # annotation_id
    lock_annotation_requested = Signal(str, bool)  # annotation_id, locked
    
    def __init__(self, parent=None):
        """Initialize the context menu manager."""
        super().__init__(parent)
        self.parent_widget = parent
        
    def create_menu(
        self, 
        annotation: ManualAnnotation, 
        position, 
        available_actions: Optional[Dict[str, bool]] = None
    ) -> QMenu:
        """Create context menu for an annotation.
        
        Args:
            annotation: The annotation for which to create the menu
            position: Position where menu should appear
            available_actions: Dict of action names and whether they're available
            
        Returns:
            QMenu ready to be shown
        """
        menu = QMenu(self.parent_widget)
        menu.setTitle(f"{annotation.tool_type.value} Annotation")
        
        # Default available actions
        if available_actions is None:
            available_actions = {
                'edit_properties': True,
                'select': True,
                'duplicate': True,
                'delete': True,
                'bring_to_front': True,
                'send_to_back': True,
                'lock': True,
                'convert': True
            }
        
        # Edit Properties action
        if available_actions.get('edit_properties', True):
            edit_action = QAction("Edit Properties...", menu)
            edit_action.setIcon(self._create_icon("edit"))
            edit_action.triggered.connect(
                lambda: self._edit_properties(annotation)
            )
            menu.addAction(edit_action)
        
        menu.addSeparator()
        
        # Selection actions
        if available_actions.get('select', True):
            select_action = QAction("Select", menu)
            select_action.setIcon(self._create_icon("select"))
            select_action.triggered.connect(
                lambda: self.select_annotation_requested.emit(annotation.id)
            )
            menu.addAction(select_action)
        
        # Duplication action
        if available_actions.get('duplicate', True):
            duplicate_action = QAction("Duplicate", menu)
            duplicate_action.setIcon(self._create_icon("duplicate"))
            duplicate_action.triggered.connect(
                lambda: self.duplicate_annotation_requested.emit(annotation.id)
            )
            menu.addAction(duplicate_action)
        
        menu.addSeparator()
        
        # Layer order actions
        if available_actions.get('bring_to_front', True):
            bring_front_action = QAction("Bring to Front", menu)
            bring_front_action.setIcon(self._create_icon("front"))
            bring_front_action.triggered.connect(
                lambda: self.bring_to_front_requested.emit(annotation.id)
            )
            menu.addAction(bring_front_action)
        
        if available_actions.get('send_to_back', True):
            send_back_action = QAction("Send to Back", menu)
            send_back_action.setIcon(self._create_icon("back"))
            send_back_action.triggered.connect(
                lambda: self.send_to_back_requested.emit(annotation.id)
            )
            menu.addAction(send_back_action)
        
        menu.addSeparator()
        
        # Lock/Unlock action
        if available_actions.get('lock', True):
            is_locked = annotation.metadata.get('locked', False)
            lock_text = "Unlock" if is_locked else "Lock"
            lock_icon = "unlock" if is_locked else "lock"
            
            lock_action = QAction(f"{lock_text} Annotation", menu)
            lock_action.setIcon(self._create_icon(lock_icon))
            lock_action.triggered.connect(
                lambda: self.lock_annotation_requested.emit(annotation.id, not is_locked)
            )
            menu.addAction(lock_action)
        
        # Convert action (for some annotation types)
        if available_actions.get('convert', True) and self._can_convert(annotation):
            convert_menu = self._create_convert_submenu(annotation, menu)
            menu.addMenu(convert_menu)
        
        menu.addSeparator()
        
        # Delete action
        if available_actions.get('delete', True):
            delete_action = QAction("Delete", menu)
            delete_action.setIcon(self._create_icon("delete"))
            delete_action.triggered.connect(
                lambda: self._delete_annotation(annotation)
            )
            menu.addAction(delete_action)
        
        return menu
    
    def _create_icon(self, icon_type: str) -> QIcon:
        """Create a simple icon for menu actions."""
        # Create simple colored squares as icons
        pixmap = QPixmap(16, 16)
        
        color_map = {
            'edit': QColor(100, 150, 255),      # Blue
            'select': QColor(255, 200, 100),    # Orange
            'duplicate': QColor(150, 255, 150), # Green
            'front': QColor(255, 255, 100),     # Yellow
            'back': QColor(200, 200, 200),      # Gray
            'lock': QColor(255, 100, 100),      # Red
            'unlock': QColor(100, 255, 100),    # Light green
            'delete': QColor(255, 50, 50),      # Dark red
            'convert': QColor(200, 100, 255),   # Purple
        }
        
        color = color_map.get(icon_type, QColor(128, 128, 128))
        pixmap.fill(color)
        
        return QIcon(pixmap)
    
    def _edit_properties(self, annotation: ManualAnnotation):
        """Open properties dialog for annotation."""
        try:
            dialog = AnnotationPropertiesDialog(annotation, self.parent_widget)
            
            # Connect signals for live updates
            dialog.annotation_updated.connect(
                lambda ann_id, props: self._apply_live_updates(ann_id, props)
            )
            
            if dialog.exec() == QMessageBox.Accepted:
                # Properties were updated
                self.edit_properties_requested.emit(annotation.id)
                
        except Exception as e:
            QMessageBox.critical(
                self.parent_widget,
                "Error",
                f"Failed to open properties dialog: {str(e)}"
            )
    
    def _apply_live_updates(self, annotation_id: str, properties: Dict[str, Any]):
        """Apply live updates from properties dialog."""
        # This would be connected to update the visual representation
        # The annotation manager would handle the actual updates
        pass
    
    def _delete_annotation(self, annotation: ManualAnnotation):
        """Delete annotation with confirmation."""
        reply = QMessageBox.question(
            self.parent_widget,
            "Delete Annotation",
            f"Are you sure you want to delete this {annotation.tool_type.value} annotation?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.delete_annotation_requested.emit(annotation.id)
    
    def _can_convert(self, annotation: ManualAnnotation) -> bool:
        """Check if annotation can be converted to other types."""
        # For now, allow conversion between rectangle and polygon
        return annotation.tool_type in [AnnotationTool.RECTANGLE, AnnotationTool.POLYGON]
    
    def _create_convert_submenu(self, annotation: ManualAnnotation, parent_menu: QMenu) -> QMenu:
        """Create conversion submenu."""
        convert_menu = QMenu("Convert to...", parent_menu)
        convert_menu.setIcon(self._create_icon("convert"))
        
        current_type = annotation.tool_type
        
        # Add conversion options based on current type
        if current_type == AnnotationTool.RECTANGLE:
            polygon_action = QAction("Polygon", convert_menu)
            polygon_action.triggered.connect(
                lambda: self._convert_annotation(annotation.id, AnnotationTool.POLYGON)
            )
            convert_menu.addAction(polygon_action)
            
        elif current_type == AnnotationTool.POLYGON:
            if self._can_convert_polygon_to_rectangle(annotation):
                rect_action = QAction("Rectangle", convert_menu)
                rect_action.triggered.connect(
                    lambda: self._convert_annotation(annotation.id, AnnotationTool.RECTANGLE)
                )
                convert_menu.addAction(rect_action)
        
        return convert_menu
    
    def _can_convert_polygon_to_rectangle(self, annotation: ManualAnnotation) -> bool:
        """Check if polygon can be converted to rectangle."""
        # Simple check: polygon should have 4 points for rectangle conversion
        geom = annotation.geometry_data
        points = geom.get('points', [])
        return len(points) == 4
    
    def _convert_annotation(self, annotation_id: str, target_type: AnnotationTool):
        """Request annotation conversion."""
        # This would be handled by the annotation manager
        # For now, just emit a signal (could be extended)
        print(f"Convert annotation {annotation_id} to {target_type.value}")


class QuickActionMenu:
    """Quick action floating menu for selected annotations."""
    
    def __init__(self, parent=None):
        """Initialize quick action menu."""
        self.parent_widget = parent
        self.menu = None
    
    def show_quick_actions(self, annotation: ManualAnnotation, position):
        """Show quick action menu at position."""
        if self.menu:
            self.menu.close()
        
        self.menu = QMenu(self.parent_widget)
        self.menu.setStyleSheet("""
            QMenu {
                background-color: rgba(50, 50, 50, 200);
                border: 1px solid gray;
                border-radius: 5px;
                color: white;
            }
            QMenu::item {
                padding: 5px 20px;
            }
            QMenu::item:selected {
                background-color: rgba(100, 100, 100, 150);
            }
        """)
        
        # Quick actions
        edit_action = QAction("Edit", self.menu)
        delete_action = QAction("Delete", self.menu)
        duplicate_action = QAction("Duplicate", self.menu)
        
        self.menu.addAction(edit_action)
        self.menu.addAction(delete_action)
        self.menu.addAction(duplicate_action)
        
        # Show menu
        self.menu.popup(position)
    
    def hide_quick_actions(self):
        """Hide quick action menu."""
        if self.menu:
            self.menu.close()
            self.menu = None 