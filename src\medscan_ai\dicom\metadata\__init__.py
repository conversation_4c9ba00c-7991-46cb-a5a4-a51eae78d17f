"""
DICOM Metadata Module

This module provides comprehensive metadata extraction and handling capabilities 
for DICOM medical image files. Includes structured data classes and extraction utilities.
"""

from .metadata import (
    DicomMetadata,
    DicomMetadataExtractor,
    ImageInfo,
    PatientInfo,
    SeriesInfo,
    StudyInfo,
    TechnicalInfo,
)

__all__ = [
    # Main extractor class
    "DicomMetadataExtractor",
    # Metadata container
    "DicomMetadata",
    # Data structures
    "PatientInfo",
    "StudyInfo", 
    "SeriesInfo",
    "ImageInfo",
    "TechnicalInfo",
]

# Module version
__version__ = "0.1.0" 