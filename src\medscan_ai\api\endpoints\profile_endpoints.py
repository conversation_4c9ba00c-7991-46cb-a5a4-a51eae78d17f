"""User Profile API Endpoints for MedScan AI.

Provides REST API endpoints for user profile management:
- User profile retrieval
- Profile information access
"""

import logging
from typing import Any, Dict, Optional

from flask import Blueprint, g, jsonify, request
from werkzeug.exceptions import BadRequest, Forbidden, Unauthorized

from ...security.authentication.authorization import require_authentication

logger = logging.getLogger(__name__)

def create_profile_blueprint() -> Blueprint:
    """Create profile endpoints blueprint."""
    
    profile_bp = Blueprint("profile", __name__, url_prefix="/api/auth")

    @profile_bp.route("/profile", methods=["GET"])
    @require_authentication
    def get_user_profile():
        """
        Get current user profile information.
        
        Requires:
            Authorization header with Bearer token
            
        Returns:
            User profile information
        """
        try:
            user_payload = g.current_user
            
            # Get full user profile (mock for now)
            user_profile = {
                "user_id": user_payload.user_id,
                "email": user_payload.email, 
                "roles": user_payload.roles,
                "department": user_payload.department
            }
            
            if user_profile:
                response_data = {
                    "status": "success",
                    "message": "User profile retrieved successfully",
                    "data": user_profile
                }
                return jsonify(response_data), 200
            else:
                response_data = {"status": "error", "message": "User profile not found"}
                return jsonify(response_data), 404
                
        except Exception as e:
            logger.error(f"Get user profile endpoint error: {str(e)}")
            response_data = {
                "status": "error",
                "message": "Profile service temporarily unavailable",
            }
            return jsonify(response_data), 500

    return profile_bp
