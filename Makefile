# MedScan AI Makefile
# Automates dependency management and development workflows for medical imaging application

.PHONY: help install install-dev install-ai deps-check deps-audit deps-update deps-outdated
.PHONY: format lint test security clean build run-cli run-gui pre-commit

# Default target
help:
	@echo "MedScan AI - Dependency Management & Development Commands"
	@echo ""
	@echo "Installation:"
	@echo "  install      Install core dependencies only"
	@echo "  install-dev  Install with development dependencies"
	@echo "  install-ai   Install with AI/ML dependencies (requires Python < 3.13)"
	@echo ""
	@echo "Dependency Management:"
	@echo "  deps-check   Run comprehensive dependency checks"
	@echo "  deps-audit   Run security vulnerability audit"
	@echo "  deps-update  Update requirements lock file"
	@echo "  deps-outdated Check for outdated packages"
	@echo ""
	@echo "Code Quality:"
	@echo "  format       Format code with black and isort"
	@echo "  lint         Run linting with flake8 and mypy"
	@echo "  test         Run test suite"
	@echo "  security     Run security scans (bandit + pip-audit)"
	@echo "  pre-commit   Run pre-commit hooks on all files"
	@echo ""
	@echo "Application:"
	@echo "  run-cli      Run MedScan CLI interface"
	@echo "  run-gui      Run MedScan GUI interface"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean        Clean build artifacts and cache"
	@echo "  build        Build distribution packages"

# Installation targets
install:
	pip install -e .

install-dev:
	pip install -e ".[dev,test]"

install-ai:
	pip install -e ".[ai]"

# Dependency management
deps-check:
	python scripts/check_dependencies.py --all

deps-audit:
	python scripts/check_dependencies.py --audit

deps-update:
	python scripts/check_dependencies.py --update

deps-outdated:
	python scripts/check_dependencies.py --outdated

# Code quality and formatting
format:
	black src/ tests/ scripts/
	isort src/ tests/ scripts/

lint:
	flake8 src/ tests/ scripts/
	mypy src/

test:
	pytest tests/ -v --cov=src/medscan_ai --cov-report=html --cov-report=term

security:
	bandit -r src/ -f json -o security-report.json
	python scripts/check_dependencies.py --audit

pre-commit:
	pre-commit run --all-files

# Application running
run-cli:
	medscan --help

run-gui:
	medscan-gui

# Build and maintenance
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf security-report.json
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build:
	python -m build

# Development workflow shortcuts
dev-setup: install-dev
	pre-commit install
	echo "Development environment ready!"

medical-check:
	python scripts/check_dependencies.py --medical

# CI/CD targets
ci-install:
	pip install -e ".[dev,test]"
	pip install pip-audit

ci-test: format lint test security deps-audit

# Docker targets (if using containers)
docker-build:
	docker build -t medscan-ai:latest .

docker-run:
	docker run -it --rm medscan-ai:latest

# Documentation
docs:
	sphinx-build -b html docs/ docs/_build/html
