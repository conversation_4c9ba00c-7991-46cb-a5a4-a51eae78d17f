"""
Custom exceptions for DICOM file handling and processing.
"""


class DicomError(Exception):
    """Base exception for all DICOM-related errors."""

    pass


class DicomFileNotFoundError(DicomError):
    """Raised when a DICOM file cannot be found."""

    pass


class DicomFileReadError(DicomError):
    """Raised when a DICOM file cannot be read or parsed."""

    pass


class DicomFormatError(DicomError):
    """Raised when a file is not a valid DICOM format."""

    pass


class DicomMetadataError(DicomError):
    """Raised when required DICOM metadata is missing or invalid."""

    pass


class DicomPixelDataError(DicomError):
    """Raised when pixel data cannot be extracted or processed."""

    pass


class DicomValidationError(DicomError):
    """Raised when DICOM data validation fails."""

    pass


class DicomCorruptedFileError(DicomError):
    """Raised when a DICOM file appears to be corrupted."""

    pass


class DicomPixelProcessingError(DicomError):
    """Raised when pixel data processing fails."""

    pass


class DicomWindowingError(DicomError):
    """Raised when windowing/leveling operations fail."""

    pass


class DicomNormalizationError(DicomError):
    """Raised when pixel data normalization fails."""

    pass
