"""JWT Session Management for MedScan AI.

Provides secure session management using JSON Web Tokens (JWTs) with:
- JWT generation, signing, and validation
- Refresh token mechanisms
- Session timeout and revocation
- Medical-grade security and audit logging
"""

import hashlib
import json
import logging
import secrets
import uuid
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

import jwt

logger = logging.getLogger(__name__)


class SessionStatus(Enum):
    """Session status enumeration."""

    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    INVALID = "invalid"


class TokenType(Enum):
    """Token type enumeration."""

    ACCESS = "access"
    REFRESH = "refresh"


@dataclass
class TokenPayload:
    """JWT token payload structure."""

    user_id: str
    session_id: str
    email: str
    username: str
    roles: List[str]
    permissions: List[str]
    department: Optional[str]
    medical_license: Optional[str]
    hierarchy_level: int = 0
    token_type: TokenType = TokenType.ACCESS
    issued_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    audience: str = "medscan-ai"
    issuer: str = "medscan-ai-auth"


@dataclass
class SessionInfo:
    """Session information structure."""

    session_id: str
    user_id: str
    device_info: Optional[Dict]
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    last_activity: datetime
    expires_at: datetime
    status: SessionStatus
    refresh_token_hash: Optional[str]


class JWTSessionManager:
    """
    Comprehensive JWT session management for medical applications.

    Features:
    - Secure JWT generation with HS256/RS256 signing
    - Access and refresh token mechanisms
    - Session tracking and revocation
    - Medical-grade security compliance
    - Comprehensive audit logging
    """

    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expiry: timedelta = timedelta(minutes=30),
        refresh_token_expiry: timedelta = timedelta(days=7),
        max_sessions_per_user: int = 5,
    ):
        """
        Initialize JWT session manager.

        Args:
            secret_key: Secret key for JWT signing
            algorithm: JWT signing algorithm (HS256, RS256)
            access_token_expiry: Access token lifetime
            refresh_token_expiry: Refresh token lifetime
            max_sessions_per_user: Maximum concurrent sessions per user
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expiry = access_token_expiry
        self.refresh_token_expiry = refresh_token_expiry
        self.max_sessions_per_user = max_sessions_per_user

        # In-memory storage for active sessions and revoked tokens
        # In production, these should be stored in Redis or database
        self.active_sessions: Dict[str, SessionInfo] = {}
        self.revoked_tokens: Set[str] = set()
        self.user_sessions: Dict[str, Set[str]] = {}  # user_id -> session_ids

        # JWT configuration
        self.jwt_config = {
            "algorithm": algorithm,
            "issuer": "medscan-ai-auth",
            "audience": "medscan-ai",
            "leeway": timedelta(seconds=10),  # Clock skew tolerance
        }

        logger.info("JWT Session Manager initialized")

    def create_session(
        self, user_data: Dict, client_info: Optional[Dict] = None
    ) -> Tuple[str, str, SessionInfo]:
        """
        Create new session with access and refresh tokens.

        Args:
            user_data: User information dictionary
            client_info: Client device and network information

        Returns:
            Tuple of (access_token, refresh_token, session_info)
        """
        try:
            user_id = user_data["user_id"]
            session_id = str(uuid.uuid4())

            # Manage session limits per user
            self._enforce_session_limits(user_id)

            # Extract client information
            ip_address = client_info.get("ip_address") if client_info else None
            user_agent = client_info.get("user_agent") if client_info else None
            device_info = client_info.get("device_info") if client_info else None

            # Create session info
            now = datetime.utcnow()
            session_info = SessionInfo(
                session_id=session_id,
                user_id=user_id,
                device_info=device_info,
                ip_address=ip_address,
                user_agent=user_agent,
                created_at=now,
                last_activity=now,
                expires_at=now + self.refresh_token_expiry,
                status=SessionStatus.ACTIVE,
                refresh_token_hash=None,
            )

            # Generate tokens
            access_token = self._generate_access_token(user_data, session_id)
            refresh_token = self._generate_refresh_token(user_data, session_id)

            # Hash refresh token for storage
            refresh_token_hash = self._hash_token(refresh_token)
            session_info.refresh_token_hash = refresh_token_hash

            # Store session
            self.active_sessions[session_id] = session_info

            # Track user sessions
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = set()
            self.user_sessions[user_id].add(session_id)

            logger.info(f"Session created for user {user_id}: {session_id}")
            return access_token, refresh_token, session_info

        except Exception as e:
            logger.error(
                f"Error creating session for user {user_data.get('user_id')}: {str(e)}"
            )
            raise

    def validate_access_token(
        self, token: str
    ) -> Tuple[bool, Optional[TokenPayload], str]:
        """
        Validate access token and return payload.

        Args:
            token: JWT access token

        Returns:
            Tuple of (is_valid, token_payload, error_message)
        """
        try:
            # Check if token is revoked
            token_jti = self._get_token_jti(token)
            if token_jti in self.revoked_tokens:
                return False, None, "Token has been revoked"

            # Decode and validate JWT
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                issuer=self.jwt_config["issuer"],
                audience=self.jwt_config["audience"],
                leeway=self.jwt_config["leeway"],
            )

            # Validate token type
            if payload.get("type") != TokenType.ACCESS.value:
                return False, None, "Invalid token type"

            # Check session validity
            session_id = payload.get("session_id")
            if not self._is_session_valid(session_id):
                return False, None, "Session is invalid or expired"

            # Update session activity
            self._update_session_activity(session_id)

            # Create token payload object
            token_payload = TokenPayload(
                user_id=payload["user_id"],
                session_id=payload["session_id"],
                email=payload["email"],
                username=payload["username"],
                roles=payload["roles"],
                permissions=payload["permissions"],
                department=payload.get("department"),
                medical_license=payload.get("medical_license"),
                token_type=TokenType.ACCESS,
                issued_at=datetime.fromtimestamp(payload["iat"]),
                expires_at=datetime.fromtimestamp(payload["exp"]),
            )

            return True, token_payload, ""

        except jwt.ExpiredSignatureError:
            return False, None, "Token has expired"
        except jwt.InvalidTokenError as e:
            return False, None, f"Invalid token: {str(e)}"
        except Exception as e:
            logger.error(f"Error validating access token: {str(e)}")
            return False, None, "Token validation error"

    def refresh_access_token(
        self, refresh_token: str
    ) -> Tuple[bool, Optional[str], str]:
        """
        Generate new access token using refresh token.

        Args:
            refresh_token: JWT refresh token

        Returns:
            Tuple of (success, new_access_token, error_message)
        """
        try:
            # Validate refresh token
            is_valid, payload, error = self._validate_refresh_token(refresh_token)
            if not is_valid:
                return False, None, error

            session_id = payload["session_id"]
            session_info = self.active_sessions.get(session_id)

            if not session_info:
                return False, None, "Session not found"

            # Verify refresh token hash
            refresh_token_hash = self._hash_token(refresh_token)
            if session_info.refresh_token_hash != refresh_token_hash:
                return False, None, "Invalid refresh token"

            # Generate new access token
            user_data = {
                "user_id": payload["user_id"],
                "email": payload["email"],
                "username": payload["username"],
                "roles": payload["roles"],
                "permissions": payload["permissions"],
                "department": payload.get("department"),
                "medical_license": payload.get("medical_license"),
            }

            new_access_token = self._generate_access_token(user_data, session_id)

            # Update session activity
            self._update_session_activity(session_id)

            logger.info(f"Access token refreshed for session {session_id}")
            return True, new_access_token, ""

        except Exception as e:
            logger.error(f"Error refreshing access token: {str(e)}")
            return False, None, "Token refresh error"

    def revoke_session(self, session_id: str) -> bool:
        """
        Revoke specific session and all its tokens.

        Args:
            session_id: Session identifier

        Returns:
            True if session was revoked successfully
        """
        try:
            session_info = self.active_sessions.get(session_id)
            if not session_info:
                return False

            # Mark session as revoked
            session_info.status = SessionStatus.REVOKED

            # Remove from active sessions
            del self.active_sessions[session_id]

            # Remove from user sessions
            user_id = session_info.user_id
            if user_id in self.user_sessions:
                self.user_sessions[user_id].discard(session_id)
                if not self.user_sessions[user_id]:
                    del self.user_sessions[user_id]

            logger.info(f"Session revoked: {session_id}")
            return True

        except Exception as e:
            logger.error(f"Error revoking session {session_id}: {str(e)}")
            return False

    def revoke_user_sessions(self, user_id: str) -> int:
        """
        Revoke all sessions for a specific user.

        Args:
            user_id: User identifier

        Returns:
            Number of sessions revoked
        """
        try:
            sessions_to_revoke = self.user_sessions.get(user_id, set()).copy()
            revoked_count = 0

            for session_id in sessions_to_revoke:
                if self.revoke_session(session_id):
                    revoked_count += 1

            logger.info(f"Revoked {revoked_count} sessions for user {user_id}")
            return revoked_count

        except Exception as e:
            logger.error(f"Error revoking user sessions for {user_id}: {str(e)}")
            return 0

    def revoke_token(self, token: str) -> bool:
        """
        Revoke specific token by adding to blacklist.

        Args:
            token: JWT token to revoke

        Returns:
            True if token was revoked successfully
        """
        try:
            token_jti = self._get_token_jti(token)
            if token_jti:
                self.revoked_tokens.add(token_jti)
                logger.info(f"Token revoked: {token_jti}")
                return True
            return False

        except Exception as e:
            logger.error(f"Error revoking token: {str(e)}")
            return False

    def get_user_sessions(self, user_id: str) -> List[SessionInfo]:
        """
        Get all active sessions for a user.

        Args:
            user_id: User identifier

        Returns:
            List of active session information
        """
        try:
            user_session_ids = self.user_sessions.get(user_id, set())
            sessions = []

            for session_id in user_session_ids:
                session_info = self.active_sessions.get(session_id)
                if session_info and session_info.status == SessionStatus.ACTIVE:
                    sessions.append(session_info)

            return sessions

        except Exception as e:
            logger.error(f"Error getting user sessions for {user_id}: {str(e)}")
            return []

    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions and tokens.

        Returns:
            Number of sessions cleaned up
        """
        try:
            now = datetime.utcnow()
            expired_sessions = []

            # Find expired sessions
            for session_id, session_info in self.active_sessions.items():
                if session_info.expires_at <= now:
                    expired_sessions.append(session_id)

            # Remove expired sessions
            cleaned_count = 0
            for session_id in expired_sessions:
                if self.revoke_session(session_id):
                    cleaned_count += 1

            logger.info(f"Cleaned up {cleaned_count} expired sessions")
            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {str(e)}")
            return 0

    # Private helper methods

    def _generate_access_token(self, user_data: Dict, session_id: str) -> str:
        """Generate JWT access token."""
        now = datetime.utcnow()
        expires_at = now + self.access_token_expiry

        payload = {
            "type": TokenType.ACCESS.value,
            "user_id": user_data["user_id"],
            "session_id": session_id,
            "email": user_data["email"],
            "username": user_data["username"],
            "roles": user_data.get("roles", []),
            "permissions": user_data.get("permissions", []),
            "department": user_data.get("department"),
            "medical_license": user_data.get("medical_license_number"),
            "iat": int(now.timestamp()),
            "exp": int(expires_at.timestamp()),
            "iss": self.jwt_config["issuer"],
            "aud": self.jwt_config["audience"],
            "jti": str(uuid.uuid4()),  # JWT ID for revocation
        }

        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def _generate_refresh_token(self, user_data: Dict, session_id: str) -> str:
        """Generate JWT refresh token."""
        now = datetime.utcnow()
        expires_at = now + self.refresh_token_expiry

        payload = {
            "type": TokenType.REFRESH.value,
            "user_id": user_data["user_id"],
            "session_id": session_id,
            "email": user_data["email"],
            "username": user_data["username"],
            "roles": user_data.get("roles", []),
            "permissions": user_data.get("permissions", []),
            "department": user_data.get("department"),
            "medical_license": user_data.get("medical_license_number"),
            "iat": int(now.timestamp()),
            "exp": int(expires_at.timestamp()),
            "iss": self.jwt_config["issuer"],
            "aud": self.jwt_config["audience"],
            "jti": str(uuid.uuid4()),
        }

        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def _validate_refresh_token(self, token: str) -> Tuple[bool, Optional[Dict], str]:
        """Validate refresh token."""
        try:
            # Check if token is revoked
            token_jti = self._get_token_jti(token)
            if token_jti in self.revoked_tokens:
                return False, None, "Refresh token has been revoked"

            # Decode and validate JWT
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                issuer=self.jwt_config["issuer"],
                audience=self.jwt_config["audience"],
                leeway=self.jwt_config["leeway"],
            )

            # Validate token type
            if payload.get("type") != TokenType.REFRESH.value:
                return False, None, "Invalid token type"

            # Check session validity
            session_id = payload.get("session_id")
            if not self._is_session_valid(session_id):
                return False, None, "Session is invalid or expired"

            return True, payload, ""

        except jwt.ExpiredSignatureError:
            return False, None, "Refresh token has expired"
        except jwt.InvalidTokenError as e:
            return False, None, f"Invalid refresh token: {str(e)}"
        except Exception as e:
            logger.error(f"Error validating refresh token: {str(e)}")
            return False, None, "Refresh token validation error"

    def _is_session_valid(self, session_id: str) -> bool:
        """Check if session is valid and active."""
        session_info = self.active_sessions.get(session_id)
        if not session_info:
            return False

        if session_info.status != SessionStatus.ACTIVE:
            return False

        if session_info.expires_at <= datetime.utcnow():
            return False

        return True

    def _update_session_activity(self, session_id: str) -> None:
        """Update last activity timestamp for session."""
        session_info = self.active_sessions.get(session_id)
        if session_info:
            session_info.last_activity = datetime.utcnow()

    def _enforce_session_limits(self, user_id: str) -> None:
        """Enforce maximum sessions per user limit."""
        user_session_ids = self.user_sessions.get(user_id, set())

        if len(user_session_ids) >= self.max_sessions_per_user:
            # Remove oldest sessions
            sessions_to_remove = []
            for session_id in user_session_ids:
                session_info = self.active_sessions.get(session_id)
                if session_info:
                    sessions_to_remove.append((session_info.created_at, session_id))

            # Sort by creation time and remove oldest
            sessions_to_remove.sort()
            sessions_to_revoke = (
                len(sessions_to_remove) - self.max_sessions_per_user + 1
            )

            for _, session_id in sessions_to_remove[:sessions_to_revoke]:
                self.revoke_session(session_id)
                logger.info(f"Revoked old session {session_id} due to session limit")

    def _hash_token(self, token: str) -> str:
        """Create hash of token for secure storage."""
        return hashlib.sha256(token.encode()).hexdigest()

    def _get_token_jti(self, token: str) -> Optional[str]:
        """Extract JWT ID from token without full validation."""
        try:
            # Decode without verification to get JTI
            payload = jwt.decode(token, options={"verify_signature": False})
            return payload.get("jti")
        except:
            return None


class SessionMiddleware:
    """
    Middleware for automatic token validation and session management.

    This middleware can be integrated with web frameworks (FastAPI, Flask, etc.)
    to automatically validate JWT tokens and manage sessions.
    """

    def __init__(self, session_manager: JWTSessionManager):
        """
        Initialize session middleware.

        Args:
            session_manager: JWT session manager instance
        """
        self.session_manager = session_manager

    def validate_request(
        self, authorization_header: str
    ) -> Tuple[bool, Optional[TokenPayload], str]:
        """
        Validate authorization header and extract token payload.

        Args:
            authorization_header: Authorization header value

        Returns:
            Tuple of (is_valid, token_payload, error_message)
        """
        try:
            if not authorization_header:
                return False, None, "Authorization header missing"

            # Extract token from Bearer scheme
            parts = authorization_header.split()
            if len(parts) != 2 or parts[0].lower() != "bearer":
                return False, None, "Invalid authorization header format"

            token = parts[1]
            return self.session_manager.validate_access_token(token)

        except Exception as e:
            logger.error(f"Error validating request: {str(e)}")
            return False, None, "Authorization validation error"
