"""
Logging Configuration Module for MedScan AI
Professional medical-grade logging setup for healthcare applications
"""

import logging
import sys
from pathlib import Path
from typing import Optional


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    Get a configured logger for MedScan AI components.
    
    Args:
        name: Logger name, typically __name__ of the calling module
        
    Returns:
        Configured logger instance suitable for medical applications
    """
    if name is None:
        name = __name__
    
    logger = logging.getLogger(name)
    
    # Only configure if not already configured
    if not logger.handlers:
        # Set appropriate level for medical software
        logger.setLevel(logging.INFO)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Create formatter suitable for medical applications
        formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        # Add handler to logger
        logger.addHandler(console_handler)
        
        # Prevent propagation to avoid duplicate messages
        logger.propagate = False
    
    return logger


def configure_medical_logging() -> None:
    """
    Configure logging for medical-grade applications.
    
    Sets up appropriate log levels, formatters, and handlers
    for healthcare compliance and audit requirements.
    """
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Set specific levels for medical components
    logging.getLogger('medscan_ai.security').setLevel(logging.DEBUG)
    logging.getLogger('medscan_ai.dicom').setLevel(logging.INFO)
    logging.getLogger('medscan_ai.ai').setLevel(logging.INFO)
    logging.getLogger('medscan_ai.database').setLevel(logging.WARNING)


# Initialize medical logging configuration
configure_medical_logging()
