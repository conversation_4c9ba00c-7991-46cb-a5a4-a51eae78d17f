"""
InteractiveImageViewer for MedScan AI GUI.

This lightweight implementation provides basic functionality required by
GUI handlers and panels:
- set_image(QPixmap)
- zoom_in / zoom_out with mouse wheel support
- reset_view / fit_to_window / actual_size / center_image helpers

The class intentionally keeps dependencies minimal but can be extended
with advanced capabilities (annotations, overlays) later.
"""

from __future__ import annotations

from typing import Optional, Dict

from PySide6.QtCore import Qt, QRectF, QPointF
from PySide6.QtGui import (
    QPixmap, QWheelEvent, QPainter, QPen, QColor
)
from PySide6.QtWidgets import (
    QGraphicsPixmapItem, QGraphicsScene, QGraphicsView, QGraphicsRectItem
)

from ..core.overlay_manager import OverlayManager, LayerType
from ..annotations.manager import AnnotationManager
from ..annotations.types import AnnotationTool

__all__ = ["InteractiveImageViewer"]


class InteractiveImageViewer(QGraphicsView):
    """Simple interactive image viewer with zoom & pan support."""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Graphics scene setup
        self._scene = QGraphicsScene(self)
        self.setScene(self._scene)

        # Store current pixmap item
        self._pixmap_item: Optional[QGraphicsPixmapItem] = None

        # Zoom state
        self.zoom_factor: float = 1.0
        self._zoom_step: float = 1.25  # 25% per step
        self._max_zoom: float = 10.0
        self._min_zoom: float = 0.1

        # Interaction behaviours
        self.setDragMode(QGraphicsView.ScrollHandDrag)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)

        # Smooth transform for better quality
        self.setRenderHints(
            self.renderHints()
            | QPainter.Antialiasing
            | QPainter.SmoothPixmapTransform
        )

        # ⬇️ ----------- YENİ ALTYAPI -----------
        self._overlay_manager = OverlayManager(self._scene)
        self._annotation_manager = AnnotationManager(self)
        self._ai_metadata_panel = None
        self._current_ai_result = None
        # --------------------------------------

    # ------------------------------------------------------------------
    # Public API expected by mixins
    # ------------------------------------------------------------------
    def set_image(self, pixmap: QPixmap):
        """Display the given QPixmap in the viewer."""
        if pixmap.isNull():
            return

        # Clear previous item
        self._scene.clear()
        self._pixmap_item = self._scene.addPixmap(pixmap)
        self._scene.setSceneRect(QRectF(pixmap.rect()))

        # Reset zoom state and fit
        self.reset_view()

    def zoom_in(self):
        """Zoom in one step."""
        self._apply_zoom(self._zoom_step)

    def zoom_out(self):
        """Zoom out one step."""
        self._apply_zoom(1 / self._zoom_step)

    def reset_view(self):
        """Reset the view to fit the image inside the viewport."""
        self.zoom_factor = 1.0
        self.fit_to_window()

    def fit_to_window(self):
        """Fit the image to the current viewport."""
        if self._pixmap_item is None:
            return
        self.fitInView(self._pixmap_item, Qt.KeepAspectRatio)
        # Update zoom factor approximation
        view_rect = self.viewport().rect()
        pixmap_rect = self._pixmap_item.boundingRect()
        if pixmap_rect.width() > 0:
            self.zoom_factor = view_rect.width() / pixmap_rect.width()

    def actual_size(self):
        """Display the image at 100% (no scaling)."""
        self.resetTransform()
        self.zoom_factor = 1.0

    def center_image(self):
        """Center the image inside the viewport."""
        self.centerOn(self._pixmap_item)

    # ------------------------------------------------------------------
    # Event handlers for mouse wheel zoom
    # ------------------------------------------------------------------
    def wheelEvent(self, event: QWheelEvent):
        delta = event.angleDelta().y()
        if delta != 0:
            if delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()
            event.accept()
        else:
            super().wheelEvent(event)

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------
    def _apply_zoom(self, factor: float):
        new_zoom = self.zoom_factor * factor
        if new_zoom < self._min_zoom or new_zoom > self._max_zoom:
            return  # Prevent excessive zoom

        self.zoom_factor = new_zoom
        self.scale(factor, factor) 

    # ⬇️ --------- GERİYE UYUMLU YARDIMCILAR ----------
    def set_pixmap(self, pixmap: QPixmap):
        """Alias kept for backwards-compatibility (tests)."""
        self.set_image(pixmap)

    # -------- OVERLAY / LAYER API ---------
    def create_overlay_layer(self, layer_type: LayerType, name: str | None = None) -> str:
        return self._overlay_manager.create_layer(layer_type, name)

    def add_graphics_item_to_layer(self, layer_id: str, item):
        layer = self._overlay_manager.get_layer(layer_id)
        if layer:
            layer.add_item(item)

    def remove_graphics_item_from_layer(self, layer_id: str, item):
        layer = self._overlay_manager.get_layer(layer_id)
        if layer:
            layer.remove_item(item)

    def get_overlay_layer(self, layer_id: str):
        return self._overlay_manager.get_layer(layer_id)

    def set_overlay_layer_visibility(self, layer_id: str, visible: bool):
        self._overlay_manager.set_layer_visibility(layer_id, visible)

    def clear_overlay_layer(self, layer_id: str):
        self._overlay_manager.clear_layer(layer_id)

    def clear_all_overlay_layers(self):
        self._overlay_manager.clear_all_layers()

    # -------- AI FINDINGS & METADATA ---------
    def set_ai_metadata_panel(self, panel):
        """Attach metadata panel and connect highlight signal."""
        self._ai_metadata_panel = panel
        if hasattr(panel, "finding_highlight_requested"):
            panel.finding_highlight_requested.connect(self._highlight_finding)

    def _ensure_detection_ids(self, detections):
        class _EnumWrapper:
            def __init__(self, value: str):
                self.value = value
            def __str__(self):
                return self.value
            def __repr__(self):
                return self.value
        for idx, det in enumerate(detections, start=1):
            # Ensure id
            if not hasattr(det, "id"):
                setattr(det, "id", str(idx))
            # Ensure confidence_level enum-like attr
            if not hasattr(det, "confidence_level"):
                lvl = (
                    "very_high" if det.confidence_score >= 0.9 else
                    "high" if det.confidence_score >= 0.75 else
                    "moderate" if det.confidence_score >= 0.5 else
                    "low" if det.confidence_score >= 0.3 else "very_low"
                )
                setattr(det, "confidence_level", _EnumWrapper(lvl))
            # Ensure severity placeholder (optional in UI)
            if not hasattr(det, "severity"):
                setattr(det, "severity", None)

    def _ensure_result_attributes(self, result):
        # Provide fallback lists expected by metadata panel
        if not hasattr(result, "high_confidence_detections"):
            setattr(result, "high_confidence_detections", [d for d in result.detections if d.confidence_score >= 0.8])
        if not hasattr(result, "critical_findings"):
            setattr(result, "critical_findings", [])
        if not hasattr(result, "requires_immediate_attention"):
            setattr(result, "requires_immediate_attention", False)

    def display_ai_findings(self, result):
        """Draw simple rectangles for AI detections; return layer id."""
        self._current_ai_result = result
        self._ensure_detection_ids(result.detections)
        layer_id = self.create_overlay_layer(LayerType.AI_FINDINGS, "AI_Findings")

        if not self._pixmap_item:
            return layer_id

        img_w = self._pixmap_item.pixmap().width()
        img_h = self._pixmap_item.pixmap().height()

        for det in result.detections:
            bb = det.bounding_box
            rect_item = QGraphicsRectItem(
                bb.x1 * img_w,
                bb.y1 * img_h,
                (bb.x2 - bb.x1) * img_w,
                (bb.y2 - bb.y1) * img_h,
            )
            rect_item.setPen(QPen(QColor(255, 0, 0), 2))
            self.add_graphics_item_to_layer(layer_id, rect_item)

        return layer_id

    def display_ai_findings_with_metadata(self, result):
        layer_id = self.display_ai_findings(result)
        self._ensure_result_attributes(result)
        if self._ai_metadata_panel is not None:
            self._ai_metadata_panel.update_interpretation_result(result)
        return layer_id

    def clear_ai_findings(self) -> bool:
        cleared = False
        for lid in self._overlay_manager.get_layers_by_type(LayerType.AI_FINDINGS):
            self.clear_overlay_layer(lid)
            cleared = True
        self._current_ai_result = None
        return cleared

    def _highlight_finding(self, finding_id: str):
        for lid in self._overlay_manager.get_layers_by_type(LayerType.AI_FINDINGS):
            layer = self.get_overlay_layer(lid)
            if not layer:
                continue
            try:
                idx = int(finding_id) - 1
                # Reset pens first
                for it in layer.items:
                    it.setPen(QPen(QColor(255, 0, 0), 2))
                if 0 <= idx < len(layer.items):
                    layer.items[idx].setPen(QPen(QColor(0, 255, 0), 3))
            except ValueError:
                pass

    # -------- ANNOTATION API (passthrough) ---------
    def mousePressEvent(self, event):
        scene_pos = self.mapToScene(event.position().toPoint())
        handled = self._annotation_manager.handle_mouse_press(scene_pos)
        # Forward to base class for Qt internal state (needed for double-click).
        super().mousePressEvent(event)
        if handled:
            event.accept()

    def mouseMoveEvent(self, event):
        scene_pos = self.mapToScene(event.position().toPoint())
        if self._annotation_manager.handle_mouse_move(scene_pos):
            event.accept()
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        scene_pos = self.mapToScene(event.position().toPoint())
        handled = self._annotation_manager.handle_mouse_release(scene_pos)
        super().mouseReleaseEvent(event)
        if handled:
            event.accept()

    def mouseDoubleClickEvent(self, event):
        scene_pos = self.mapToScene(event.position().toPoint())
        handled = self._annotation_manager.handle_double_click(scene_pos)
        super().mouseDoubleClickEvent(event)
        if handled:
            event.accept()

    def set_annotation_tool(self, tool: AnnotationTool):
        self._annotation_manager.set_active_tool(tool)

    def clear_all_annotations(self):
        self._annotation_manager.clear_all_annotations()
    # ------------------------------------------------ 