"""
Authentication Flow Security Tests for MedScan AI
Tests focused on authentication mechanisms and password security

Extracted from the original auth_security_tests.py file for better modularity.
"""

import time
from typing import Dict, List, Any
from . import (
    SecurityTestResult, MockDatabaseSession, logger,
    PASSWORD_MANAGER_AVAILABLE, AUTH_SERVICE_AVAILABLE, CREDENTIAL_SERVICE_AVAILABLE,
    RBAC_MANAGER_AVAILABLE, SESSION_MANAGER_AVAILABLE, MFA_SERVICE_AVAILABLE
)

if PASSWORD_MANAGER_AVAILABLE:
    from medscan_ai.security.authentication.password_manager import PasswordManager

if AUTH_SERVICE_AVAILABLE and CREDENTIAL_SERVICE_AVAILABLE and RBAC_MANAGER_AVAILABLE:
    from medscan_ai.security.authentication.auth_service import AuthenticationService
    from medscan_ai.security.authentication.credential_service import UserCredentialService
    from medscan_ai.security.access_control.rbac_manager import RB<PERSON>Manager
    from medscan_ai.security.authentication.session_manager import JWTSessionManager
    from medscan_ai.security.authentication.mfa_service import M<PERSON><PERSON>ervice


class AuthenticationSecurityTests:
    """
    Authentication-specific security tests
    """

    def __init__(self):
        """Initialize authentication security testing"""
        self.test_results: List[SecurityTestResult] = []
        self.available_services = []
        
        # Mock database session
        mock_session = MockDatabaseSession()
        
        # Initialize services with error handling
        self.password_manager = None
        self.credential_service = None
        self.rbac_manager = None
        self.session_manager = None
        self.mfa_service = None
        self.auth_service = None
        
        # Try password manager (standalone)
        if PASSWORD_MANAGER_AVAILABLE:
            try:
                self.password_manager = PasswordManager()
                self.available_services.append('password_manager')
                logger.info("✅ PasswordManager initialized")
            except Exception as e:
                logger.warning(f"❌ PasswordManager failed: {e}")
        
        # Try credential service
        if CREDENTIAL_SERVICE_AVAILABLE:
            try:
                self.credential_service = UserCredentialService(db_session=mock_session)
                self.available_services.append('credential_service')
                logger.info("✅ CredentialService initialized")
            except Exception as e:
                logger.warning(f"❌ CredentialService failed: {e}")
        
        # Try RBAC manager
        if RBAC_MANAGER_AVAILABLE:
            try:
                self.rbac_manager = RBACManager(db_session=mock_session)
                self.available_services.append('rbac_manager')
                logger.info("✅ RBACManager initialized")
            except Exception as e:
                logger.warning(f"❌ RBACManager failed: {e}")
        
        # Try session manager
        if SESSION_MANAGER_AVAILABLE:
            try:
                self.session_manager = JWTSessionManager(
                    secret_key="test_secret_key_for_security_testing",
                    algorithm="HS256"
                )
                self.available_services.append('session_manager')
                logger.info("✅ SessionManager initialized")
            except Exception as e:
                logger.warning(f"❌ SessionManager failed: {e}")
        
        # Try MFA service
        if MFA_SERVICE_AVAILABLE:
            try:
                self.mfa_service = MFAService(db_session=mock_session)
                self.available_services.append('mfa_service')
                logger.info("✅ MFAService initialized")
            except Exception as e:
                logger.warning(f"❌ MFAService failed: {e}")
        
        # Try authentication service (requires credential_service and rbac_manager)
        if (AUTH_SERVICE_AVAILABLE and 
            self.credential_service and self.rbac_manager):
            try:
                self.auth_service = AuthenticationService(
                    credential_service=self.credential_service,
                    rbac_manager=self.rbac_manager,
                    session_manager=self.session_manager,
                    mfa_service=self.mfa_service,
                    session=mock_session
                )
                self.available_services.append('auth_service')
                logger.info("✅ AuthenticationService initialized")
            except Exception as e:
                logger.warning(f"❌ AuthenticationService failed: {e}")

    def run_authentication_tests(self) -> List[SecurityTestResult]:
        """
        Run all authentication security tests
        
        Returns:
            List of test results
        """
        logger.info("Starting authentication security testing")
        
        # Clear previous results
        self.test_results = []
        
        # Run tests based on available services
        if 'password_manager' in self.available_services:
            self._test_password_security()
        
        if 'auth_service' in self.available_services:
            self._test_authentication_flow()
        
        # These tests can run with minimal services
        self._test_injection_vulnerabilities()
        self._test_brute_force_protection()
        
        return self.test_results

    def _test_password_security(self):
        """Test password hashing and storage security"""
        logger.info("Testing password security")
        
        # Test 1: Password hashing strength
        try:
            test_password = "TestPassword123!"
            
            # Test password hashing with PasswordManager's correct interface
            password_hash, salt = self.password_manager.hash_password(test_password)
            
            if password_hash and salt:
                # Test password verification
                verification = self.password_manager.verify_password(
                    test_password, password_hash, salt
                )
                
                if verification:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Password Hashing Security",
                            passed=True,
                            details="Password hashing and verification working correctly with Argon2",
                            severity="high"
                        )
                    )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Password Hashing Security",
                            passed=False,
                            details="Password verification failed",
                            severity="critical",
                            recommendations=["Fix password verification logic"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Password Hashing Security",
                        passed=False,
                        details="Password hashing returned invalid result",
                        severity="critical",
                        recommendations=["Fix password hashing implementation"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Password Hashing Security",
                    passed=False,
                    details=f"Password hashing test failed: {str(e)}",
                    severity="critical",
                    recommendations=["Fix password manager implementation"]
                )
            )

        # Test 2: Password complexity validation
        try:
            weak_passwords = [
                "123456", "password", "abc123", "qwerty", "admin"
            ]
            
            strong_passwords = [
                "MyStr0ng!Password#123", "C0mpl3x&P@ssw0rd$", "Secure!Pass2024#"
            ]
            
            weak_rejected = 0
            strong_accepted = 0
            
            # Test weak passwords - should be rejected
            for weak_pass in weak_passwords:
                try:
                    self.password_manager._validate_password(weak_pass)
                    # If no exception raised, password was accepted (bad)
                    pass
                except ValueError:
                    # Exception raised means password was rejected (good)
                    weak_rejected += 1
            
            # Test strong passwords - should be accepted
            for strong_pass in strong_passwords:
                try:
                    self.password_manager._validate_password(strong_pass)
                    # No exception means password was accepted (good)
                    strong_accepted += 1
                except ValueError:
                    # Exception means password was rejected (bad)
                    pass
            
            if weak_rejected == len(weak_passwords) and strong_accepted == len(strong_passwords):
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Password Complexity Validation",
                        passed=True,
                        details="Password complexity validation working correctly",
                        severity="medium"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Password Complexity Validation",
                        passed=False,
                        details=f"Weak passwords rejected: {weak_rejected}/{len(weak_passwords)}, Strong passwords accepted: {strong_accepted}/{len(strong_passwords)}",
                        severity="medium",
                        recommendations=["Improve password complexity validation"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Password Complexity Validation",
                    passed=False,
                    details=f"Password complexity test failed: {str(e)}",
                    severity="medium",
                    recommendations=["Implement password complexity validation"]
                )
            )
        
        # Test 3: Password strength analysis
        try:
            test_passwords = [
                ("weak123", "weak"),
                ("Medium@Pass1", "medium"),
                ("VeryStrong!Password#2024", "very_strong")
            ]
            
            strength_tests_passed = 0
            
            for password, expected_level in test_passwords:
                try:
                    strength = self.password_manager.get_password_strength(password)
                    if strength['level'] == expected_level:
                        strength_tests_passed += 1
                except Exception:
                    pass
            
            if strength_tests_passed >= 2:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Password Strength Analysis",
                        passed=True,
                        details=f"Password strength analysis working: {strength_tests_passed}/3 tests passed",
                        severity="low"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Password Strength Analysis",
                        passed=False,
                        details=f"Password strength analysis limited: {strength_tests_passed}/3 tests passed",
                        severity="low",
                        recommendations=["Improve password strength analysis accuracy"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Password Strength Analysis",
                    passed=False,
                    details=f"Password strength analysis test failed: {str(e)}",
                    severity="low",
                    recommendations=["Implement password strength analysis"]
                )
            )

    def _test_authentication_flow(self):
        """Test complete authentication flow security"""
        logger.info("Testing authentication flow")
        
        if not self.auth_service:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Authentication Flow Security",
                    passed=False,
                    details="Authentication service not available for testing",
                    severity="high",
                    recommendations=["Initialize authentication service"]
                )
            )
            return
        
        # Test 1: Valid authentication
        try:
            test_username = f"test_user_{int(time.time())}"
            test_password = "TestPassword123!"
            
            # This would normally test user creation and authentication
            # For now, we test that the service handles authentication attempts properly
            
            # Test authentication with non-existent user
            auth_result = self.auth_service.authenticate_user(test_username, test_password)
            
            if not auth_result.get('success', True):  # Should fail for non-existent user
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Authentication Flow - Non-existent User",
                        passed=True,
                        details="Authentication correctly rejected non-existent user",
                        severity="medium"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Authentication Flow - Non-existent User",
                        passed=False,
                        details="Authentication incorrectly accepted non-existent user",
                        severity="high",
                        recommendations=["Fix user existence validation in authentication"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Authentication Flow Security",
                    passed=False,
                    details=f"Authentication flow test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix authentication service implementation"]
                )
            )

    def _test_injection_vulnerabilities(self):
        """Test for SQL injection and similar vulnerabilities"""
        logger.info("Testing injection vulnerabilities")
        
        # Test SQL injection patterns
        injection_patterns = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "admin'--",
            "' UNION SELECT * FROM users --",
            "1; SELECT * FROM users"
        ]
        
        for pattern in injection_patterns:
            try:
                # Test injection in username field
                if self.auth_service:
                    result = self.auth_service.authenticate_user(pattern, "password")
                    
                    if not result.get('success', True):
                        # Good - injection was rejected
                        continue
                    else:
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="SQL Injection Vulnerability",
                                passed=False,
                                details=f"Potential SQL injection vulnerability with pattern: {pattern}",
                                severity="critical",
                                recommendations=["Implement proper input sanitization and parameterized queries"]
                            )
                        )
                        return
                        
            except Exception as e:
                # Exceptions are okay - they indicate the injection was caught
                continue
        
        self.test_results.append(
            SecurityTestResult(
                test_name="SQL Injection Protection",
                passed=True,
                details="SQL injection patterns were properly rejected",
                severity="high"
            )
        )

    def _test_brute_force_protection(self):
        """Test brute force attack protection"""
        logger.info("Testing brute force protection")
        
        # Test multiple failed login attempts
        test_username = "test_user_brute_force"
        failed_attempts = 0
        
        for i in range(10):  # Try 10 failed login attempts
            try:
                if self.auth_service:
                    result = self.auth_service.authenticate_user(test_username, f"wrong_password_{i}")
                    
                    if not result.get('success', True):
                        failed_attempts += 1
                    else:
                        # If authentication succeeds with wrong password, that's a problem
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="Brute Force Protection",
                                passed=False,
                                details=f"Authentication succeeded with wrong password on attempt {i+1}",
                                severity="critical",
                                recommendations=["Fix authentication logic"]
                            )
                        )
                        return
                        
            except Exception as e:
                # Exceptions might indicate rate limiting or account locking
                if "rate limit" in str(e).lower() or "locked" in str(e).lower():
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Brute Force Protection",
                            passed=True,
                            details=f"Brute force protection active after {i+1} attempts: {str(e)}",
                            severity="medium"
                        )
                    )
                    return
        
        # If we made it through all attempts without being blocked
        if failed_attempts >= 8:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Brute Force Protection",
                    passed=False,
                    details=f"No brute force protection detected after {failed_attempts} failed attempts",
                    severity="high",
                    recommendations=["Implement account locking and rate limiting"]
                )
            )
        else:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Brute Force Protection",
                    passed=True,
                    details="Basic brute force protection appears to be in place",
                    severity="medium"
                )
            )


def run_authentication_security_tests() -> List[SecurityTestResult]:
    """
    Convenience function to run authentication security tests
    
    Returns:
        List of test results
    """
    auth_tests = AuthenticationSecurityTests()
    return auth_tests.run_authentication_tests() 