"""Password Manager for MedScan AI Authentication.

Provides secure password hashing and validation using Argon2 algorithm
with medical-grade security parameters for HIPAA/GDPR compliance.
"""

import base64
import logging
import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Tuple

from passlib.context import CryptContext
from passlib.hash import argon2

logger = logging.getLogger(__name__)


class PasswordManager:
    """Secure password management using Argon2 hashing.

    Provides medical-grade password security with configurable parameters
    optimized for healthcare environments and regulatory compliance.
    """

    # Medical-grade Argon2 parameters
    # Higher than standard security due to sensitive medical data
    ARGON2_TIME_COST = 3  # Number of iterations (medical grade: 3-5)
    ARGON2_MEMORY_COST = 65536  # Memory in KB (medical grade: 64MB+)
    ARGON2_PARALLELISM = 2  # Number of parallel threads
    ARGON2_HASH_LENGTH = 32  # Hash output length in bytes
    SALT_LENGTH = 16  # Salt length in bytes

    # Password policy for medical environments
    MIN_PASSWORD_LENGTH = 12
    MAX_PASSWORD_LENGTH = 128

    def __init__(self):
        """Initialize password manager with secure Argon2 configuration."""
        # Configure Argon2 context with medical-grade parameters
        self.pwd_context = CryptContext(
            schemes=["argon2"],
            default="argon2",
            argon2__time_cost=self.ARGON2_TIME_COST,
            argon2__memory_cost=self.ARGON2_MEMORY_COST,
            argon2__parallelism=self.ARGON2_PARALLELISM,
            argon2__hash_len=self.ARGON2_HASH_LENGTH,
            argon2__salt_size=self.SALT_LENGTH,
            # Disable deprecated schemes
            deprecated="auto",
        )

        logger.info("PasswordManager initialized with medical-grade Argon2 parameters")

    def generate_salt(self) -> str:
        """Generate cryptographically secure salt.

        Returns:
            Base64-encoded salt string
        """
        salt_bytes = secrets.token_bytes(self.SALT_LENGTH)
        salt_b64 = base64.b64encode(salt_bytes).decode("utf-8")

        logger.debug("Generated new cryptographic salt")
        return salt_b64

    def hash_password(
        self, password: str, salt: Optional[str] = None
    ) -> Tuple[str, str]:
        """Hash password using Argon2 with secure salt.

        Args:
            password: Plain text password to hash
            salt: Optional existing salt (generates new if None)

        Returns:
            Tuple of (password_hash, salt)

        Raises:
            ValueError: If password doesn't meet security requirements
        """
        # Validate password
        self._validate_password(password)

        # Generate salt if not provided
        if salt is None:
            salt = self.generate_salt()

        # Hash password with salt
        try:
            # Add salt to password before hashing
            salted_password = f"{password}{salt}"
            password_hash = self.pwd_context.hash(salted_password)

            logger.info("Password hashed successfully with Argon2")
            return password_hash, salt

        except Exception as e:
            logger.error(f"Password hashing failed: {e}")
            raise ValueError(f"Failed to hash password: {e}")

    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify password against stored hash and salt.

        Args:
            password: Plain text password to verify
            password_hash: Stored Argon2 hash
            salt: Stored salt value

        Returns:
            True if password is valid, False otherwise
        """
        try:
            # Recreate salted password
            salted_password = f"{password}{salt}"

            # Verify against stored hash
            is_valid = self.pwd_context.verify(salted_password, password_hash)

            if is_valid:
                logger.info("Password verification successful")
            else:
                logger.warning("Password verification failed")

            return is_valid

        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False

    def needs_rehash(self, password_hash: str) -> bool:
        """Check if password hash needs to be updated.

        Args:
            password_hash: Stored password hash

        Returns:
            True if hash should be updated with new parameters
        """
        try:
            return self.pwd_context.needs_update(password_hash)
        except Exception:
            # If we can't check, assume it needs update for safety
            return True

    def _validate_password(self, password: str) -> None:
        """Validate password meets security requirements.

        Args:
            password: Password to validate

        Raises:
            ValueError: If password doesn't meet requirements
        """
        if not password:
            raise ValueError("Password cannot be empty")

        if len(password) < self.MIN_PASSWORD_LENGTH:
            raise ValueError(
                f"Password must be at least {self.MIN_PASSWORD_LENGTH} characters"
            )

        if len(password) > self.MAX_PASSWORD_LENGTH:
            raise ValueError(
                f"Password cannot exceed {self.MAX_PASSWORD_LENGTH} characters"
            )

        # Medical environment password complexity requirements
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )

    def generate_secure_password(self, length: int = 16) -> str:
        """Generate a cryptographically secure password.

        Args:
            length: Password length (default: 16)

        Returns:
            Secure random password

        Raises:
            ValueError: If length is invalid
        """
        if length < self.MIN_PASSWORD_LENGTH:
            raise ValueError(
                f"Password length must be at least {self.MIN_PASSWORD_LENGTH}"
            )

        if length > self.MAX_PASSWORD_LENGTH:
            raise ValueError(
                f"Password length cannot exceed {self.MAX_PASSWORD_LENGTH}"
            )

        # Character sets for secure password generation
        uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        lowercase = "abcdefghijklmnopqrstuvwxyz"
        digits = "0123456789"
        special = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        all_chars = uppercase + lowercase + digits + special

        # Ensure at least one character from each set
        password = [
            secrets.choice(uppercase),
            secrets.choice(lowercase),
            secrets.choice(digits),
            secrets.choice(special),
        ]

        # Fill remaining length with random characters
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))

        # Shuffle to avoid predictable patterns
        secrets.SystemRandom().shuffle(password)

        secure_password = "".join(password)
        logger.info(f"Generated secure password of length {length}")

        return secure_password

    def get_password_strength(self, password: str) -> dict:
        """Analyze password strength for medical security requirements.

        Args:
            password: Password to analyze

        Returns:
            Dictionary with strength analysis
        """
        analysis = {
            "length": len(password),
            "has_uppercase": any(c.isupper() for c in password),
            "has_lowercase": any(c.islower() for c in password),
            "has_digits": any(c.isdigit() for c in password),
            "has_special": any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password),
            "meets_minimum": len(password) >= self.MIN_PASSWORD_LENGTH,
            "score": 0,
            "level": "weak",
        }

        # Calculate strength score
        score = 0
        if analysis["length"] >= 12:
            score += 2
        if analysis["length"] >= 16:
            score += 1
        if analysis["has_uppercase"]:
            score += 1
        if analysis["has_lowercase"]:
            score += 1
        if analysis["has_digits"]:
            score += 1
        if analysis["has_special"]:
            score += 2

        # Additional complexity bonus
        unique_chars = len(set(password))
        if unique_chars >= len(password) * 0.7:
            score += 1

        analysis["score"] = score

        # Determine strength level for medical use
        if score >= 8:
            analysis["level"] = "very_strong"
        elif score >= 6:
            analysis["level"] = "strong"
        elif score >= 4:
            analysis["level"] = "medium"
        else:
            analysis["level"] = "weak"

        return analysis


class PasswordPolicy:
    """Password policy management for medical environments.

    Enforces organizational password policies with medical-grade
    security requirements and compliance standards.
    """

    def __init__(self):
        """Initialize password policy with medical defaults."""
        self.min_length = 12
        self.max_age_days = 90  # HIPAA recommended
        self.history_count = 12  # Remember last 12 passwords
        self.lockout_threshold = 5  # Lock after 5 failed attempts
        self.lockout_duration_minutes = 30

        logger.info("PasswordPolicy initialized with medical security defaults")

    def check_password_age(self, password_changed_at: datetime) -> dict:
        """Check if password meets age requirements.

        Args:
            password_changed_at: When password was last changed

        Returns:
            Dictionary with age analysis
        """
        now = datetime.utcnow()
        age_days = (now - password_changed_at).days
        max_age = timedelta(days=self.max_age_days)
        expires_at = password_changed_at + max_age
        days_until_expiry = (expires_at - now).days

        return {
            "age_days": age_days,
            "max_age_days": self.max_age_days,
            "is_expired": age_days > self.max_age_days,
            "days_until_expiry": max(0, days_until_expiry),
            "expires_at": expires_at,
            "needs_change_soon": days_until_expiry <= 7,  # Warn 7 days before
        }

    def should_lock_account(self, failed_attempts: int) -> bool:
        """Check if account should be locked due to failed attempts.

        Args:
            failed_attempts: Number of consecutive failed login attempts

        Returns:
            True if account should be locked
        """
        return failed_attempts >= self.lockout_threshold

    def get_lockout_duration(self) -> timedelta:
        """Get account lockout duration.

        Returns:
            Lockout duration as timedelta
        """
        return timedelta(minutes=self.lockout_duration_minutes)
