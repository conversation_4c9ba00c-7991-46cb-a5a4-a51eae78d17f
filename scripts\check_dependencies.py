#!/usr/bin/env python3
"""Dependency Management and Security Checker for MedScan AI.

This script provides comprehensive dependency management capabilities:
- Security vulnerability scanning
- Dependency version checking
- Requirements.txt synchronization
- Medical data security validation
- HIPAA compliance checks.
"""

import argparse
import json
import logging
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DependencyManager:
    """Manages dependencies for MedScan AI medical imaging application."""

    def __init__(self, project_root: Path):
        """Initialize dependency manager with project root path."""
        self.project_root = project_root
        self.pyproject_path = project_root / "pyproject.toml"
        self.requirements_lock = project_root / "requirements-lock.txt"

    def run_security_audit(self) -> <PERSON>ple[bool, str]:
        """Run pip-audit security scan for vulnerabilities."""
        logger.info("Running security audit...")

        try:
            result = subprocess.run(
                [
                    "pip-audit",
                    "--format",
                    "json",
                    "--requirement",
                    str(self.requirements_lock),
                ],
                capture_output=True,
                text=True,
                timeout=120,
            )

            if result.returncode == 0:
                logger.info("✅ No security vulnerabilities found")
                return True, "Security audit passed"
            else:
                vulnerabilities = json.loads(result.stdout) if result.stdout else {}
                vuln_count = len(vulnerabilities.get("vulnerabilities", []))
                logger.error(f"❌ Security vulnerabilities found: {vuln_count}")
                return False, result.stdout

        except subprocess.TimeoutExpired:
            logger.error("❌ Security audit timed out")
            return False, "Audit timed out"
        except Exception as e:
            logger.error(f"❌ Security audit failed: {e}")
            return False, str(e)

    def check_outdated_packages(self) -> Tuple[bool, List[Dict]]:
        """Check for outdated packages."""
        logger.info("Checking for outdated packages...")

        try:
            result = subprocess.run(
                ["pip", "list", "--outdated", "--format", "json"],
                capture_output=True,
                text=True,
                timeout=60,
            )

            if result.returncode == 0:
                outdated = json.loads(result.stdout) if result.stdout else []
                if outdated:
                    logger.warning(f"⚠️  {len(outdated)} packages are outdated")
                    for pkg in outdated:
                        logger.warning(
                            f"  {pkg['name']}: {pkg['version']} -> "
                            f"{pkg['latest_version']}"
                        )
                else:
                    logger.info("✅ All packages are up to date")
                return True, outdated
            else:
                logger.error("❌ Failed to check outdated packages")
                return False, []

        except Exception as e:
            logger.error(f"❌ Failed to check outdated packages: {e}")
            return False, []

    def validate_medical_security(self) -> Tuple[bool, List[str]]:
        """Validate medical data security requirements."""
        logger.info("Validating medical data security...")

        issues = []

        # Check for secure random number generation
        try:
            import secrets  # noqa: F401

            import cryptography  # noqa: F401

            logger.info("✅ Cryptographic libraries available")
        except ImportError as e:
            issues.append(f"Missing cryptographic library: {e}")

        # Check for SQL injection protection
        try:
            import sqlalchemy  # noqa: F401

            logger.info("✅ SQLAlchemy ORM available for safe DB operations")
        except ImportError:
            issues.append("SQLAlchemy not available - risk of SQL injection")

        # Check for secure temporary file handling
        try:
            import tempfile  # noqa: F401

            logger.info("✅ Secure temporary file handling available")
        except ImportError:
            issues.append("Secure temporary file handling not available")

        # Check for HTTPS/TLS support
        try:
            import ssl  # noqa: F401

            import requests  # noqa: F401

            logger.info("✅ HTTPS/TLS support available")
        except ImportError:
            issues.append("HTTPS/TLS support not available")

        if issues:
            logger.error(f"❌ Medical security validation failed: {len(issues)} issues")
            for issue in issues:
                logger.error(f"  - {issue}")
            return False, issues
        else:
            logger.info("✅ Medical security validation passed")
            return True, []

    def update_requirements_lock(self) -> bool:
        """Update the locked requirements file."""
        logger.info("Updating requirements lock file...")

        try:
            result = subprocess.run(
                ["pip", "freeze"], capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                with open(self.requirements_lock, "w") as f:
                    f.write(result.stdout)
                logger.info(f"✅ Updated {self.requirements_lock}")
                return True
            else:
                logger.error("❌ Failed to freeze requirements")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to update requirements lock: {e}")
            return False

    def run_comprehensive_check(self) -> bool:
        """Run all dependency checks."""
        logger.info("🔍 Starting comprehensive dependency check...")

        all_passed = True

        # Update requirements lock
        if not self.update_requirements_lock():
            all_passed = False

        # Security audit
        security_passed, security_msg = self.run_security_audit()
        if not security_passed:
            all_passed = False

        # Check outdated packages
        outdated_passed, outdated_packages = self.check_outdated_packages()
        if not outdated_passed:
            all_passed = False

        # Medical security validation
        medical_passed, medical_issues = self.validate_medical_security()
        if not medical_passed:
            all_passed = False

        if all_passed:
            logger.info("✅ All dependency checks passed!")
        else:
            logger.error("❌ Some dependency checks failed!")

        return all_passed


def main() -> None:
    """Main entry point."""
    parser = argparse.ArgumentParser(description="MedScan AI Dependency Manager")
    parser.add_argument("--audit", action="store_true", help="Run security audit only")
    parser.add_argument(
        "--outdated", action="store_true", help="Check outdated packages only"
    )
    parser.add_argument(
        "--medical", action="store_true", help="Run medical security checks only"
    )
    parser.add_argument(
        "--update", action="store_true", help="Update requirements lock only"
    )
    parser.add_argument("--all", action="store_true", help="Run all checks (default)")

    args = parser.parse_args()

    project_root = Path(__file__).parent.parent
    manager = DependencyManager(project_root)

    success = True

    if args.audit:
        success, _ = manager.run_security_audit()
    elif args.outdated:
        success, _ = manager.check_outdated_packages()
    elif args.medical:
        success, _ = manager.validate_medical_security()
    elif args.update:
        success = manager.update_requirements_lock()
    else:
        # Run all checks by default
        success = manager.run_comprehensive_check()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
