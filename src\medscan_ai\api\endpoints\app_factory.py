"""Flask Application Factory for MedScan AI Authentication API.

Provides Flask application factory and configuration:
- Application initialization
- Blueprint registration
- Middleware setup
"""

import logging
from typing import Optional

from flask import Flask, g

from ...database.engine import get_session
from ...security.access_control.rbac_manager import RBACManager
from ...security.authentication import (
    AuthenticationService,
    UserCredentialService,
)
from ...security.authentication.authorization import AuthorizationMiddleware
from ...security.authentication.session_manager import SessionMiddleware

logger = logging.getLogger(__name__)

def create_auth_app(
    auth_service: Optional[AuthenticationService] = None,
    session_middleware: Optional[SessionMiddleware] = None,
    auth_middleware: Optional[AuthorizationMiddleware] = None
) -> Flask:
    """
    Create Flask application with authentication endpoints.

    Args:
        auth_service: Optional authentication service instance
        session_middleware: Optional session middleware instance
        auth_middleware: Optional authorization middleware instance

    Returns:
        Configured Flask application
    """
    app = Flask(__name__)
    app.config["SECRET_KEY"] = "your-secret-key-here"  # Use proper secret in production

    # Initialize services if not provided
    if not auth_service or not session_middleware or not auth_middleware:
        try:
            credential_service = UserCredentialService(get_session())
            rbac_manager = RBACManager(get_session())
            auth_service = auth_service or AuthenticationService(credential_service, rbac_manager)
            
            session_middleware = session_middleware or SessionMiddleware(auth_service.session_manager)
            auth_middleware = auth_middleware or AuthorizationMiddleware(
                session_middleware, rbac_manager, auth_service
            )
        except RuntimeError:
            # For testing environment where database might not be initialized
            logger.warning("Could not initialize authentication services - using None placeholders")
            auth_service = None
            session_middleware = None
            auth_middleware = None

    # Import blueprints (avoid circular imports)
    from .authentication_endpoints import create_authentication_blueprint
    from .registration_endpoints import create_registration_blueprint
    from .mfa_endpoints import create_mfa_blueprint
    from .profile_endpoints import create_profile_blueprint
    from .admin_endpoints import create_admin_blueprint
    from .medical_endpoints import create_medical_blueprint

    # Register blueprints with services
    if auth_service and session_middleware and auth_middleware:
        app.register_blueprint(create_authentication_blueprint(auth_service, session_middleware, auth_middleware))
        app.register_blueprint(create_registration_blueprint(auth_service, session_middleware, auth_middleware))
        app.register_blueprint(create_mfa_blueprint(auth_service, session_middleware, auth_middleware))
        app.register_blueprint(create_profile_blueprint())
        app.register_blueprint(create_admin_blueprint())
        app.register_blueprint(create_medical_blueprint())

    # Setup authorization middleware in Flask app context
    @app.before_request
    def setup_auth_middleware():
        """Setup authorization middleware in Flask request context."""
        g.auth_middleware = auth_middleware

    return app


if __name__ == "__main__":
    # For development only
    app = create_auth_app()
    app.run(debug=True, host="0.0.0.0", port=5000)
