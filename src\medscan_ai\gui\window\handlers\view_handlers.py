"""
View control operations for MedScan AI GUI.

This module provides zoom, pan, reset, and other view manipulation
capabilities for the medical image viewer.
"""

from typing import Optional
from PySide6.QtCore import QEvent


class ViewHandlerMixin:
    """
    Mixin class providing view control capabilities for the main window.
    
    This mixin should be used with a QMainWindow that has:
    - image_viewer: InteractiveImageViewer instance
    - main_splitter: QSplitter for panel management
    """

    def _zoom_in(self):
        """Zoom in on the current image."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            self.image_viewer.zoom_in()
            
            # Update status
            if hasattr(self, 'statusBar'):
                zoom_level = getattr(self.image_viewer, 'zoom_factor', 1.0)
                self.statusBar().showMessage(f"Zoom: {zoom_level:.1f}x", 2000)

    def _zoom_out(self):
        """Zoom out on the current image."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            self.image_viewer.zoom_out()
            
            # Update status
            if hasattr(self, 'statusBar'):
                zoom_level = getattr(self.image_viewer, 'zoom_factor', 1.0)
                self.statusBar().showMessage(f"Zoom: {zoom_level:.1f}x", 2000)

    def _reset_view(self):
        """Reset the image view to fit the viewer."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            self.image_viewer.reset_view()
            
            # Update status
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("View reset to fit", 2000)

    def _fit_to_window(self):
        """Fit the image to the current window size."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            self.image_viewer.fit_to_window()
            
            # Update status
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Image fitted to window", 2000)

    def _actual_size(self):
        """Display image at actual size (100% zoom)."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            self.image_viewer.actual_size()
            
            # Update status
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Actual size (100%)", 2000)

    def _on_splitter_moved(self, pos: int, index: int):
        """
        Handle splitter movement for responsive layout.
        
        Args:
            pos: New position of the splitter
            index: Index of the splitter handle that moved
        """
        try:
            if hasattr(self, 'main_splitter'):
                sizes = self.main_splitter.sizes()
                total_width = sum(sizes)
                
                # Ensure minimum sizes for panels
                min_left = 200
                min_center = 300  
                min_right = 250
                
                # Adjust sizes if any panel is too small
                if len(sizes) >= 3:
                    left_size, center_size, right_size = sizes[0], sizes[1], sizes[2]
                    
                    # Check and adjust minimum sizes
                    adjusted = False
                    
                    if left_size < min_left:
                        left_size = min_left
                        adjusted = True
                    
                    if center_size < min_center:
                        center_size = min_center
                        adjusted = True
                        
                    if right_size < min_right:
                        right_size = min_right
                        adjusted = True
                    
                    # Redistribute if adjustments were made
                    if adjusted:
                        new_total = left_size + center_size + right_size
                        if new_total <= total_width:
                            self.main_splitter.setSizes([left_size, center_size, right_size])

                # Update image viewer if center panel size changed
                if index == 0 or index == 1:  # Left or center panel changed
                    self._update_image_viewer_size()

        except Exception as e:
            # Silently handle splitter resize errors
            pass

    def _update_image_viewer_size(self):
        """Update image viewer size after panel resize."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            # Small delay to allow layout to settle
            from PySide6.QtCore import QTimer
            QTimer.singleShot(50, self._refresh_image_layout)

    def _refresh_image_layout(self):
        """Refresh image layout after resize."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            try:
                # Trigger a layout update
                self.image_viewer.update()
                
                # Optionally reset view to fit new size
                if hasattr(self, '_auto_fit_enabled') and self._auto_fit_enabled:
                    self.image_viewer.fit_to_window()
                    
            except Exception:
                pass

    def _toggle_fullscreen(self):
        """Toggle fullscreen mode for image viewer."""
        if self.isFullScreen():
            self.showNormal()
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Exited fullscreen mode", 2000)
        else:
            self.showFullScreen()
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Entered fullscreen mode", 2000)

    def _center_image(self):
        """Center the image in the viewer."""
        if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
            self.image_viewer.center_image()
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Image centered", 2000)

    def _enable_auto_fit(self, enabled: bool = True):
        """
        Enable or disable automatic fit-to-window on resize.
        
        Args:
            enabled: Whether to enable auto-fit
        """
        self._auto_fit_enabled = enabled
        
        if hasattr(self, 'statusBar'):
            status = "enabled" if enabled else "disabled"
            self.statusBar().showMessage(f"Auto-fit {status}", 2000)

    def resizeEvent(self, event):
        """
        Handle window resize events with responsive behavior.
        
        Args:
            event: QResizeEvent
        """
        # Call parent resize event
        super().resizeEvent(event)
        
        try:
            # Get current and new sizes
            old_size = event.oldSize()
            new_size = event.size()
            
            # Check if this is a significant resize
            if old_size.isValid():
                width_change = abs(new_size.width() - old_size.width())
                height_change = abs(new_size.height() - old_size.height())
                
                # Only respond to significant changes (> 50 pixels)
                if width_change > 50 or height_change > 50:
                    self._handle_responsive_resize(new_size)

        except Exception:
            # Silently handle resize errors
            pass

    def _handle_responsive_resize(self, new_size):
        """
        Handle responsive layout adjustments on window resize.
        
        Args:
            new_size: New window size
        """
        try:
            # Update splitter sizes proportionally
            if hasattr(self, 'main_splitter'):
                current_sizes = self.main_splitter.sizes()
                
                if len(current_sizes) >= 3:
                    # Calculate new proportional sizes
                    total_current = sum(current_sizes)
                    new_width = new_size.width() - 32  # Account for margins
                    
                    if total_current > 0:
                        # Maintain proportions but respect minimums
                        left_ratio = current_sizes[0] / total_current
                        right_ratio = current_sizes[2] / total_current
                        
                        new_left = max(200, int(new_width * left_ratio))
                        new_right = max(250, int(new_width * right_ratio))  
                        new_center = max(300, new_width - new_left - new_right)
                        
                        self.main_splitter.setSizes([new_left, new_center, new_right])

            # Update image viewer layout
            self._update_image_viewer_size()

        except Exception:
            pass 