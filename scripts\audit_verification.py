#!/usr/bin/env python3
"""Automated Audit Log Verification Script.

Production-ready script for scheduled verification of audit log integrity,
completeness, and security. Can be run via cron jobs or monitoring systems.

Usage:
    python audit_verification.py --days=30 --report-dir=/var/logs/audit
    python audit_verification.py --quick-check
    python audit_verification.py --full-verification --email-alerts
"""

import sys
import os
import argparse
import logging
import json
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from medscan_ai.security.audit.verification import (
    AuditLogVerificationService,
    VerificationResult,
    generate_verification_report_markdown
)
from medscan_ai.database.engine import get_session


def setup_logging(log_level: str = "INFO", log_file: str = None):
    """Set up logging configuration."""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    if log_file:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
    else:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[logging.StreamHandler(sys.stdout)]
        )


def send_alert_email(report, email_config: dict):
    """Send email alert for verification failures."""
    if not email_config or not email_config.get('enabled'):
        return
    
    try:
        # Create message
        msg = MimeMultipart()
        msg['From'] = email_config['smtp_from']
        msg['To'] = ', '.join(email_config['alert_recipients'])
        
        if report.overall_status == VerificationResult.FAIL:
            msg['Subject'] = f"🚨 CRITICAL: Audit Log Verification FAILED - {report.report_id}"
            priority = "CRITICAL"
        elif report.overall_status == VerificationResult.WARNING:
            msg['Subject'] = f"⚠️ WARNING: Audit Log Verification Issues - {report.report_id}"
            priority = "WARNING"
        else:
            msg['Subject'] = f"✅ INFO: Audit Log Verification PASSED - {report.report_id}"
            priority = "INFO"
        
        # Create email body
        body = f"""
MedScan AI Audit Log Verification Report
=========================================

Priority: {priority}
Report ID: {report.report_id}
Timestamp: {report.end_time.strftime('%Y-%m-%d %H:%M:%S UTC')}
Overall Status: {report.overall_status.value}

Summary:
- Total Records Checked: {report.total_records_checked:,}
- Checks Passed: {report.summary['checks_passed']}
- Checks Failed: {report.summary['checks_failed']}
- Checks with Warnings: {report.summary['checks_with_warnings']}
- Affected Records: {report.summary['total_affected_records']}

"""
        
        # Add details for failed checks
        failed_checks = [c for c in report.checks_performed if c.status == VerificationResult.FAIL]
        if failed_checks:
            body += "\n🚨 FAILED CHECKS:\n"
            for check in failed_checks:
                body += f"- {check.check_type}: {check.message}\n"
                body += f"  Affected Records: {check.affected_records}\n"
        
        warning_checks = [c for c in report.checks_performed if c.status == VerificationResult.WARNING]
        if warning_checks:
            body += "\n⚠️ WARNING CHECKS:\n"
            for check in warning_checks:
                body += f"- {check.check_type}: {check.message}\n"
        
        body += f"""
For detailed analysis, see the full verification report.

This is an automated alert from MedScan AI Audit Verification System.
Report generated at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}
"""
        
        msg.attach(MimeText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
        if email_config.get('smtp_use_tls'):
            server.starttls()
        if email_config.get('smtp_username'):
            server.login(email_config['smtp_username'], email_config['smtp_password'])
        
        server.send_message(msg)
        server.quit()
        
        logging.info(f"Alert email sent to {len(email_config['alert_recipients'])} recipients")
        
    except Exception as e:
        logging.error(f"Failed to send alert email: {str(e)}")


def save_verification_report(report, report_dir: str, format_type: str = "json"):
    """Save verification report to file."""
    report_dir = Path(report_dir)
    report_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = report.end_time.strftime('%Y%m%d_%H%M%S')
    
    if format_type == "json":
        # Save JSON report
        json_file = report_dir / f"audit_verification_{timestamp}.json"
        report_data = {
            "report_id": report.report_id,
            "start_time": report.start_time.isoformat(),
            "end_time": report.end_time.isoformat(),
            "overall_status": report.overall_status.value,
            "total_records_checked": report.total_records_checked,
            "summary": report.summary,
            "checks": [
                {
                    "check_type": check.check_type,
                    "status": check.status.value,
                    "message": check.message,
                    "details": check.details,
                    "timestamp": check.timestamp.isoformat(),
                    "affected_records": check.affected_records
                }
                for check in report.checks_performed
            ]
        }
        
        with open(json_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logging.info(f"JSON report saved: {json_file}")
        
    elif format_type == "markdown":
        # Save Markdown report
        md_file = report_dir / f"audit_verification_{timestamp}.md"
        markdown_content = generate_verification_report_markdown(report)
        
        with open(md_file, 'w') as f:
            f.write(markdown_content)
        
        logging.info(f"Markdown report saved: {md_file}")


def run_quick_check():
    """Run quick audit verification check (last 24 hours)."""
    logging.info("Running quick audit verification check...")
    
    try:
        verification_service = AuditLogVerificationService()
        
        # Run lightweight checks
        hash_check = verification_service.verify_hash_chain_integrity(days_back=1)
        completeness_check = verification_service.verify_log_completeness(days_back=1)
        
        print(f"\n🔍 Quick Verification Results:")
        print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        print(f"Hash Chain Integrity: {hash_check.status.value}")
        print(f"Log Completeness: {completeness_check.status.value}")
        
        overall_status = VerificationResult.PASS
        if hash_check.status == VerificationResult.FAIL or completeness_check.status == VerificationResult.FAIL:
            overall_status = VerificationResult.FAIL
        elif hash_check.status == VerificationResult.WARNING or completeness_check.status == VerificationResult.WARNING:
            overall_status = VerificationResult.WARNING
        
        status_icon = "✅" if overall_status == VerificationResult.PASS else "❌" if overall_status == VerificationResult.FAIL else "⚠️"
        print(f"Overall Status: {status_icon} {overall_status.value}")
        print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        
        return overall_status == VerificationResult.PASS
        
    except Exception as e:
        logging.error(f"Quick check failed: {str(e)}")
        print(f"❌ Quick check failed: {str(e)}")
        return False


def run_full_verification(days_back: int, report_dir: str, email_config: dict = None):
    """Run comprehensive audit verification."""
    logging.info(f"Running full audit verification for {days_back} days...")
    
    try:
        verification_service = AuditLogVerificationService()
        
        # Run comprehensive verification
        report = verification_service.run_comprehensive_verification(days_back)
        
        # Save reports
        if report_dir:
            save_verification_report(report, report_dir, "json")
            save_verification_report(report, report_dir, "markdown")
        
        # Send alerts if configured
        if email_config and (report.overall_status != VerificationResult.PASS):
            send_alert_email(report, email_config)
        
        # Print summary
        print(f"\n📊 Comprehensive Verification Report")
        print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        print(f"Report ID: {report.report_id}")
        print(f"Duration: {report.summary['duration_seconds']:.2f} seconds")
        print(f"Records Checked: {report.total_records_checked:,}")
        print(f"")
        print(f"Results:")
        print(f"  ✅ Passed: {report.summary['checks_passed']}")
        print(f"  ❌ Failed: {report.summary['checks_failed']}")
        print(f"  ⚠️  Warnings: {report.summary['checks_with_warnings']}")
        print(f"  🚨 Errors: {report.summary['checks_with_errors']}")
        print(f"")
        
        status_icon = "✅" if report.overall_status == VerificationResult.PASS else "❌" if report.overall_status == VerificationResult.FAIL else "⚠️"
        print(f"Overall Status: {status_icon} {report.overall_status.value}")
        
        # Show failed checks
        failed_checks = [c for c in report.checks_performed if c.status == VerificationResult.FAIL]
        if failed_checks:
            print(f"\n❌ Failed Checks:")
            for check in failed_checks:
                print(f"  - {check.check_type}: {check.message}")
                print(f"    Affected Records: {check.affected_records}")
        
        print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        
        return report.overall_status == VerificationResult.PASS
        
    except Exception as e:
        logging.error(f"Full verification failed: {str(e)}")
        print(f"❌ Full verification failed: {str(e)}")
        return False


def load_config(config_file: str = None) -> dict:
    """Load configuration from file."""
    if not config_file:
        config_file = os.path.join(os.path.dirname(__file__), 'audit_verification_config.json')
    
    if not os.path.exists(config_file):
        # Return default config
        return {
            "email_alerts": {
                "enabled": False,
                "smtp_server": "localhost",
                "smtp_port": 587,
                "smtp_use_tls": True,
                "smtp_from": "<EMAIL>",
                "alert_recipients": ["<EMAIL>"]
            },
            "logging": {
                "level": "INFO",
                "file": "/var/log/medscan_ai/audit_verification.log"
            },
            "verification": {
                "default_days_back": 30,
                "default_report_dir": "/var/log/medscan_ai/verification_reports"
            }
        }
    
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.warning(f"Failed to load config from {config_file}: {str(e)}")
        return {}


def main():
    """Main entry point for audit verification script."""
    parser = argparse.ArgumentParser(
        description="MedScan AI Audit Log Verification Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --quick-check                    # Quick 24-hour check
  %(prog)s --days=30 --report-dir=/var/logs # Full 30-day verification
  %(prog)s --full-verification --email-alerts # Full check with alerts
  %(prog)s --config=/etc/medscan/audit.json   # Use custom config
        """
    )
    
    parser.add_argument('--quick-check', action='store_true',
                       help='Run quick verification check (last 24 hours)')
    parser.add_argument('--full-verification', action='store_true',
                       help='Run comprehensive verification')
    parser.add_argument('--days', type=int, default=30,
                       help='Number of days back to verify (default: 30)')
    parser.add_argument('--report-dir', type=str,
                       help='Directory to save verification reports')
    parser.add_argument('--email-alerts', action='store_true',
                       help='Enable email alerts for failures')
    parser.add_argument('--config', type=str,
                       help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level')
    parser.add_argument('--log-file', type=str,
                       help='Log file path')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Set up logging
    log_file = args.log_file or config.get('logging', {}).get('file')
    setup_logging(args.log_level, log_file)
    
    logging.info("Starting MedScan AI Audit Verification")
    logging.info(f"Arguments: {vars(args)}")
    
    success = False
    
    try:
        if args.quick_check:
            success = run_quick_check()
        elif args.full_verification or args.report_dir:
            # Determine report directory
            report_dir = args.report_dir or config.get('verification', {}).get('default_report_dir', './verification_reports')
            
            # Email configuration
            email_config = None
            if args.email_alerts:
                email_config = config.get('email_alerts', {})
                if not email_config.get('enabled', True):
                    logging.warning("Email alerts requested but not enabled in config")
                    email_config = None
            
            success = run_full_verification(args.days, report_dir, email_config)
        else:
            print("No verification type specified. Use --quick-check or --full-verification")
            parser.print_help()
            sys.exit(1)
        
        if success:
            logging.info("Audit verification completed successfully")
            print("✅ Verification completed successfully")
            sys.exit(0)
        else:
            logging.error("Audit verification completed with failures")
            print("❌ Verification completed with failures")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logging.info("Verification interrupted by user")
        print("\n⚠️ Verification interrupted")
        sys.exit(130)
    except Exception as e:
        logging.error(f"Verification failed with error: {str(e)}")
        print(f"🚨 Verification failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 