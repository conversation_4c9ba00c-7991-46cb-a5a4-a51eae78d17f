"""
AI findings dialog for comprehensive display and analysis.
"""

from typing import Optional
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QTextEdit, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QWidget
)

# Import AI postprocessing results
try:
    from ...ai.postprocessing.result_interpreter import InterpretationResult
except ImportError:
    InterpretationResult = None


class AIFindingDialog(QDialog):
    """
    Modal dialog for comprehensive AI findings display and analysis.
    
    Features:
    - Tabbed interface for different data views
    - Detailed finding information
    - Interactive finding selection
    - Comprehensive metadata display
    - Export and reporting capabilities
    """
    
    # Signal emitted when user selects a finding
    finding_selected = Signal(str)  # finding_id
    
    def __init__(self, result: InterpretationResult, selected_finding_id: str = None, parent=None):
        """
        Initialize AI finding dialog.
        
        Args:
            result: InterpretationResult to display
            selected_finding_id: Optional ID of finding to highlight initially
            parent: Parent widget
        """
        super().__init__(parent)
        
        self.result = result
        self.selected_finding_id = selected_finding_id
        
        # Setup UI
        self._setup_ui()
        self._populate_data()
        
        # Set dialog properties
        self.setWindowTitle("AI Analysis Detailed Report")
        self.setModal(True)
        self.resize(800, 600)
        
    def _setup_ui(self):
        """Setup the user interface components."""
        layout = QVBoxLayout(self)
        
        # Tab widget for different views
        self.tab_widget = QTabWidget()
        
        # Overview tab
        self.overview_tab = self._create_overview_tab()
        self.tab_widget.addTab(self.overview_tab, "Overview")
        
        # Findings table tab
        self.findings_tab = self._create_findings_tab()
        self.tab_widget.addTab(self.findings_tab, "Findings Details")
        
        # Metadata tab
        self.metadata_tab = self._create_metadata_tab()
        self.tab_widget.addTab(self.metadata_tab, "Processing Metadata")
        
        layout.addWidget(self.tab_widget)
        
        # Button bar
        button_layout = QHBoxLayout()
        
        self.export_button = QPushButton("Export Report")
        self.export_button.clicked.connect(self._export_detailed_report)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
    def _create_overview_tab(self):
        """Create the overview tab widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Title
        title_label = QLabel("AI Analysis Overview")
        title_font = QFont("Arial", 14, QFont.Bold)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Overview text area
        self.overview_text = QTextEdit()
        self.overview_text.setReadOnly(True)
        layout.addWidget(self.overview_text)
        
        return widget
        
    def _create_findings_tab(self):
        """Create the findings details tab widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Findings table
        self.findings_table = QTableWidget()
        self.findings_table.setColumnCount(6)
        self.findings_table.setHorizontalHeaderLabels([
            "ID", "Type", "Confidence", "Severity", "Location", "Clinical Significance"
        ])
        
        # Configure table
        self.findings_table.setAlternatingRowColors(True)
        self.findings_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.findings_table.itemSelectionChanged.connect(self._on_finding_selection_changed)
        
        layout.addWidget(self.findings_table)
        
        return widget
        
    def _create_metadata_tab(self):
        """Create the processing metadata tab widget."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Title
        title_label = QLabel("Processing Metadata")
        title_font = QFont("Arial", 12, QFont.Bold)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Metadata text area
        self.metadata_text = QTextEdit()
        self.metadata_text.setReadOnly(True)
        self.metadata_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.metadata_text)
        
        return widget
        
    def _populate_data(self):
        """Populate all tabs with data from the interpretation result."""
        if not self.result:
            return
            
        self._populate_overview()
        self._populate_findings_table()
        self._populate_metadata()
        
        # Select initial finding if specified
        if self.selected_finding_id:
            self._select_finding_in_table(self.selected_finding_id)
            
    def _populate_overview(self):
        """Populate the overview tab with summary information."""
        lines = [
            "=== AI ANALYSIS SUMMARY ===",
            "",
            f"Overall Confidence: {self.result.overall_confidence:.1%}",
            f"Total Detections: {len(self.result.detections)}",
            f"High Confidence Findings: {len(self.result.high_confidence_detections)}",
            f"Critical Findings: {len(self.result.critical_findings)}",
            ""
        ]
        
        if self.result.requires_immediate_attention:
            lines.extend([
                "⚠️ IMMEDIATE ATTENTION REQUIRED ⚠️",
                ""
            ])
            
        # Add summary by anomaly type
        type_counts = {}
        for detection in self.result.detections:
            anomaly_type = detection.anomaly_type.value
            type_counts[anomaly_type] = type_counts.get(anomaly_type, 0) + 1
            
        if type_counts:
            lines.append("Findings by Type:")
            for anomaly_type, count in sorted(type_counts.items()):
                lines.append(f"  • {anomaly_type.replace('_', ' ').title()}: {count}")
            lines.append("")
            
        # Add critical findings summary
        if self.result.critical_findings:
            lines.append("Critical Findings:")
            for finding in self.result.critical_findings[:5]:  # Show top 5
                lines.append(f"  • {finding.anomaly_type.value.replace('_', ' ').title()} "
                           f"(Confidence: {finding.confidence_score:.1%})")
            if len(self.result.critical_findings) > 5:
                lines.append(f"  ... and {len(self.result.critical_findings) - 5} more")
                
        self.overview_text.setText("\n".join(lines))
        
    def _populate_findings_table(self):
        """Populate the findings table with detection details."""
        self.findings_table.setRowCount(len(self.result.detections))
        
        for row, detection in enumerate(self.result.detections):
            # ID
            id_item = QTableWidgetItem(detection.id)
            self.findings_table.setItem(row, 0, id_item)
            
            # Type
            type_item = QTableWidgetItem(
                detection.anomaly_type.value.replace('_', ' ').title()
            )
            self.findings_table.setItem(row, 1, type_item)
            
            # Confidence
            conf_item = QTableWidgetItem(
                f"{detection.confidence_score:.1%} ({detection.confidence_level.value})"
            )
            self.findings_table.setItem(row, 2, conf_item)
            
            # Severity
            if detection.severity:
                sev_item = QTableWidgetItem(detection.severity.value.title())
            else:
                sev_item = QTableWidgetItem("N/A")
            self.findings_table.setItem(row, 3, sev_item)
            
            # Location
            if detection.bounding_box:
                bbox = detection.bounding_box
                loc_item = QTableWidgetItem(
                    f"({bbox.x1:.2f}, {bbox.y1:.2f}) - ({bbox.x2:.2f}, {bbox.y2:.2f})"
                )
            else:
                loc_item = QTableWidgetItem("N/A")
            self.findings_table.setItem(row, 4, loc_item)
            
            # Clinical significance
            if detection.clinical_significance:
                sig_item = QTableWidgetItem(detection.clinical_significance[:50] + "...")
            else:
                sig_item = QTableWidgetItem("Not specified")
            self.findings_table.setItem(row, 5, sig_item)
            
        # Resize columns to content
        self.findings_table.resizeColumnsToContents()
        
    def _populate_metadata(self):
        """Populate the metadata tab with processing information."""
        lines = [
            "=== PROCESSING METADATA ===",
            ""
        ]
        
        if hasattr(self.result, 'metadata') and self.result.metadata:
            meta = self.result.metadata
            
            # Model information
            if 'model_name' in meta:
                lines.append(f"Model: {meta['model_name']}")
            if 'model_version' in meta:
                lines.append(f"Version: {meta['model_version']}")
            if 'processing_time' in meta:
                lines.append(f"Processing Time: {meta['processing_time']:.2f} seconds")
                
            lines.append("")
            
            # Image information
            if 'image_dimensions' in meta:
                dims = meta['image_dimensions']
                lines.append(f"Image Dimensions: {dims[0]} x {dims[1]}")
            if 'modality' in meta:
                lines.append(f"Modality: {meta['modality']}")
                
            lines.append("")
            
            # Processing parameters
            if 'preprocessing' in meta:
                lines.append("Preprocessing Steps:")
                for step in meta['preprocessing']:
                    lines.append(f"  • {step}")
                    
        else:
            lines.append("No metadata available")
            
        self.metadata_text.setText("\n".join(lines))
        
    def _on_finding_selection_changed(self):
        """Handle finding selection change in the table."""
        selected_items = self.findings_table.selectedItems()
        if selected_items:
            # Get the ID from the first column
            row = selected_items[0].row()
            finding_id = self.findings_table.item(row, 0).text()
            self.finding_selected.emit(finding_id)
            
    def _select_finding_in_table(self, finding_id: str):
        """Select a specific finding in the table by ID."""
        for row in range(self.findings_table.rowCount()):
            if self.findings_table.item(row, 0).text() == finding_id:
                self.findings_table.selectRow(row)
                break
                
    def _export_detailed_report(self):
        """Export detailed report to file."""
        # This would implement comprehensive report export
        # Could generate PDF, HTML, or structured JSON report
        pass 