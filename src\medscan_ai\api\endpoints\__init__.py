"""MedScan AI API Endpoints Package.

Modularized authentication API endpoints with domain separation:
- Authentication endpoints (login, logout, refresh, validate)
- Registration endpoints (user registration)
- MFA endpoints (multi-factor authentication)
- Profile endpoints (user profile management)
- Admin endpoints (administrative functions)
- Medical endpoints (clinical data access)
- App factory (Flask application creation)

This modular structure improves maintainability and follows
single responsibility principle while maintaining backward
compatibility through the main auth_endpoints module.
"""

# Import all endpoint factories for easy access
from .authentication_endpoints import create_authentication_blueprint
from .registration_endpoints import create_registration_blueprint
from .mfa_endpoints import create_mfa_blueprint
from .profile_endpoints import create_profile_blueprint
from .admin_endpoints import create_admin_blueprint
from .medical_endpoints import create_medical_blueprint
from .app_factory import create_auth_app

__all__ = [
    "create_authentication_blueprint",
    "create_registration_blueprint", 
    "create_mfa_blueprint",
    "create_profile_blueprint",
    "create_admin_blueprint",
    "create_medical_blueprint",
    "create_auth_app",
] 