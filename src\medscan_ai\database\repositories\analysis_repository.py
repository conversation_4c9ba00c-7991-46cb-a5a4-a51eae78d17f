"""Analysis repository for AI analysis operations."""

from typing import List

from sqlalchemy.orm import Session

from ..models.analysis import Analysis
from .base import BaseRepository


class AnalysisRepository(BaseRepository[Analysis]):
    """Repository for AI analysis operations."""

    def __init__(self, session: Session):
        super().__init__(Analysis, session)

    def find_by_image_id(self, image_id: int) -> List[Analysis]:
        """Find all analyses for a specific image."""
        return self.filter_by(image_id=image_id)
