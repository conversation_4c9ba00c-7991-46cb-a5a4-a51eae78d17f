"""
Memory-Efficient Data Loader for 100GB+ Medical Imaging Datasets

This module provides streaming data loading capabilities for large medical imaging
datasets (NIH ChestX-ray14, CheXpert) without loading entire dataset into memory.

Key Features:
- Streaming data loading with tf.data API
- Memory-mapped file access for large DICOM files
- Progressive loading and disk caching
- GPU memory optimization with dynamic batching
- Asynchronous preprocessing pipelines
"""

import os
import json
import mmap
from pathlib import Path
from typing import Tuple, Dict, List, Optional
from functools import lru_cache

import tensorflow as tf
import numpy as np
import cv2
import pydicom
import pandas as pd

try:
    from tqdm import tqdm
except ImportError:
    # Fallback if tqdm is not available
    def tqdm(iterable, **kwargs):
        return iterable

from ...core.utils.logging_config import get_logger
from .memory_monitor import MemoryMonitor

logger = get_logger(__name__)


class MemoryMappedImageReader:
    """Memory-mapped image file reader for large files."""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self._mmap_file = None
        self._file_handle = None
        
    def __enter__(self):
        self._file_handle = open(self.file_path, 'rb')
        self._mmap_file = mmap.mmap(self._file_handle.fileno(), 0, access=mmap.ACCESS_READ)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._mmap_file:
            self._mmap_file.close()
        if self._file_handle:
            self._file_handle.close()
            
    def read_dicom(self) -> pydicom.Dataset:
        """Read DICOM dataset from memory-mapped file."""
        if not self._mmap_file:
            raise RuntimeError("File not opened. Use context manager.")
            
        from io import BytesIO
        self._mmap_file.seek(0)
        return pydicom.dcmread(BytesIO(self._mmap_file.read()))
    
    def read_image(self) -> np.ndarray:
        """Read regular image from memory-mapped file."""
        if not self._mmap_file:
            raise RuntimeError("File not opened. Use context manager.")
        
        # For regular images, we still need to decode
        self._mmap_file.seek(0)
        image_data = self._mmap_file.read()
        
        # Decode image using OpenCV
        image_array = np.frombuffer(image_data, dtype=np.uint8)
        image = cv2.imdecode(image_array, cv2.IMREAD_GRAYSCALE)
        
        return image


class DatasetIndexer:
    """Create and manage index for large medical imaging datasets."""
    
    def __init__(
        self,
        dataset_path: str,
        cache_dir: str = "data/cache",
        supported_extensions: Optional[List[str]] = None
    ):
        self.dataset_path = Path(dataset_path)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        if supported_extensions is None:
            self.supported_extensions = {'.dcm', '.jpg', '.jpeg', '.png', '.tiff', '.tif'}
        else:
            self.supported_extensions = set(supported_extensions)
        
        # Cache files
        self.index_cache_file = self.cache_dir / f"dataset_index_{self.dataset_path.name}.json"
        self.labels_cache_file = self.cache_dir / f"dataset_labels_{self.dataset_path.name}.json"
        
        # Dataset index
        self._file_index = None
        self._labels_mapping = None
        
    def build_index(self, force_rebuild: bool = False) -> Optional[Dict]:
        """Build or load dataset file index."""
        
        if not force_rebuild and self.index_cache_file.exists():
            logger.info("Loading existing dataset index...")
            try:
                with open(self.index_cache_file, 'r') as f:
                    self._file_index = json.load(f)
                logger.info(f"Loaded index with {len(self._file_index)} files")
                return self._file_index
            except Exception as e:
                logger.warning(f"Error loading index cache: {e}, rebuilding...")
        
        logger.info(f"Building dataset index for: {self.dataset_path}")
        
        file_index = {}
        file_count = 0
        
        # Walk through dataset directory
        for root, dirs, files in os.walk(self.dataset_path):
            root_path = Path(root)
            
            for file in tqdm(files, desc=f"Indexing {root_path.name}"):
                file_path = root_path / file
                file_ext = file_path.suffix.lower()
                
                if file_ext in self.supported_extensions:
                    # Extract metadata
                    metadata = self._extract_file_metadata(file_path)
                    
                    if metadata:
                        relative_path = file_path.relative_to(self.dataset_path)
                        file_index[str(relative_path)] = metadata
                        file_count += 1
        
        logger.info(f"Indexed {file_count} medical image files")
        
        # Save index to cache
        self._save_index_cache(file_index)
        self._file_index = file_index
        
        return file_index  # type: ignore
    
    def _extract_file_metadata(self, file_path: Path) -> Optional[Dict]:
        """Extract metadata from medical image file."""
        try:
            stat = file_path.stat()
            metadata = {
                'file_path': str(file_path),
                'file_size_mb': stat.st_size / (1024 * 1024),
                'file_type': file_path.suffix.lower(),
                'width': 0,
                'height': 0,
                'channels': 1,
                'patient_id': '',
                'study_id': '',
                'modality': 'CR',
                'labels': [],
                'modified_time': stat.st_mtime
            }
            
            # Extract image dimensions and DICOM metadata
            if file_path.suffix.lower() == '.dcm':
                metadata.update(self._extract_dicom_metadata(file_path))
            else:
                metadata.update(self._extract_image_metadata(file_path))
            
            # Extract labels from file structure or naming
            metadata['labels'] = self._extract_labels_from_path(file_path)
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting metadata from {file_path}: {e}")
            return None
    
    def _extract_dicom_metadata(self, file_path: Path) -> Dict:
        """Extract metadata from DICOM file."""
        metadata = {}
        
        try:
            # Use memory-mapped reading for large DICOM files
            with MemoryMappedImageReader(str(file_path)) as reader:
                ds = reader.read_dicom()
                
                metadata.update({
                    'width': int(ds.Columns) if hasattr(ds, 'Columns') else 0,
                    'height': int(ds.Rows) if hasattr(ds, 'Rows') else 0,
                    'channels': 1,  # Most medical images are grayscale
                    'patient_id': str(ds.PatientID) if hasattr(ds, 'PatientID') else '',
                    'study_id': str(ds.StudyInstanceUID) if hasattr(ds, 'StudyInstanceUID') else '',
                    'modality': str(ds.Modality) if hasattr(ds, 'Modality') else 'CR',
                    'acquisition_date': str(ds.AcquisitionDate) if hasattr(ds, 'AcquisitionDate') else '',
                    'institution': str(ds.InstitutionName) if hasattr(ds, 'InstitutionName') else ''
                })
                
        except Exception as e:
            logger.warning(f"Error reading DICOM metadata from {file_path}: {e}")
            
        return metadata
    
    def _extract_image_metadata(self, file_path: Path) -> Dict:
        """Extract metadata from regular image file."""
        metadata = {}
        
        try:
            # Use OpenCV to get image dimensions without loading full image
            image = cv2.imread(str(file_path), cv2.IMREAD_UNCHANGED)
            if image is not None:
                if len(image.shape) == 2:
                    h, w = image.shape
                    c = 1
                else:
                    h, w, c = image.shape
                
                metadata.update({
                    'width': w,
                    'height': h,
                    'channels': c
                })
                
        except Exception as e:
            logger.warning(f"Error reading image metadata from {file_path}: {e}")
            
        return metadata
    
    def _extract_labels_from_path(self, file_path: Path) -> List[str]:
        """Extract labels from file path based on dataset structure."""
        labels = []
        
        # Common medical imaging label extraction patterns
        
        # NIH ChestX-ray14 pattern: labels in CSV files
        if 'nih' in str(file_path).lower() or 'chestx' in str(file_path).lower():
            labels = self._extract_nih_labels(file_path)
        
        # CheXpert pattern: labels in CSV files
        elif 'chexpert' in str(file_path).lower():
            labels = self._extract_chexpert_labels(file_path)
        
        # Generic pattern: extract from directory structure
        else:
            labels = self._extract_generic_labels(file_path)
        
        return labels
    
    def _extract_nih_labels(self, file_path: Path) -> List[str]:
        """Extract NIH ChestX-ray14 labels."""
        # This would typically read from Data_Entry_2017.csv
        # For now, return empty list - will be populated during label loading
        return []
    
    def _extract_chexpert_labels(self, file_path: Path) -> List[str]:
        """Extract CheXpert labels."""
        # This would typically read from train.csv or valid.csv
        # For now, return empty list - will be populated during label loading
        return []
    
    def _extract_generic_labels(self, file_path: Path) -> List[str]:
        """Extract labels from directory structure."""
        labels = []
        
        # Look for common patterns in path
        path_parts = file_path.parts
        
        for part in path_parts:
            part_lower = part.lower()
            
            # Common medical imaging categories
            if any(keyword in part_lower for keyword in ['normal', 'abnormal', 'pneumonia', 'covid', 'tb']):
                labels.append(part)
        
        return labels
    
    def _save_index_cache(self, file_index: Dict):
        """Save file index to cache."""
        try:
            with open(self.index_cache_file, 'w') as f:
                json.dump(file_index, f, indent=2)
            logger.info(f"Dataset index saved to: {self.index_cache_file}")
        except Exception as e:
            logger.error(f"Error saving index cache: {e}")
    
    def load_labels(self, labels_file: Optional[str] = None) -> Dict:
        """Load labels from CSV file or other sources."""
        
        if labels_file and Path(labels_file).exists():
            return self._load_labels_from_file(labels_file)
        
        # Try to find common label files in dataset
        common_label_files = [
            self.dataset_path / "Data_Entry_2017.csv",  # NIH ChestX-ray14
            self.dataset_path / "train.csv",            # CheXpert train
            self.dataset_path / "valid.csv",            # CheXpert validation
            self.dataset_path / "labels.csv",           # Generic
            self.dataset_path / "annotations.csv"      # Generic
        ]
        
        for label_file in common_label_files:
            if label_file.exists():
                logger.info(f"Found label file: {label_file}")
                return self._load_labels_from_file(str(label_file))
        
        logger.warning("No label file found, using empty labels")
        return {}
    
    def _load_labels_from_file(self, labels_file: str) -> Dict:
        """Load labels from CSV file."""
        try:
            df = pd.read_csv(labels_file)
            labels_mapping = {}
            
            # Handle different CSV formats
            if 'Image Index' in df.columns and 'Finding Labels' in df.columns:
                # NIH ChestX-ray14 format
                for _, row in df.iterrows():
                    image_name = row['Image Index']
                    labels = row['Finding Labels'].split('|') if pd.notna(row['Finding Labels']) else []
                    labels_mapping[image_name] = labels
                    
            elif 'Path' in df.columns:
                # CheXpert format
                label_columns = [col for col in df.columns if col not in ['Path', 'Sex', 'Age', 'Frontal/Lateral', 'AP/PA']]
                
                for _, row in df.iterrows():
                    image_path = row['Path']
                    labels = []
                    
                    for label_col in label_columns:
                        if pd.notna(row[label_col]) and row[label_col] == 1:
                            labels.append(label_col)
                    
                    labels_mapping[image_path] = labels
            
            logger.info(f"Loaded labels for {len(labels_mapping)} images")
            return labels_mapping
            
        except Exception as e:
            logger.error(f"Error loading labels from {labels_file}: {e}")
            return {}
    
    def get_file_index(self) -> Dict:
        """Get the file index (build if not exists)."""
        if self._file_index is None:
            self.build_index()
        return self._file_index or {}
    
    def get_dataset_stats(self) -> Dict:
        """Get dataset statistics."""
        if self._file_index is None:
            self.build_index()
        
        if self._file_index is None:
            return {
                'total_files': 0,
                'total_size_gb': 0.0,
                'file_types': {},
                'common_resolutions': {}
            }
        
        total_files = len(self._file_index)
        total_size_mb = sum(item['file_size_mb'] for item in self._file_index.values())
        
        # File type distribution
        file_types = {}
        for item in self._file_index.values():
            file_type = item['file_type']
            file_types[file_type] = file_types.get(file_type, 0) + 1
        
        # Resolution distribution
        resolutions = {}
        for item in self._file_index.values():
            if item['width'] > 0 and item['height'] > 0:
                resolution = f"{item['width']}x{item['height']}"
                resolutions[resolution] = resolutions.get(resolution, 0) + 1
        
        return {
            'total_files': total_files,
            'total_size_gb': total_size_mb / 1024,
            'file_types': file_types,
            'common_resolutions': dict(sorted(resolutions.items(), key=lambda x: x[1], reverse=True)[:10])
        }


class StreamingDataLoader:
    """Memory-efficient streaming data loader for large medical datasets."""
    
    def __init__(
        self,
        dataset_path: str,
        cache_dir: str = "data/cache",
        target_image_size: Tuple[int, int] = (224, 224),
        batch_size: int = 32,
        num_classes: int = 14,  # Default for ChestX-ray14
        prefetch_buffer_size: int = tf.data.AUTOTUNE,
        num_parallel_calls: int = tf.data.AUTOTUNE,
        memory_limit_gb: float = 16.0
    ):
        self.dataset_path = Path(dataset_path)
        self.cache_dir = Path(cache_dir)
        self.target_image_size = target_image_size
        self.batch_size = batch_size
        self.num_classes = num_classes
        self.prefetch_buffer_size = prefetch_buffer_size
        self.num_parallel_calls = num_parallel_calls
        
        # Memory monitoring
        self.memory_monitor = MemoryMonitor(
            max_ram_usage_gb=memory_limit_gb,
            max_vram_usage_gb=8.0,
            monitoring_interval=10.0
        )
        
        # Initialize indexer
        self.indexer = DatasetIndexer(
            dataset_path=str(dataset_path),
            cache_dir=str(cache_dir)
        )
        
        # Dataset properties
        self._file_index = None
        self._labels_mapping = None
        self._class_names = None
        
        logger.info(f"StreamingDataLoader initialized for: {dataset_path}")
    
    def prepare_dataset(
        self,
        labels_file: Optional[str] = None,
        validation_split: float = 0.2,
        test_split: float = 0.1,
        seed: int = 42
    ) -> Dict[str, tf.data.Dataset]:
        """Prepare train, validation, and test datasets."""
        
        logger.info("Preparing streaming datasets...")
        
        # Build file index
        self._file_index = self.indexer.get_file_index()
        
        # Load labels
        self._labels_mapping = self.indexer.load_labels(labels_file)
        
        # Create file list with labels
        file_list = []
        for rel_path, metadata in self._file_index.items():
            abs_path = self.dataset_path / rel_path
            
            # Get labels for this file
            labels = self._get_file_labels(rel_path, metadata)
            
            file_list.append({
                'file_path': str(abs_path),
                'labels': labels,
                'metadata': metadata
            })
        
        logger.info(f"Found {len(file_list)} files with labels")
        
        # Shuffle and split
        np.random.seed(seed)
        indices = np.random.permutation(len(file_list))
        
        n_test = int(len(file_list) * test_split)
        n_val = int(len(file_list) * validation_split)
        n_train = len(file_list) - n_test - n_val
        
        train_files = [file_list[i] for i in indices[:n_train]]
        val_files = [file_list[i] for i in indices[n_train:n_train + n_val]]
        test_files = [file_list[i] for i in indices[n_train + n_val:]]
        
        logger.info(f"Dataset splits: Train={len(train_files)}, Val={len(val_files)}, Test={len(test_files)}")
        
        # Create TensorFlow datasets
        datasets = {}
        
        datasets['train'] = self._create_tf_dataset(
            train_files,
            shuffle_buffer_size=1000,
            repeat=True,
            augment=True
        )
        
        datasets['validation'] = self._create_tf_dataset(
            val_files,
            shuffle_buffer_size=0,
            repeat=False,
            augment=False
        )
        
        datasets['test'] = self._create_tf_dataset(
            test_files,
            shuffle_buffer_size=0,
            repeat=False,
            augment=False
        )
        
        return datasets
    
    def _get_file_labels(self, rel_path: str, metadata: Dict) -> List[str]:
        """Get labels for a file."""
        labels = []
        
        # First try to get from labels mapping (CSV file)
        if self._labels_mapping is not None:
            filename = Path(rel_path).name
            if filename in self._labels_mapping:
                labels = self._labels_mapping[filename]
            elif rel_path in self._labels_mapping:
                labels = self._labels_mapping[rel_path]
        
        # Fallback to metadata labels
        if not labels:
            labels = metadata.get('labels', [])
        
        # Ensure labels is a list
        if isinstance(labels, str):
            labels = [labels] if labels != 'No Finding' else []
        
        return labels
    
    def _create_tf_dataset(
        self,
        file_list: List[Dict],
        shuffle_buffer_size: int = 0,
        repeat: bool = False,
        augment: bool = False
    ) -> tf.data.Dataset:
        """Create TensorFlow dataset from file list."""
        
        # Extract file paths and labels
        file_paths = [item['file_path'] for item in file_list]
        labels_list = [item['labels'] for item in file_list]
        
        # Create dataset from file paths and labels
        dataset = tf.data.Dataset.from_tensor_slices((file_paths, labels_list))
        
        # Shuffle if requested
        if shuffle_buffer_size > 0:
            dataset = dataset.shuffle(buffer_size=shuffle_buffer_size)
        
        # Repeat if requested
        if repeat:
            dataset = dataset.repeat()
        
        # Map loading and preprocessing function
        dataset = dataset.map(
            lambda path, labels: self._load_and_preprocess_py(path, labels, augment),
            num_parallel_calls=self.num_parallel_calls
        )
        
        # Filter out failed loads
        dataset = dataset.filter(lambda image, label: tf.reduce_all(tf.greater(tf.shape(image), 0)))
        
        # Batch and prefetch
        dataset = dataset.batch(self.batch_size, drop_remainder=True)
        dataset = dataset.prefetch(buffer_size=self.prefetch_buffer_size)
        
        return dataset
    
    def _load_and_preprocess_py(self, file_path: tf.Tensor, labels: tf.Tensor, augment: bool = False):
        """Python function for loading and preprocessing (wrapped for tf.py_function)."""
        
        # Use tf.py_function to call Python code
        image, processed_labels = tf.py_function(  # type: ignore
            func=lambda path, lbl: self._load_and_preprocess_image(path.numpy().decode('utf-8'), lbl.numpy(), augment),
            inp=[file_path, labels],
            Tout=[tf.float32, tf.float32]
        )
        
        # Set shapes for proper batching
        image.set_shape([*self.target_image_size, 1])
        processed_labels.set_shape([self.num_classes])
        
        return image, processed_labels
    
    def _load_and_preprocess_image(self, file_path: str, labels_raw, augment: bool = False) -> Tuple[np.ndarray, np.ndarray]:
        """Load and preprocess single image."""
        
        try:
            # Check memory before loading
            if not self.memory_monitor.check_memory_limits()["overall_within_limit"]:
                logger.warning("Memory limit reached, returning dummy image")
                return self._get_dummy_image_and_labels()
            
            # Load image
            if file_path.endswith('.dcm'):
                image = self._load_dicom_image(file_path)
            else:
                image = self._load_regular_image(file_path)
            
            # Preprocess image
            image = self._preprocess_image(image, augment)
            
            # Process labels
            labels = self._process_labels(labels_raw)
            
            return image, labels
            
        except Exception as e:
            logger.warning(f"Error loading {file_path}: {e}")
            return self._get_dummy_image_and_labels()
    
    def _load_dicom_image(self, file_path: str) -> np.ndarray:
        """Load DICOM image efficiently."""
        try:
            with MemoryMappedImageReader(file_path) as reader:
                ds = reader.read_dicom()
                image = ds.pixel_array
                
                # Apply DICOM windowing if present
                if hasattr(ds, 'WindowCenter') and hasattr(ds, 'WindowWidth'):
                    center = float(ds.WindowCenter)
                    width = float(ds.WindowWidth)
                    image = self._apply_dicom_windowing(image, center, width)
                
                return image
        except Exception as e:
            logger.warning(f"Error loading DICOM {file_path}: {e}")
            raise
    
    def _load_regular_image(self, file_path: str) -> np.ndarray:
        """Load regular image file."""
        image = cv2.imread(file_path, cv2.IMREAD_GRAYSCALE)
        if image is None:
            raise ValueError(f"Could not load image: {file_path}")
        return image
    
    def _apply_dicom_windowing(self, image: np.ndarray, center: float, width: float) -> np.ndarray:
        """Apply DICOM windowing to image."""
        img_min = center - width // 2
        img_max = center + width // 2
        image = np.clip(image, img_min, img_max)
        image = ((image - img_min) / (img_max - img_min) * 255).astype(np.uint8)
        return image
    
    def _preprocess_image(self, image: np.ndarray, augment: bool = False) -> np.ndarray:
        """Preprocess image for model input."""
        
        # Resize to target size
        image = cv2.resize(image, self.target_image_size, interpolation=cv2.INTER_AREA)
        
        # Ensure grayscale format
        if len(image.shape) == 2:
            image = np.expand_dims(image, axis=-1)
        
        # Convert to float32 and normalize
        image = image.astype(np.float32) / 255.0
        
        # Apply augmentation if requested
        if augment:
            image = self._apply_augmentation(image)
        
        return image
    
    def _apply_augmentation(self, image: np.ndarray) -> np.ndarray:
        """Apply basic data augmentation."""
        
        # Random horizontal flip (50% chance)
        if np.random.random() > 0.5:
            image = cv2.flip(image, 1)
        
        # Random brightness adjustment
        brightness_factor = np.random.uniform(0.8, 1.2)
        image = np.clip(image * brightness_factor, 0.0, 1.0)
        
        # Random contrast adjustment
        contrast_factor = np.random.uniform(0.8, 1.2)
        image = np.clip((image - 0.5) * contrast_factor + 0.5, 0.0, 1.0)
        
        return image
    
    def _process_labels(self, labels_raw) -> np.ndarray:
        """Process labels into multi-hot encoding."""
        
        # Initialize label vector
        label_vector = np.zeros(self.num_classes, dtype=np.float32)
        
        # Convert numpy array to list if needed
        if hasattr(labels_raw, 'shape'):
            if labels_raw.shape == ():
                # Single label
                labels_list = [labels_raw.item()]
            else:
                labels_list = labels_raw.tolist()
        else:
            labels_list = labels_raw if isinstance(labels_raw, list) else [labels_raw]
        
        # Map labels to indices (this would be dataset-specific)
        label_to_index = self._get_label_mapping()
        
        for label in labels_list:
            if isinstance(label, bytes):
                label = label.decode('utf-8')
            
            label_str = str(label).strip()
            if label_str in label_to_index:
                idx = label_to_index[label_str]
                if 0 <= idx < self.num_classes:
                    label_vector[idx] = 1.0
        
        return label_vector
    
    def _get_label_mapping(self) -> Dict[str, int]:
        """Get mapping from label names to indices."""
        
        # Default ChestX-ray14 labels
        chestx_labels = [
            'Atelectasis', 'Cardiomegaly', 'Effusion', 'Infiltration',
            'Mass', 'Nodule', 'Pneumonia', 'Pneumothorax',
            'Consolidation', 'Edema', 'Emphysema', 'Fibrosis',
            'Pleural_Thickening', 'Hernia'
        ]
        
        return {label: i for i, label in enumerate(chestx_labels)}
    
    def _get_dummy_image_and_labels(self) -> Tuple[np.ndarray, np.ndarray]:
        """Return dummy image and labels for error cases."""
        dummy_image = np.zeros((*self.target_image_size, 1), dtype=np.float32)
        dummy_labels = np.zeros(self.num_classes, dtype=np.float32)
        return dummy_image, dummy_labels
    
    def get_dataset_info(self) -> Dict:
        """Get information about the dataset."""
        if self._file_index is None:
            self._file_index = self.indexer.get_file_index()
        
        stats = self.indexer.get_dataset_stats()
        memory_info = self.memory_monitor.get_current_memory_info()
        
        return {
            'dataset_path': str(self.dataset_path),
            'total_files': stats['total_files'],
            'total_size_gb': stats['total_size_gb'],
            'target_image_size': self.target_image_size,
            'batch_size': self.batch_size,
            'num_classes': self.num_classes,
            'file_types': stats['file_types'],
            'common_resolutions': stats['common_resolutions'],
            'memory_usage': {
                'ram_gb': memory_info['ram_used_gb'],
                'vram_gb': memory_info['vram_used_gb']
            }
        }


# Utility functions for easy usage
def create_streaming_loader(
    dataset_path: str,
    batch_size: int = 32,
    image_size: Tuple[int, int] = (224, 224),
    memory_limit_gb: float = 16.0
) -> StreamingDataLoader:
    """Create a streaming data loader with sensible defaults."""
    
    return StreamingDataLoader(
        dataset_path=dataset_path,
        target_image_size=image_size,
        batch_size=batch_size,
        memory_limit_gb=memory_limit_gb
    )


def test_streaming_loader(dataset_path: str, max_batches: int = 5):
    """Test the streaming data loader with a small dataset."""
    
    logger.info(f"Testing streaming loader on: {dataset_path}")
    
    # Create loader
    loader = create_streaming_loader(
        dataset_path=dataset_path,
        batch_size=8,  # Small batch for testing
        memory_limit_gb=8.0
    )
    
    # Prepare datasets
    datasets = loader.prepare_dataset(validation_split=0.1, test_split=0.1)
    
    # Test training dataset
    train_ds = datasets['train']
    
    logger.info("Testing streaming data loading...")
    
    for batch_idx, (images, labels) in enumerate(train_ds.take(max_batches)):
        memory_info = loader.memory_monitor.get_current_memory_info()
        
        logger.info(
            f"Batch {batch_idx + 1}: "
            f"Images shape={images.shape}, Labels shape={labels.shape}, "
            f"Memory: {memory_info['ram_used_gb']:.1f}GB RAM, {memory_info['vram_used_gb']:.1f}GB VRAM"
        )
        
        # Check memory limits
        if not loader.memory_monitor.check_memory_limits()["overall_within_limit"]:
            logger.warning("Memory limits exceeded during testing!")
            break
    
    # Get dataset info
    info = loader.get_dataset_info()
    logger.info(f"Dataset info: {json.dumps(info, indent=2)}")
    
    logger.info("Streaming loader test completed!")


# Example usage
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test memory-efficient data loader")
    parser.add_argument("--dataset-path", required=True, help="Path to dataset directory")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size for testing")
    parser.add_argument("--image-size", type=int, default=224, help="Target image size")
    parser.add_argument("--max-batches", type=int, default=5, help="Maximum batches to test")
    parser.add_argument("--memory-limit", type=float, default=16.0, help="Memory limit in GB")
    
    args = parser.parse_args()
    
    # Test the loader
    test_streaming_loader(
        dataset_path=args.dataset_path,
        max_batches=args.max_batches
    )
 