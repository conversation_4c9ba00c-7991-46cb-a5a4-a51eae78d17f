"""
Layer management panel for annotation visibility and organization.
"""

from typing import List, Dict, Any, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QCheckBox, QSlider, QGroupBox,
    QComboBox, QLineEdit, QSpinBox, QFrame, QSplitter,
    QTreeWidget, QTreeWidgetItem, QHeaderView, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QIcon, QPixmap, QColor, QFont, QPalette

from .types import AnnotationTool, ManualAnnotation


class AnnotationLayerItem(QWidget):
    """Widget representing a single annotation layer."""
    
    visibility_changed = Signal(str, bool)  # annotation_id, visible
    opacity_changed = Signal(str, int)      # annotation_id, opacity (0-100)
    selection_changed = Signal(str, bool)   # annotation_id, selected
    lock_changed = Signal(str, bool)        # annotation_id, locked
    
    def __init__(self, annotation: ManualAnnotation, parent=None):
        """Initialize layer item widget."""
        super().__init__(parent)
        self.annotation = annotation
        self.is_updating = False
        
        self._setup_ui()
        self._update_from_annotation()
    
    def _setup_ui(self):
        """Set up the layer item UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(5)
        
        # Visibility checkbox
        self.visibility_checkbox = QCheckBox()
        self.visibility_checkbox.setChecked(True)
        self.visibility_checkbox.setToolTip("Show/Hide annotation")
        self.visibility_checkbox.toggled.connect(self._on_visibility_changed)
        layout.addWidget(self.visibility_checkbox)
        
        # Lock checkbox  
        self.lock_checkbox = QCheckBox()
        self.lock_checkbox.setToolTip("Lock/Unlock annotation for editing")
        self.lock_checkbox.toggled.connect(self._on_lock_changed)
        layout.addWidget(self.lock_checkbox)
        
        # Annotation type icon
        self.type_icon = QLabel()
        self.type_icon.setFixedSize(16, 16)
        self._set_type_icon()
        layout.addWidget(self.type_icon)
        
        # Annotation name/title
        self.title_label = QLabel()
        self.title_label.setMinimumWidth(100)
        font = self.title_label.font()
        font.setPointSize(9)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)
        
        # Opacity slider
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(100)
        self.opacity_slider.setFixedWidth(60)
        self.opacity_slider.setToolTip("Opacity")
        self.opacity_slider.valueChanged.connect(self._on_opacity_changed)
        layout.addWidget(self.opacity_slider)
        
        # Opacity percentage label
        self.opacity_label = QLabel("100%")
        self.opacity_label.setFixedWidth(30)
        self.opacity_label.setAlignment(Qt.AlignCenter)
        font = self.opacity_label.font()
        font.setPointSize(8)
        self.opacity_label.setFont(font)
        layout.addWidget(self.opacity_label)
        
        # Color indicator
        self.color_indicator = QLabel()
        self.color_indicator.setFixedSize(12, 12)
        self._set_color_indicator()
        layout.addWidget(self.color_indicator)
    
    def _set_type_icon(self):
        """Set the type icon based on annotation type."""
        # Create simple icons for different types
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        color_map = {
            AnnotationTool.RECTANGLE: QColor(100, 150, 255),
            AnnotationTool.FREEHAND: QColor(255, 150, 100),
            AnnotationTool.POLYGON: QColor(150, 255, 100),
            AnnotationTool.NONE: QColor(128, 128, 128)
        }
        
        color = color_map.get(self.annotation.tool_type, QColor(128, 128, 128))
        pixmap.fill(color)
        
        self.type_icon.setPixmap(pixmap)
        self.type_icon.setToolTip(f"Type: {self.annotation.tool_type.value}")
    
    def _set_color_indicator(self):
        """Set the color indicator from annotation color."""
        color_str = self.annotation.metadata.get('pen_color', '#FF0000')
        try:
            color = QColor(color_str)
            if color.isValid():
                pixmap = QPixmap(12, 12)
                pixmap.fill(color)
                self.color_indicator.setPixmap(pixmap)
                self.color_indicator.setToolTip(f"Color: {color_str}")
        except:
            pass
    
    def _update_from_annotation(self):
        """Update UI from annotation data."""
        self.is_updating = True
        
        # Title
        title = self.annotation.metadata.get('title', '')
        if not title:
            title = f"{self.annotation.tool_type.value} {self.annotation.id[:8]}"
        self.title_label.setText(title)
        
        # Visibility
        visible = self.annotation.metadata.get('visible', True)
        self.visibility_checkbox.setChecked(visible)
        
        # Lock state
        locked = self.annotation.metadata.get('locked', False)
        self.lock_checkbox.setChecked(locked)
        
        # Opacity
        opacity = self.annotation.metadata.get('opacity', 100)
        self.opacity_slider.setValue(opacity)
        self.opacity_label.setText(f"{opacity}%")
        
        self.is_updating = False
    
    def _on_visibility_changed(self, checked: bool):
        """Handle visibility change."""
        if not self.is_updating:
            self.visibility_changed.emit(self.annotation.id, checked)
    
    def _on_lock_changed(self, checked: bool):
        """Handle lock state change."""
        if not self.is_updating:
            self.lock_changed.emit(self.annotation.id, checked)
    
    def _on_opacity_changed(self, value: int):
        """Handle opacity change."""
        self.opacity_label.setText(f"{value}%")
        if not self.is_updating:
            self.opacity_changed.emit(self.annotation.id, value)
    
    def set_selected(self, selected: bool):
        """Set the selection state of this item."""
        if selected:
            self.setStyleSheet("QWidget { background-color: rgba(100, 150, 255, 50); }")
        else:
            self.setStyleSheet("")
    
    def update_annotation(self, annotation: ManualAnnotation):
        """Update the item with new annotation data."""
        self.annotation = annotation
        self._update_from_annotation()
        self._set_color_indicator()


class AnnotationLayerPanel(QWidget):
    """Panel for managing annotation layers and visibility."""
    
    # Signals
    annotation_visibility_changed = Signal(str, bool)
    annotation_opacity_changed = Signal(str, int)
    annotation_selection_changed = Signal(str, bool)
    annotation_lock_changed = Signal(str, bool)
    filter_changed = Signal(dict)
    
    def __init__(self, parent=None):
        """Initialize the layer panel."""
        super().__init__(parent)
        self.annotation_items: Dict[str, AnnotationLayerItem] = {}
        self.annotations: Dict[str, ManualAnnotation] = {}
        
        self.setFixedWidth(300)
        self.setMinimumHeight(200)
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """Set up the panel UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Panel title
        title_label = QLabel("Annotation Layers")
        title_font = title_label.font()
        title_font.setBold(True)
        title_font.setPointSize(10)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Filter section
        filter_group = self._create_filter_section()
        layout.addWidget(filter_group)
        
        # Layer list
        self.layer_list = QListWidget()
        self.layer_list.setSelectionMode(QListWidget.MultiSelection)
        self.layer_list.setAlternatingRowColors(True)
        self.layer_list.itemSelectionChanged.connect(self._on_selection_changed)
        layout.addWidget(self.layer_list)
        
        # Controls section
        controls_group = self._create_controls_section()
        layout.addWidget(controls_group)
        
        # Statistics section
        stats_group = self._create_statistics_section()
        layout.addWidget(stats_group)
    
    def _create_filter_section(self) -> QGroupBox:
        """Create the filter section."""
        group = QGroupBox("Filters")
        layout = QVBoxLayout(group)
        
        # Type filter
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Type:"))
        self.type_filter = QComboBox()
        self.type_filter.addItem("All Types", "all")
        for tool_type in AnnotationTool:
            if tool_type != AnnotationTool.NONE:
                self.type_filter.addItem(tool_type.value, tool_type)
        self.type_filter.currentDataChanged.connect(self._apply_filters)
        type_layout.addWidget(self.type_filter)
        layout.addLayout(type_layout)
        
        # Search filter
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search:"))
        self.search_filter = QLineEdit()
        self.search_filter.setPlaceholderText("Search annotations...")
        self.search_filter.textChanged.connect(self._apply_filters)
        search_layout.addWidget(self.search_filter)
        layout.addLayout(search_layout)
        
        return group
    
    def _create_controls_section(self) -> QGroupBox:
        """Create the controls section."""
        group = QGroupBox("Layer Controls")
        layout = QVBoxLayout(group)
        
        # Visibility controls
        visibility_layout = QHBoxLayout()
        
        self.show_all_btn = QPushButton("Show All")
        self.show_all_btn.clicked.connect(self._show_all_annotations)
        visibility_layout.addWidget(self.show_all_btn)
        
        self.hide_all_btn = QPushButton("Hide All")
        self.hide_all_btn.clicked.connect(self._hide_all_annotations)
        visibility_layout.addWidget(self.hide_all_btn)
        
        layout.addLayout(visibility_layout)
        
        # Global opacity
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("Global Opacity:"))
        self.global_opacity_slider = QSlider(Qt.Horizontal)
        self.global_opacity_slider.setRange(0, 100)
        self.global_opacity_slider.setValue(100)
        self.global_opacity_slider.valueChanged.connect(self._on_global_opacity_changed)
        opacity_layout.addWidget(self.global_opacity_slider)
        
        self.global_opacity_label = QLabel("100%")
        self.global_opacity_label.setFixedWidth(30)
        opacity_layout.addWidget(self.global_opacity_label)
        
        layout.addLayout(opacity_layout)
        
        return group
    
    def _create_statistics_section(self) -> QGroupBox:
        """Create the statistics section."""
        group = QGroupBox("Statistics")
        layout = QVBoxLayout(group)
        
        self.stats_label = QLabel("No annotations")
        self.stats_label.setWordWrap(True)
        layout.addWidget(self.stats_label)
        
        return group
    
    def _setup_connections(self):
        """Set up signal connections."""
        pass
    
    def add_annotation(self, annotation: ManualAnnotation):
        """Add an annotation to the layer panel."""
        if annotation.id in self.annotation_items:
            return  # Already exists
        
        self.annotations[annotation.id] = annotation
        
        # Create layer item
        layer_item = AnnotationLayerItem(annotation)
        layer_item.visibility_changed.connect(self.annotation_visibility_changed)
        layer_item.opacity_changed.connect(self.annotation_opacity_changed)
        layer_item.selection_changed.connect(self.annotation_selection_changed)
        layer_item.lock_changed.connect(self.annotation_lock_changed)
        
        # Add to list
        list_item = QListWidgetItem()
        list_item.setSizeHint(layer_item.sizeHint())
        self.layer_list.addItem(list_item)
        self.layer_list.setItemWidget(list_item, layer_item)
        
        self.annotation_items[annotation.id] = layer_item
        
        self._update_statistics()
        self._apply_filters()
    
    def remove_annotation(self, annotation_id: str):
        """Remove an annotation from the layer panel."""
        if annotation_id not in self.annotation_items:
            return
        
        # Find and remove the list item
        for i in range(self.layer_list.count()):
            item = self.layer_list.item(i)
            widget = self.layer_list.itemWidget(item)
            if widget and widget.annotation.id == annotation_id:
                self.layer_list.takeItem(i)
                break
        
        # Clean up
        del self.annotation_items[annotation_id]
        if annotation_id in self.annotations:
            del self.annotations[annotation_id]
        
        self._update_statistics()
    
    def update_annotation(self, annotation: ManualAnnotation):
        """Update an existing annotation in the layer panel."""
        if annotation.id in self.annotation_items:
            self.annotations[annotation.id] = annotation
            self.annotation_items[annotation.id].update_annotation(annotation)
    
    def clear_annotations(self):
        """Clear all annotations from the layer panel."""
        self.layer_list.clear()
        self.annotation_items.clear()
        self.annotations.clear()
        self._update_statistics()
    
    def _apply_filters(self):
        """Apply current filters to the annotation list."""
        type_filter = self.type_filter.currentData()
        search_text = self.search_filter.text().lower()
        
        for i in range(self.layer_list.count()):
            item = self.layer_list.item(i)
            widget = self.layer_list.itemWidget(item)
            
            if widget:
                # Type filter
                type_match = (type_filter == "all" or 
                            widget.annotation.tool_type == type_filter)
                
                # Search filter
                annotation_text = (
                    widget.annotation.metadata.get('title', '') + " " +
                    widget.annotation.metadata.get('description', '') + " " +
                    widget.annotation.tool_type.value
                ).lower()
                search_match = search_text in annotation_text
                
                visible = type_match and search_match
                item.setHidden(not visible)
    
    def _update_statistics(self):
        """Update the statistics display."""
        total = len(self.annotations)
        visible = sum(1 for ann in self.annotations.values() 
                     if ann.metadata.get('visible', True))
        locked = sum(1 for ann in self.annotations.values() 
                    if ann.metadata.get('locked', False))
        
        # Count by type
        type_counts = {}
        for ann in self.annotations.values():
            tool_type = ann.tool_type.value
            type_counts[tool_type] = type_counts.get(tool_type, 0) + 1
        
        stats_text = f"Total: {total}\nVisible: {visible}\nLocked: {locked}\n"
        if type_counts:
            stats_text += "Types:\n"
            for type_name, count in type_counts.items():
                stats_text += f"  {type_name}: {count}\n"
        
        self.stats_label.setText(stats_text.strip())
    
    def _show_all_annotations(self):
        """Show all annotations."""
        for annotation_id in self.annotations:
            self.annotation_visibility_changed.emit(annotation_id, True)
    
    def _hide_all_annotations(self):
        """Hide all annotations."""
        for annotation_id in self.annotations:
            self.annotation_visibility_changed.emit(annotation_id, False)
    
    def _on_global_opacity_changed(self, value: int):
        """Handle global opacity slider change."""
        self.global_opacity_label.setText(f"{value}%")
        # Apply to all annotations
        for annotation_id in self.annotations:
            self.annotation_opacity_changed.emit(annotation_id, value)
    
    def _on_selection_changed(self):
        """Handle layer list selection change."""
        selected_items = self.layer_list.selectedItems()
        
        # Update visual selection state
        for annotation_id, layer_item in self.annotation_items.items():
            is_selected = False
            for item in selected_items:
                widget = self.layer_list.itemWidget(item)
                if widget and widget.annotation.id == annotation_id:
                    is_selected = True
                    break
            
            layer_item.set_selected(is_selected)
            self.annotation_selection_changed.emit(annotation_id, is_selected)
    
    def set_annotation_selection(self, annotation_id: str, selected: bool):
        """Set the selection state of an annotation."""
        if annotation_id in self.annotation_items:
            self.annotation_items[annotation_id].set_selected(selected) 