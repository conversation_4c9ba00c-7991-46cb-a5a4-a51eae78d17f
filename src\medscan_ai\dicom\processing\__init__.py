"""
DICOM Processing Module

This module provides pixel data processing capabilities for DICOM medical images.
Includes both standard and optimized processing algorithms for medical imaging pipelines.
"""

from .pixel_processor import PixelProcessor
from .optimized_pixel_processor import (
    MemoryMappedPixelArray,
    ChunkedPixelProcessor, 
    OptimizedPixelProcessor,
)

__all__ = [
    # Standard processing
    "PixelProcessor",
    # Optimized processing
    "OptimizedPixelProcessor",
    "MemoryMappedPixelArray",
    "ChunkedPixelProcessor",
]

# Module version
__version__ = "0.1.0" 