{"system_info": {"cpu_cores": 16, "memory_gb": 15.420856475830078, "platform": "win32", "python_version": "3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]"}, "timestamp": "2025-06-25 19:15:26", "performance_targets": {"dicom_loading_100mb": 5.0, "ai_analysis_standard": 30.0, "ui_responsiveness": 0.1, "memory_efficiency": 500.0, "concurrent_files": 5, "lazy_loading_speedup": 7.0, "opencv_optimization": 3.0}, "test_metrics": [{"operation": "standard_loading_small_color-pl.dcm", "duration_seconds": 0.01141269999789074, "memory_mb": 0.91015625, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (120, 256, 3)"}}, {"operation": "lazy_loading_small_color-pl.dcm", "duration_seconds": 0.003779800026677549, "memory_mb": 0.12264156341552734, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "unknown"}}, {"operation": "standard_loading_small_color-px.dcm", "duration_seconds": 0.002845900016836822, "memory_mb": 0.1961050033569336, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (120, 256, 3)"}}, {"operation": "lazy_loading_small_color-px.dcm", "duration_seconds": 0.0026990000042133033, "memory_mb": 0.11727046966552734, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "unknown"}}, {"operation": "standard_loading_small_emri_small.dcm", "duration_seconds": 0.004721800039988011, "memory_mb": 0.2079334259033203, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (10, 64, 64)"}}, {"operation": "lazy_loading_small_emri_small.dcm", "duration_seconds": 0.004273700003977865, "memory_mb": 0.13923931121826172, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": ""}}, {"operation": "standard_loading_medium_693_J2KR.dcm", "duration_seconds": 0.19009460002416745, "memory_mb": 5.59375, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (512, 512)"}}, {"operation": "lazy_loading_medium_693_J2KR.dcm", "duration_seconds": 0.00336930004414171, "memory_mb": 0.1442708969116211, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "CQ500-CT-310"}}, {"operation": "standard_loading_medium_693_UNCI.dcm", "duration_seconds": 0.005271599977277219, "memory_mb": 1.0573148727416992, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (512, 512)"}}, {"operation": "lazy_loading_medium_693_UNCI.dcm", "duration_seconds": 0.003283599973656237, "memory_mb": 0.5512685775756836, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "CQ500-CT-310"}}, {"operation": "standard_loading_medium_693_UNCR.dcm", "duration_seconds": 0.004266800009645522, "memory_mb": 1.0315942764282227, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (512, 512)"}}, {"operation": "lazy_loading_medium_693_UNCR.dcm", "duration_seconds": 0.002904199995100498, "memory_mb": 0.545954704284668, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "CQ500-CT-310"}}, {"operation": "standard_loading_large_eCT_Supplemental.dcm", "duration_seconds": 0.008873699989635497, "memory_mb": 2.0850067138671875, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (2, 512, 512)"}}, {"operation": "lazy_loading_large_eCT_Supplemental.dcm", "duration_seconds": 0.011799999978393316, "memory_mb": 1.0897178649902344, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "0010"}}, {"operation": "standard_loading_large_MR2_UNCI.dcm", "duration_seconds": 0.010622700036037713, "memory_mb": 4.039878845214844, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (1024, 1024)"}}, {"operation": "lazy_loading_large_MR2_UNCI.dcm", "duration_seconds": 0.005971500009763986, "memory_mb": 2.0517778396606445, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "5MR2"}}, {"operation": "standard_loading_large_MR2_UNCR.dcm", "duration_seconds": 0.008035300008486956, "memory_mb": 4.032547950744629, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (1024, 1024)"}}, {"operation": "lazy_loading_large_MR2_UNCR.dcm", "duration_seconds": 0.005544300016481429, "memory_mb": 2.046072006225586, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "5MR2"}}, {"operation": "standard_loading_xlarge_color3d_jpeg_baseline.dcm", "duration_seconds": 2.7364552000071853, "memory_mb": 955.1164674758911, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (120, 480, 640, 3)"}}, {"operation": "lazy_loading_xlarge_color3d_jpeg_baseline.dcm", "duration_seconds": 0.025089100003242493, "memory_mb": 5.929024696350098, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "204"}}, {"operation": "standard_loading_xlarge_RG1_UNCI.dcm", "duration_seconds": 0.05136460001813248, "memory_mb": 13.768559455871582, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (1955, 1841)"}}, {"operation": "lazy_loading_xlarge_RG1_UNCI.dcm", "duration_seconds": 0.017095200018957257, "memory_mb": 6.915340423583984, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "9RG1"}}, {"operation": "standard_loading_xlarge_RG1_UNCR.dcm", "duration_seconds": 0.034011100011412054, "memory_mb": 13.760807037353516, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (1955, 1841)"}}, {"operation": "lazy_loading_xlarge_RG1_UNCR.dcm", "duration_seconds": 0.014290500024799258, "memory_mb": 6.908371925354004, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "9RG1"}}, {"operation": "standard_resize_693_J2KR.dcm", "duration_seconds": 0.006155700015369803, "memory_mb": 0.73046875, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "ndarray: [[-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n ...\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]]"}}, {"operation": "optimized_resize_693_J2KR.dcm", "duration_seconds": 0.0006114999996498227, "memory_mb": 0.09857845306396484, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "ndarray: [[-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n ...\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]\n [-2000 -2000 -2000 ... -2000 -2000 -2000]]"}}, {"operation": "batch_resize_693_J2KR.dcm", "duration_seconds": 0.0015392000204883516, "memory_mb": 0.45703125, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "[array([[-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       ...,\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000]], dtype=int16), array([[-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       ...,\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000]], dtype=int16), array([[-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       ...,\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000],\n       [-2000, -2000, -2000, ..., -2000, -2000, -2000]], dtype=int16)]"}}, {"operation": "standard_resize_693_UNCI.dcm", "duration_seconds": 0.00023910001618787646, "memory_mb": 0.09711360931396484, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "ndarray: [[-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n ...\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]]"}}, {"operation": "optimized_resize_693_UNCI.dcm", "duration_seconds": 0.0008016999927349389, "memory_mb": 0.09711360931396484, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "ndarray: [[-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n ...\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]\n [-2016 -2016 -2016 ... -2016 -2016 -2016]]"}}, {"operation": "batch_resize_693_UNCI.dcm", "duration_seconds": 0.0004220000118948519, "memory_mb": 0.28882503509521484, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "[array([[-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       ...,\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016]], dtype=int16), array([[-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       ...,\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016]], dtype=int16), array([[-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       ...,\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016],\n       [-2016, -2016, -2016, ..., -2016, -2016, -2016]], dtype=int16)]"}}, {"operation": "ai_extraction_693_J2KR.dcm", "duration_seconds": 0.06467239995254204, "memory_mb": 8.649114608764648, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (512, 512)"}}, {"operation": "tflite_preprocessing_693_J2KR.dcm", "duration_seconds": 0.03304309997474775, "memory_mb": 1.7347288131713867, "cpu_percent": 0.0, "success": false, "error_message": "TensorFlow Lite preprocessing failed for xray_anomaly_detection:latest: No versions found for model 'xray_anomaly_detection'", "additional_data": {"result": null}}, {"operation": "ai_extraction_693_UNCI.dcm", "duration_seconds": 0.026740400004200637, "memory_mb": 9.044487953186035, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "tuple: (512, 512)"}}, {"operation": "tflite_preprocessing_693_UNCI.dcm", "duration_seconds": 0.01478049997240305, "memory_mb": 1.0381460189819336, "cpu_percent": 0.0, "success": false, "error_message": "TensorFlow Lite preprocessing failed for xray_anomaly_detection:latest: No versions found for model 'xray_anomaly_detection'", "additional_data": {"result": null}}, {"operation": "worker_creation", "duration_seconds": 0.001745699963066727, "memory_mb": 0.05859375, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "[<medscan_ai.core.threading.dicom_worker.DicomLoadingWorker(0x11213824db0) at 0x00000112147334C0>, 'ai_worker_placeholder', 'batch_worker_placeholder', 'image_worker_placeholder']"}}, {"operation": "concurrent_file_processing", "duration_seconds": 0.011642699944786727, "memory_mb": 0.1732187271118164, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": 3}}, {"operation": "memory_efficiency_eCT_Supplemental.dcm", "duration_seconds": 0.007980900001712143, "memory_mb": 1.1003608703613281, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "0010_1.3.6.1.4.1.5962.1.2.10.1166562673.14401"}}, {"operation": "memory_efficiency_MR2_UNCI.dcm", "duration_seconds": 0.007118500012438744, "memory_mb": 2.0517778396606445, "cpu_percent": 0.0, "success": true, "error_message": null, "additional_data": {"result": "5MR2_1.3.6.1.4.1.5962.1.2.5.20040826185059.5457"}}], "summary": {"total_tests": 38, "successful_tests": 36, "failed_tests": 2, "average_duration": 0.09160405556031037, "average_memory": 29.20021544562446, "max_memory": 955.1164674758911, "total_test_time": 3.439544200897217, "performance_analysis": ["Lazy loading speedup: 30.6x", "✅ Lazy loading target achieved", "OpenCV optimization speedup: 4.5x", "✅ Memory efficiency target achieved"], "targets_met": 2, "targets_missed": 0}}