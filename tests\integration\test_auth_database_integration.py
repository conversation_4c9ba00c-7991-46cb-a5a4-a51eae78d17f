"""
Integration tests for Authentication service and database persistence flow.

This module tests the complete authentication flow including user registration,
login, session management, and database persistence across multiple components.
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, Optional
import hashlib

# Import authentication and database modules
from src.medscan_ai.security.authentication.auth_service import AuthenticationService
from src.medscan_ai.security.authentication.credential_service import UserCredentialService
from src.medscan_ai.security.authentication.session_manager import JWTSessionManager
from src.medscan_ai.security.access_control.rbac_manager import RBACManager
from src.medscan_ai.database.repositories.base import BaseRepository
from src.medscan_ai.database.models.user import User


@pytest.mark.integration
@pytest.mark.security
@pytest.mark.database
@pytest.mark.hipaa
class TestAuthDatabaseIntegration:
    """Integration tests for authentication service and database persistence."""

    def setup_method(self):
        """Set up test environment for each test method."""
        self.temp_dir = tempfile.mkdtemp(prefix="auth_test_")
        
    def teardown_method(self):
        """Clean up after each test method."""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @pytest.fixture
    def mock_database_session(self):
        """Create comprehensive mock database session."""
        session = Mock()
        session.query.return_value = session
        session.filter.return_value = session
        session.filter_by.return_value = session
        session.first.return_value = None
        session.all.return_value = []
        session.add.return_value = None
        session.commit.return_value = None
        session.rollback.return_value = None
        session.delete.return_value = None
        return session

    @pytest.fixture
    def mock_user_data(self):
        """Create realistic test user data."""
        return {
            "username": "dr_smith",
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "full_name": "Dr. John Smith",
            "role": "radiologist",
            "department": "radiology",
            "facility": "Central Hospital",
            "medical_license": "MD123456",
            "phone": "******-0123"
        }

    @pytest.fixture
    def mock_repositories(self):
        """Create mock repository ecosystem."""
        user_repo = Mock()
        session_repo = Mock()
        audit_repo = Mock()

        # Configure user repository with dynamic update side effect
        def user_update_side_effect(entity_id, data=None, **kwargs):
            if data is None:
                data = {}
            data.update(kwargs)
            response = {"id": entity_id, "username": "dr_smith", "last_login": datetime.utcnow()}
            response.update(data)
            return response

        user_repo.create.return_value = {"id": 1, "username": "dr_smith", "email": "<EMAIL>"}
        user_repo.find_by_username.return_value = None
        user_repo.find_by_email.return_value = None
        user_repo.update.side_effect = user_update_side_effect

        # Configure session repository with dynamic update side effect
        def session_update_side_effect(entity_id, data=None, **kwargs):
            if data is None:
                data = {}
            data.update(kwargs)
            response = {"id": 1, "session_id": entity_id, "user_id": 1, "is_active": False, "last_activity": datetime.utcnow()}
            response.update(data)
            return response

        session_repo.create.return_value = {"id": 1, "session_id": "test-session-123", "user_id": 1}
        session_repo.find_by_session_id.return_value = None
        session_repo.find_by_id.return_value = {"session_id": "test-session-123", "user_id": 1, "is_active": True}
        session_repo.update.side_effect = session_update_side_effect
        session_repo.delete.return_value = True

        # Configure audit repository with dynamic event type
        def audit_create_side_effect(**kwargs):
            response = {"id": 1, "user_id": 1}
            response.update(kwargs)
            return response

        audit_repo.create.side_effect = audit_create_side_effect

        return {
            "user": user_repo,
            "session": session_repo,
            "audit": audit_repo
        }

    def test_complete_user_registration_flow(self, temp_db, mock_audit_repository):
        """Test complete user registration from credential creation to database storage."""
        with temp_db() as session:
            # Step 1: Initialize services
            credential_service = UserCredentialService(db_session=session)
            session_manager = JWTSessionManager(secret_key="test-secret-key-123")
            
            # Step 2: Verify password
            test_password = "SecureTestPassword123!"
            
            # Step 3: Create test user directly in database
            test_user = User(
                username="test_integration_user",
                email="<EMAIL>", 
                password_hash="mock_hash",
                password_salt="mock_salt",
                role="Technician",
                is_verified=True,  # Set verified to allow login
                is_active=True
            )
            session.add(test_user)
            session.commit()  # Remove await
            
            # Step 4: Mock credential service methods to simulate real operations
            with patch.object(credential_service, '_find_user_by_login', return_value=test_user):
                with patch.object(credential_service.password_manager, 'verify_password', return_value=True):
                    with patch.object(credential_service, '_log_audit_event') as mock_audit:
                        
                        # Step 5: Test authentication flow
                        authenticated_user = credential_service.authenticate_user(
                            username="test_integration_user",
                            password=test_password,
                            ip_address="*************"
                        )
                        
                        # Step 6: Verify user authenticated successfully  
                        assert authenticated_user is not None
                        assert authenticated_user.username == "test_integration_user"
                        assert authenticated_user.email == "<EMAIL>"
                        
                        # Step 7: Verify audit log was called
                        mock_audit.assert_called()
                        
                        # Step 8: Verify database consistency
                        db_user = session.query(User).filter(User.username == "test_integration_user").first()
                        assert db_user is not None
                        assert db_user.email == "<EMAIL>"

    def test_user_login_with_session_management(self, mock_repositories, mock_database_session):
        """Test user login with JWT session creation and database tracking."""
        
        # Create mock user data within test
        mock_user_data = {
            "id": 1,
            "username": "test_user",
            "email": "<EMAIL>",
            "role": "Technician"
        }
        
        with patch('src.medscan_ai.database.engine.get_session') as mock_get_session:
            mock_get_session.return_value = mock_database_session
            
            # Step 1: Initialize services
            credential_service = UserCredentialService(db_session=mock_database_session)
            session_manager = JWTSessionManager(secret_key="test-secret-key-456")
            
            # Step 2: Verify password
            with patch.object(credential_service, 'authenticate_user') as mock_auth:
                mock_auth.return_value = mock_user_data  # Return user data on success
                
                authenticated_user = credential_service.authenticate_user(
                    username=mock_user_data["username"],
                    password="TestPassword123!",
                    ip_address="************"
                )
                
                assert authenticated_user is not None
                
            # Step 3: Create JWT session
            user_session_data = {
                "user_id": mock_user_data["id"],
                "email": mock_user_data["email"],
                "username": mock_user_data["username"],
                "roles": [mock_user_data["role"]],
                "permissions": ["read", "write"]
            }
            
            access_token, refresh_token, session_info = session_manager.create_session(
                user_data=user_session_data,
                client_info={"ip_address": "************", "user_agent": "Test Browser"}
            )
            
            # Step 4: Validate JWT tokens
            assert access_token is not None
            assert refresh_token is not None
            assert session_info.user_id == mock_user_data["id"]
            
            # Step 5: Verify session in database (mock)
            session_record = mock_repositories["session"].create({
                "session_id": session_info.session_id,
                "user_id": mock_user_data["id"],
                "access_token_hash": hashlib.sha256(access_token.encode()).hexdigest(),
                "refresh_token_hash": hashlib.sha256(refresh_token.encode()).hexdigest(),
                "ip_address": "************",
                "created_at": session_info.created_at,
                "expires_at": session_info.expires_at
            })
            
            # Step 6: Log session creation audit
            audit_record = mock_repositories["audit"].create({
                "event_type": "user_login",
                "user_id": mock_user_data["id"],
                "session_id": session_info.session_id,
                "details": {"login_method": "password", "ip_address": "************"},
                "timestamp": datetime.utcnow()
            })
            
            # Verify integration workflow
            mock_repositories["session"].create.assert_called_once()
            mock_repositories["audit"].create.assert_called_once()
            assert session_record["user_id"] == mock_user_data["id"]
            assert audit_record["event_type"] == "user_login"

    def test_failed_login_with_security_measures(self, mock_repositories):
        """Test failed login attempts with security lockdown and audit logging."""
        
        # Create mock user data within test
        mock_user_data = {
            "id": 1,
            "username": "test_user",
            "email": "<EMAIL>",
            "role": "Technician",
            "failed_login_attempts": 0
        }
        
        # Step 1: Initialize credential service
        credential_service = UserCredentialService(db_session=mock_repositories["user"].session)
        
        # Step 2: Simulate multiple failed login attempts
        failed_attempts = []
        for attempt in range(1, 4):
            with patch.object(credential_service, 'authenticate_user') as mock_auth:
                mock_auth.return_value = None  # Failed authentication
                
                result = credential_service.authenticate_user(
                    username=mock_user_data["username"],
                    password="WrongPassword123!",
                    ip_address="************"
                )
                
                assert result is None
                failed_attempts.append(attempt)
                
                # Step 3: Log failed attempt
                audit_record = mock_repositories["audit"].create({
                    "event_type": "login_failed",
                    "user_id": mock_user_data["id"],
                    "details": {
                        "reason": "invalid_password",
                        "attempt_number": attempt,
                        "ip_address": "************"
                    },
                    "timestamp": datetime.utcnow()
                })
                
        # Step 4: Check account lockout after 3 failed attempts
        user_update = mock_repositories["user"].update(mock_user_data["id"], {
            "failed_login_attempts": 3,
            "account_locked_until": datetime.utcnow() + timedelta(minutes=15)
        })
        
        # Step 5: Log account lockout
        lockout_audit = mock_repositories["audit"].create({
            "event_type": "account_locked",
            "user_id": mock_user_data["id"],
            "details": {"reason": "max_failed_attempts", "lockout_duration": "15_minutes"},
            "timestamp": datetime.utcnow()
        })
        
        # Verify security measures
        assert len(failed_attempts) == 3
        assert user_update["failed_login_attempts"] == 3
        assert lockout_audit["event_type"] == "account_locked"

    def test_session_cleanup_and_logout(self, mock_repositories):
        """Test session cleanup on logout with database updates and audit logging."""
        
        # Step 1: Create mock active session
        active_session = {
            "session_id": "test-session-789",
            "user_id": 1,
            "created_at": datetime.utcnow() - timedelta(hours=2),
            "last_activity": datetime.utcnow() - timedelta(minutes=30),
            "expires_at": datetime.utcnow() + timedelta(hours=6),
            "is_active": True
        }
        
        mock_repositories["session"].find_by_id.return_value = active_session
        
        # Step 2: Initialize session manager
        session_manager = JWTSessionManager(secret_key="test-secret-key-789")
        
        # Step 3: Revoke session (logout)
        with patch.object(session_manager, 'revoke_session') as mock_revoke:
            mock_revoke.return_value = True
            
            revoked = session_manager.revoke_session(active_session["session_id"])
            assert revoked is True
        
        # Step 4: Update session in database
        updated_session = mock_repositories["session"].update(
            active_session["session_id"],
            {
                "is_active": False,
                "revoked_at": datetime.utcnow(),
                "revoked_reason": "user_logout"
            }
        )
        
        # Step 5: Log logout audit event
        audit_record = mock_repositories["audit"].create({
            "event_type": "user_logout",
            "user_id": active_session["user_id"],
            "session_id": active_session["session_id"],
            "details": {"logout_method": "manual", "session_duration": "2_hours"},
            "timestamp": datetime.utcnow()
        })
        
        # Verify cleanup process
        mock_repositories["session"].update.assert_called_once()
        mock_repositories["audit"].create.assert_called_once()
        
        # Verify session state
        assert updated_session["is_active"] is False
        assert audit_record["event_type"] == "user_logout"

    def test_concurrent_authentication_operations(self):
        """Test concurrent authentication operations to verify thread safety."""
        
        # Step 1: Create multiple mock authentication attempts
        auth_tasks = []
        
        for i in range(5):
            # Mock each authentication attempt
            task_data = {
                "username": f"user_{i}",
                "password": "TestPassword123!",
                "expected_result": i % 2 == 0  # Every other attempt succeeds
            }
            auth_tasks.append(task_data)
        
        # Step 2: Process concurrent authentication (simulated)
        results = []
        for task in auth_tasks:
            # Simulate authentication result
            result = {
                "username": task["username"],
                "success": task["expected_result"],
                "timestamp": datetime.utcnow()
            }
            results.append(result)
        
        # Step 3: Verify all operations completed
        assert len(results) == 5
        
        # Step 4: Verify success/failure distribution
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        assert len(successful) == 3  # users 0, 2, 4
        assert len(failed) == 2     # users 1, 3
        
        # Step 5: Verify no data corruption (all usernames unique)
        usernames = [r["username"] for r in results]
        assert len(set(usernames)) == 5

    def test_rbac_integration_with_database(self, mock_repositories):
        """Test Role-Based Access Control integration with database operations."""
        
        # Create mock user data within test
        mock_user_data = {
            "id": 1,
            "username": "test_user",
            "email": "<EMAIL>",
            "role": "Technician"
        }
        
        # Step 1: Initialize RBAC manager
        rbac_manager = RBACManager()
        
        # Step 2: Mock role assignment operation
        with patch.object(rbac_manager, 'assign_role_to_user') as mock_assign:
            mock_assign.return_value = True
            
            # Step 3: Assign role to user
            role_assigned = rbac_manager.assign_role_to_user(
                user_id=mock_user_data["id"],
                role_id="role_123",
                assigned_by="admin"
            )
            assert role_assigned is True
        
        # Step 4: Update user role in database
        updated_user = mock_repositories["user"].update(
            mock_user_data["id"],
            {"role": mock_user_data["role"]}
        )
        
        # Step 5: Create role assignment audit record
        audit_record = mock_repositories["audit"].create({
            "event_type": "role_assigned",
            "user_id": mock_user_data["id"],
            "details": {
                "role": mock_user_data["role"],
                "assigned_by": "admin",
                "assignment_reason": "user_registration"
            },
            "timestamp": datetime.utcnow()
        })
        
        # Verify RBAC integration
        mock_repositories["user"].update.assert_called_once()
        mock_repositories["audit"].create.assert_called_once()
        
        # Verify role assignment
        assert updated_user["role"] == mock_user_data["role"]
        assert audit_record["event_type"] == "role_assigned"