"""
Batch Processing Worker for Multi-File Operations

Provides background batch processing capabilities with queue management,
progress tracking, and resource optimization for medical imaging workflows.
"""

import time
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
from queue import Queue, Empty
from dataclasses import dataclass, field
from enum import Enum

from .base_worker import MedScanWorker, WorkerError

logger = logging.getLogger(__name__)


class JobPriority(Enum):
    """Priority levels for batch jobs."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class JobStatus(Enum):
    """Status of individual batch jobs."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchJob:
    """Individual job in a batch processing queue."""
    job_id: str
    job_type: str
    input_data: Any
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: JobPriority = JobPriority.NORMAL
    status: JobStatus = JobStatus.PENDING
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    processing_time: float = 0.0
    retry_count: int = 0
    max_retries: int = 2
    
    @property
    def duration(self) -> float:
        """Get job processing duration."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0


class BatchProcessingResult:
    """Container for batch processing results."""
    
    def __init__(self, batch_id: str):
        self.batch_id = batch_id
        self.jobs: List[BatchJob] = []
        self.total_jobs = 0
        self.completed_jobs = 0
        self.failed_jobs = 0
        self.cancelled_jobs = 0
        self.total_processing_time = 0.0
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.errors: List[str] = []
    
    def add_job(self, job: BatchJob) -> None:
        """Add a job to the batch result."""
        self.jobs.append(job)
        self.total_jobs += 1
        
        if job.status == JobStatus.COMPLETED:
            self.completed_jobs += 1
        elif job.status == JobStatus.FAILED:
            self.failed_jobs += 1
            if job.error:
                self.errors.append(f"Job {job.job_id}: {job.error}")
        elif job.status == JobStatus.CANCELLED:
            self.cancelled_jobs += 1
        
        self.total_processing_time += job.processing_time
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_jobs == 0:
            return 0.0
        return (self.completed_jobs / self.total_jobs) * 100.0
    
    @property
    def duration(self) -> float:
        """Get total batch duration."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0


class BatchProcessingWorker(MedScanWorker):
    """
    Background worker for batch processing operations.
    
    Features:
    - Job queue management with priorities
    - Parallel processing with configurable worker threads
    - Progress tracking for entire batch and individual jobs
    - Resource management and memory optimization
    - Error handling and retry mechanisms
    - Cancellation support
    """
    
    def __init__(self,
                 jobs: List[BatchJob],
                 batch_id: str = None,
                 max_workers: int = None,
                 job_processor: Callable[[BatchJob], Any] = None,
                 continue_on_error: bool = True,
                 parent=None):
        """
        Initialize batch processing worker.
        
        Args:
            jobs: List of jobs to process
            batch_id: Unique identifier for this batch
            max_workers: Maximum number of parallel workers (None for auto)
            job_processor: Function to process individual jobs
            continue_on_error: Whether to continue processing if individual jobs fail
            parent: Parent QObject
        """
        self.jobs = jobs.copy()  # Create a copy to avoid external modifications
        self.batch_id = batch_id or f"batch_{int(time.time())}"
        self.continue_on_error = continue_on_error
        self.job_processor = job_processor
        
        # Determine optimal worker count
        if max_workers is None:
            import os
            self.max_workers = min(4, os.cpu_count() or 2)  # Conservative default
        else:
            self.max_workers = max(1, min(max_workers, len(jobs)))
        
        # Initialize base worker
        operation_name = f"Batch Processing ({len(jobs)} jobs, {self.max_workers} workers)"
        super().__init__(operation_name, parent)
        
        # Job management
        self.job_queue = Queue()
        self.processing_jobs: Dict[str, BatchJob] = {}
        self.completed_jobs: List[BatchJob] = []
        self.failed_jobs: List[BatchJob] = []
        
        # Threading controls
        self.worker_threads: List[threading.Thread] = []
        self.results_lock = threading.Lock()
        
        # Prepare job queue
        self._prepare_job_queue()
        
        # Result container
        self.batch_result = BatchProcessingResult(self.batch_id)
    
    def _prepare_job_queue(self):
        """Prepare and prioritize job queue."""
        # Sort jobs by priority (highest first)
        sorted_jobs = sorted(self.jobs, key=lambda job: job.priority.value, reverse=True)
        
        # Add jobs to queue
        for job in sorted_jobs:
            job.status = JobStatus.PENDING
            self.job_queue.put(job)
    
    def do_work(self) -> BatchProcessingResult:
        """
        Perform batch processing work.
        
        Returns:
            BatchProcessingResult with all job results
        """
        self.batch_result.start_time = time.time()
        
        try:
            # Validate job processor
            if self.job_processor is None:
                raise WorkerError("No job processor function provided")
            
            # Start worker threads
            self.update_progress(5, "Starting worker threads...")
            self._start_worker_threads()
            
            # Monitor progress
            self._monitor_batch_progress()
            
            # Wait for completion
            self._wait_for_completion()
            
            # Finalize results
            self._finalize_batch_results()
            
            return self.batch_result
            
        except Exception as e:
            self._handle_batch_error(str(e))
            raise WorkerError(f"Batch processing failed: {str(e)}")
        
        finally:
            self._cleanup_worker_threads()
    
    def _start_worker_threads(self):
        """Start worker threads for parallel processing."""
        for i in range(self.max_workers):
            thread = threading.Thread(
                target=self._worker_thread_function,
                name=f"BatchWorker-{i+1}",
                daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)
        
        logger.info(f"Started {len(self.worker_threads)} worker threads for batch {self.batch_id}")
    
    def _worker_thread_function(self):
        """Worker thread function for processing jobs."""
        thread_name = threading.current_thread().name
        
        while not self.is_cancellation_requested():
            try:
                # Get next job from queue (with timeout)
                job = self.job_queue.get(timeout=1.0)
                
                if job is None:  # Sentinel value for shutdown
                    break
                
                # Process job
                self._process_single_job(job, thread_name)
                
                # Mark queue task as done
                self.job_queue.task_done()
                
            except Empty:
                # Timeout - check if there are more jobs or if we should exit
                if self.job_queue.empty() and not self._has_active_jobs():
                    break
                continue
                
            except Exception as e:
                logger.error(f"Worker thread {thread_name} error: {e}")
                if not self.continue_on_error:
                    break
        
        logger.debug(f"Worker thread {thread_name} finished")
    
    def _process_single_job(self, job: BatchJob, worker_name: str):
        """Process a single job in a worker thread."""
        job.start_time = time.time()
        job.status = JobStatus.RUNNING
        
        with self.results_lock:
            self.processing_jobs[job.job_id] = job
        
        logger.debug(f"Worker {worker_name} processing job {job.job_id}")
        
        try:
            # Check for cancellation before processing
            if self.is_cancellation_requested():
                job.status = JobStatus.CANCELLED
                return
            
            # Process the job
            job.result = self.job_processor(job)
            job.status = JobStatus.COMPLETED
            job.end_time = time.time()
            job.processing_time = job.end_time - job.start_time
            
            with self.results_lock:
                self.completed_jobs.append(job)
                if job.job_id in self.processing_jobs:
                    del self.processing_jobs[job.job_id]
            
            self.increment_processed_count(1)
            logger.debug(f"Job {job.job_id} completed in {job.processing_time:.2f}s")
            
        except Exception as e:
            job.error = str(e)
            job.status = JobStatus.FAILED
            job.end_time = time.time()
            job.processing_time = job.end_time - job.start_time
            
            with self.results_lock:
                self.failed_jobs.append(job)
                if job.job_id in self.processing_jobs:
                    del self.processing_jobs[job.job_id]
            
            self.increment_error_count(1)
            logger.warning(f"Job {job.job_id} failed: {e}")
            
            # Handle retry logic
            if job.retry_count < job.max_retries and self.continue_on_error:
                job.retry_count += 1
                job.status = JobStatus.PENDING
                job.error = None
                self.job_queue.put(job)
                logger.info(f"Retrying job {job.job_id} (attempt {job.retry_count + 1})")
    
    def _monitor_batch_progress(self):
        """Monitor and report batch processing progress."""
        total_jobs = len(self.jobs)
        
        while not self.is_cancellation_requested():
            with self.results_lock:
                completed = len(self.completed_jobs)
                failed = len(self.failed_jobs)
                processing = len(self.processing_jobs)
                pending = self.job_queue.qsize()
            
            # Calculate progress
            processed = completed + failed
            if total_jobs > 0:
                progress = int((processed / total_jobs) * 100)
                progress = min(95, progress)  # Reserve 5% for finalization
            else:
                progress = 100
            
            # Update progress message
            status_msg = f"Processed: {processed}/{total_jobs}, Active: {processing}, Pending: {pending}"
            self.update_progress(progress, status_msg)
            
            # Check if processing is complete
            if processed >= total_jobs and processing == 0:
                break
            
            # Small delay to avoid excessive updates
            time.sleep(0.5)
    
    def _wait_for_completion(self):
        """Wait for all worker threads to complete."""
        # Add sentinel values to signal shutdown
        for _ in self.worker_threads:
            self.job_queue.put(None)
        
        # Wait for all threads to finish
        for thread in self.worker_threads:
            thread.join(timeout=30.0)  # 30 second timeout per thread
            if thread.is_alive():
                logger.warning(f"Worker thread {thread.name} did not finish gracefully")
    
    def _finalize_batch_results(self):
        """Finalize batch processing results."""
        self.batch_result.end_time = time.time()
        
        # Add all jobs to result
        all_jobs = self.completed_jobs + self.failed_jobs
        for job in all_jobs:
            self.batch_result.add_job(job)
        
        # Final progress update
        success_rate = self.batch_result.success_rate
        duration = self.batch_result.duration
        
        self.update_progress(100, f"Batch completed: {success_rate:.1f}% success rate in {duration:.1f}s")
        
        logger.info(
            f"Batch {self.batch_id} completed: "
            f"{self.batch_result.completed_jobs}/{self.batch_result.total_jobs} successful, "
            f"Duration: {duration:.2f}s"
        )
    
    def _handle_batch_error(self, error_message: str):
        """Handle batch-level errors."""
        self.batch_result.end_time = time.time()
        self.batch_result.errors.append(f"Batch error: {error_message}")
        
        # Mark any pending/processing jobs as cancelled
        with self.results_lock:
            for job in self.processing_jobs.values():
                job.status = JobStatus.CANCELLED
                self.batch_result.add_job(job)
    
    def _cleanup_worker_threads(self):
        """Cleanup worker thread resources."""
        try:
            # Clear job queue
            while not self.job_queue.empty():
                try:
                    self.job_queue.get_nowait()
                except Empty:
                    break
            
            # Clear thread references
            self.worker_threads.clear()
            self.processing_jobs.clear()
            
        except Exception as e:
            logger.warning(f"Error cleaning up worker threads: {e}")
    
    def _has_active_jobs(self) -> bool:
        """Check if there are any active (processing) jobs."""
        with self.results_lock:
            return len(self.processing_jobs) > 0
    
    def get_batch_statistics(self) -> Dict[str, Any]:
        """Get batch-specific statistics."""
        base_stats = self.get_current_stats()
        
        with self.results_lock:
            batch_stats = {
                'batch_id': self.batch_id,
                'total_jobs': len(self.jobs),
                'completed_jobs': len(self.completed_jobs),
                'failed_jobs': len(self.failed_jobs),
                'processing_jobs': len(self.processing_jobs),
                'pending_jobs': self.job_queue.qsize(),
                'max_workers': self.max_workers,
                'active_workers': len([t for t in self.worker_threads if t.is_alive()]),
                'continue_on_error': self.continue_on_error,
                'success_rate': self.batch_result.success_rate if self.batch_result.total_jobs > 0 else 0.0
            }
        
        return {**base_stats, **batch_stats}
    
    def cleanup_resources(self) -> None:
        """Cleanup batch processing resources."""
        try:
            self._cleanup_worker_threads()
            
            # Clear results
            self.completed_jobs.clear()
            self.failed_jobs.clear()
            
        except Exception as e:
            logger.warning(f"Error cleaning up batch worker resources: {e}")