"""
Medical image GUI components.
Provides interactive viewer, AI display, and annotation tools.
"""

# Core components
from .core import (
    InteractiveImageViewer,
    OverlayManager,
    LayerType,
    OverlayLayer,
    ImageDisplayHelper,
    numpy_to_qimage,
    numpy_to_qpixmap,
    scale_pixmap_to_fit,
    create_error_pixmap,
    get_image_info_text,
    apply_window_level_to_display
)

# AI display components
from .ai_display import (
    AIFindingsVisualizer,
    AIMetadataPanel,
    DifferentialDiagnosis,
    AIFindingDialog
)

# Annotation components
from .annotations import (
    AnnotationTool,
    ManualAnnotation,
    AnnotationToolBase,
    RectangleTool,
    PolygonTool,
    FreehandTool,
    EraserTool,
    AnnotationManager
)

# Main window and utilities
from .main import *
from .utils import *

__all__ = [
    # Core components
    'InteractiveImageViewer',
    'OverlayManager',
    'LayerType', 
    'OverlayLayer',
    'ImageDisplayHelper',
    
    # Utility functions
    'numpy_to_qimage',
    'numpy_to_qpixmap',
    'scale_pixmap_to_fit',
    'create_error_pixmap',
    'get_image_info_text',
    'apply_window_level_to_display',
    
    # AI display
    'AIFindingsVisualizer',
    'AIMetadataPanel',
    'DifferentialDiagnosis',
    'AIFindingDialog',
    
    # Annotations
    'AnnotationTool',
    'ManualAnnotation',
    'AnnotationToolBase',
    'RectangleTool',
    'PolygonTool',
    'FreehandTool',
    'EraserTool',
    'AnnotationManager',
]
