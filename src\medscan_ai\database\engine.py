"""Database engine and session management for MedScan AI.

Provides SQLAlchemy engine configuration, session management,
and database initialization with SQLCipher encryption support.
"""

import logging
import os
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, Optional

from sqlalchemy import Engine, create_engine, event, text
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import StaticPool

from .models.base import Base

logger = logging.getLogger(__name__)


class DatabaseEngine:
    """Database engine manager for MedScan AI."""

    def __init__(self, database_url: str = None, encryption_key: str = None):
        self.encryption_key = encryption_key
        self.database_url = database_url or self._get_default_database_url()
        self.engine: Optional[Engine] = None
        self.session_factory: Optional[sessionmaker] = None

    def _check_sqlcipher_available(self) -> bool:
        """Check if SQLCipher is available for use."""
        try:
            import pysqlcipher3

            return True
        except ImportError:
            try:
                import sqlcipher3

                return True
            except ImportError:
                return False

    def _get_default_database_url(self) -> str:
        """Get default database URL with SQLCipher support if available."""
        data_dir = Path("data/database")
        data_dir.mkdir(parents=True, exist_ok=True)
        db_path = data_dir / "medscan.db"

        # Use SQLCipher dialect if encryption key is provided and SQLCipher is available
        if self.encryption_key and self._check_sqlcipher_available():
            logger.info("SQLCipher is available, using encrypted database")
            return f"sqlite+pysqlcipher://:{self.encryption_key}@/{db_path}"
        elif self.encryption_key and not self._check_sqlcipher_available():
            logger.warning(
                "SQLCipher not available, falling back to unencrypted SQLite database"
            )
            logger.warning("Install pysqlcipher3 or sqlcipher3 for encryption support")
            return f"sqlite:///{db_path}"
        else:
            return f"sqlite:///{db_path}"

    def create_engine(self, **engine_kwargs) -> Engine:
        """Create SQLAlchemy engine with SQLCipher support."""
        if self.engine:
            return self.engine

        engine_config = {
            "echo": False,
            "pool_pre_ping": True,
            "connect_args": {"check_same_thread": False, "timeout": 30},
        }

        # Configure for SQLite/SQLCipher
        if self.database_url.startswith(("sqlite", "sqlite+pysqlcipher")):
            engine_config["poolclass"] = StaticPool
            # Note: SQLite doesn't support pool_size and max_overflow with StaticPool

            # Add SQLCipher specific pragmas
            if self._is_encrypted:
                self._configure_sqlcipher_pragmas(engine_config)

        engine_config.update(engine_kwargs)
        self.engine = create_engine(self.database_url, **engine_config)

        # Set up event listeners for SQLCipher
        self._setup_sqlcipher_events()

        encryption_status = "encrypted" if self._is_encrypted else "unencrypted"
        logger.info(
            f"Database engine created ({encryption_status}): {self.database_url}"
        )
        return self.engine

    @property
    def _is_encrypted(self) -> bool:
        """Check if database is actually encrypted."""
        return (
            bool(self.encryption_key)
            and self._check_sqlcipher_available()
            and self.database_url.startswith("sqlite+pysqlcipher")
        )

    def _configure_sqlcipher_pragmas(self, engine_config: dict):
        """Configure SQLCipher specific pragmas."""
        # SQLCipher pragmas for security and performance
        sqlcipher_pragmas = {
            "cipher": "aes-256-cbc",  # Use AES-256-CBC encryption
            "kdf_iter": "64000",  # Key derivation iterations (PBKDF2)
            "cipher_page_size": "4096",  # Page size for encrypted database
            "cipher_use_hmac": "ON",  # Use HMAC for authentication
        }

        # Add pragmas to connect_args
        if "connect_args" not in engine_config:
            engine_config["connect_args"] = {}
        engine_config["connect_args"]["sqlcipher_pragmas"] = sqlcipher_pragmas

    def _setup_sqlcipher_events(self):
        """Set up SQLCipher specific event listeners."""
        if not self.engine or not self._is_encrypted:
            return

        @event.listens_for(self.engine, "connect")
        def set_sqlcipher_pragma(dbapi_connection, connection_record):
            """Set SQLCipher pragmas on connection."""
            cursor = dbapi_connection.cursor()

            # Set encryption key (already in URL, but ensuring it's set)
            if self.encryption_key:
                cursor.execute(f"PRAGMA key = '{self.encryption_key}'")

            # Set additional SQLCipher pragmas for security
            cursor.execute("PRAGMA cipher = 'aes-256-cbc'")
            cursor.execute("PRAGMA kdf_iter = 64000")
            cursor.execute("PRAGMA cipher_page_size = 4096")
            cursor.execute("PRAGMA cipher_use_hmac = ON")

            # Standard SQLite pragmas for performance
            cursor.execute("PRAGMA journal_mode = WAL")
            cursor.execute("PRAGMA synchronous = NORMAL")
            cursor.execute("PRAGMA cache_size = 10000")
            cursor.execute("PRAGMA temp_store = MEMORY")
            cursor.execute("PRAGMA foreign_keys = ON")

            cursor.close()
            logger.debug("SQLCipher pragmas set successfully")

    def get_session(self) -> Session:
        """Get a new database session."""
        if not self.session_factory:
            if not self.engine:
                self.create_engine()
            self.session_factory = sessionmaker(bind=self.engine)
        return self.session_factory()

    def create_tables(self):
        """Create all database tables."""
        if not self.engine:
            self.create_engine()

        # Verify SQLCipher is working if encryption is enabled
        if self._is_encrypted:
            self.verify_encryption()

        Base.metadata.create_all(bind=self.engine)
        logger.info("Database tables created successfully")

    @contextmanager
    def session_scope(self):
        """Provide a transactional scope around a series of operations."""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def verify_encryption(self) -> bool:
        """Verify that SQLCipher encryption is working properly."""
        try:
            with self.session_scope() as session:
                # Test encryption by executing a simple query
                result = session.execute(
                    text("SELECT name FROM sqlite_master WHERE type='table'")
                )
                tables = [row[0] for row in result.fetchall()]
                logger.info(
                    f"SQLCipher encryption verified - found {len(tables)} tables"
                )
                return True
        except Exception as e:
            logger.error(f"SQLCipher encryption verification failed: {e}")
            return False

    def test_encryption_key(self, test_key: str) -> bool:
        """Test if a given encryption key works with the database."""
        if not self._is_encrypted:
            logger.warning("Database is not configured for encryption")
            return False

        # Create a temporary engine with the test key
        test_url = self.database_url.replace(
            f":{self.encryption_key}@", f":{test_key}@"
        )

        try:
            test_engine = create_engine(
                test_url,
                poolclass=StaticPool,
                connect_args={"check_same_thread": False},
            )

            with test_engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            test_engine.dispose()
            logger.info("Encryption key test successful")
            return True

        except Exception as e:
            logger.error(f"Encryption key test failed: {e}")
            return False

    def get_database_info(self) -> Dict[str, Any]:
        """Get database information including encryption status."""
        info = {
            "database_url": self.database_url,
            "is_encrypted": self._is_encrypted,
            "has_encryption_key": bool(self.encryption_key),
            "engine_created": bool(self.engine),
        }

        if self.engine:
            with self.session_scope() as session:
                try:
                    # Get database file size
                    result = session.execute(text("PRAGMA page_count"))
                    page_count = result.scalar()
                    result = session.execute(text("PRAGMA page_size"))
                    page_size = result.scalar()

                    info.update(
                        {
                            "page_count": page_count,
                            "page_size": page_size,
                            "database_size": (
                                page_count * page_size
                                if page_count and page_size
                                else 0
                            ),
                        }
                    )

                    # Get SQLCipher version if encrypted
                    if self._is_encrypted:
                        result = session.execute(text("PRAGMA cipher_version"))
                        cipher_version = result.scalar()
                        info["cipher_version"] = cipher_version

                except Exception as e:
                    logger.warning(f"Could not get extended database info: {e}")

        return info


# Global database engine
_database_engine: Optional[DatabaseEngine] = None


def create_database_engine(
    database_url: str = None, encryption_key: str = None
) -> DatabaseEngine:
    """Create the global database engine."""
    global _database_engine
    _database_engine = DatabaseEngine(database_url, encryption_key)
    return _database_engine


def get_session() -> Session:
    """Get a database session."""
    if not _database_engine:
        raise RuntimeError("Database not initialized")
    return _database_engine.get_session()


def init_database(
    database_url: str = None, encryption_key: str = None
) -> DatabaseEngine:
    """Initialize database with tables."""
    engine = create_database_engine(database_url, encryption_key)
    engine.create_engine()
    engine.create_tables()
    return engine
