"""
Basic test for annotation persistence functionality.
Tests the complete save/load cycle for manual annotations.
"""

import pytest
import tempfile
import os
from typing import Dict, Any
from unittest.mock import Mock, MagicMock
from PySide6.QtCore import QRectF

# Test imports (adjust paths as needed for actual testing)
try:
    from src.medscan_ai.database.models.annotation import Annotation, AnnotationType
    from src.medscan_ai.database.repositories.annotation_repository import AnnotationRepository  
    from src.medscan_ai.gui.helpers.coordinate_transformer import (
        CoordinateTransformer, ImageMetadata, ViewState
    )
    from src.medscan_ai.gui.annotations.persistence_service import (
        AnnotationPersistenceService, AnnotationSaveResult, AnnotationLoadResult
    )
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Import error (expected in development): {e}")
    IMPORTS_AVAILABLE = False


def create_mock_repository():
    """Create a mock annotation repository for testing."""
    mock_repo = Mock(spec=AnnotationRepository)
    mock_session = Mock()
    mock_repo.session = mock_session
    
    # Mock successful annotation creation
    mock_annotation = Mock()
    mock_annotation.id = "test-annotation-123"
    mock_annotation.annotation_type = AnnotationType.RECTANGLE
    mock_annotation.coordinates = {
        'type': 'RECTANGLE',
        'x': 10.0, 'y': 20.0, 
        'width': 100.0, 'height': 80.0,
        'pixel_coords': True
    }
    mock_annotation.title = "Test Annotation"
    mock_annotation.created_at = "2025-01-25T08:00:00Z"
    
    mock_repo.create_annotation.return_value = mock_annotation
    mock_repo.get_annotations_for_image.return_value = [mock_annotation]
    mock_repo.get_annotations_for_study.return_value = [mock_annotation]
    
    return mock_repo


def create_test_transformer():
    """Create a coordinate transformer for testing."""
    if not IMPORTS_AVAILABLE:
        return None
        
    metadata = ImageMetadata(
        pixel_width=512,
        pixel_height=512,
        pixel_spacing_x=0.5,  # 0.5mm per pixel
        pixel_spacing_y=0.5
    )
    return CoordinateTransformer(metadata)


def create_test_view_state():
    """Create a test view state."""
    if not IMPORTS_AVAILABLE:
        return None
        
    return ViewState(
        scene_rect=QRectF(0, 0, 512, 512),
        view_rect=QRectF(0, 0, 512, 512), 
        zoom_factor=1.0
    )


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Annotation modules not available")
def test_annotation_persistence_service_creation():
    """Test creating annotation persistence service."""
    mock_repo = create_mock_repository()
    transformer = create_test_transformer()
    
    service = AnnotationPersistenceService(mock_repo, transformer)
    
    assert service.repository == mock_repo
    assert service.transformer == transformer
    assert service._current_study_id is None
    assert service._current_user_id is None


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Annotation modules not available")
def test_set_medical_context():
    """Test setting medical context for annotations."""
    mock_repo = create_mock_repository()
    transformer = create_test_transformer()
    service = AnnotationPersistenceService(mock_repo, transformer)
    
    # Set context
    service.set_current_context(
        study_id=123,
        image_id=456,
        user_id="user-789"
    )
    
    assert service._current_study_id == 123
    assert service._current_image_id == 456
    assert service._current_user_id == "user-789"


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Annotation modules not available")
def test_save_rectangle_annotation():
    """Test saving rectangle annotation to database."""
    mock_repo = create_mock_repository()
    transformer = create_test_transformer()
    service = AnnotationPersistenceService(mock_repo, transformer)
    
    # Set context first
    service.set_current_context(study_id=123, user_id="user-789")
    
    # Create test rectangle and view state
    scene_rect = QRectF(50, 60, 200, 150)
    view_state = create_test_view_state()
    
    # Save annotation
    result = service.save_rectangle_annotation(
        scene_rect=scene_rect,
        view_state=view_state,
        title="Test Rectangle",
        description="Test rectangle annotation",
        color="#FF0000",
        line_width=2
    )
    
    assert isinstance(result, AnnotationSaveResult)
    assert result.success is True
    assert result.annotation_id == "test-annotation-123"
    
    # Verify repository was called
    mock_repo.create_annotation.assert_called_once()
    mock_repo.session.commit.assert_called_once()


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Annotation modules not available")
def test_save_without_context_fails():
    """Test that saving without medical context fails."""
    mock_repo = create_mock_repository()
    transformer = create_test_transformer()
    service = AnnotationPersistenceService(mock_repo, transformer)
    
    # Don't set context - should fail
    scene_rect = QRectF(50, 60, 200, 150)
    view_state = create_test_view_state()
    
    result = service.save_rectangle_annotation(
        scene_rect=scene_rect,
        view_state=view_state
    )
    
    assert result.success is False
    assert "Medical context not set" in result.error_message


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Annotation modules not available")
def test_load_annotations_for_image():
    """Test loading annotations for current image."""
    mock_repo = create_mock_repository()
    transformer = create_test_transformer()
    service = AnnotationPersistenceService(mock_repo, transformer)
    
    # Set context
    service.set_current_context(study_id=123, image_id=456, user_id="user-789")
    view_state = create_test_view_state()
    
    # Load annotations
    result = service.load_annotations_for_current_image(view_state)
    
    assert isinstance(result, AnnotationLoadResult)
    assert result.success is True
    assert len(result.annotations) == 1
    
    # Verify repository was called
    mock_repo.get_annotations_for_image.assert_called_once_with(
        image_id=456, annotation_types=None
    )


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Annotation modules not available")
def test_coordinate_transformation():
    """Test coordinate transformation between scene and pixel coordinates."""
    transformer = create_test_transformer()
    view_state = create_test_view_state()
    
    # Test scene to pixel transformation
    scene_x, scene_y = 100.0, 150.0
    pixel_x, pixel_y = transformer.scene_to_pixel(scene_x, scene_y, view_state)
    
    # Should be same in this 1:1 case
    assert pixel_x == 100.0
    assert pixel_y == 150.0
    
    # Test reverse transformation
    back_scene_x, back_scene_y = transformer.pixel_to_scene(pixel_x, pixel_y, view_state)
    assert back_scene_x == scene_x
    assert back_scene_y == scene_y


def test_annotation_types():
    """Test annotation type constants."""
    if not IMPORTS_AVAILABLE:
        pytest.skip("Annotation modules not available")
        
    # Test that annotation types are defined
    assert hasattr(AnnotationType, 'RECTANGLE')
    assert hasattr(AnnotationType, 'FREEHAND') 
    assert hasattr(AnnotationType, 'POLYGON')
    assert hasattr(AnnotationType, 'CIRCLE')
    assert hasattr(AnnotationType, 'ARROW')
    assert hasattr(AnnotationType, 'TEXT')
    assert hasattr(AnnotationType, 'MEASUREMENT')


def test_basic_functionality_without_dependencies():
    """Test basic functionality that doesn't require Qt or database."""
    # Test coordinate calculation
    def calculate_distance(x1, y1, x2, y2):
        return ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
    
    distance = calculate_distance(0, 0, 3, 4)
    assert distance == 5.0
    
    # Test basic annotation data structure
    annotation_data = {
        'type': 'RECTANGLE',
        'coordinates': {'x': 10, 'y': 20, 'width': 100, 'height': 50},
        'metadata': {'color': '#FF0000', 'title': 'Test'}
    }
    
    assert annotation_data['type'] == 'RECTANGLE'
    assert annotation_data['coordinates']['width'] == 100
    assert annotation_data['metadata']['color'] == '#FF0000'


if __name__ == "__main__":
    print("Running annotation persistence tests...")
    
    # Run basic tests
    test_basic_functionality_without_dependencies()
    print("✅ Basic functionality test passed")
    
    if IMPORTS_AVAILABLE:
        try:
            test_annotation_persistence_service_creation()
            print("✅ Service creation test passed")
            
            test_set_medical_context()
            print("✅ Medical context test passed")
            
            test_coordinate_transformation()
            print("✅ Coordinate transformation test passed")
            
            test_annotation_types()
            print("✅ Annotation types test passed")
            
            print("✅ All available tests passed!")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            raise
    else:
        print("⚠️  Some tests skipped due to missing dependencies")
        print("✅ Basic tests completed successfully") 