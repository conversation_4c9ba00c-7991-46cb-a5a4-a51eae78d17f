# MedScan AI - Python Dependencies
# Core GUI Framework
PySide6>=6.9.0,<7.0.0

# Medical Imaging & DICOM
pydicom>=3.0.1,<4.0.0; python_version < "3.12"
pydicom>=3.0.1,<4.0.0; python_version >= "3.12"
opencv-python>=4.8.1,<5.0.0
Pillow>=10.0.0,<11.0.0
scikit-image>=0.21.0,<1.0.0
nibabel>=5.2.0,<6.0.0

# AI/ML Framework - PyTorch
torch>=2.0.1,<2.1.0
torchvision>=0.15.2,<0.16.0
torchaudio>=2.0.2,<2.1.0
numpy>=1.24.0,<2.0.0
scipy>=1.11.0,<2.0.0
albumentations>=1.3.1,<2.0.0
matplotlib>=3.7.0,<4.0.0
pynvml>=11.5.0,<12.0.0

# Database & Storage
pysqlite3>=2.8.0,<3.0.0
sqlalchemy>=2.0.0,<2.1.0
alembic>=1.13.1,<2.0.0

# Security & Cryptography
cryptography>=42.0.5,<43.0.0
passlib[argon2]>=1.7.4,<2.0.0
PyJWT>=2.8.0,<3.0.0
pyotp>=2.9.0,<3.0.0
qrcode[pil]>=7.4.2,<8.0.0
keyring>=24.3.0,<25.0.0

# Report Generation
reportlab>=4.0.0,<5.0.0
jinja2>=3.1.0,<4.0.0
weasyprint>=60.0,<61.0

# Configuration & Environment
python-decouple>=3.8,<4.0
pydantic>=2.3.0,<3.0.0
dynaconf>=3.2.0,<4.0.0

# Logging & Monitoring
structlog>=23.1.0,<24.0.0
python-json-logger>=2.0.0,<3.0.0
psutil>=5.9.0,<6.0.0

# Network & APIs
requests>=2.31.0,<3.0.0
aiohttp>=3.8.0,<4.0.0
websockets>=11.0.0,<12.0.0
flask>=3.0.3,<4.0.0

# Healthcare Standards
hl7>=0.4.0,<1.0.0
fhir.resources>=7.0.0,<8.0.0

# Development Tools
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0
mypy>=1.5.0,<2.0.0
isort>=5.12.0,<6.0.0

# Testing Framework
pytest>=7.4.0,<8.0.0
pytest-cov>=4.1.0,<5.0.0
pytest-qt>=4.2.0,<5.0.0
pytest-mock>=3.11.0,<4.0.0
pytest-asyncio>=0.21.0,<1.0.0

# Performance & Optimization
numba>=0.58.0,<1.0.0
memory-profiler>=0.61.0,<1.0.0

# Documentation
sphinx>=7.1.0,<8.0.0
sphinx-rtd-theme>=1.3.0,<2.0.0

# Security Analysis
bandit>=1.7.0,<2.0.0
safety>=2.3.0,<3.0.0

# Build & Packaging
pyinstaller>=6.0.0,<7.0.0
setuptools>=68.0.0,<69.0.0
wheel>=0.41.0,<1.0.0
pip-tools>=7.3.0,<8.0.0

# Additional Utilities
click>=8.1.0,<9.0.0
tqdm>=4.66.0,<5.0.0
rich>=13.5.0,<14.0.0
typer>=0.9.0,<1.0.0 