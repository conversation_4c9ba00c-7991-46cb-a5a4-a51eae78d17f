"""
Main entry point for MedScan AI GUI application.

This module provides a clean entry point for the medical imaging application,
leveraging the modular window architecture for maintainable, professional
healthcare software.
"""

import os
import sys
from typing import Optional

from PySide6.QtWidgets import QApplication

from .window import MedScanMainWindow


def main(args: Optional[list] = None) -> int:
    """
    Main entry point for MedScan AI GUI application.
    
    Args:
        args: Optional command line arguments
        
    Returns:
        Exit code (0 for success, non-zero for error)
    """
    try:
        # Create QApplication instance
        app = QApplication(args or sys.argv)
        
        # Set application properties
        app.setApplicationName("MedScan AI")
        app.setApplicationVersion("0.1.0")
        app.setOrganizationName("MedScan AI")
        app.setOrganizationDomain("medscan-ai.com")
        
        # Set application icon (if available)
        # icon_path = os.path.join(os.path.dirname(__file__), "assets", "icons", "medscan_icon.png")
        # if os.path.exists(icon_path):
        #     app.setWindowIcon(QIcon(icon_path))
        
        # Create and show main window
        main_window = MedScanMainWindow()
        main_window.show()
        
        # Start event loop
        return app.exec()
        
    except Exception as e:
        print(f"Fatal error starting MedScan AI: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 