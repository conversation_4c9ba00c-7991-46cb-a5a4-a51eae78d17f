"""
Unit tests for Authentication Service module.

Tests core functionality including:
- User authentication and validation
- Session management
- Password handling
- Role-based access control
- Error handling and edge cases
"""

import pytest
import uuid
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

# Import fixtures from our mocking strategy (removed MockSecurityServices as it doesn't exist)
from tests.fixtures.medical_data import MockMedicalDataFactory

# Import the module under test
from src.medscan_ai.security.authentication.auth_service import (
    AuthenticationService,
    AuthenticationResult,
    RegistrationResult
)


class TestAuthenticationService:
    """Test suite for AuthenticationService class."""
    
    @pytest.fixture
    def mock_credential_service(self):
        """Create a mock credential service."""
        return AsyncMock()
    
    @pytest.fixture
    def mock_rbac_manager(self):
        """Create a mock RBAC manager."""
        return Mock()
    
    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager."""
        return Mock()
    
    @pytest.fixture
    def mock_mfa_service(self):
        """Create a mock MFA service."""
        return Mock()
    
    @pytest.fixture
    def auth_service(self, mock_credential_service, mock_rbac_manager, 
                    mock_session_manager, mock_mfa_service):
        """Create an AuthenticationService instance for testing."""
        return AuthenticationService(
            credential_service=mock_credential_service,
            rbac_manager=mock_rbac_manager,
            session_manager=mock_session_manager,
            mfa_service=mock_mfa_service
        )
    
    @pytest.fixture
    def mock_user(self):
        """Create a mock user object."""
        user = Mock()
        user.user_id = "123e4567-e89b-12d3-a456-************"  # String UUID
        user.email = "<EMAIL>"
        user.username = "testuser"
        user.is_active = True
        user.account_locked_until = None
        user.medical_license_number = "ML123456"
        user.is_mfa_enabled = False
        user.failed_login_attempts = 0
        user.department = "radiology"
        user.facility = "General Hospital"
        return user
    
    def test_initialization(self, mock_credential_service, mock_rbac_manager):
        """Test AuthenticationService initialization."""
        auth_service = AuthenticationService(
            credential_service=mock_credential_service,
            rbac_manager=mock_rbac_manager
        )
        
        assert auth_service.credential_service == mock_credential_service
        assert auth_service.rbac_manager == mock_rbac_manager
        assert auth_service.session_manager is not None
        assert auth_service.mfa_service is not None
        assert auth_service.password_manager is not None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, mock_user, 
                                           mock_credential_service, mock_session_manager):
        """Test successful user authentication."""
        # Setup mocks
        mock_credential_service.get_user_by_email.return_value = mock_user
        mock_credential_service.verify_password.return_value = True
        
        # Create mock session object with proper types
        mock_session = Mock()
        mock_session.session_id = "session_123"
        mock_session.created_at = datetime.utcnow()
        mock_session.expires_at = datetime.utcnow() + timedelta(minutes=30)
        mock_session.ip_address = "127.0.0.1"
        mock_session.user_agent = "test_agent"
        # Mock the needed numeric attributes properly
        mock_session.expires_in = 1800  # 30 minutes in seconds
        mock_session.user_id = "123e4567-e89b-12d3-a456-************"
        
        # Ensure access token expiry is a real timedelta
        mock_session_manager.access_token_expiry = timedelta(minutes=30)

        mock_session_manager.create_session.return_value = (
            "access_token", "refresh_token", mock_session
        )
        
        # Mock additional methods
        auth_service._is_password_expired = AsyncMock(return_value=False)
        auth_service._validate_medical_license = AsyncMock(return_value=True)
        auth_service._is_registration_pending = AsyncMock(return_value=False)
        auth_service._get_full_user_data = AsyncMock(return_value={"user": "data"})
        auth_service._reset_failed_attempts = AsyncMock()
        auth_service._log_authentication_event = AsyncMock()
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify results
        assert result == AuthenticationResult.SUCCESS
        assert auth_data is not None
        assert "access_token" in auth_data
        assert "refresh_token" in auth_data
        assert "user" in auth_data
        assert "session" in auth_data
        
        # Verify mock calls
        mock_credential_service.get_user_by_email.assert_called_once_with("<EMAIL>")
        mock_credential_service.verify_password.assert_called_once_with(
            mock_user.user_id, "password123"
        )
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_credentials(self, auth_service, 
                                                        mock_credential_service):
        """Test authentication with invalid credentials."""
        # Setup mocks - user not found
        mock_credential_service.get_user_by_email.return_value = None
        mock_credential_service.get_user_by_username.return_value = None
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify results
        assert result == AuthenticationResult.INVALID_CREDENTIALS
        assert auth_data is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_account_disabled(self, auth_service, mock_user,
                                                     mock_credential_service):
        """Test authentication with disabled account."""
        # Setup mocks
        mock_user.is_active = False
        mock_credential_service.get_user_by_email.return_value = mock_user
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify results
        assert result == AuthenticationResult.ACCOUNT_DISABLED
        assert auth_data is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_account_locked(self, auth_service, mock_user,
                                                   mock_credential_service):
        """Test authentication with locked account."""
        # Setup mocks
        mock_user.account_locked_until = datetime.utcnow() + timedelta(hours=1)
        mock_credential_service.get_user_by_email.return_value = mock_user
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify results
        assert result == AuthenticationResult.ACCOUNT_LOCKED
        assert auth_data is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self, auth_service, mock_user,
                                                   mock_credential_service):
        """Test authentication with wrong password."""
        # Setup mocks
        mock_credential_service.get_user_by_email.return_value = mock_user
        mock_credential_service.verify_password.return_value = False
        auth_service._handle_failed_login = AsyncMock()
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "wrongpassword"
        )
        
        # Verify results
        assert result == AuthenticationResult.INVALID_CREDENTIALS
        assert auth_data is None
        
        # Verify failed login handling
        auth_service._handle_failed_login.assert_called_once_with(mock_user)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_password_expired(self, auth_service, mock_user,
                                                     mock_credential_service):
        """Test authentication with expired password."""
        # Setup mocks
        mock_credential_service.get_user_by_email.return_value = mock_user
        mock_credential_service.verify_password.return_value = True
        auth_service._is_password_expired = AsyncMock(return_value=True)
        auth_service._get_basic_user_data = Mock(return_value={"basic": "data"})
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify results
        assert result == AuthenticationResult.PASSWORD_EXPIRED
        assert auth_data == {"basic": "data"}
    
    @pytest.mark.asyncio
    async def test_authenticate_user_mfa_required(self, auth_service, mock_user,
                                                 mock_credential_service):
        """Test authentication requiring MFA."""
        # Setup mocks
        mock_user.is_mfa_enabled = True
        mock_credential_service.get_user_by_email.return_value = mock_user
        mock_credential_service.verify_password.return_value = True
        auth_service._is_password_expired = AsyncMock(return_value=False)
        auth_service._validate_medical_license = AsyncMock(return_value=True)
        auth_service._is_registration_pending = AsyncMock(return_value=False)
        auth_service._get_basic_user_data = Mock(return_value={"basic": "data"})
        
        # Test authentication without MFA code
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify results
        assert result == AuthenticationResult.MFA_REQUIRED
        assert auth_data is not None
        assert "mfa_required" in auth_data
        assert auth_data["mfa_required"] is True
    
    @pytest.mark.asyncio
    async def test_authenticate_user_mfa_success(self, auth_service, mock_user,
                                                mock_credential_service, mock_mfa_service,
                                                mock_session_manager):
        """Test successful MFA authentication."""
        # Setup mocks
        mock_user.is_mfa_enabled = True
        mock_credential_service.get_user_by_email.return_value = mock_user
        mock_credential_service.verify_password.return_value = True
        
        # Setup MFA verification
        mfa_result = Mock()
        mfa_result.success = True
        mock_mfa_service.verify_mfa.return_value = mfa_result
        
        # Create mock session object with proper types
        mock_session = Mock()
        mock_session.session_id = "session_123"
        mock_session.created_at = datetime.utcnow()
        mock_session.expires_at = datetime.utcnow() + timedelta(minutes=30)
        mock_session.ip_address = "127.0.0.1"
        mock_session.user_agent = "test_agent"
        # Mock the needed numeric attributes properly
        mock_session.expires_in = 1800  # 30 minutes in seconds
        mock_session.user_id = "123e4567-e89b-12d3-a456-************"
        
        # Ensure access token expiry is a real timedelta
        mock_session_manager.access_token_expiry = timedelta(minutes=30)

        mock_session_manager.create_session.return_value = (
            "access_token", "refresh_token", mock_session
        )
        
        # Mock additional methods
        auth_service._is_password_expired = AsyncMock(return_value=False)
        auth_service._validate_medical_license = AsyncMock(return_value=True)
        auth_service._is_registration_pending = AsyncMock(return_value=False)
        auth_service._get_full_user_data = AsyncMock(return_value={"user": "data"})
        auth_service._reset_failed_attempts = AsyncMock()
        auth_service._log_authentication_event = AsyncMock()
        
        # Test authentication with MFA code
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123", "123456"
        )
        
        # Verify results
        assert result == AuthenticationResult.SUCCESS
        assert auth_data is not None
        assert "access_token" in auth_data
        
        # Verify MFA verification was called
        mock_mfa_service.verify_mfa.assert_called_once_with(mock_user, "123456")
    
    @pytest.mark.asyncio
    @patch('src.medscan_ai.security.authentication.auth_service.PasswordPolicy')
    async def test_register_user_success(self, mock_password_policy, auth_service, mock_credential_service,
                                        mock_rbac_manager):
        """Test successful user registration."""
        # Setup mocks
        mock_credential_service.get_user_by_email.return_value = None
        mock_credential_service.get_user_by_username.return_value = None
        
        # Mock PasswordPolicy
        mock_policy = Mock()
        mock_password_policy.get_medical_policy.return_value = mock_policy
        
        # Mock password validation
        auth_service.password_manager.validate_password = Mock(return_value=True)
        
        # Mock role validation
        mock_role = Mock()
        mock_role.name = "radiologist"
        mock_rbac_manager.get_role_by_name.return_value = mock_role
        
        # Mock user creation
        new_user = Mock()
        new_user.user_id = "123e4567-e89b-12d3-a456-426614174001"
        new_user.email = "<EMAIL>"
        mock_credential_service.create_user.return_value = new_user
        
        auth_service._get_basic_user_data = Mock(return_value={"user": "data"})
        
        # Ensure radiologist role does not require admin approval in this test
        auth_service.admin_approval_required = []
        
        # Test registration
        result, user_data = await auth_service.register_user(
            email="<EMAIL>",
            username="newuser",
            password="SecurePass123!",
            first_name="Test",
            last_name="User",
            role_name="radiologist",
            medical_license="ML789012",
            department="radiology"  # Add required department
        )
        
        # Verify results
        assert result == RegistrationResult.SUCCESS
        assert user_data is not None
    
    @pytest.mark.asyncio
    async def test_register_user_email_exists(self, auth_service, mock_user,
                                             mock_credential_service):
        """Test registration with existing email."""
        # Setup mocks - email already exists
        mock_credential_service.get_user_by_email.return_value = mock_user
        
        # Test registration
        result, user_data = await auth_service.register_user(
            email="<EMAIL>",  # Existing email
            username="newuser",
            password="SecurePass123!",
            first_name="Test",
            last_name="User",
            role_name="viewer"
        )
        
        # Verify results
        assert result == RegistrationResult.EMAIL_EXISTS
        assert user_data is None
    
    @pytest.mark.asyncio
    async def test_logout_user_success(self, auth_service, mock_session_manager,
                                      mock_credential_service):
        """Test successful user logout."""
        # Setup mocks
        mock_token_payload = Mock()
        mock_token_payload.session_id = "session_123"
        mock_token_payload.user_id = str(uuid.uuid4())
        mock_token_payload.email = "<EMAIL>"
        
        mock_session_manager.validate_access_token.return_value = (
            True, mock_token_payload, ""
        )
        mock_session_manager.revoke_session.return_value = True
        
        mock_user = Mock()
        mock_credential_service.get_user_by_id.return_value = mock_user
        auth_service._log_authentication_event = AsyncMock()
        
        # Test logout
        success, message = await auth_service.logout_user("valid_token")
        
        # Verify results
        assert success is True
        assert "successful" in message.lower()
        
        # Verify mock calls
        mock_session_manager.validate_access_token.assert_called_once_with("valid_token")
        mock_session_manager.revoke_session.assert_called_once_with("session_123")
    
    @pytest.mark.asyncio
    async def test_logout_user_invalid_token(self, auth_service, mock_session_manager):
        """Test logout with invalid token."""
        # Setup mocks
        mock_session_manager.validate_access_token.return_value = (
            False, None, "Invalid token"
        )
        
        # Test logout
        success, message = await auth_service.logout_user("invalid_token")
        
        # Verify results
        assert success is False
        assert "Invalid token" in message
    
    def test_validate_request_token_success(self, auth_service, mock_session_manager):
        """Test successful token validation for API requests."""
        # Setup mocks
        mock_token_payload = Mock()
        mock_token_payload.user_id = "123e4567-e89b-12d3-a456-************"
        mock_token_payload.session_id = "session_123"
        mock_token_payload.email = "<EMAIL>"
        mock_token_payload.username = "testuser"
        mock_token_payload.roles = ["radiologist"]
        mock_token_payload.permissions = ["read_dicom"]
        mock_token_payload.department = "radiology"
        mock_token_payload.medical_license = "ML123456"
        
        # Mock session middleware properly
        auth_service.session_middleware = Mock()
        auth_service.session_middleware.validate_request = Mock(return_value=(
            True, mock_token_payload, ""
        ))
        
        # Test token validation
        is_valid, user_info, error = auth_service.validate_request_token(
            "Bearer valid_token"
        )
        
        # Verify results
        assert is_valid is True
        assert user_info is not None
        assert user_info["user_id"] == mock_token_payload.user_id
        assert user_info["email"] == mock_token_payload.email
        assert error == ""
    
    def test_validate_request_token_invalid(self, auth_service):
        """Test token validation with invalid token."""
        # Setup mocks
        auth_service.session_middleware = Mock()
        auth_service.session_middleware.validate_request = Mock(return_value=(
            False, None, "Token expired"
        ))
        
        # Test token validation
        is_valid, user_info, error = auth_service.validate_request_token(
            "Bearer invalid_token"
        )
        
        # Verify results
        assert is_valid is False
        assert user_info is None
        assert error == "Token expired"
    
    @pytest.mark.asyncio
    @patch('src.medscan_ai.security.authentication.auth_service.PasswordPolicy')
    async def test_change_password_success(self, mock_password_policy, auth_service, mock_user,
                                          mock_credential_service):
        """Test successful password change."""
        # Setup mocks
        mock_credential_service.get_user_by_id.return_value = mock_user
        mock_credential_service.verify_password.return_value = True
        mock_credential_service.update_password.return_value = True
        
        # Mock PasswordPolicy
        mock_policy = Mock()
        mock_password_policy.get_medical_policy.return_value = mock_policy
        
        # Mock password validation
        auth_service.password_manager.validate_password = Mock(return_value=True)
        
        # Test password change
        success, message = await auth_service.change_password(
            mock_user.user_id, "oldpassword", "newpassword123"
        )
        
        # Verify results
        assert success is True
        assert "successful" in message.lower()
        
        # Verify mock calls
        mock_credential_service.verify_password.assert_called_once_with(
            mock_user.user_id, "oldpassword"
        )
        mock_credential_service.change_password.assert_called_once_with(
            mock_user.user_id, "newpassword123"
        )
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, auth_service, mock_user,
                                                mock_credential_service):
        """Test password change with wrong current password."""
        # Setup mocks
        mock_credential_service.get_user_by_id.return_value = mock_user
        mock_credential_service.verify_password.return_value = False
        
        # Test password change
        success, message = await auth_service.change_password(
            mock_user.user_id, "wrongpassword", "newpassword123"
        )
        
        # Verify results
        assert success is False
        assert "current password" in message.lower()
    
    @pytest.mark.asyncio
    async def test_get_user_profile(self, auth_service, mock_user, mock_credential_service):
        """Test user profile retrieval."""
        # Setup mocks
        mock_credential_service.get_user_by_id.return_value = mock_user
        auth_service._get_full_user_data = AsyncMock(return_value={"profile": "data"})
        
        # Test profile retrieval
        profile = await auth_service.get_user_profile(mock_user.user_id)
        
        # Verify results
        assert profile == {"profile": "data"}
        mock_credential_service.get_user_by_id.assert_called_once_with(mock_user.user_id)
    
    @pytest.mark.asyncio
    async def test_error_handling_in_authentication(self, auth_service, 
                                                   mock_credential_service):
        """Test error handling during authentication process."""
        # Setup mocks to raise exception
        mock_credential_service.get_user_by_email.side_effect = Exception("Database error")
        
        # Test authentication
        result, auth_data = await auth_service.authenticate_user(
            "<EMAIL>", "password123"
        )
        
        # Verify error handling
        assert result == AuthenticationResult.INVALID_CREDENTIALS
        assert auth_data is None
