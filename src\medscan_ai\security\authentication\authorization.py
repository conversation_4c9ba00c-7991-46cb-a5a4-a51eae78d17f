"""RBAC Authorization Middleware for MedScan AI.

Provides comprehensive role-based access control middleware and decorators 
for API endpoints with medical-grade security and audit logging.
"""

import functools
import logging
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set, Union

from flask import g, jsonify, request
from werkzeug.exceptions import Forbidden, Unauthorized

from ...database.models.user import User
from ..access_control.rbac_manager import RBACManager
from .auth_service import AuthenticationService
from .session_manager import SessionMiddleware, TokenPayload

logger = logging.getLogger(__name__)


class AuthorizationResult:
    """Result of authorization check."""
    
    def __init__(self, authorized: bool, message: str = "", audit_data: Optional[Dict] = None):
        self.authorized = authorized
        self.message = message
        self.audit_data = audit_data or {}


class AuthorizationMiddleware:
    """
    Advanced RBAC authorization middleware for medical applications.
    
    Provides role-based and permission-based access control with
    comprehensive audit logging and medical-specific authorization rules.
    """
    
    def __init__(
        self,
        session_middleware: SessionMiddleware,
        rbac_manager: RBACManager,
        auth_service: Optional[AuthenticationService] = None
    ):
        """
        Initialize authorization middleware.
        
        Args:
            session_middleware: Session management middleware
            rbac_manager: RBAC manager for role/permission operations
            auth_service: Authentication service (optional, for audit logging)
        """
        self.session_middleware = session_middleware
        self.rbac_manager = rbac_manager
        self.auth_service = auth_service
        
        # Medical role hierarchy (higher levels include lower level permissions)
        self.medical_role_hierarchy = {
            "super_admin": 10,
            "admin": 9,
            "chief_physician": 8,
            "senior_physician": 7,
            "radiologist": 6,
            "cardiologist": 6,
            "neurologist": 6,
            "oncologist": 6,
            "emergency_physician": 5,
            "general_practitioner": 4,
            "resident": 3,
            "technician": 2,
            "nurse": 1,
            "guest": 0
        }
        
        # Critical permissions that require extra logging
        self.critical_permissions = {
            "patient_data_access",
            "dicom_access",
            "report_generation",
            "system_administration",
            "user_management",
            "audit_access"
        }
    
    def get_current_user_info(self) -> Optional[TokenPayload]:
        """
        Get current user information from request context.
        
        Returns:
            Token payload if user is authenticated, None otherwise
        """
        try:
            authorization = request.headers.get("Authorization")
            if not authorization:
                return None
                
            is_valid, token_payload, _ = self.session_middleware.validate_request(authorization)
            return token_payload if is_valid else None
            
        except Exception as e:
            logger.error(f"Error getting current user info: {str(e)}")
            return None
    
    def check_role_authorization(
        self, 
        required_roles: Union[str, List[str]], 
        user_payload: TokenPayload,
        require_all: bool = False
    ) -> AuthorizationResult:
        """
        Check if user has required role(s).
        
        Args:
            required_roles: Role name(s) required for access
            user_payload: User token payload
            require_all: If True, user must have ALL roles; if False, ANY role
            
        Returns:
            AuthorizationResult with authorization decision
        """
        try:
            if isinstance(required_roles, str):
                required_roles = [required_roles]
                
            user_roles = set(user_payload.roles)
            required_roles_set = set(required_roles)
            
            if require_all:
                # User must have ALL required roles
                has_authorization = required_roles_set.issubset(user_roles)
                missing_roles = required_roles_set - user_roles
                message = f"Missing required roles: {list(missing_roles)}" if not has_authorization else "All required roles present"
            else:
                # User must have ANY of the required roles
                has_authorization = bool(required_roles_set.intersection(user_roles))
                message = "Required role found" if has_authorization else f"None of required roles found: {required_roles}"
            
            audit_data = {
                "user_id": user_payload.user_id,
                "user_roles": list(user_roles),
                "required_roles": required_roles,
                "require_all": require_all,
                "authorization_granted": has_authorization
            }
            
            return AuthorizationResult(has_authorization, message, audit_data)
            
        except Exception as e:
            logger.error(f"Error checking role authorization: {str(e)}")
            return AuthorizationResult(False, "Authorization check failed")
    
    def check_permission_authorization(
        self,
        required_permissions: Union[str, List[str]],
        user_payload: TokenPayload,
        require_all: bool = True
    ) -> AuthorizationResult:
        """
        Check if user has required permission(s).
        
        Args:
            required_permissions: Permission name(s) required for access
            user_payload: User token payload
            require_all: If True, user must have ALL permissions; if False, ANY permission
            
        Returns:
            AuthorizationResult with authorization decision
        """
        try:
            if isinstance(required_permissions, str):
                required_permissions = [required_permissions]
                
            user_permissions = set(user_payload.permissions)
            required_permissions_set = set(required_permissions)
            
            if require_all:
                # User must have ALL required permissions
                has_authorization = required_permissions_set.issubset(user_permissions)
                missing_permissions = required_permissions_set - user_permissions
                message = f"Missing required permissions: {list(missing_permissions)}" if not has_authorization else "All required permissions present"
            else:
                # User must have ANY of the required permissions
                has_authorization = bool(required_permissions_set.intersection(user_permissions))
                message = "Required permission found" if has_authorization else f"None of required permissions found: {required_permissions}"
            
            audit_data = {
                "user_id": user_payload.user_id,
                "user_permissions": list(user_permissions),
                "required_permissions": required_permissions,
                "require_all": require_all,
                "authorization_granted": has_authorization,
                "critical_permission_check": bool(required_permissions_set.intersection(self.critical_permissions))
            }
            
            return AuthorizationResult(has_authorization, message, audit_data)
            
        except Exception as e:
            logger.error(f"Error checking permission authorization: {str(e)}")
            return AuthorizationResult(False, "Authorization check failed")
    
    def check_medical_hierarchy_authorization(
        self,
        min_role_level: str,
        user_payload: TokenPayload
    ) -> AuthorizationResult:
        """
        Check if user's medical role meets minimum hierarchy level.
        
        Args:
            min_role_level: Minimum role level required
            user_payload: User token payload
            
        Returns:
            AuthorizationResult with authorization decision
        """
        try:
            min_level = self.medical_role_hierarchy.get(min_role_level, 999)
            user_roles = user_payload.roles
            
            # Find highest user role level
            user_max_level = 0
            user_highest_role = "guest"
            for role in user_roles:
                role_level = self.medical_role_hierarchy.get(role, 0)
                if role_level > user_max_level:
                    user_max_level = role_level
                    user_highest_role = role
            
            has_authorization = user_max_level >= min_level
            message = f"User role '{user_highest_role}' (level {user_max_level}) {'meets' if has_authorization else 'does not meet'} minimum level {min_level} ('{min_role_level}')"
            
            audit_data = {
                "user_id": user_payload.user_id,
                "user_highest_role": user_highest_role,
                "user_max_level": user_max_level,
                "required_min_role": min_role_level,
                "required_min_level": min_level,
                "authorization_granted": has_authorization
            }
            
            return AuthorizationResult(has_authorization, message, audit_data)
            
        except Exception as e:
            logger.error(f"Error checking medical hierarchy authorization: {str(e)}")
            return AuthorizationResult(False, "Authorization check failed")
    
    def check_department_authorization(
        self,
        required_departments: Union[str, List[str]],
        user_payload: TokenPayload
    ) -> AuthorizationResult:
        """
        Check if user belongs to required department(s).
        
        Args:
            required_departments: Department name(s) required for access
            user_payload: User token payload
            
        Returns:
            AuthorizationResult with authorization decision
        """
        try:
            if isinstance(required_departments, str):
                required_departments = [required_departments]
                
            user_department = user_payload.department
            has_authorization = user_department in required_departments if user_department else False
            
            message = f"User department '{user_department}' {'is' if has_authorization else 'is not'} in required departments: {required_departments}"
            
            audit_data = {
                "user_id": user_payload.user_id,
                "user_department": user_department,
                "required_departments": required_departments,
                "authorization_granted": has_authorization
            }
            
            return AuthorizationResult(has_authorization, message, audit_data)
            
        except Exception as e:
            logger.error(f"Error checking department authorization: {str(e)}")
            return AuthorizationResult(False, "Authorization check failed")
    
    def log_authorization_event(
        self,
        event_type: str,
        user_payload: Optional[TokenPayload],
        authorization_result: AuthorizationResult,
        endpoint: str,
        additional_data: Optional[Dict] = None
    ) -> None:
        """
        Log authorization event for audit trail.
        
        Args:
            event_type: Type of authorization event
            user_payload: User token payload (if available)
            authorization_result: Result of authorization check
            endpoint: API endpoint being accessed
            additional_data: Additional audit data
        """
        try:
            audit_data = {
                "event_type": event_type,
                "endpoint": endpoint,
                "timestamp": datetime.utcnow().isoformat(),
                "ip_address": request.environ.get("REMOTE_ADDR"),
                "user_agent": request.headers.get("User-Agent"),
                "authorization_granted": authorization_result.authorized,
                "authorization_message": authorization_result.message,
                "request_method": request.method,
                "request_path": request.path,
            }
            
            if user_payload:
                audit_data.update({
                    "user_id": user_payload.user_id,
                    "user_email": user_payload.email,
                    "user_roles": user_payload.roles,
                    "user_permissions": user_payload.permissions,
                    "user_department": user_payload.department,
                    "session_id": user_payload.session_id
                })
            
            if additional_data:
                audit_data.update(additional_data)
                
            if authorization_result.audit_data:
                audit_data.update(authorization_result.audit_data)
            
            # Log based on severity
            if authorization_result.authorized:
                user_permissions = user_payload.permissions if user_payload else []
                if any(perm in self.critical_permissions for perm in user_permissions):
                    logger.warning(f"CRITICAL ACCESS GRANTED: {audit_data}")
                else:
                    logger.info(f"AUTHORIZATION GRANTED: {audit_data}")
            else:
                logger.warning(f"AUTHORIZATION DENIED: {audit_data}")
                
        except Exception as e:
            logger.error(f"Error logging authorization event: {str(e)}")


# Flask Decorators for Authorization

def require_authentication(f: Callable) -> Callable:
    """
    Decorator that requires valid authentication token.
    
    Args:
        f: Function to decorate
        
    Returns:
        Decorated function
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Get authorization middleware from Flask app context
            auth_middleware = getattr(g, 'auth_middleware', None)
            if not auth_middleware:
                # Fallback to module-level middleware (used in unit tests)
                try:
                    # Import at runtime to avoid circular imports
                    import src.medscan_ai.api.auth_endpoints as api_module
                    auth_middleware = getattr(api_module, 'auth_middleware', None)
                except Exception as e:
                    logger.debug(f"Failed to import auth_endpoints module: {str(e)}")
                    auth_middleware = None

            if not auth_middleware:
                logger.error("Authorization middleware not found in app context or module scope")
                return jsonify({
                    "status": "error",
                    "message": "Authorization system not available"
                }), 500
            
            # Get current user info
            try:
                user_payload = auth_middleware.get_current_user_info()
            except Exception as e:
                logger.debug(f"Failed to get user info from middleware: {str(e)}")
                user_payload = None

            # Check if user_payload is valid
            if not user_payload:
                # Check for pre-set current_user in g (for tests)
                user_payload = getattr(g, "current_user", None)

            # Validate user payload has required attributes
            if not user_payload or not hasattr(user_payload, "user_id"):
                # Log authentication failure
                if auth_middleware:
                    try:
                        auth_middleware.log_authorization_event(
                            "AUTHENTICATION_FAILED",
                            None,
                            AuthorizationResult(False, "No valid authentication token"),
                            f"{request.method} {request.path}"
                        )
                    except Exception as e:
                        logger.debug(f"Failed to log auth event: {str(e)}")
                
                return jsonify({
                    "status": "error",
                    "message": "Authentication required"
                }), 401
            
            # Store user info in request context for further use
            g.current_user = user_payload
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Authentication decorator error: {str(e)}")
            return jsonify({
                "status": "error",
                "message": "Authentication system error"
            }), 500
    
    return decorated_function


def require_role(
    roles: Union[str, List[str]], 
    require_all: bool = False
) -> Callable:
    """
    Decorator that requires specific role(s).
    
    Args:
        roles: Required role name(s)
        require_all: If True, user must have ALL roles; if False, ANY role
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        @require_authentication
        def decorated_function(*args, **kwargs):
            try:
                auth_middleware = g.auth_middleware
                user_payload = g.current_user
                
                result = auth_middleware.check_role_authorization(roles, user_payload, require_all)
                
                auth_middleware.log_authorization_event(
                    "ROLE_AUTHORIZATION_CHECK",
                    user_payload,
                    result,
                    f"{request.method} {request.path}",
                    {"required_roles": roles, "require_all": require_all}
                )
                
                if not result.authorized:
                    return jsonify({
                        "status": "error",
                        "message": f"Insufficient role privileges: {result.message}"
                    }), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Role authorization decorator error: {str(e)}")
                return jsonify({
                    "status": "error",
                    "message": "Authorization system error"
                }), 500
        
        return decorated_function
    return decorator


def require_permission(
    permissions: Union[str, List[str]], 
    require_all: bool = True
) -> Callable:
    """
    Decorator that requires specific permission(s).
    
    Args:
        permissions: Required permission name(s)
        require_all: If True, user must have ALL permissions; if False, ANY permission
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        @require_authentication
        def decorated_function(*args, **kwargs):
            try:
                auth_middleware = g.auth_middleware
                user_payload = g.current_user
                
                result = auth_middleware.check_permission_authorization(permissions, user_payload, require_all)
                
                auth_middleware.log_authorization_event(
                    "PERMISSION_AUTHORIZATION_CHECK",
                    user_payload,
                    result,
                    f"{request.method} {request.path}",
                    {"required_permissions": permissions, "require_all": require_all}
                )
                
                if not result.authorized:
                    return jsonify({
                        "status": "error",
                        "message": f"Insufficient permissions: {result.message}"
                    }), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Permission authorization decorator error: {str(e)}")
                return jsonify({
                    "status": "error",
                    "message": "Authorization system error"
                }), 500
        
        return decorated_function
    return decorator


def require_medical_role(min_role_level: str) -> Callable:
    """
    Decorator that requires minimum medical role hierarchy level.
    
    Args:
        min_role_level: Minimum medical role level required
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        @require_authentication
        def decorated_function(*args, **kwargs):
            try:
                auth_middleware = g.auth_middleware
                user_payload = g.current_user
                
                result = auth_middleware.check_medical_hierarchy_authorization(min_role_level, user_payload)
                
                auth_middleware.log_authorization_event(
                    "MEDICAL_HIERARCHY_AUTHORIZATION_CHECK",
                    user_payload,
                    result,
                    f"{request.method} {request.path}",
                    {"required_min_role": min_role_level}
                )
                
                if not result.authorized:
                    return jsonify({
                        "status": "error",
                        "message": f"Insufficient medical role level: {result.message}"
                    }), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Medical role authorization decorator error: {str(e)}")
                return jsonify({
                    "status": "error",
                    "message": "Authorization system error"
                }), 500
        
        return decorated_function
    return decorator


def require_department(departments: Union[str, List[str]]) -> Callable:
    """
    Decorator that requires user to belong to specific department(s).
    
    Args:
        departments: Required department name(s)
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        @require_authentication
        def decorated_function(*args, **kwargs):
            try:
                auth_middleware = g.auth_middleware
                user_payload = g.current_user
                
                result = auth_middleware.check_department_authorization(departments, user_payload)
                
                auth_middleware.log_authorization_event(
                    "DEPARTMENT_AUTHORIZATION_CHECK",
                    user_payload,
                    result,
                    f"{request.method} {request.path}",
                    {"required_departments": departments}
                )
                
                if not result.authorized:
                    return jsonify({
                        "status": "error",
                        "message": f"Department access denied: {result.message}"
                    }), 403
                
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Department authorization decorator error: {str(e)}")
                return jsonify({
                    "status": "error",
                    "message": "Authorization system error"
                }), 500
        
        return decorated_function
    return decorator


# Convenience decorators for common medical roles

def admin_only(f: Callable) -> Callable:
    """Decorator for admin-only endpoints."""
    return require_role(["admin", "super_admin"])(f)


def medical_staff_only(f: Callable) -> Callable:
    """Decorator for medical staff endpoints."""
    return require_medical_role("technician")(f)


def physician_only(f: Callable) -> Callable:
    """Decorator for physician-level endpoints."""
    return require_medical_role("general_practitioner")(f)


def senior_physician_only(f: Callable) -> Callable:
    """Decorator for senior physician endpoints."""
    return require_medical_role("senior_physician")(f)


def radiologist_access(f: Callable) -> Callable:
    """Decorator for radiology-specific endpoints."""
    return require_role(["radiologist", "admin", "super_admin"])(f)


def patient_data_access(f: Callable) -> Callable:
    """Decorator for patient data access (requires specific permission)."""
    return require_permission("patient_data_access")(f)


def dicom_access(f: Callable) -> Callable:
    """Decorator for DICOM imaging access."""
    return require_permission("dicom_access")(f)


def report_generation_access(f: Callable) -> Callable:
    """Decorator for report generation access."""
    return require_permission("report_generation")(f)


def audit_access(f: Callable) -> Callable:
    """Decorator for audit log access."""
    return require_permission("audit_access")(f) 