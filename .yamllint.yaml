# YAML linting configuration for MedScan AI
extends: default

rules:
  # Allow longer lines for readability
  line-length:
    max: 120
    level: warning

  # Allow more indentation for nested structures
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false

  # Don't require document start markers for all files
  document-start:
    present: false

  # Allow truthy values like 'yes', 'no', 'on', 'off'
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no', 'on', 'off']
    check-keys: false

  # Allow empty values for optional configurations
  empty-values:
    forbid-in-block-mappings: false
    forbid-in-flow-mappings: false

  # Comments configuration
  comments:
    min-spaces-from-content: 1
    require-starting-space: true

  # Brackets configuration
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1

  # Braces configuration
  braces:
    min-spaces-inside: 0
    max-spaces-inside: 1

  # Commas configuration
  commas:
    max-spaces-before: 0
    min-spaces-after: 1
    max-spaces-after: 1

ignore: |
  .github/
  .venv/
  node_modules/
  build/
  dist/
