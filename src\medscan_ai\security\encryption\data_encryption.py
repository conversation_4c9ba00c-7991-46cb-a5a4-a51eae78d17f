"""
Data Encryption Service for MedScan AI
AES-256-GCM encryption for data at rest with KMS integration
"""

import base64
import json
import logging
import secrets
from datetime import datetime
from typing import Any, Dict, Optional, Tuple, Union

from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives.kdf.hkdf import HKDF

from ..access_control.key_access import KeyAccessController, KeyType, SecurityRole
from .key_generation import KeyGenerationService
from .key_storage import KeyStorageManager

logger = logging.getLogger(__name__)


class EncryptionError(Exception):
    """Custom exception for encryption operations"""

    pass


class DataEncryptionService:
    """
    AES-256-GCM encryption service for data at rest
    Integrates with Key Management System for secure key operations
    """

    def __init__(
        self,
        storage_manager: Optional[KeyStorageManager] = None,
        access_controller: Optional[KeyAccessController] = None,
    ):
        """
        Initialize the data encryption service

        Args:
            storage_manager: Optional KeyStorageManager instance
            access_controller: Optional KeyAccessController instance
        """
        self.storage_manager = storage_manager or KeyStorageManager()
        self.access_controller = access_controller or KeyAccessController(
            self.storage_manager
        )
        self.key_generator = KeyGenerationService()

        # Cache for frequently used keys (in-memory only)
        self._key_cache: Dict[str, Tuple[bytes, datetime]] = {}
        self._cache_ttl_seconds = 300  # 5 minutes

        logger.info("Data encryption service initialized")

    def encrypt_field(
        self,
        data: Union[str, bytes],
        field_type: str,
        user_role: SecurityRole = SecurityRole.APPLICATION,
        user_id: str = None,
        additional_data: bytes = None,
    ) -> Dict[str, Any]:
        """
        Encrypt a single data field using AES-256-GCM

        Args:
            data: The data to encrypt (string or bytes)
            field_type: Type of field (e.g., 'patient_name', 'ssn', 'medical_record')
            user_role: Security role of the requesting user
            user_id: Optional user identifier
            additional_data: Optional additional authenticated data

        Returns:
            Dict containing encrypted data and metadata
        """
        try:
            # Convert string to bytes if necessary
            if isinstance(data, str):
                data_bytes = data.encode("utf-8")
            else:
                data_bytes = data

            # Get or create data encryption key for this field type
            key_id = f"field_{field_type}"
            encryption_key = self._get_or_create_field_key(key_id, user_role, user_id)

            # Generate random nonce (96 bits for GCM)
            nonce = secrets.token_bytes(12)

            # Create AESGCM cipher
            aesgcm = AESGCM(encryption_key)

            # Encrypt the data
            ciphertext = aesgcm.encrypt(nonce, data_bytes, additional_data)

            # Create encrypted field metadata
            encrypted_field = {
                "ciphertext": base64.b64encode(ciphertext).decode("utf-8"),
                "nonce": base64.b64encode(nonce).decode("utf-8"),
                "key_id": key_id,
                "field_type": field_type,
                "algorithm": "AES-256-GCM",
                "encrypted_at": datetime.utcnow().isoformat(),
                "additional_data": (
                    base64.b64encode(additional_data).decode("utf-8")
                    if additional_data
                    else None
                ),
            }

            logger.debug(f"Successfully encrypted field: {field_type}")
            return encrypted_field

        except Exception as e:
            logger.error(f"Failed to encrypt field {field_type}: {e}")
            raise EncryptionError(f"Field encryption failed: {e}")

    def decrypt_field(
        self,
        encrypted_field: Dict[str, Any],
        user_role: SecurityRole = SecurityRole.APPLICATION,
        user_id: str = None,
        return_string: bool = True,
    ) -> Union[str, bytes]:
        """
        Decrypt a single data field

        Args:
            encrypted_field: Dictionary containing encrypted field data
            user_role: Security role of the requesting user
            user_id: Optional user identifier
            return_string: Whether to return string (True) or bytes (False)

        Returns:
            Decrypted data as string or bytes
        """
        try:
            # Extract metadata
            key_id = encrypted_field["key_id"]
            field_type = encrypted_field["field_type"]
            algorithm = encrypted_field.get("algorithm", "AES-256-GCM")

            if algorithm != "AES-256-GCM":
                raise EncryptionError(f"Unsupported algorithm: {algorithm}")

            # Get decryption key
            encryption_key = self._get_field_key(key_id, user_role, user_id)

            # Decode encrypted data
            ciphertext = base64.b64decode(encrypted_field["ciphertext"])
            nonce = base64.b64decode(encrypted_field["nonce"])
            additional_data = (
                base64.b64decode(encrypted_field["additional_data"])
                if encrypted_field.get("additional_data")
                else None
            )

            # Create AESGCM cipher
            aesgcm = AESGCM(encryption_key)

            # Decrypt the data
            plaintext = aesgcm.decrypt(nonce, ciphertext, additional_data)

            logger.debug(f"Successfully decrypted field: {field_type}")

            if return_string:
                return plaintext.decode("utf-8")
            else:
                return plaintext

        except Exception as e:
            logger.error(f"Failed to decrypt field: {e}")
            raise EncryptionError(f"Field decryption failed: {e}")

    def encrypt_record(
        self,
        record: Dict[str, Any],
        sensitive_fields: list,
        record_type: str = "medical_record",
        user_role: SecurityRole = SecurityRole.APPLICATION,
        user_id: str = None,
    ) -> Dict[str, Any]:
        """
        Encrypt sensitive fields in a database record

        Args:
            record: The database record dictionary
            sensitive_fields: List of field names to encrypt
            record_type: Type of record for key management
            user_role: Security role of the requesting user
            user_id: Optional user identifier

        Returns:
            Record with encrypted sensitive fields
        """
        try:
            encrypted_record = record.copy()
            encryption_metadata = {
                "encrypted_fields": [],
                "encryption_timestamp": datetime.utcnow().isoformat(),
                "record_type": record_type,
            }

            for field_name in sensitive_fields:
                if field_name in record and record[field_name] is not None:
                    # Create field-specific additional data for authentication
                    additional_data = f"field:{field_name}|record:{record_type}".encode(
                        "utf-8"
                    )

                    # Encrypt the field
                    encrypted_field = self.encrypt_field(
                        data=record[field_name],
                        field_type=f"{record_type}_{field_name}",
                        user_role=user_role,
                        user_id=user_id,
                        additional_data=additional_data,
                    )

                    # Replace original field with encrypted version
                    encrypted_record[field_name] = encrypted_field
                    encryption_metadata["encrypted_fields"].append(field_name)

            # Add encryption metadata
            encrypted_record["_encryption_metadata"] = encryption_metadata

            logger.info(
                f"Successfully encrypted {len(encryption_metadata['encrypted_fields'])} fields in {record_type}"
            )
            return encrypted_record

        except Exception as e:
            logger.error(f"Failed to encrypt record: {e}")
            raise EncryptionError(f"Record encryption failed: {e}")

    def decrypt_record(
        self,
        encrypted_record: Dict[str, Any],
        user_role: SecurityRole = SecurityRole.APPLICATION,
        user_id: str = None,
    ) -> Dict[str, Any]:
        """
        Decrypt sensitive fields in a database record

        Args:
            encrypted_record: The encrypted database record
            user_role: Security role of the requesting user
            user_id: Optional user identifier

        Returns:
            Record with decrypted sensitive fields
        """
        try:
            decrypted_record = encrypted_record.copy()

            # Get encryption metadata
            encryption_metadata = encrypted_record.get("_encryption_metadata", {})
            encrypted_fields = encryption_metadata.get("encrypted_fields", [])
            record_type = encryption_metadata.get("record_type", "unknown")

            for field_name in encrypted_fields:
                if field_name in encrypted_record and isinstance(
                    encrypted_record[field_name], dict
                ):
                    # Create field-specific additional data for authentication
                    additional_data = f"field:{field_name}|record:{record_type}".encode(
                        "utf-8"
                    )

                    # Add additional data to encrypted field for decryption
                    encrypted_field = encrypted_record[field_name].copy()
                    if additional_data:
                        encrypted_field["additional_data"] = base64.b64encode(
                            additional_data
                        ).decode("utf-8")

                    # Decrypt the field
                    decrypted_value = self.decrypt_field(
                        encrypted_field=encrypted_field,
                        user_role=user_role,
                        user_id=user_id,
                    )

                    # Replace encrypted field with decrypted value
                    decrypted_record[field_name] = decrypted_value

            # Remove encryption metadata from decrypted record
            if "_encryption_metadata" in decrypted_record:
                del decrypted_record["_encryption_metadata"]

            logger.info(
                f"Successfully decrypted {len(encrypted_fields)} fields in {record_type}"
            )
            return decrypted_record

        except Exception as e:
            logger.error(f"Failed to decrypt record: {e}")
            raise EncryptionError(f"Record decryption failed: {e}")

    def encrypt_file(
        self,
        file_path: str,
        file_type: str = "medical_file",
        user_role: SecurityRole = SecurityRole.APPLICATION,
        user_id: str = None,
    ) -> Dict[str, Any]:
        """
        Encrypt a file using AES-256-GCM

        Args:
            file_path: Path to the file to encrypt
            file_type: Type of file for key management
            user_role: Security role of the requesting user
            user_id: Optional user identifier

        Returns:
            Dictionary containing encryption metadata
        """
        try:
            # Read file data
            with open(file_path, "rb") as f:
                file_data = f.read()

            # Create file-specific additional data
            import os

            file_name = os.path.basename(file_path)
            additional_data = f"file:{file_name}|type:{file_type}".encode("utf-8")

            # Encrypt file data
            encrypted_field = self.encrypt_field(
                data=file_data,
                field_type=f"file_{file_type}",
                user_role=user_role,
                user_id=user_id,
                additional_data=additional_data,
            )

            # Write encrypted file
            encrypted_file_path = f"{file_path}.encrypted"
            with open(encrypted_file_path, "w") as f:
                json.dump(encrypted_field, f, indent=2)

            # Create metadata
            metadata = {
                "original_file": file_path,
                "encrypted_file": encrypted_file_path,
                "file_type": file_type,
                "file_size": len(file_data),
                "encrypted_at": datetime.utcnow().isoformat(),
                "key_id": encrypted_field["key_id"],
            }

            logger.info(f"Successfully encrypted file: {file_path}")
            return metadata

        except Exception as e:
            logger.error(f"Failed to encrypt file {file_path}: {e}")
            raise EncryptionError(f"File encryption failed: {e}")

    def decrypt_file(
        self,
        encrypted_file_path: str,
        output_path: str = None,
        user_role: SecurityRole = SecurityRole.APPLICATION,
        user_id: str = None,
    ) -> str:
        """
        Decrypt a file

        Args:
            encrypted_file_path: Path to the encrypted file
            output_path: Optional output path for decrypted file
            user_role: Security role of the requesting user
            user_id: Optional user identifier

        Returns:
            Path to the decrypted file
        """
        try:
            # Read encrypted file data
            with open(encrypted_file_path, "r") as f:
                encrypted_field = json.load(f)

            # Decrypt file data
            decrypted_data = self.decrypt_field(
                encrypted_field=encrypted_field,
                user_role=user_role,
                user_id=user_id,
                return_string=False,
            )

            # Determine output path
            if not output_path:
                output_path = encrypted_file_path.replace(".encrypted", ".decrypted")

            # Write decrypted file
            with open(output_path, "wb") as f:
                f.write(decrypted_data)

            logger.info(f"Successfully decrypted file: {encrypted_file_path}")
            return output_path

        except Exception as e:
            logger.error(f"Failed to decrypt file {encrypted_file_path}: {e}")
            raise EncryptionError(f"File decryption failed: {e}")

    def _get_or_create_field_key(
        self, key_id: str, user_role: SecurityRole, user_id: str = None
    ) -> bytes:
        """
        Get existing field key or create new one

        Args:
            key_id: Unique identifier for the key
            user_role: Security role of the requesting user
            user_id: Optional user identifier

        Returns:
            AES-256 encryption key
        """
        try:
            # Try to get existing key
            key = self._get_field_key(key_id, user_role, user_id)
            if key:
                return key

            # Create new key if not found
            logger.info(f"Creating new field encryption key: {key_id}")

            # Generate new data encryption key
            new_key, _ = self.key_generator.generate_data_encryption_key(key_id)

            # Store the key using access controller
            self.access_controller.store_key_with_authorization(
                key_id=key_id,
                key=new_key,
                key_type=KeyType.DATABASE,  # Field keys are database keys
                role=user_role,
                user_id=user_id,
                description=f"Field encryption key for {key_id}",
            )

            # Cache the key
            self._cache_key(key_id, new_key)

            return new_key

        except Exception as e:
            logger.error(f"Failed to get or create field key {key_id}: {e}")
            raise EncryptionError(f"Key management failed: {e}")

    def _get_field_key(
        self, key_id: str, user_role: SecurityRole, user_id: str = None
    ) -> Optional[bytes]:
        """
        Get field encryption key with caching

        Args:
            key_id: Unique identifier for the key
            user_role: Security role of the requesting user
            user_id: Optional user identifier

        Returns:
            AES-256 encryption key or None if not found
        """
        try:
            # Check cache first
            cached_key = self._get_cached_key(key_id)
            if cached_key:
                return cached_key

            # Get key from storage with authorization
            key = self.access_controller.get_key_with_authorization(
                key_id=key_id, role=user_role, user_id=user_id
            )

            if key:
                # Cache the key
                self._cache_key(key_id, key)

            return key

        except Exception as e:
            logger.error(f"Failed to get field key {key_id}: {e}")
            return None

    def _cache_key(self, key_id: str, key: bytes) -> None:
        """Cache encryption key with TTL"""
        self._key_cache[key_id] = (key, datetime.utcnow())

    def _get_cached_key(self, key_id: str) -> Optional[bytes]:
        """Get key from cache if not expired"""
        if key_id in self._key_cache:
            key, cached_at = self._key_cache[key_id]
            age_seconds = (datetime.utcnow() - cached_at).total_seconds()

            if age_seconds < self._cache_ttl_seconds:
                return key
            else:
                # Remove expired key
                del self._key_cache[key_id]

        return None

    def clear_key_cache(self) -> None:
        """Clear all cached keys"""
        self._key_cache.clear()
        logger.info("Encryption key cache cleared")

    def rotate_field_key(
        self,
        key_id: str,
        user_role: SecurityRole = SecurityRole.SECURITY_ADMIN,
        user_id: str = None,
    ) -> bool:
        """
        Rotate a field encryption key

        Args:
            key_id: Unique identifier for the key to rotate
            user_role: Security role (must be SECURITY_ADMIN or higher)
            user_id: Optional user identifier

        Returns:
            True if rotation successful
        """
        try:
            if user_role not in [
                SecurityRole.SECURITY_ADMIN,
                SecurityRole.SYSTEM_ADMIN,
            ]:
                raise EncryptionError("Insufficient privileges for key rotation")

            # Generate new key
            new_key, _ = self.key_generator.generate_data_encryption_key(
                f"{key_id}_rotated"
            )

            # Store new key with rotated identifier
            new_key_id = f"{key_id}_v{int(datetime.utcnow().timestamp())}"

            self.access_controller.store_key_with_authorization(
                key_id=new_key_id,
                key=new_key,
                key_type=KeyType.DATABASE,
                role=user_role,
                user_id=user_id,
                description=f"Rotated key for {key_id}",
            )

            # Clear cache for old key
            if key_id in self._key_cache:
                del self._key_cache[key_id]

            logger.info(f"Successfully rotated key: {key_id} -> {new_key_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to rotate key {key_id}: {e}")
            raise EncryptionError(f"Key rotation failed: {e}")

    def get_encryption_stats(self) -> Dict[str, Any]:
        """
        Get encryption service statistics

        Returns:
            Dictionary containing service statistics
        """
        return {
            "cached_keys": len(self._key_cache),
            "cache_ttl_seconds": self._cache_ttl_seconds,
            "supported_algorithms": ["AES-256-GCM"],
            "key_sizes": {"AES": 256},
            "nonce_size": 96,  # bits
            "service_status": "operational",
        }


# Factory and convenience functions for external use
def create_data_encryption_service(
    storage_manager: Optional[KeyStorageManager] = None,
    access_controller: Optional[KeyAccessController] = None,
) -> DataEncryptionService:
    """
    Create a DataEncryptionService instance
    
    Args:
        storage_manager: Optional KeyStorageManager instance
        access_controller: Optional KeyAccessController instance
        
    Returns:
        Configured DataEncryptionService instance
    """
    return DataEncryptionService(storage_manager, access_controller)


def encrypt_sensitive_data(
    data: Union[str, bytes],
    field_type: str,
    user_role: SecurityRole = SecurityRole.APPLICATION,
    user_id: str = None,
) -> Dict[str, Any]:
    """
    Convenience function to encrypt sensitive data
    
    Args:
        data: Data to encrypt
        field_type: Type of field being encrypted
        user_role: Security role of the requesting user
        user_id: Optional user identifier
        
    Returns:
        Encrypted field dictionary
    """
    service = create_data_encryption_service()
    return service.encrypt_field(data, field_type, user_role, user_id)


def decrypt_sensitive_data(
    encrypted_field: Dict[str, Any],
    user_role: SecurityRole = SecurityRole.APPLICATION,
    user_id: str = None,
    return_string: bool = True,
) -> Union[str, bytes]:
    """
    Convenience function to decrypt sensitive data
    
    Args:
        encrypted_field: Encrypted field dictionary
        user_role: Security role of the requesting user
        user_id: Optional user identifier
        return_string: Whether to return string or bytes
        
    Returns:
        Decrypted data
    """
    service = create_data_encryption_service()
    return service.decrypt_field(encrypted_field, user_role, user_id, return_string)
