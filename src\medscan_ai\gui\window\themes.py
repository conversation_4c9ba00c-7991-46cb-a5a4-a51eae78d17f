"""
UI theming and styling for MedScan AI GUI.

This module provides medical-grade UI themes, colors, fonts,
and styling for professional healthcare interfaces.
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QColor, QFont, QPalette


class MedicalThemeManager:
    """
    Manager for medical UI themes and styling.
    
    Provides professional, healthcare-appropriate color schemes,
    fonts, and styling for medical imaging applications.
    """

    # Medical color scheme
    COLORS = {
        # Primary medical colors
        'primary_blue': '#1976D2',
        'primary_green': '#2E7D32', 
        'primary_white': '#FFFFFF',
        'primary_black': '#000000',
        
        # Background colors
        'bg_light': '#F5F5F5',
        'bg_medium': '#E8E8E8',
        'bg_dark': '#2C2C2C',
        'bg_panel': '#FAFAFA',
        
        # Accent colors
        'accent_blue': '#E3F2FD',
        'accent_green': '#E8F5E8',
        'accent_yellow': '#FFF9C4',
        'accent_red': '#FFEBEE',
        
        # Status colors
        'success': '#4CAF50',
        'warning': '#FF9800', 
        'error': '#F44336',
        'info': '#2196F3',
        
        # Border and separator colors
        'border_light': '#CCCCCC',
        'border_medium': '#999999',
        'border_dark': '#666666',
        
        # Text colors
        'text_primary': '#212121',
        'text_secondary': '#666666',
        'text_disabled': '#BDBDBD',
    }

    # Medical fonts
    FONTS = {
        'primary': 'Arial',
        'secondary': 'Segoe UI',
        'monospace': 'Consolas',
        'sizes': {
            'small': 9,
            'normal': 10,
            'medium': 12,
            'large': 14,
            'xlarge': 16,
        }
    }

    @classmethod
    def apply_medical_theme(cls, window):
        """
        Apply comprehensive medical theme to a QMainWindow.
        
        Args:
            window: QMainWindow instance to theme
        """
        try:
            # Set window background
            window.setStyleSheet(f"""
                QMainWindow {{
                    background-color: {cls.COLORS['bg_panel']};
                    color: {cls.COLORS['text_primary']};
                    font-family: '{cls.FONTS['primary']}';
                    font-size: {cls.FONTS['sizes']['normal']}px;
                }}
            """)

            # Apply palette for consistent theming
            palette = window.palette()
            
            # Window colors
            palette.setColor(QPalette.Window, QColor(cls.COLORS['bg_panel']))
            palette.setColor(QPalette.WindowText, QColor(cls.COLORS['text_primary']))
            
            # Button colors  
            palette.setColor(QPalette.Button, QColor(cls.COLORS['bg_light']))
            palette.setColor(QPalette.ButtonText, QColor(cls.COLORS['text_primary']))
            
            # Selection colors
            palette.setColor(QPalette.Highlight, QColor(cls.COLORS['primary_blue']))
            palette.setColor(QPalette.HighlightedText, QColor(cls.COLORS['primary_white']))
            
            # Input colors
            palette.setColor(QPalette.Base, QColor(cls.COLORS['primary_white']))
            palette.setColor(QPalette.Text, QColor(cls.COLORS['text_primary']))
            
            window.setPalette(palette)

        except Exception as e:
            print(f"Warning: Failed to apply medical theme: {e}")

    @classmethod
    def get_panel_style(cls, panel_type: str = "default") -> str:
        """
        Get stylesheet for different panel types.
        
        Args:
            panel_type: Type of panel ('default', 'info', 'warning', 'success', 'error')
            
        Returns:
            CSS stylesheet string
        """
        base_style = f"""
            QFrame {{
                border: 2px solid {cls.COLORS['border_light']};
                border-radius: 8px;
                background-color: {cls.COLORS['primary_white']};
                padding: 8px;
                margin: 4px;
            }}
        """

        if panel_type == "info":
            return base_style + f"""
                QFrame {{
                    border-color: {cls.COLORS['info']};
                    background-color: {cls.COLORS['accent_blue']};
                }}
            """
        elif panel_type == "warning":
            return base_style + f"""
                QFrame {{
                    border-color: {cls.COLORS['warning']};
                    background-color: {cls.COLORS['accent_yellow']};
                }}
            """
        elif panel_type == "success":
            return base_style + f"""
                QFrame {{
                    border-color: {cls.COLORS['success']};
                    background-color: {cls.COLORS['accent_green']};
                }}
            """
        elif panel_type == "error":
            return base_style + f"""
                QFrame {{
                    border-color: {cls.COLORS['error']};
                    background-color: {cls.COLORS['accent_red']};
                }}
            """
        else:
            return base_style

    @classmethod
    def get_button_style(cls, button_type: str = "default") -> str:
        """
        Get stylesheet for different button types.
        
        Args:
            button_type: Type of button ('default', 'primary', 'success', 'warning', 'danger')
            
        Returns:
            CSS stylesheet string
        """
        base_style = f"""
            QPushButton {{
                border: 1px solid {cls.COLORS['border_medium']};
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: {cls.FONTS['sizes']['normal']}px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                border-width: 2px;
            }}
            QPushButton:pressed {{
                border-width: 2px;
                padding: 7px 11px 5px 13px;
            }}
        """

        if button_type == "primary":
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['primary_blue']};
                    color: {cls.COLORS['primary_white']};
                    border-color: {cls.COLORS['primary_blue']};
                }}
                QPushButton:hover {{
                    background-color: #1565C0;
                }}
            """
        elif button_type == "success":
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['success']};
                    color: {cls.COLORS['primary_white']};
                    border-color: {cls.COLORS['success']};
                }}
                QPushButton:hover {{
                    background-color: #388E3C;
                }}
            """
        elif button_type == "warning":
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['warning']};
                    color: {cls.COLORS['primary_white']};
                    border-color: {cls.COLORS['warning']};
                }}
                QPushButton:hover {{
                    background-color: #F57C00;
                }}
            """
        elif button_type == "danger":
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['error']};
                    color: {cls.COLORS['primary_white']};
                    border-color: {cls.COLORS['error']};
                }}
                QPushButton:hover {{
                    background-color: #D32F2F;
                }}
            """
        else:
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS['bg_light']};
                    color: {cls.COLORS['text_primary']};
                }}
                QPushButton:hover {{
                    background-color: {cls.COLORS['bg_medium']};
                }}
            """

    @classmethod
    def get_input_style(cls) -> str:
        """Get stylesheet for input widgets."""
        return f"""
            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {{
                border: 1px solid {cls.COLORS['border_light']};
                border-radius: 4px;
                padding: 4px 8px;
                background-color: {cls.COLORS['primary_white']};
                font-size: {cls.FONTS['sizes']['normal']}px;
            }}
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
                border-color: {cls.COLORS['primary_blue']};
                border-width: 2px;
            }}
        """

    @classmethod
    def get_slider_style(cls) -> str:
        """Get stylesheet for slider widgets."""
        return f"""
            QSlider::groove:horizontal {{
                border: 1px solid {cls.COLORS['border_medium']};
                height: 8px;
                background: {cls.COLORS['bg_light']};
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {cls.COLORS['primary_blue']};
                border: 1px solid {cls.COLORS['border_medium']};
                width: 18px;
                height: 18px;
                border-radius: 9px;
                margin: -5px 0;
            }}
            QSlider::handle:horizontal:hover {{
                background: #1565C0;
            }}
        """

    @classmethod
    def get_medical_font(cls, size: str = "normal", weight: str = "normal") -> QFont:
        """
        Get medical-appropriate font.
        
        Args:
            size: Font size ('small', 'normal', 'medium', 'large', 'xlarge')
            weight: Font weight ('normal', 'bold')
            
        Returns:
            QFont instance
        """
        font = QFont(cls.FONTS['primary'])
        font.setPixelSize(cls.FONTS['sizes'].get(size, cls.FONTS['sizes']['normal']))
        
        if weight == "bold":
            font.setWeight(QFont.Bold)
        
        return font

    @classmethod
    def get_status_color(cls, status: str) -> str:
        """
        Get color for status indicators.
        
        Args:
            status: Status type ('success', 'warning', 'error', 'info')
            
        Returns:
            Color hex string
        """
        return cls.COLORS.get(status, cls.COLORS['text_secondary']) 