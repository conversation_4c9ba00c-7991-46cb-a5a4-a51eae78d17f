"""
Annotation system for manual image annotations.
"""

from .types import AnnotationTool, ManualAnnotation
from .tools import (
    AnnotationToolBase,
    RectangleTool,
    PolygonTool,
    FreehandTool,
    EraserTool
)
from .manager import AnnotationManager
from .persistence_service import (
    AnnotationPersistenceService,
    AnnotationSaveResult,
    AnnotationLoadResult
)
from .properties_dialog import AnnotationPropertiesDialog
from .context_menu import AnnotationContextMenu, QuickActionMenu
from .layer_panel import AnnotationLayerPanel, AnnotationLayerItem
from .graphics_renderer import AnnotationGraphicsRenderer
from .event_handler import AnnotationEventHandler
from .context_menu_handler import AnnotationContextMenuHandler

__all__ = [
    # Types and enums
    'AnnotationTool',
    'ManualAnnotation',
    
    # Tools
    'AnnotationToolBase',
    'RectangleTool',
    'PolygonTool', 
    'FreehandTool',
    'EraserTool',
    
    # Manager
    'AnnotationManager',
    
    # Persistence
    'AnnotationPersistenceService',
    'AnnotationSaveResult',
    'AnnotationLoadResult',
    
    # UI Components (Task 10.6)
    'AnnotationPropertiesDialog',
    'AnnotationContextMenu',
    'QuickActionMenu',
    'AnnotationLayerPanel',
    'AnnotationLayerItem',
    
    # Modular Components (Refactored)
    'AnnotationGraphicsRenderer',
    'AnnotationEventHandler',
    'AnnotationContextMenuHandler'
]
