"""
Freehand annotation tool implementation.
"""

from typing import Optional, List
import uuid
import math
from PySide6.QtCore import QPoint, Qt, QPointF
from PySide6.QtGui import QPen, QBrush, QPainterPath
from PySide6.QtWidgets import QGraphicsPathItem, QGraphicsScene

from .base import AnnotationToolBase
from ..types import AnnotationTool, ManualAnnotation


class FreehandTool(AnnotationToolBase):
    """
    Tool for drawing freehand annotations on medical images.
    Supports smooth path drawing with optional path smoothing.
    """
    
    def __init__(self):
        """Initialize freehand tool."""
        super().__init__(AnnotationTool.FREEHAND)
        self.points: List[QPoint] = []
        self.path = QPainterPath()
        self.smoothing_enabled = True
        self.min_distance = 3.0  # Minimum distance between points
        
    def _start_drawing_impl(self, scene_pos: QPoint, scene: QGraphicsScene) -> bool:
        """Start drawing a freehand path."""
        self.points = [scene_pos]
        self.path = QPainterPath()
        self.path.moveTo(scene_pos)
        
        # Create preview path item
        self.preview_item = QGraphicsPathItem()
        pen = QPen(self.pen_color)
        pen.setWidth(self.pen_width)
        pen.setStyle(Qt.SolidLine)
        self.preview_item.setPen(pen)
        
        # No fill for freehand drawings
        self.preview_item.setBrush(QBrush(Qt.NoBrush))
        
        scene.addItem(self.preview_item)
        self._update_path_preview()
        return True
        
    def _continue_drawing_impl(self, scene_pos: QPoint) -> bool:
        """Continue drawing by adding points to the path."""
        if not self.preview_item:
            return False
            
        # Check minimum distance to avoid too many close points
        if self.points:
            last_point = self.points[-1]
            distance = math.sqrt(
                (scene_pos.x() - last_point.x()) ** 2 + 
                (scene_pos.y() - last_point.y()) ** 2
            )
            
            if distance < self.min_distance:
                return True  # Skip this point
                
        self.points.append(scene_pos)
        self.path.lineTo(scene_pos)
        self._update_path_preview()
        return True
        
    def _finish_drawing_impl(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """Finish drawing and create freehand annotation."""
        if len(self.points) < 2:
            return None
            
        # Apply smoothing if enabled
        final_points = self.points
        if self.smoothing_enabled:
            final_points = self._smooth_path(self.points)
            
        # Create annotation data
        geometry_data = {
            'points': [{'x': p.x(), 'y': p.y()} for p in final_points],
            'smoothed': self.smoothing_enabled
        }
        
        # Create annotation
        annotation = ManualAnnotation(
            annotation_id=str(uuid.uuid4()),
            tool_type=self.tool_type,
            geometry_data=geometry_data,
            metadata={
                'pen_color': self.pen_color.name(),
                'pen_width': self.pen_width,
                'point_count': len(final_points),
                'smoothing_enabled': self.smoothing_enabled,
                'min_distance': self.min_distance
            }
        )
        
        return annotation
        
    def _update_path_preview(self):
        """Update the preview path item."""
        if not self.preview_item:
            return
            
        if self.smoothing_enabled and len(self.points) > 2:
            # Create smoothed path for preview
            smoothed_points = self._smooth_path(self.points)
            smooth_path = QPainterPath()
            if smoothed_points:
                smooth_path.moveTo(smoothed_points[0])
                for point in smoothed_points[1:]:
                    smooth_path.lineTo(point)
            self.preview_item.setPath(smooth_path)
        else:
            # Use raw path
            self.preview_item.setPath(self.path)
            
    def _smooth_path(self, points: List[QPoint]) -> List[QPoint]:
        """
        Apply smoothing to the path points using simple averaging.
        
        Args:
            points: Raw points from drawing
            
        Returns:
            List of smoothed points
        """
        if len(points) < 3:
            return points
            
        smoothed = [points[0]]  # Keep first point
        
        # Apply simple smoothing filter
        for i in range(1, len(points) - 1):
            prev_point = points[i - 1]
            curr_point = points[i]
            next_point = points[i + 1]
            
            # Average with neighbors
            smooth_x = (prev_point.x() + curr_point.x() + next_point.x()) / 3
            smooth_y = (prev_point.y() + curr_point.y() + next_point.y()) / 3
            
            smoothed.append(QPoint(int(smooth_x), int(smooth_y)))
            
        smoothed.append(points[-1])  # Keep last point
        return smoothed
        
    def set_smoothing(self, enabled: bool):
        """Enable or disable path smoothing."""
        self.smoothing_enabled = enabled
        
    def set_min_distance(self, distance: float):
        """Set minimum distance between points."""
        self.min_distance = max(1.0, distance)
        
    def _cancel_drawing_impl(self):
        """Cancel drawing and clear points."""
        self.points.clear()
        self.path = QPainterPath() 