"""RBAC Service for MedScan AI Role-Based Access Control.

Provides comprehensive role and permission management with medical-specific
operations, CRUD functionality, and HIPAA/GDPR compliance features.

REFACTORED: This file now delegates to modular components for better maintainability.
The original monolithic implementation has been split into specialized modules
in the rbac/ subdirectory while maintaining full backward compatibility.
"""

import logging
from typing import Any, Dict, List, Optional, Set, Tuple
from sqlalchemy.orm import Session

from ...core.utils.logging_config import get_logger
from ...database.engine import get_session
from .rbac.service import RBACService as ModularRBACService

logger = get_logger(__name__)


class RBACService:
    """Service for Role-Based Access Control operations.

    Provides medical-grade RBAC functionality with comprehensive
    permission management, audit logging, and compliance features.
    
    REFACTORED: This class now delegates to the new modular RBAC service
    to maintain backward compatibility while improving code organization.
    
    All operations are delegated to specialized managers:
    - RoleManager: Role CRUD operations
    - PermissionManager: Permission CRUD and role-permission relationships
    - RBACManager: User-role assignments and complex operations
    """

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize RBAC service.

        Args:
            db_session: Database session (optional, will create if not provided)
        """
        # Delegate to the new modular service
        self._service = ModularRBACService(db_session)
        self.db_session = self._service.db_session
        self.logger = logger

    # ===================
    # Role Management - Delegated to RoleManager
    # ===================

    def create_role(self, *args, **kwargs):
        """Create a new role. Delegates to modular service."""
        return self._service.create_role(*args, **kwargs)

    def get_role_by_id(self, role_id: str):
        """Get role by ID. Delegates to modular service."""
        return self._service.get_role_by_id(role_id)

    def get_role_by_name(self, name: str):
        """Get role by name. Delegates to modular service."""
        return self._service.get_role_by_name(name)

    def list_roles(self, *args, **kwargs):
        """List roles. Delegates to modular service."""
        return self._service.list_roles(*args, **kwargs)

    def update_role(self, *args, **kwargs):
        """Update role. Delegates to modular service."""
        return self._service.update_role(*args, **kwargs)

    def delete_role(self, role_id: str):
        """Delete role. Delegates to modular service."""
        return self._service.delete_role(role_id)

    # ===================
    # Permission Management - Delegated to PermissionManager
    # ===================

    def create_permission(self, *args, **kwargs):
        """Create a new permission. Delegates to modular service."""
        return self._service.create_permission(*args, **kwargs)

    def get_permission_by_id(self, permission_id: str):
        """Get permission by ID. Delegates to modular service."""
        return self._service.get_permission_by_id(permission_id)

    def get_permission_by_name(self, name: str):
        """Get permission by name. Delegates to modular service."""
        return self._service.get_permission_by_name(name)

    def list_permissions(self, *args, **kwargs):
        """List permissions. Delegates to modular service."""
        return self._service.list_permissions(*args, **kwargs)

    def update_permission(self, *args, **kwargs):
        """Update permission. Delegates to modular service."""
        return self._service.update_permission(*args, **kwargs)

    def delete_permission(self, permission_id: str):
        """Delete permission. Delegates to modular service."""
        return self._service.delete_permission(permission_id)

    def grant_permission_to_role(self, *args, **kwargs):
        """Grant permission to role. Delegates to modular service."""
        return self._service.grant_permission_to_role(*args, **kwargs)

    def revoke_permission_from_role(self, *args, **kwargs):
        """Revoke permission from role. Delegates to modular service."""
        return self._service.revoke_permission_from_role(*args, **kwargs)

    def get_role_permissions(self, role_id: str, active_only: bool = True):
        """Get role permissions. Delegates to modular service."""
        return self._service.get_role_permissions(role_id, active_only)

    # ===================
    # User-Role Management - Delegated to RBACManager
    # ===================

    def assign_role_to_user(self, *args, **kwargs):
        """Assign role to user. Delegates to modular service."""
        return self._service.assign_role_to_user(*args, **kwargs)

    def revoke_role_from_user(self, *args, **kwargs):
        """Revoke role from user. Delegates to modular service."""
        return self._service.revoke_role_from_user(*args, **kwargs)

    def get_user_roles(self, user_id: str, active_only: bool = True):
        """Get user roles. Delegates to modular service."""
        return self._service.get_user_roles(user_id, active_only)

    def get_user_permissions(self, user_id: str):
        """Get user permissions. Delegates to modular service."""
        return self._service.get_user_permissions(user_id)

    def user_has_permission(self, user_id: str, permission_name: str):
        """Check if user has permission. Delegates to modular service."""
        return self._service.user_has_permission(user_id, permission_name)

    # ===================
    # High-Level Operations - Delegated to RBACManager
    # ===================

    def initialize_medical_roles_and_permissions(self):
        """Initialize medical roles and permissions. Delegates to modular service."""
        return self._service.initialize_medical_roles_and_permissions()

    def clone_role(self, *args, **kwargs):
        """Clone role. Delegates to modular service."""
        return self._service.clone_role(*args, **kwargs)

    def get_role_hierarchy_tree(self):
        """Get role hierarchy tree. Delegates to modular service."""
        return self._service.get_role_hierarchy_tree()

    def transfer_user_roles(self, *args, **kwargs):
        """Transfer user roles. Delegates to modular service."""
        return self._service.transfer_user_roles(*args, **kwargs)

    # ===================
    # System Management & Reporting
    # ===================

    def get_system_stats(self):
        """Get system stats. Delegates to modular service."""
        return self._service.get_system_stats()

    def validate_user_access(self, *args, **kwargs):
        """Validate user access. Delegates to modular service."""
        return self._service.validate_user_access(*args, **kwargs)

    def get_role_usage_report(self):
        """Get role usage report. Delegates to modular service."""
        return self._service.get_role_usage_report()

    # ===================
    # Backward Compatibility Properties
    # ===================

    @property
    def role_manager(self):
        """Access to role manager for direct operations."""
        return self._service.role_manager

    @property
    def permission_manager(self):
        """Access to permission manager for direct operations."""
        return self._service.permission_manager

    @property
    def rbac_manager(self):
        """Access to RBAC manager for direct operations."""
        return self._service.rbac_manager 
