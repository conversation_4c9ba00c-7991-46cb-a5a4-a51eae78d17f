{"system_info": {"cpu_cores": 16, "memory_gb": 15.420856475830078, "platform": "win32"}, "file_statistics": {"small": ["test_data\\dicom_samples\\JLSL_08_07_0_1F.dcm", "test_data\\dicom_samples\\JLSL_16_15_1_1F.dcm", "test_data\\dicom_samples\\SC_rgb_expb.dcm", "test_data\\dicom_samples\\SC_rgb.dcm", "test_data\\dicom_samples\\SC_rgb_dcmtk_ebcr_dcmd.dcm", "test_data\\dicom_samples\\SC_rgb_dcmtk_ebcyn1_dcmd.dcm", "test_data\\dicom_samples\\SC_rgb_dcmtk_ebcyn2_dcmd.dcm", "test_data\\dicom_samples\\SC_rgb_dcmtk_ebcynp_dcmd.dcm", "test_data\\dicom_samples\\SC_rgb_dcmtk_ebcys2_dcmd.dcm", "test_data\\dicom_samples\\SC_rgb_dcmtk_ebcys4_dcmd.dcm", "test_data\\dicom_samples\\SC_ybr_full_uncompressed.dcm", "test_data\\dicom_samples\\SC_rgb_gdcm2k_uncompressed.dcm", "test_data\\dicom_samples\\emri_small_jpeg_2k_lossless_too_short.dcm", "test_data\\dicom_samples\\emri_small_jpeg_2k_lossless.dcm", "test_data\\dicom_samples\\emri_small_jpeg_ls_lossless.dcm", "test_data\\dicom_samples\\OBXXXX1A_rle.dcm", "test_data\\dicom_samples\\emri_small_RLE.dcm", "test_data\\dicom_samples\\US1_J2KI.dcm", "test_data\\dicom_samples\\SC_rgb_expb_16bit.dcm", "test_data\\dicom_samples\\SC_rgb_expb_2frame.dcm", "test_data\\dicom_samples\\SC_rgb_16bit.dcm", "test_data\\dicom_samples\\SC_rgb_2frame.dcm", "test_data\\dicom_samples\\JLSN_RGB_ILV0.dcm", "test_data\\dicom_samples\\parametric_map_float.dcm", "test_data\\dicom_samples\\emri_small.dcm", "test_data\\dicom_samples\\emri_small_big_endian.dcm", "test_data\\dicom_samples\\OBXXXX1A_rle_2frame.dcm", "test_data\\dicom_samples\\color-pl.dcm", "test_data\\dicom_samples\\color-px.dcm", "test_data\\dicom_samples\\JLSL_RGB_ILV2.dcm", "test_data\\dicom_samples\\JLSL_RGB_ILV1.dcm", "test_data\\dicom_samples\\liver_nonbyte_aligned.dcm", "test_data\\dicom_samples\\liver_expb.dcm", "test_data\\dicom_samples\\JLSL_RGB_ILV0.dcm", "test_data\\dicom_samples\\liver.dcm"], "medium": ["test_data\\dicom_samples\\693_J2KR.dcm", "test_data\\dicom_samples\\MR2_J2KI.dcm", "test_data\\dicom_samples\\JPEG-LL.dcm", "test_data\\dicom_samples\\SC_rgb_expb_32bit.dcm", "test_data\\dicom_samples\\SC_rgb_32bit.dcm", "test_data\\dicom_samples\\SC_rgb_expb_16bit_2frame.dcm", "test_data\\dicom_samples\\SC_rgb_16bit_2frame.dcm", "test_data\\dicom_samples\\parametric_map_double_float.dcm", "test_data\\dicom_samples\\US1_J2KR.dcm", "test_data\\dicom_samples\\bad_sequence.dcm", "test_data\\dicom_samples\\explicit_VR-UN.dcm", "test_data\\dicom_samples\\RG3_J2KI.dcm", "test_data\\dicom_samples\\JPGLosslessP14SV1_1s_1f_8b.dcm", "test_data\\dicom_samples\\HTJ2K_08_RGB.dcm", "test_data\\dicom_samples\\SC_rgb_expb_32bit_2frame.dcm", "test_data\\dicom_samples\\SC_rgb_32bit_2frame.dcm", "test_data\\dicom_samples\\CRIMAGEI", "test_data\\dicom_samples\\XAIMAGEI", "test_data\\dicom_samples\\CRIMAGEA", "test_data\\dicom_samples\\XAIMAGEA", "test_data\\dicom_samples\\vlut_04.dcm", "test_data\\dicom_samples\\DXIMAGEI", "test_data\\dicom_samples\\DXIMAGEA", "test_data\\dicom_samples\\MGIMAGEI", "test_data\\dicom_samples\\MGIMAGEA", "test_data\\dicom_samples\\OT-PAL-8-face.dcm", "test_data\\dicom_samples\\HTJ2KLossless_08_RGB.dcm", "test_data\\dicom_samples\\OBXXXX1A_expb.dcm", "test_data\\dicom_samples\\OBXXXX1A.dcm", "test_data\\dicom_samples\\MR-SIEMENS-DICOM-WithOverlays.dcm", "test_data\\dicom_samples\\693_UNCR.dcm", "test_data\\dicom_samples\\693_UNCI.dcm", "test_data\\dicom_samples\\JPEG2000_UNC.dcm", "test_data\\dicom_samples\\Sola_L-spine.MR.Comp_DR-Gain_DR.1005.1.2021.04.27.14.33.25.557.74030119.dcm", "test_data\\dicom_samples\\Altea_t2_stir_sag_DR_88115187.dcm", "test_data\\dicom_samples\\Vida_Head.MR.Comp_DR-Gain_DR.1005.1.2021.04.27.14.20.13.818.14380335.dcm", "test_data\\dicom_samples\\mlut_18.dcm", "test_data\\dicom_samples\\MR2_J2KR.dcm", "test_data\\dicom_samples\\Vida_Knee.MR.Comp_DR-Gain_DR.1005.1.2021.04.27.14.44.20.58.57125557.dcm", "test_data\\dicom_samples\\RG1_J2KI.dcm", "test_data\\dicom_samples\\Sola_L-spine.MR.Comp_DR-Gain_DR.1005.2.2021.04.27.14.33.34.775.34421056.dcm", "test_data\\dicom_samples\\RG3_J2KR.dcm", "test_data\\dicom_samples\\gdcm-US-ALOKA-16.dcm", "test_data\\dicom_samples\\gdcm-US-ALOKA-16_big.dcm", "test_data\\dicom_samples\\US1_UNCR.dcm", "test_data\\dicom_samples\\US1_UNCI.dcm", "test_data\\dicom_samples\\OBXXXX1A_expb_2frame.dcm", "test_data\\dicom_samples\\OBXXXX1A_2frame.dcm"], "large": ["test_data\\dicom_samples\\eCT_Supplemental.dcm", "test_data\\dicom_samples\\MR2_UNCR.dcm", "test_data\\dicom_samples\\MR2_UNCI.dcm", "test_data\\dicom_samples\\RG1_J2KR.dcm"], "xlarge": ["test_data\\dicom_samples\\color3d_jpeg_baseline.dcm", "test_data\\dicom_samples\\RG3_UNCR.dcm", "test_data\\dicom_samples\\RG3_UNCI.dcm", "test_data\\dicom_samples\\RG1_UNCR.dcm", "test_data\\dicom_samples\\RG1_UNCI.dcm"]}, "dicom_loading_results": {"small": {"file_count": 35, "standard_loading": {"avg_time": 0.0015032291412353516, "total_time": 0.004509687423706055, "avg_memory_mb": 0.037760416666666664, "files_tested": 3}, "lazy_loading": {"avg_time": 0.0075286865234375, "total_time": 0.0376434326171875, "avg_memory_mb": 0.059375, "files_tested": 5}, "file_sizes_mb": [0.0056591033935546875, 0.021417617797851562, 0.02983856201171875, 0.029850006103515625, 0.030195236206054688]}, "medium": {"file_count": 48, "standard_loading": {"avg_time": 0.07120740413665771, "total_time": 0.28482961654663086, "avg_memory_mb": 0.7529296875, "files_tested": 4}, "lazy_loading": {"avg_time": 0.0010010242462158204, "total_time": 0.0050051212310791016, "avg_memory_mb": 0.04453125, "files_tested": 5}, "file_sizes_mb": [0.10210037231445312, 0.10828971862792969, 0.11347389221191406, 0.11565017700195312, 0.11566162109375]}, "large": {"file_count": 4, "standard_loading": {"avg_time": 0.15314626693725586, "total_time": 0.6125850677490234, "avg_memory_mb": 2.927734375, "files_tested": 4}, "lazy_loading": {"avg_time": 0.003627181053161621, "total_time": 0.014508724212646484, "avg_memory_mb": 0.0029296875, "files_tested": 4}, "file_sizes_mb": [1.0041255950927734, 2.001750946044922, 2.002035140991211, 4.086679458618164]}, "xlarge": {"file_count": 5, "standard_loading": {"avg_time": 0.5709664821624756, "total_time": 2.854832410812378, "avg_memory_mb": 0.56171875, "files_tested": 5}, "lazy_loading": {"avg_time": 0.008017206192016601, "total_time": 0.04008603096008301, "avg_memory_mb": 0.0, "files_tested": 5}, "file_sizes_mb": [5.859365463256836, 5.909538269042969, 5.909826278686523, 6.866508483886719, 6.866794586181641]}}, "opencv_results": {"small": {"error": "Unable to decompress 'JPEG-LS Lossless Image Compression' pixel data because all plugins are missing dependencies:\n\tgdcm - requires gdcm>=3.0.10\n\tpylibjpeg - requires pylibjpeg>=2.0 and pylibjpeg-libjpeg>=2.1\n\tpyjpegls - requires numpy and pyjpegls>=1.2"}, "medium": {"original_shape": [512, 512], "file_size_mb": 0.10210037231445312, "128x128": {"standard_opencv": {"avg_time": 0.0003345807393391927, "min_time": 0.0, "output_shape": [128, 128]}, "optimized_opencv": {"error": "cannot import name 'setup_logging' from 'medscan_ai.core.utils.logging_config' (D:\\MedScan AI\\src\\medscan_ai\\core\\utils\\logging_config.py)"}}, "256x256": {"standard_opencv": {"avg_time": 0.0, "min_time": 0.0, "output_shape": [256, 256]}, "optimized_opencv": {"error": "cannot import name 'setup_logging' from 'medscan_ai.core.utils.logging_config' (D:\\MedScan AI\\src\\medscan_ai\\core\\utils\\logging_config.py)"}}, "512x512": {"standard_opencv": {"avg_time": 0.00033601125081380207, "min_time": 0.0, "output_shape": [512, 512]}, "optimized_opencv": {"error": "cannot import name 'setup_logging' from 'medscan_ai.core.utils.logging_config' (D:\\MedScan AI\\src\\medscan_ai\\core\\utils\\logging_config.py)"}}}, "large": {"original_shape": [512, 512], "file_size_mb": 1.0041255950927734, "128x128": {"standard_opencv": {"avg_time": 0.0, "min_time": 0.0, "output_shape": [128, 128]}, "optimized_opencv": {"error": "cannot import name 'setup_logging' from 'medscan_ai.core.utils.logging_config' (D:\\MedScan AI\\src\\medscan_ai\\core\\utils\\logging_config.py)"}}, "256x256": {"standard_opencv": {"avg_time": 0.0003352959950764974, "min_time": 0.0, "output_shape": [256, 256]}, "optimized_opencv": {"error": "cannot import name 'setup_logging' from 'medscan_ai.core.utils.logging_config' (D:\\MedScan AI\\src\\medscan_ai\\core\\utils\\logging_config.py)"}}, "512x512": {"standard_opencv": {"avg_time": 0.0003178914388020833, "min_time": 0.0, "output_shape": [512, 512]}, "optimized_opencv": {"error": "cannot import name 'setup_logging' from 'medscan_ai.core.utils.logging_config' (D:\\MedScan AI\\src\\medscan_ai\\core\\utils\\logging_config.py)"}}}}, "ai_results": {"file_1": {"filename": "693_J2KR.dcm", "file_size_mb": 0.10210037231445312, "ai_224x224": {"processing_time": 0.01959681510925293, "output_shape": [224, 224], "output_dtype": "float32", "preprocessing_info": {"original_shape": [512, 512], "output_shape": [224, 224], "output_format": "float32", "output_range": [0.0, 1.0], "windowing_applied": true, "grayscale_conversion": true, "resized": true, "target_size": [224, 224], "bit_depth": 16, "photometric_interpretation": "MONOCHROME2"}}, "ai_256x256": {"processing_time": 0.015510082244873047, "output_shape": [256, 256], "output_dtype": "float32", "preprocessing_info": {"original_shape": [512, 512], "output_shape": [256, 256], "output_format": "float32", "output_range": [0.0, 1.0], "windowing_applied": true, "grayscale_conversion": true, "resized": true, "target_size": [256, 256], "bit_depth": 16, "photometric_interpretation": "MONOCHROME2"}}, "ai_512x512": {"processing_time": 0.01751112937927246, "output_shape": [512, 512], "output_dtype": "float32", "preprocessing_info": {"original_shape": [512, 512], "output_shape": [512, 512], "output_format": "float32", "output_range": [0.0, 1.0], "windowing_applied": true, "grayscale_conversion": true, "resized": true, "target_size": [512, 512], "bit_depth": 16, "photometric_interpretation": "MONOCHROME2"}}}, "file_2": {"filename": "MR2_J2KI.dcm", "file_size_mb": 0.10828971862792969, "ai_224x224": {"processing_time": 0.10072565078735352, "output_shape": [224, 224], "output_dtype": "float32", "preprocessing_info": {"original_shape": [1024, 1024], "output_shape": [224, 224], "output_format": "float32", "output_range": [0.0, 1.0], "windowing_applied": true, "grayscale_conversion": true, "resized": true, "target_size": [224, 224], "bit_depth": 12, "photometric_interpretation": "MONOCHROME2"}}, "ai_256x256": {"processing_time": 0.09212636947631836, "output_shape": [256, 256], "output_dtype": "float32", "preprocessing_info": {"original_shape": [1024, 1024], "output_shape": [256, 256], "output_format": "float32", "output_range": [0.0, 1.0], "windowing_applied": true, "grayscale_conversion": true, "resized": true, "target_size": [256, 256], "bit_depth": 12, "photometric_interpretation": "MONOCHROME2"}}, "ai_512x512": {"processing_time": 0.09260869026184082, "output_shape": [512, 512], "output_dtype": "float32", "preprocessing_info": {"original_shape": [1024, 1024], "output_shape": [512, 512], "output_format": "float32", "output_range": [0.0, 1.0], "windowing_applied": true, "grayscale_conversion": true, "resized": true, "target_size": [512, 512], "bit_depth": 12, "photometric_interpretation": "MONOCHROME2"}}}, "file_3": {"filename": "JPEG-LL.dcm", "file_size_mb": 0.11347389221191406, "error": "Unable to decompress 'JPEG Lossless, Non-Hierarchical, First-Order Prediction (Process 14 [Selection Value 1])' pixel data because all plugins are missing dependencies:\n\tgdcm - requires gdcm>=3.0.10\n\tpylibjpeg - requires pylibjpeg>=2.0 and pylibjpeg-libjpeg>=2.1"}}, "batch_results": {"batch_3": {"error": "Only prepared 1/3 files"}, "batch_5": {"error": "Only prepared 3/5 files"}, "batch_10": {"error": "Only prepared 8/10 files"}}}