"""
RBAC Service Module for MedScan AI RBAC System
Main service class providing unified interface for all RBAC operations

Extracted and refactored from the original rbac_service.py file for better modularity.
This service orchestrates the role manager, permission manager, and RBAC manager.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple
from sqlalchemy.orm import Session

from ....core.utils.logging_config import get_logger
from ....database.engine import get_session
from ....database.models import UserRole
from .manager import RBACManager
from .roles import RoleManager
from .permissions import PermissionManager

logger = get_logger(__name__)


class RBACService:
    """
    Main RBAC Service providing unified interface for all role and permission operations.
    
    This service acts as the primary entry point for RBAC functionality and maintains
    backward compatibility with the original monolithic rbac_service.py implementation.
    """

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize RBAC service.

        Args:
            db_session: Database session (optional, will create if not provided)
        """
        self.db_session = db_session or get_session()
        self.logger = logger
        
        # Initialize managers
        self.rbac_manager = RBACManager(self.db_session)
        self.role_manager = RoleManager(self.db_session)
        self.permission_manager = PermissionManager(self.db_session)

    # ===================
    # Role Management (delegated to RoleManager)
    # ===================

    def create_role(self, *args, **kwargs):
        """Create a new role. Delegates to RoleManager."""
        return self.role_manager.create_role(*args, **kwargs)

    def get_role_by_id(self, role_id: str):
        """Get role by ID. Delegates to RoleManager."""
        return self.role_manager.get_role_by_id(role_id)

    def get_role_by_name(self, name: str):
        """Get role by name. Delegates to RoleManager."""
        return self.role_manager.get_role_by_name(name)

    def list_roles(self, *args, **kwargs):
        """List roles. Delegates to RoleManager."""
        return self.role_manager.list_roles(*args, **kwargs)

    def update_role(self, *args, **kwargs):
        """Update role. Delegates to RoleManager."""
        return self.role_manager.update_role(*args, **kwargs)

    def delete_role(self, role_id: str):
        """Delete role. Delegates to RoleManager."""
        return self.role_manager.delete_role(role_id)

    # ===================
    # Permission Management (delegated to PermissionManager)
    # ===================

    def create_permission(self, *args, **kwargs):
        """Create a new permission. Delegates to PermissionManager."""
        return self.permission_manager.create_permission(*args, **kwargs)

    def get_permission_by_id(self, permission_id: str):
        """Get permission by ID. Delegates to PermissionManager."""
        return self.permission_manager.get_permission_by_id(permission_id)

    def get_permission_by_name(self, name: str):
        """Get permission by name. Delegates to PermissionManager."""
        return self.permission_manager.get_permission_by_name(name)

    def list_permissions(self, *args, **kwargs):
        """List permissions. Delegates to PermissionManager."""
        return self.permission_manager.list_permissions(*args, **kwargs)

    def update_permission(self, *args, **kwargs):
        """Update permission. Delegates to PermissionManager."""
        return self.permission_manager.update_permission(*args, **kwargs)

    def delete_permission(self, permission_id: str):
        """Delete permission. Delegates to PermissionManager."""
        return self.permission_manager.delete_permission(permission_id)

    def grant_permission_to_role(self, *args, **kwargs):
        """Grant permission to role. Delegates to PermissionManager."""
        return self.permission_manager.grant_permission_to_role(*args, **kwargs)

    def revoke_permission_from_role(self, *args, **kwargs):
        """Revoke permission from role. Delegates to PermissionManager."""
        return self.permission_manager.revoke_permission_from_role(*args, **kwargs)

    def get_role_permissions(self, role_id: str, active_only: bool = True):
        """Get role permissions. Delegates to PermissionManager."""
        return self.permission_manager.get_role_permissions(role_id, active_only)

    # ===================
    # User Management (delegated to RBACManager)
    # ===================

    def assign_role_to_user(self, *args, **kwargs):
        """Assign role to user. Delegates to RBACManager."""
        return self.rbac_manager.assign_role_to_user(*args, **kwargs)

    def revoke_role_from_user(self, *args, **kwargs):
        """Revoke role from user. Delegates to RBACManager."""
        return self.rbac_manager.revoke_role_from_user(*args, **kwargs)

    def get_user_roles(self, user_id: str, active_only: bool = True):
        """Get user roles. Delegates to RBACManager."""
        return self.rbac_manager.get_user_roles(user_id, active_only)

    def get_user_permissions(self, user_id: str):
        """Get user permissions. Delegates to RBACManager."""
        return self.rbac_manager.get_user_permissions(user_id)

    def user_has_permission(self, user_id: str, permission_name: str):
        """Check if user has permission. Delegates to RBACManager."""
        return self.rbac_manager.user_has_permission(user_id, permission_name)

    # ===================
    # High-Level Operations (delegated to RBACManager)
    # ===================

    def initialize_medical_roles_and_permissions(self):
        """Initialize medical roles and permissions. Delegates to RBACManager."""
        return self.rbac_manager.initialize_medical_roles_and_permissions()

    def clone_role(self, *args, **kwargs):
        """Clone role. Delegates to RBACManager."""
        return self.rbac_manager.clone_role(*args, **kwargs)

    def get_role_hierarchy_tree(self):
        """Get role hierarchy tree. Delegates to RBACManager."""
        return self.rbac_manager.get_role_hierarchy_tree()

    def transfer_user_roles(self, *args, **kwargs):
        """Transfer user roles. Delegates to RBACManager."""
        return self.rbac_manager.transfer_user_roles(*args, **kwargs)

    # ===================
    # Convenience Methods
    # ===================

    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive RBAC system statistics.
        
        Returns:
            Dictionary containing system statistics
        """
        try:
            # Get role statistics
            total_roles = len(self.role_manager.list_roles(active_only=False))
            active_roles = len(self.role_manager.list_roles(active_only=True))
            
            # Get permission statistics
            total_permissions = len(self.permission_manager.list_permissions(active_only=False))
            active_permissions = len(self.permission_manager.list_permissions(active_only=True))
            high_risk_permissions = len(self.permission_manager.get_high_risk_permissions())
            phi_permissions = len(self.permission_manager.get_phi_permissions())
            mfa_required_permissions = len(self.permission_manager.get_mfa_required_permissions())
            
            # Get user assignment statistics
            total_assignments = (
                self.db_session.query(UserRole)
                .count()
            )
            active_assignments = (
                self.db_session.query(UserRole)
                .filter(UserRole.is_active == True)
                .count()
            )
            
            return {
                'roles': {
                    'total': total_roles,
                    'active': active_roles,
                    'inactive': total_roles - active_roles
                },
                'permissions': {
                    'total': total_permissions,
                    'active': active_permissions,
                    'inactive': total_permissions - active_permissions,
                    'high_risk': high_risk_permissions,
                    'phi_access': phi_permissions,
                    'mfa_required': mfa_required_permissions
                },
                'user_assignments': {
                    'total': total_assignments,
                    'active': active_assignments,
                    'revoked': total_assignments - active_assignments
                },
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get system stats: {str(e)}")
            return {
                'error': str(e),
                'generated_at': datetime.utcnow().isoformat()
            }

    def validate_user_access(
        self, 
        user_id: str, 
        required_permissions: List[str],
        require_all: bool = True
    ) -> Dict[str, Any]:
        """Validate user access against multiple permissions.
        
        Args:
            user_id: User identifier
            required_permissions: List of required permission names
            require_all: Whether user must have all permissions (True) or any (False)
            
        Returns:
            Dictionary with validation results
        """
        user_permissions = self.get_user_permissions(user_id)
        
        results = {}
        has_access = []
        missing_access = []
        
        for permission in required_permissions:
            if permission in user_permissions:
                has_access.append(permission)
                results[permission] = True
            else:
                missing_access.append(permission)
                results[permission] = False
        
        if require_all:
            overall_access = len(missing_access) == 0
        else:
            overall_access = len(has_access) > 0
        
        return {
            'user_id': user_id,
            'overall_access': overall_access,
            'require_all': require_all,
            'total_required': len(required_permissions),
            'has_access': has_access,
            'missing_access': missing_access,
            'detailed_results': results,
            'checked_at': datetime.utcnow().isoformat()
        }

    def get_role_usage_report(self) -> Dict[str, Any]:
        """Generate role usage report.
        
        Returns:
            Dictionary containing role usage statistics
        """
        roles = self.role_manager.list_roles(active_only=True)
        
        role_usage = []
        
        for role in roles:
            # Count active users with this role
            user_count = (
                self.db_session.query(UserRole)
                .filter(
                    UserRole.role_id == role.id,
                    UserRole.is_active == True
                )
                .count()
            )
            
            # Get role permissions
            permissions = self.permission_manager.get_role_permissions(role.id, active_only=True)
            
            role_usage.append({
                'role_id': role.id,
                'role_name': role.name,
                'display_name': role.display_name,
                'hierarchy_level': role.hierarchy_level,
                'security_clearance': role.security_clearance,
                'medical_specialty': role.medical_specialty,
                'user_count': user_count,
                'permission_count': len(permissions),
                'max_users': role.max_users,
                'utilization_rate': (user_count / role.max_users * 100) if role.max_users else None
            })
        
        # Sort by user count descending
        role_usage.sort(key=lambda x: x['user_count'], reverse=True)
        
        return {
            'total_roles': len(role_usage),
            'roles': role_usage,
            'generated_at': datetime.utcnow().isoformat()
        } 
