"""
Security Testing Module for MedScan AI
Comprehensive security testing and validation framework

This package provides:
- Core security validation and testing (SecurityValidator)
- Penetration testing capabilities (PenetrationTester)
- Compliance testing for HIPAA/GDPR (ComplianceTester)
- Authentication security testing (AuthenticationSecurityTests)
- Unified security testing interface
"""

from .security_validator import SecurityValida<PERSON>, SecurityTestResult, run_security_validation
from .penetration_tests import PenetrationTester, run_penetration_tests
from .compliance_tests import ComplianceTester, run_compliance_tests
from .auth_security_tests import AuthenticationSecurityTests, run_authentication_security_tests

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class UnifiedSecurityTesting:
    """
    Unified interface for all security testing capabilities
    """

    def __init__(self):
        """Initialize unified security testing"""
        self.security_validator = SecurityValidator()
        self.penetration_tester = PenetrationTester()
        self.compliance_tester = ComplianceTester()
        self.auth_tester = AuthenticationSecurityTests()
        
        logger.info("Unified security testing initialized")

    def run_all_security_tests(self) -> Dict[str, Any]:
        """
        Run all security tests and return comprehensive results
        
        Returns:
            Dictionary containing all test results
        """
        logger.info("Starting unified security testing")
        
        results = {
            "unified_security_assessment": {
                "timestamp": SecurityTestResult(
                    test_name="timestamp",
                    passed=True,
                    details=""
                ).timestamp.isoformat(),
                "test_categories": {}
            }
        }
        
        # Run core security validation
        try:
            logger.info("Running core security validation tests")
            results["unified_security_assessment"]["test_categories"]["core_security"] = \
                self.security_validator.run_comprehensive_security_tests()
        except Exception as e:
            logger.error(f"Core security testing failed: {e}")
            results["unified_security_assessment"]["test_categories"]["core_security"] = {
                "error": str(e),
                "status": "failed"
            }
        
        # Run authentication security tests
        try:
            logger.info("Running authentication security tests")
            results["unified_security_assessment"]["test_categories"]["authentication_security"] = \
                self.auth_tester.run_comprehensive_auth_tests()
        except Exception as e:
            logger.error(f"Authentication security testing failed: {e}")
            results["unified_security_assessment"]["test_categories"]["authentication_security"] = {
                "error": str(e),
                "status": "failed"
            }
        
        # Run penetration tests
        try:
            logger.info("Running penetration tests")
            results["unified_security_assessment"]["test_categories"]["penetration_testing"] = \
                self.penetration_tester.run_comprehensive_penetration_tests()
        except Exception as e:
            logger.error(f"Penetration testing failed: {e}")
            results["unified_security_assessment"]["test_categories"]["penetration_testing"] = {
                "error": str(e),
                "status": "failed"
            }
        
        # Run compliance tests
        try:
            logger.info("Running compliance tests")
            results["unified_security_assessment"]["test_categories"]["compliance_testing"] = \
                self.compliance_tester.run_comprehensive_compliance_tests()
        except Exception as e:
            logger.error(f"Compliance testing failed: {e}")
            results["unified_security_assessment"]["test_categories"]["compliance_testing"] = {
                "error": str(e),
                "status": "failed"
            }
        
        # Generate unified summary
        results["unified_security_assessment"]["summary"] = self._generate_unified_summary(results)
        
        logger.info("Unified security testing completed")
        return results

    def _generate_unified_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate unified summary of all test results"""
        summary = {
            "total_test_categories": 0,
            "passed_categories": 0,
            "failed_categories": 0,
            "total_tests": 0,
            "total_passed": 0,
            "total_failed": 0,
            "overall_security_score": 0.0,
            "critical_issues": 0,
            "high_issues": 0,
            "medium_issues": 0,
            "low_issues": 0,
            "overall_readiness": "Unknown"
        }
        
        test_categories = results["unified_security_assessment"]["test_categories"]
        
        for category_name, category_results in test_categories.items():
            summary["total_test_categories"] += 1
            
            if isinstance(category_results, dict) and "error" not in category_results:
                summary["passed_categories"] += 1
                
                # Extract test statistics if available
                if "summary" in category_results:
                    cat_summary = category_results["summary"]
                    summary["total_tests"] += cat_summary.get("total_tests", 0)
                    summary["total_passed"] += cat_summary.get("passed_tests", 0)
                    summary["total_failed"] += cat_summary.get("failed_tests", 0)
                
                # Extract issue counts if available
                if "issues_by_severity" in category_results:
                    issues = category_results["issues_by_severity"]
                    summary["critical_issues"] += issues.get("critical", 0)
                    summary["high_issues"] += issues.get("high", 0)
                    summary["medium_issues"] += issues.get("medium", 0)
                    summary["low_issues"] += issues.get("low", 0)
            else:
                summary["failed_categories"] += 1
        
        # Calculate overall security score
        if summary["total_tests"] > 0:
            summary["overall_security_score"] = round(
                (summary["total_passed"] / summary["total_tests"]) * 100, 2
            )
        
        # Determine overall readiness
        if summary["critical_issues"] == 0 and summary["high_issues"] == 0:
            summary["overall_readiness"] = "Production Ready"
        elif summary["critical_issues"] == 0:
            summary["overall_readiness"] = "Staging Ready"
        else:
            summary["overall_readiness"] = "Development Only"
        
        return summary

    def get_security_statistics(self) -> Dict[str, Any]:
        """Get security testing statistics"""
        return {
            "available_test_categories": [
                "core_security",
                "authentication_security", 
                "penetration_testing",
                "compliance_testing"
            ],
            "total_available_tests": "50+",
            "supported_compliance_frameworks": ["HIPAA", "GDPR"],
            "security_validation_coverage": [
                "Encryption (AES-256-GCM)",
                "TLS 1.3 Enforcement",
                "Authentication & Authorization",
                "RBAC Implementation",
                "Session Management",
                "Multi-Factor Authentication",
                "Vulnerability Scanning",
                "Penetration Testing",
                "Compliance Validation"
            ]
        }

    def validate_all_services(self) -> bool:
        """Validate that all security services are properly initialized"""
        services_status = {
            "security_validator": self.security_validator is not None,
            "penetration_tester": self.penetration_tester is not None,
            "compliance_tester": self.compliance_tester is not None,
            "auth_tester": self.auth_tester is not None and self.auth_tester.services_available
        }
        
        all_available = all(services_status.values())
        
        if not all_available:
            logger.warning(f"Some security services not available: {services_status}")
        
        return all_available


def run_unified_security_assessment() -> Dict[str, Any]:
    """
    Run comprehensive unified security assessment
    
    Returns:
        Dictionary containing all security test results
    """
    unified_tester = UnifiedSecurityTesting()
    return unified_tester.run_all_security_tests()


def validate_security_testing_setup() -> bool:
    """
    Validate that security testing framework is properly set up
    
    Returns:
        True if all components are available, False otherwise
    """
    unified_tester = UnifiedSecurityTesting()
    return unified_tester.validate_all_services()


__all__ = [
    'SecurityValidator',
    'SecurityTestResult', 
    'PenetrationTester',
    'ComplianceTester',
    'AuthenticationSecurityTests',
    'UnifiedSecurityTesting',
    'run_security_validation',
    'run_penetration_tests',
    'run_compliance_tests',
    'run_authentication_security_tests',
    'run_unified_security_assessment',
    'validate_security_testing_setup'
]
