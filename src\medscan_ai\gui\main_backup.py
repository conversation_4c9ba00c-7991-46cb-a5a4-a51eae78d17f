"""
Main entry point for MedScan AI GUI application.
Modern medical imaging analysis interface for healthcare professionals.
"""

import os
import sys
from typing import Optional

import numpy as np
from PySide6.QtCore import QSize, Qt
from PySide6.QtGui import QColor, QFont, QIcon, QPalette, QPixmap
from PySide6.QtWidgets import (
    QApplication,
    QFileDialog,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QMainWindow,
    QMenu,
    QMenuBar,
    QMessageBox,
    QPushButton,
    QSizePolicy,
    QSlider,
    QSpinBox,
    QSplitter,
    QStatusBar,
    QTextEdit,
    QToolBar,
    QVBoxLayout,
    QWidget,
)

# Import DICOM functionality
from ..dicom import Dicom<PERSON>rror, DicomReader
from .utils import (
    ImageDisplayHelper,
    InteractiveImageViewer,
    create_error_pixmap,
    numpy_to_qpixmap,
    scale_pixmap_to_fit,
)


class MedScanMainWindow(QMainWindow):
    """
    Main application window for MedScan AI.
    Designed for medical professionals with clean, efficient interface.
    """

    def __init__(self):
        """Initialize the main window with medical-focused UI."""
        super().__init__()

        # Initialize DICOM functionality
        self._dicom_reader = DicomReader(validate_on_load=True)
        self._image_helper = ImageDisplayHelper()
        self._current_pixmap = None
        self._patient_info_widget = None

        # Windowing state
        self._current_window_center = 0
        self._current_window_width = 100
        self._raw_pixel_array = None
        self._windowing_enabled = False

        self._init_window()
        self._create_menu_bar()
        self._create_toolbar()
        self._create_central_widget()
        self._create_status_bar()
        self._apply_medical_theme()

    def _init_window(self):
        """Initialize the main window properties."""
        self.setWindowTitle("MedScan AI - Medical Imaging Analysis v0.1.0")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)

        # Set window icon (placeholder for now)
        # self.setWindowIcon(QIcon("assets/icons/medscan_icon.png"))

    def _create_menu_bar(self):
        """Create comprehensive menu bar for medical workflow."""
        menubar = self.menuBar()

        # File Menu - Patient and study management
        file_menu = menubar.addMenu("&File")
        file_menu.addAction("&New Patient Study", self._placeholder_action)
        file_menu.addAction("&Open DICOM Files...", self._open_dicom_files)
        file_menu.addAction("Open &Recent", self._placeholder_action)
        file_menu.addSeparator()
        file_menu.addAction("&Import Images...", self._placeholder_action)
        file_menu.addAction("&Export Results...", self._placeholder_action)
        file_menu.addSeparator()
        file_menu.addAction("E&xit", self.close)

        # Patient Menu - Patient management
        patient_menu = menubar.addMenu("&Patient")
        patient_menu.addAction("&Patient Information", self._placeholder_action)
        patient_menu.addAction("&Medical History", self._placeholder_action)
        patient_menu.addAction("&Previous Studies", self._placeholder_action)
        patient_menu.addSeparator()
        patient_menu.addAction("&Search Patients...", self._placeholder_action)

        # Analysis Menu - AI and image analysis
        analysis_menu = menubar.addMenu("&Analysis")
        analysis_menu.addAction("&Run AI Analysis", self._placeholder_action)
        analysis_menu.addAction("&Manual Annotation", self._placeholder_action)
        analysis_menu.addAction("&Comparison View", self._placeholder_action)
        analysis_menu.addSeparator()
        analysis_menu.addAction("&Analysis History", self._placeholder_action)
        analysis_menu.addAction("&Generate Report", self._placeholder_action)

        # View Menu - Display and visualization
        view_menu = menubar.addMenu("&View")
        view_menu.addAction("&Image Viewer", self._placeholder_action)
        view_menu.addAction("&3D Reconstruction", self._placeholder_action)
        view_menu.addAction("&Multi-planar View", self._placeholder_action)
        view_menu.addSeparator()
        view_menu.addAction("&Zoom In", self._placeholder_action)
        view_menu.addAction("&Zoom Out", self._placeholder_action)
        view_menu.addAction("&Reset View", self._placeholder_action)
        view_menu.addSeparator()
        view_menu.addAction("&Fullscreen", self._placeholder_action)

        # Tools Menu - Utilities and preferences
        tools_menu = menubar.addMenu("&Tools")
        tools_menu.addAction("&Measurement Tools", self._placeholder_action)
        tools_menu.addAction("&DICOM Tags Viewer", self._placeholder_action)
        tools_menu.addAction("&Batch Processing", self._placeholder_action)
        tools_menu.addSeparator()
        tools_menu.addAction("&Preferences...", self._placeholder_action)
        tools_menu.addAction("&Security Settings...", self._placeholder_action)

        # Help Menu - Documentation and support
        help_menu = menubar.addMenu("&Help")
        help_menu.addAction("&User Guide", self._placeholder_action)
        help_menu.addAction("&Medical Guidelines", self._placeholder_action)
        help_menu.addAction("&Keyboard Shortcuts", self._placeholder_action)
        help_menu.addSeparator()
        help_menu.addAction("&About MedScan AI", self._show_about)
        help_menu.addAction("&System Information", self._placeholder_action)

    def _create_toolbar(self):
        """Create main toolbar with common medical workflow actions."""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)

        # Common medical workflow actions
        toolbar.addAction("📁 Open", self._open_dicom_files)
        toolbar.addAction("👤 Patient", self._placeholder_action)
        toolbar.addAction("🔍 Analyze", self._placeholder_action)
        toolbar.addAction("📊 Report", self._placeholder_action)
        toolbar.addSeparator()
        toolbar.addAction("🔍+ Zoom In", self._zoom_in)
        toolbar.addAction("🔍- Zoom Out", self._zoom_out)
        toolbar.addAction("🔄 Reset", self._reset_view)

        self.addToolBar(toolbar)

    def _create_central_widget(self):
        """Create the central widget with medical workflow layout."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(8)
        main_layout.setContentsMargins(8, 8, 8, 8)

        # Create splitter for resizable panels
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setHandleWidth(6)
        self.main_splitter.setChildrenCollapsible(True)

        # Left panel - Patient/Study information
        left_panel = self._create_left_panel()
        self.main_splitter.addWidget(left_panel)

        # Center panel - Image viewer placeholder
        center_panel = self._create_center_panel()
        self.main_splitter.addWidget(center_panel)

        # Right panel - Analysis results
        right_panel = self._create_right_panel()
        self.main_splitter.addWidget(right_panel)

        # Set initial proportional sizes (left: 250px, center: flexible, right: 350px)
        self.main_splitter.setSizes([250, 600, 350])

        # Set stretch factors for responsive behavior
        self.main_splitter.setStretchFactor(0, 0)  # Left panel - fixed width
        self.main_splitter.setStretchFactor(1, 1)  # Center panel - stretches
        self.main_splitter.setStretchFactor(2, 0)  # Right panel - fixed width

        # Connect resize event to handle responsive behavior
        self.main_splitter.splitterMoved.connect(self._on_splitter_moved)

        main_layout.addWidget(self.main_splitter)

    def _create_left_panel(self):
        """Create left panel for patient and study information."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(200)
        panel.setMaximumWidth(400)

        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # Panel title
        title = QLabel("📋 Patient Information")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet(
            "color: #2E7D32; padding: 8px; background-color: #E8F5E8; border-radius: 4px;"
        )
        layout.addWidget(title)

        # Patient info with responsive sizing
        self._patient_info_widget = QTextEdit()
        self._patient_info_widget.setPlainText(
            """Patient: [No patient selected]
ID: ---
DOB: ---
Gender: ---
Study Date: ---
Modality: ---
Series: ---

Status: Ready for new patient
"""
        )
        self._patient_info_widget.setMinimumHeight(150)
        self._patient_info_widget.setMaximumHeight(250)
        self._patient_info_widget.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Preferred
        )
        layout.addWidget(self._patient_info_widget)

        # Study list placeholder with responsive sizing
        study_title = QLabel("📁 Studies")
        study_title.setFont(QFont("Arial", 10, QFont.Bold))
        study_title.setStyleSheet("color: #1976D2; padding: 4px;")
        layout.addWidget(study_title)

        study_list = QTextEdit()
        study_list.setPlainText(
            "No studies loaded.\n\nUse File > Open DICOM Files\nto load medical images."
        )
        study_list.setMinimumHeight(100)
        study_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(study_list)

        # Add responsive spacing
        layout.addStretch()

        return panel

    def _create_center_panel(self):
        """Create center panel for image viewing with responsive sizing."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(300)

        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # Panel title with responsive styling
        title = QLabel("🖼️ Medical Image Viewer")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet(
            "color: #1976D2; padding: 8px; background-color: #E3F2FD; border-radius: 4px;"
        )
        layout.addWidget(title)

        # Interactive image viewer with zoom and pan capabilities
        self.image_viewer = InteractiveImageViewer()
        self.image_viewer.setMinimumSize(300, 300)
        self.image_viewer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Style the viewer
        self.image_viewer.setStyleSheet(
            """
            QGraphicsView {
                border: 2px solid #CCCCCC;
                border-radius: 10px;
                background-color: #000000;
            }
        """
        )

        layout.addWidget(self.image_viewer)

        # Create placeholder label for when no image is loaded
        self.image_placeholder = QLabel()
        self.image_placeholder.setMinimumSize(300, 300)
        self.image_placeholder.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Expanding
        )
        self.image_placeholder.setStyleSheet(
            """
            QLabel {
                border: 2px dashed #CCCCCC;
                border-radius: 10px;
                background-color: #F5F5F5;
                color: #666666;
                font-size: 14px;
                text-align: center;
            }
        """
        )
        self.image_placeholder.setText(
            """
🏥 MedScan AI - Interactive Image Viewer

No medical images loaded

Supported formats:
• DICOM (.dcm)
• JPEG, PNG
• TIFF, BMP

Features:
• Mouse wheel zoom at cursor
• Left-click drag to pan
• Keyboard shortcuts (+, -, 0, 1)

Drag & drop files here
or use File > Open DICOM Files
        """
        )
        self.image_placeholder.setAlignment(Qt.AlignCenter)
        self.image_placeholder.setWordWrap(True)

        # Initially show placeholder, hide viewer
        layout.addWidget(self.image_placeholder)
        self.image_viewer.hide()

        # Image controls with responsive layout
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(4)

        # Create responsive control buttons
        from PySide6.QtWidgets import QPushButton

        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.setMaximumWidth(40)
        zoom_in_btn.setToolTip("Zoom In")
        zoom_in_btn.clicked.connect(self._zoom_in)

        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.setMaximumWidth(40)
        zoom_out_btn.setToolTip("Zoom Out")
        zoom_out_btn.clicked.connect(self._zoom_out)

        reset_btn = QPushButton("🔄")
        reset_btn.setMaximumWidth(40)
        reset_btn.setToolTip("Reset View")
        reset_btn.clicked.connect(self._reset_view)

        # Add buttons to layout
        controls_layout.addWidget(zoom_in_btn)
        controls_layout.addWidget(zoom_out_btn)
        controls_layout.addWidget(reset_btn)
        controls_layout.addStretch()  # Push buttons to left

        layout.addLayout(controls_layout)

        # Add windowing controls
        windowing_panel = self._create_windowing_controls()
        layout.addWidget(windowing_panel)

        return panel

    def _create_windowing_controls(self):
        """Create windowing controls panel for medical image manipulation."""
        panel = QGroupBox("🔧 Window/Level Controls")
        panel.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #CCCCCC;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        )

        layout = QGridLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 15, 12, 12)

        # Window Level (Center) controls
        level_label = QLabel("Window Level:")
        level_label.setStyleSheet("font-weight: bold; color: #1976D2;")
        layout.addWidget(level_label, 0, 0)

        self._window_level_slider = QSlider(Qt.Horizontal)
        self._window_level_slider.setRange(-1000, 1000)
        self._window_level_slider.setValue(0)
        self._window_level_slider.setTickPosition(QSlider.TicksBelow)
        self._window_level_slider.setTickInterval(200)
        self._window_level_slider.valueChanged.connect(self._on_window_level_changed)
        layout.addWidget(self._window_level_slider, 0, 1, 1, 2)

        self._window_level_spinbox = QSpinBox()
        self._window_level_spinbox.setRange(-1000, 1000)
        self._window_level_spinbox.setValue(0)
        self._window_level_spinbox.setSuffix(" HU")
        self._window_level_spinbox.valueChanged.connect(
            self._on_window_level_spinbox_changed
        )
        layout.addWidget(self._window_level_spinbox, 0, 3)

        # Window Width controls
        width_label = QLabel("Window Width:")
        width_label.setStyleSheet("font-weight: bold; color: #1976D2;")
        layout.addWidget(width_label, 1, 0)

        self._window_width_slider = QSlider(Qt.Horizontal)
        self._window_width_slider.setRange(1, 4000)
        self._window_width_slider.setValue(100)
        self._window_width_slider.setTickPosition(QSlider.TicksBelow)
        self._window_width_slider.setTickInterval(500)
        self._window_width_slider.valueChanged.connect(self._on_window_width_changed)
        layout.addWidget(self._window_width_slider, 1, 1, 1, 2)

        self._window_width_spinbox = QSpinBox()
        self._window_width_spinbox.setRange(1, 4000)
        self._window_width_spinbox.setValue(100)
        self._window_width_spinbox.setSuffix(" HU")
        self._window_width_spinbox.valueChanged.connect(
            self._on_window_width_spinbox_changed
        )
        layout.addWidget(self._window_width_spinbox, 1, 3)

        # Medical presets buttons
        presets_label = QLabel("Medical Presets:")
        presets_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        layout.addWidget(presets_label, 2, 0)

        presets_layout = QHBoxLayout()
        presets_layout.setSpacing(4)

        # Create preset buttons
        auto_btn = QPushButton("Auto")
        auto_btn.setToolTip("Auto windowing from DICOM metadata")
        auto_btn.clicked.connect(lambda: self._apply_windowing_preset("auto"))

        brain_btn = QPushButton("Brain")
        brain_btn.setToolTip("Brain tissue windowing (WL:40, WW:80)")
        brain_btn.clicked.connect(lambda: self._apply_windowing_preset("brain"))

        lung_btn = QPushButton("Lung")
        lung_btn.setToolTip("Lung tissue windowing (WL:-600, WW:1600)")
        lung_btn.clicked.connect(lambda: self._apply_windowing_preset("lung"))

        bone_btn = QPushButton("Bone")
        bone_btn.setToolTip("Bone tissue windowing (WL:300, WW:1500)")
        bone_btn.clicked.connect(lambda: self._apply_windowing_preset("bone"))

        soft_btn = QPushButton("Soft")
        soft_btn.setToolTip("Soft tissue windowing (WL:50, WW:400)")
        soft_btn.clicked.connect(lambda: self._apply_windowing_preset("soft"))

        # Style preset buttons
        preset_style = """
            QPushButton {
                background-color: #E3F2FD;
                border: 1px solid #1976D2;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #BBDEFB;
            }
            QPushButton:pressed {
                background-color: #90CAF9;
            }
        """

        for btn in [auto_btn, brain_btn, lung_btn, bone_btn, soft_btn]:
            btn.setStyleSheet(preset_style)
            btn.setMaximumWidth(50)
            presets_layout.addWidget(btn)

        presets_layout.addStretch()
        layout.addLayout(presets_layout, 2, 1, 1, 3)

        # Reset button
        reset_windowing_btn = QPushButton("🔄 Reset W/L")
        reset_windowing_btn.setToolTip("Reset windowing to default values")
        reset_windowing_btn.clicked.connect(self._reset_windowing)
        reset_windowing_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #FFF3E0;
                border: 1px solid #FF9800;
                border-radius: 3px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FFE0B2;
            }
        """
        )
        layout.addWidget(reset_windowing_btn, 3, 0, 1, 4)

        # Initially disable controls (will be enabled when DICOM is loaded)
        panel.setEnabled(False)
        self._windowing_panel = panel

        return panel

    def _create_right_panel(self):
        """Create right panel for analysis results with responsive sizing."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMinimumWidth(250)
        panel.setMaximumWidth(500)

        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # Panel title with responsive styling
        title = QLabel("🤖 AI Analysis Results")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet(
            "color: #7B1FA2; padding: 8px; background-color: #F3E5F5; border-radius: 4px;"
        )
        layout.addWidget(title)

        # Analysis results with responsive sizing
        results = QTextEdit()
        results.setPlainText(
            """AI Analysis Status: Ready

No analysis performed yet.

To start analysis:
1. Load medical images
2. Select analysis type
3. Click 'Analyze' button

Available AI Models:
• Chest X-Ray Anomaly Detection
• Lung Nodule Classification
• Fracture Detection
• General Abnormality Screening

Results will appear here with:
• Confidence scores
• Highlighted regions
• Medical recommendations
• Comparison with normal ranges
"""
        )
        results.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results.setMinimumHeight(200)
        layout.addWidget(results)

        # Analysis controls with responsive layout
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(4)

        from PySide6.QtWidgets import QPushButton

        analyze_btn = QPushButton("🔍 Analyze")
        analyze_btn.setMinimumWidth(80)
        analyze_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        )
        analyze_btn.clicked.connect(self._placeholder_action)

        report_btn = QPushButton("📊 Report")
        report_btn.setMinimumWidth(80)
        report_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """
        )
        report_btn.clicked.connect(self._placeholder_action)

        controls_layout.addWidget(analyze_btn)
        controls_layout.addWidget(report_btn)
        controls_layout.addStretch()  # Push buttons to left

        layout.addLayout(controls_layout)

        return panel

    def _create_status_bar(self):
        """Create status bar with medical workflow information."""
        status_bar = QStatusBar()

        # Status messages
        status_bar.showMessage("Ready - No patient loaded")

        # Add permanent widgets
        status_bar.addPermanentWidget(QLabel("👤 User: Dr. Admin"))
        status_bar.addPermanentWidget(QLabel("🔒 HIPAA Compliant"))
        status_bar.addPermanentWidget(QLabel("v0.1.0"))

        self.setStatusBar(status_bar)

    def _apply_medical_theme(self):
        """Apply medical-professional theme to the application."""
        # Set application palette for medical theme
        palette = QPalette()

        # Background colors
        palette.setColor(QPalette.Window, QColor(250, 250, 250))
        palette.setColor(QPalette.WindowText, QColor(33, 33, 33))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))

        # Accent colors
        palette.setColor(QPalette.Highlight, QColor(25, 118, 210))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))

        self.setPalette(palette)

        # Apply stylesheet for professional medical look
        self.setStyleSheet(
            """
            QMainWindow {
                background-color: #FAFAFA;
            }
            QMenuBar {
                background-color: #FFFFFF;
                border-bottom: 1px solid #E0E0E0;
                padding: 4px;
            }
            QMenuBar::item {
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
            QToolBar {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                spacing: 3px;
                padding: 5px;
            }
            QFrame {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 6px;
            }
            QTextEdit {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
            }
            QStatusBar {
                background-color: #FFFFFF;
                border-top: 1px solid #E0E0E0;
                padding: 4px;
            }
        """
        )

    def _placeholder_action(self):
        """Placeholder action for menu items not yet implemented."""
        QMessageBox.information(
            self,
            "Feature Coming Soon",
            "This feature will be implemented in upcoming releases.\n\n"
            "MedScan AI is currently in development phase.",
        )

    def _show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self,
            "About MedScan AI",
            """<h3>MedScan AI v0.1.0</h3>
            <p><b>Medical Imaging Analysis Software</b></p>
            <p>Advanced AI-powered medical image analysis for healthcare professionals.</p>

            <p><b>Features:</b></p>
            <ul>
                <li>DICOM image support</li>
                <li>AI-powered anomaly detection</li>
                <li>HIPAA compliant security</li>
                <li>Comprehensive reporting</li>
            </ul>

            <p><b>Status:</b> Development Phase<br>
            <b>Build:</b> GUI Framework v0.1.0</p>

            <p>© 2024 MedScan AI Development Team</p>
            """,
        )

    # DICOM Functionality Methods

    def _open_dicom_files(self):
        """Open DICOM file dialog and load selected files."""
        try:
            file_dialog = QFileDialog(self)
            file_dialog.setFileMode(QFileDialog.ExistingFiles)
            file_dialog.setNameFilter("DICOM Files (*.dcm *.DCM);;All Files (*)")
            file_dialog.setDirectory(os.path.expanduser("~"))
            file_dialog.setWindowTitle("Open DICOM Files")

            if file_dialog.exec():
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    self._load_dicom_file(selected_files[0])  # Load first file for now

        except Exception as e:
            QMessageBox.critical(
                self, "Error Opening Files", f"Failed to open DICOM files:\n{str(e)}"
            )

    def _load_dicom_file(self, file_path: str):
        """Load and display a DICOM file with memory optimization."""
        try:
            # Debug logs removed

            # Update status
            self.statusBar().showMessage(
                f"Loading DICOM file: {os.path.basename(file_path)}"
            )

            # Load DICOM file
            self._dicom_reader.load_file(file_path)
            # Debug logs removed

            # Get dataset for optimized processing
            dataset = self._dicom_reader.get_dataset()
            # Debug logs removed

            # Get raw pixel array for traditional windowing (fallback)
            self._raw_pixel_array = self._dicom_reader.get_pixel_array()
            # Debug logs removed

            # Initialize windowing with auto values or defaults
            self._initialize_windowing()
            # Debug logs removed

            # Use optimized display if available
            if hasattr(self.image_viewer, "set_dicom_dataset_optimized"):
                # Memory-optimized path
                self.image_viewer.set_dicom_dataset_optimized(
                    dataset,
                    window_center=self._current_window_center,
                    window_width=self._current_window_width,
                )
                # Ensure viewer visible
                self.image_placeholder.hide()
                self.image_viewer.show()

                # Get memory usage info
                memory_info = self.image_viewer.get_memory_usage_info()
                memory_status = f"Memory: {memory_info['current_pixmap_mb']}MB"

                # Check if image is large and suggest optimization
                if memory_info["current_pixmap_mb"] > 100:
                    self.image_viewer.optimize_for_large_images(True)
                    memory_status += " (Large image mode)"

                self.statusBar().showMessage(
                    f"Loaded: {os.path.basename(file_path)} - {memory_status}"
                )

            else:
                # Fallback to traditional method
                pixel_array = self._apply_current_windowing()

                self._current_pixmap = numpy_to_qpixmap(pixel_array)

                self._display_image()

                self.statusBar().showMessage(f"Loaded: {os.path.basename(file_path)}")

            # Enable windowing controls
            self._windowing_panel.setEnabled(True)
            self._windowing_enabled = True

            # Update patient information
            self._update_patient_info()

        except DicomError as e:
            # Exception logged internally, debug prints removed

            QMessageBox.warning(
                self, "DICOM Loading Error", f"Failed to load DICOM file:\n{str(e)}"
            )
            self.statusBar().showMessage("Ready - No patient loaded")

        except Exception as e:
            # Exception logged internally, debug prints removed
            QMessageBox.critical(
                self, "File Loading Error", f"Unexpected error loading file:\n{str(e)}"
            )
            self.statusBar().showMessage("Ready - No patient loaded")

    def _display_image(self):
        """Display the current pixmap in the interactive image viewer."""
        # Debug logs removed

        if self._current_pixmap is None or self._current_pixmap.isNull():
            # Debug logs removed
            # Hide viewer and show placeholder
            self.image_viewer.hide()
            self.image_placeholder.show()
            return

        # Debug logs removed
        # Hide placeholder and show interactive viewer
        self.image_placeholder.hide()
        self.image_viewer.show()

        # Set pixmap in interactive viewer
        # Debug logs removed
        self.image_viewer.set_pixmap(self._current_pixmap)
        # Debug logs removed

    def _update_patient_info(self):
        """Update patient information display with DICOM metadata."""
        try:
            metadata = self._dicom_reader.get_metadata()
            if metadata is None:
                return

            # Format patient information
            patient_info = f"""Patient: {metadata.patient.patient_name or 'Unknown'}
ID: {metadata.patient.patient_id or 'N/A'}
DOB: {metadata.patient.patient_birth_date or 'N/A'}
Gender: {metadata.patient.patient_sex or 'N/A'}
Study Date: {metadata.study.study_date or 'N/A'}
Modality: {metadata.image.modality or 'N/A'}
Series: {metadata.series.series_description or 'N/A'}

Image Info:
• Size: {metadata.image.rows}x{metadata.image.columns}
• Bits: {metadata.technical.bits_stored or 'N/A'}
• Photometric: {metadata.technical.photometric_interpretation or 'N/A'}

Status: DICOM loaded successfully
"""

            self._patient_info_widget.setPlainText(patient_info)

        except Exception as e:
            self._patient_info_widget.setPlainText(
                f"""Patient: [Error loading metadata]

Error: {str(e)}

Status: DICOM loaded with metadata errors
"""
            )

    def _zoom_in(self):
        """Zoom into the image."""
        if self._current_pixmap is not None and hasattr(self, "image_viewer"):
            self.image_viewer.zoom_in()
            zoom_factor = self.image_viewer.get_zoom_factor()
            self.statusBar().showMessage(f"Zoom: {zoom_factor:.2f}x")

    def _zoom_out(self):
        """Zoom out of the image."""
        if self._current_pixmap is not None and hasattr(self, "image_viewer"):
            self.image_viewer.zoom_out()
            zoom_factor = self.image_viewer.get_zoom_factor()
            self.statusBar().showMessage(f"Zoom: {zoom_factor:.2f}x")

    def _reset_view(self):
        """Reset image view to fit window."""
        if self._current_pixmap is not None and hasattr(self, "image_viewer"):
            self.image_viewer.reset_zoom()
            zoom_factor = self.image_viewer.get_zoom_factor()
            self.statusBar().showMessage(f"View reset - Zoom: {zoom_factor:.2f}x")

    # Windowing Methods

    def _initialize_windowing(self):
        """Initialize windowing parameters from DICOM metadata or defaults."""
        try:
            metadata = self._dicom_reader.get_metadata()

            # Try to get windowing from DICOM metadata
            if (
                metadata
                and hasattr(metadata.technical, "window_center")
                and metadata.technical.window_center
            ):
                self._current_window_center = float(metadata.technical.window_center)
            else:
                # Use auto-calculated values based on pixel data
                self._current_window_center = float(self._raw_pixel_array.mean())

            if (
                metadata
                and hasattr(metadata.technical, "window_width")
                and metadata.technical.window_width
            ):
                self._current_window_width = float(metadata.technical.window_width)
            else:
                # Use auto-calculated values based on pixel data range
                data_range = float(
                    self._raw_pixel_array.max() - self._raw_pixel_array.min()
                )
                self._current_window_width = max(100, data_range * 0.8)

            # Update UI controls
            self._update_windowing_controls()

        except Exception as e:
            # Use safe defaults
            self._current_window_center = 0
            self._current_window_width = 100
            self._update_windowing_controls()

    def _update_windowing_controls(self):
        """Update windowing UI controls with current values."""
        # Block signals to prevent recursive updates
        self._window_level_slider.blockSignals(True)
        self._window_level_spinbox.blockSignals(True)
        self._window_width_slider.blockSignals(True)
        self._window_width_spinbox.blockSignals(True)

        # Update values
        self._window_level_slider.setValue(int(self._current_window_center))
        self._window_level_spinbox.setValue(int(self._current_window_center))
        self._window_width_slider.setValue(int(self._current_window_width))
        self._window_width_spinbox.setValue(int(self._current_window_width))

        # Re-enable signals
        self._window_level_slider.blockSignals(False)
        self._window_level_spinbox.blockSignals(False)
        self._window_width_slider.blockSignals(False)
        self._window_width_spinbox.blockSignals(False)

    def _apply_current_windowing(self):
        """Apply current windowing settings to raw pixel array."""
        if self._raw_pixel_array is None:
            return None

        # Use our utility function for windowing
        from .utils import apply_window_level_to_display

        return apply_window_level_to_display(
            self._raw_pixel_array,
            self._current_window_center,
            self._current_window_width,
        )

    def _on_window_level_changed(self, value):
        """Handle window level slider change."""
        self._current_window_center = value
        self._window_level_spinbox.blockSignals(True)
        self._window_level_spinbox.setValue(value)
        self._window_level_spinbox.blockSignals(False)
        self._update_windowed_image()

    def _on_window_level_spinbox_changed(self, value):
        """Handle window level spinbox change."""
        self._current_window_center = value
        self._window_level_slider.blockSignals(True)
        self._window_level_slider.setValue(value)
        self._window_level_slider.blockSignals(False)
        self._update_windowed_image()

    def _on_window_width_changed(self, value):
        """Handle window width slider change."""
        self._current_window_width = value
        self._window_width_spinbox.blockSignals(True)
        self._window_width_spinbox.setValue(value)
        self._window_width_spinbox.blockSignals(False)
        self._update_windowed_image()

    def _on_window_width_spinbox_changed(self, value):
        """Handle window width spinbox change."""
        self._current_window_width = value
        self._window_width_slider.blockSignals(True)
        self._window_width_slider.setValue(value)
        self._window_width_slider.blockSignals(False)
        self._update_windowed_image()

    def _update_windowed_image(self):
        """Update the displayed image with current windowing settings."""
        if not self._windowing_enabled or self._raw_pixel_array is None:
            return

        try:
            # Use optimized windowing if available
            if hasattr(self.image_viewer, "update_windowing_optimized"):
                # Memory-optimized path
                self.image_viewer.update_windowing_optimized(
                    self._current_window_center, self._current_window_width
                )

                # Get memory usage info for status
                memory_info = self.image_viewer.get_memory_usage_info()
                cache_info = (
                    f"Cache: {memory_info['cache_size']} items"
                    if memory_info["cache_enabled"]
                    else ""
                )

                # Update status bar
                self.statusBar().showMessage(
                    f"Windowing - Level: {self._current_window_center:.0f}, Width: {self._current_window_width:.0f} - {cache_info}"
                )

            else:
                # Fallback to traditional method
                windowed_array = self._apply_current_windowing()
                self._current_pixmap = numpy_to_qpixmap(windowed_array)
                self._display_image()

                # Update status bar
                self.statusBar().showMessage(
                    f"Windowing - Level: {self._current_window_center:.0f}, Width: {self._current_window_width:.0f}"
                )

        except Exception as e:
            self.statusBar().showMessage(f"Windowing error: {str(e)}")

    def _apply_windowing_preset(self, preset_name):
        """Apply medical windowing presets."""
        presets = {
            "auto": self._auto_windowing,
            "brain": (40, 80),
            "lung": (-600, 1600),
            "bone": (300, 1500),
            "soft": (50, 400),
        }

        if preset_name == "auto":
            self._auto_windowing()
        elif preset_name in presets:
            level, width = presets[preset_name]
            self._current_window_center = level
            self._current_window_width = width
            self._update_windowing_controls()
            self._update_windowed_image()
            self.statusBar().showMessage(
                f"Applied {preset_name.title()} windowing preset"
            )

    def _auto_windowing(self):
        """Calculate automatic windowing based on image statistics."""
        if self._raw_pixel_array is None:
            return

        try:
            # Use percentile-based auto windowing
            p1 = float(np.percentile(self._raw_pixel_array, 1))
            p99 = float(np.percentile(self._raw_pixel_array, 99))

            self._current_window_center = (p1 + p99) / 2
            self._current_window_width = p99 - p1

            self._update_windowing_controls()
            self._update_windowed_image()
            self.statusBar().showMessage("Applied automatic windowing")

        except Exception as e:
            self.statusBar().showMessage(f"Auto windowing error: {str(e)}")

    def _reset_windowing(self):
        """Reset windowing to default values."""
        self._current_window_center = 0
        self._current_window_width = 100
        self._update_windowing_controls()
        if self._windowing_enabled:
            self._update_windowed_image()
        self.statusBar().showMessage("Windowing reset to defaults")

    def _on_splitter_moved(self, pos, index):
        """Handle splitter movement for responsive behavior."""
        # Get current window size
        window_width = self.width()

        # Adjust panel behavior based on window size
        if window_width < 900:  # Small window
            # Collapse side panels more aggressively
            sizes = self.main_splitter.sizes()
            total = sum(sizes)

            # Ensure center panel gets most space
            new_sizes = [
                min(sizes[0], total * 0.25),  # Left max 25%
                max(total * 0.5, total - sizes[0] - sizes[2]),  # Center min 50%
                min(sizes[2], total * 0.25),  # Right max 25%
            ]

            self.main_splitter.setSizes(new_sizes)

    def resizeEvent(self, event):
        """Handle window resize events for responsive behavior."""
        super().resizeEvent(event)

        # Update image placeholder size based on window size
        if hasattr(self, "image_placeholder"):
            window_size = event.size()

            # Adjust image placeholder text based on available space
            if window_size.width() < 800:
                # Compact view for smaller windows
                self.image_placeholder.setText(
                    """
🏥 MedScan AI

No medical images loaded

Mouse wheel: zoom
Left drag: pan

Drag & drop files here
or use File > Open DICOM Files
                """
                )
            else:
                # Full view for larger windows
                self.image_placeholder.setText(
                    """
🏥 MedScan AI - Interactive Image Viewer

No medical images loaded

Supported formats:
• DICOM (.dcm)
• JPEG, PNG
• TIFF, BMP

Features:
• Mouse wheel zoom at cursor
• Left-click drag to pan
• Keyboard shortcuts (+, -, 0, 1)

Drag & drop files here
or use File > Open DICOM Files
                """
                )

        # Adjust splitter sizes for optimal viewing
        if hasattr(self, "main_splitter") and window_size.width() > 0:
            current_sizes = self.main_splitter.sizes()
            total_width = sum(current_sizes)

            if total_width > 0:
                if window_size.width() < 900:
                    # Compact layout for smaller screens
                    new_sizes = [200, window_size.width() - 450, 250]
                elif window_size.width() < 1200:
                    # Medium layout
                    new_sizes = [250, window_size.width() - 600, 350]
                else:
                    # Full layout for large screens
                    new_sizes = [300, window_size.width() - 700, 400]

                # Ensure minimum sizes
                new_sizes[0] = max(new_sizes[0], 200)  # Left min
                new_sizes[1] = max(new_sizes[1], 300)  # Center min
                new_sizes[2] = max(new_sizes[2], 250)  # Right min

                self.main_splitter.setSizes(new_sizes)


def main(args: Optional[list] = None) -> int:
    """
    Main entry point for the MedScan AI GUI application.

    Args:
        args: Command line arguments (defaults to sys.argv[1:])

    Returns:
        Exit code (0 for success, non-zero for error)
    """
    if args is None:
        args = sys.argv[1:]

    try:
        # Create the application
        app = QApplication(args)

        # Set application properties
        app.setApplicationName("MedScan AI")
        app.setApplicationVersion("0.1.0")
        app.setOrganizationName("MedScan AI Development Team")
        app.setApplicationDisplayName("MedScan AI - Medical Imaging Analysis")

        # Create and show main window
        main_window = MedScanMainWindow()
        main_window.show()

        # Run the application
        return app.exec()

    except ImportError as e:
        print(f"Error: Unable to import required GUI libraries: {e}")
        print("Please ensure PySide6 is installed: pip install PySide6")
        return 1
    except Exception as e:
        print(f"Error starting GUI: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
