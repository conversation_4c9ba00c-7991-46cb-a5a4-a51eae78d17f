"""
Memory-optimized DICOM pixel processor for large file handling.

This module provides optimized pixel processing with:
- Memory mapping for large files
- Lazy loading and chunked processing
- In-place operations to minimize memory overhead
- Efficient caching strategies
"""

import mmap
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, Iterator, Optional, Tuple, Union

import numpy as np
import pydicom
from pydicom.dataset import Dataset

from ..exceptions import (
    DicomNormalizationError,
    DicomPixelProcessingError,
    DicomWindowingError,
)


class MemoryMappedPixelArray:
    """
    Memory-mapped wrapper for large pixel arrays.
    Allows access to pixel data without loading entire array into memory.
    """

    def __init__(
        self, file_path: str, shape: Tuple[int, ...], dtype: np.dtype, offset: int = 0
    ):
        """
        Initialize memory-mapped pixel array.

        Args:
            file_path: Path to file containing pixel data
            shape: Shape of the pixel array
            dtype: Data type of pixels
            offset: Byte offset to pixel data in file
        """
        self.file_path = file_path
        self.shape = shape
        self.dtype = dtype
        self.offset = offset
        self._mmap_file = None
        self._array = None

    def __enter__(self):
        """Context manager entry."""
        self._open_mmap()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self._close_mmap()

    def _open_mmap(self):
        """Open memory-mapped file."""
        try:
            self._mmap_file = open(self.file_path, "rb")
            self._mmap = mmap.mmap(self._mmap_file.fileno(), 0, access=mmap.ACCESS_READ)

            # Create numpy array view of memory-mapped data
            self._array = np.frombuffer(
                self._mmap, dtype=self.dtype, offset=self.offset
            ).reshape(self.shape)

        except Exception as e:
            self._close_mmap()
            raise DicomPixelProcessingError(f"Failed to open memory-mapped array: {e}")

    def _close_mmap(self):
        """Close memory-mapped file."""
        if self._mmap:
            self._mmap.close()
            self._mmap = None
        if self._mmap_file:
            self._mmap_file.close()
            self._mmap_file = None
        self._array = None

    def get_chunk(self, slice_obj: slice) -> np.ndarray:
        """
        Get a chunk of the array.

        Args:
            slice_obj: Slice object for the desired chunk

        Returns:
            Numpy array containing the requested chunk
        """
        if self._array is None:
            self._open_mmap()

        return self._array[slice_obj].copy()

    def get_full_array(self) -> np.ndarray:
        """Get the full array (use with caution for large arrays)."""
        if self._array is None:
            self._open_mmap()
        return self._array.copy()

    @property
    def array(self) -> np.ndarray:
        """Direct access to memory-mapped array (read-only)."""
        if self._array is None:
            self._open_mmap()
        return self._array


class ChunkedPixelProcessor:
    """
    Processes large pixel arrays in chunks to minimize memory usage.
    """

    def __init__(self, chunk_size: int = 1024 * 1024):  # 1MB chunks by default
        """
        Initialize chunked processor.

        Args:
            chunk_size: Size of chunks in bytes
        """
        self.chunk_size = chunk_size
        self._temp_files = []

    def __del__(self):
        """Cleanup temporary files."""
        self._cleanup_temp_files()

    def _cleanup_temp_files(self):
        """Remove temporary files."""
        for temp_file in self._temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception:
                pass  # Ignore cleanup errors
        self._temp_files.clear()

    def _calculate_chunk_params(
        self, array_shape: Tuple[int, ...], dtype: np.dtype
    ) -> Tuple[int, int]:
        """
        Calculate optimal chunk parameters.

        Args:
            array_shape: Shape of the array to process
            dtype: Data type of the array

        Returns:
            Tuple of (rows_per_chunk, total_chunks)
        """
        total_pixels = np.prod(array_shape)
        bytes_per_pixel = dtype.itemsize
        total_bytes = total_pixels * bytes_per_pixel

        if total_bytes <= self.chunk_size:
            # Small enough to process in one chunk
            return array_shape[0], 1

        # Calculate rows per chunk for 2D arrays
        if len(array_shape) == 2:
            rows, cols = array_shape
            bytes_per_row = cols * bytes_per_pixel
            rows_per_chunk = max(1, self.chunk_size // bytes_per_row)
            total_chunks = (rows + rows_per_chunk - 1) // rows_per_chunk
            return rows_per_chunk, total_chunks

        # For other dimensions, process linearly
        pixels_per_chunk = max(1, self.chunk_size // bytes_per_pixel)
        return (
            pixels_per_chunk,
            (total_pixels + pixels_per_chunk - 1) // pixels_per_chunk,
        )

    def process_windowing_chunked(
        self,
        pixel_array: np.ndarray,
        window_center: float,
        window_width: float,
        output_range: Tuple[float, float] = (0.0, 1.0),
    ) -> np.ndarray:
        """
        Apply windowing to large arrays using chunked processing.

        Args:
            pixel_array: Input pixel array
            window_center: Window center value
            window_width: Window width value
            output_range: Output value range

        Returns:
            Windowed pixel array
        """
        if pixel_array.size * pixel_array.itemsize <= self.chunk_size:
            # Small enough to process in memory
            return self._apply_windowing_inplace(
                pixel_array, window_center, window_width, output_range
            )

        # Create temporary file for output
        temp_fd, temp_path = tempfile.mkstemp(suffix=".dat")
        self._temp_files.append(temp_path)

        try:
            # Calculate chunk parameters
            rows_per_chunk, total_chunks = self._calculate_chunk_params(
                pixel_array.shape, pixel_array.dtype
            )

            # Create memory-mapped output array
            output_array = np.memmap(
                temp_path, dtype=np.float32, mode="w+", shape=pixel_array.shape
            )

            # Process in chunks
            for chunk_idx in range(total_chunks):
                start_row = chunk_idx * rows_per_chunk
                end_row = min(start_row + rows_per_chunk, pixel_array.shape[0])

                # Get chunk
                chunk = pixel_array[start_row:end_row].astype(np.float32)

                # Apply windowing to chunk
                windowed_chunk = self._apply_windowing_inplace(
                    chunk, window_center, window_width, output_range
                )

                # Write to output
                output_array[start_row:end_row] = windowed_chunk

                # Force memory sync
                del chunk, windowed_chunk

            # Return copy and cleanup
            result = output_array.copy()
            del output_array

            return result

        finally:
            try:
                os.close(temp_fd)
            except Exception:
                pass

    def _apply_windowing_inplace(
        self,
        array: np.ndarray,
        window_center: float,
        window_width: float,
        output_range: Tuple[float, float],
    ) -> np.ndarray:
        """
        Apply windowing in-place to minimize memory usage.

        Args:
            array: Input array (will be modified)
            window_center: Window center
            window_width: Window width
            output_range: Output range

        Returns:
            Windowed array
        """
        try:
            # Ensure array is float for calculations
            if array.dtype != np.float32:
                array = array.astype(np.float32)

            # Calculate window bounds
            window_min = window_center - (window_width / 2.0)
            window_max = window_center + (window_width / 2.0)

            # Apply windowing in-place
            array -= window_min
            array /= window_width

            # Clip to [0, 1] range
            np.clip(array, 0.0, 1.0, out=array)

            # Scale to output range
            range_size = output_range[1] - output_range[0]
            array *= range_size
            array += output_range[0]

            return array

        except Exception as e:
            raise DicomWindowingError(f"Windowing failed: {e}")


class OptimizedPixelProcessor:
    """
    Memory-optimized pixel processor for large DICOM files.

    Features:
    - Memory mapping for very large files
    - Chunked processing to limit memory usage
    - In-place operations where possible
    - Efficient caching for repeated operations
    """

    def __init__(
        self,
        max_memory_mb: int = 512,
        chunk_size_mb: int = 64,
        enable_caching: bool = True,
    ):
        """
        Initialize optimized processor.

        Args:
            max_memory_mb: Maximum memory to use for processing (MB)
            chunk_size_mb: Chunk size for large array processing (MB)
            enable_caching: Whether to enable result caching
        """
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.chunk_processor = ChunkedPixelProcessor(chunk_size_mb * 1024 * 1024)
        self.enable_caching = enable_caching
        self._cache = {} if enable_caching else None

    def _get_cache_key(self, dataset: Dataset, operation: str, **kwargs) -> str:
        """Generate cache key for operation."""
        if not self.enable_caching:
            return None

        sop_instance_uid = getattr(dataset, "SOPInstanceUID", "unknown")
        params = "_".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
        return f"{sop_instance_uid}_{operation}_{params}"

    def _check_cache(self, cache_key: str) -> Optional[np.ndarray]:
        """Check if result is cached."""
        if not self.enable_caching or not cache_key:
            return None
        return self._cache.get(cache_key)

    def _store_cache(self, cache_key: str, result: np.ndarray) -> None:
        """Store result in cache."""
        if not self.enable_caching or not cache_key:
            return

        # Limit cache size (simple LRU-like behavior)
        if len(self._cache) > 10:
            # Remove oldest entries
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]

        self._cache[cache_key] = result.copy()

    def extract_pixel_array_optimized(self, dataset: Dataset) -> np.ndarray:
        """
        Extract pixel array with memory optimization.

        Args:
            dataset: DICOM dataset

        Returns:
            Optimized pixel array
        """
        cache_key = self._get_cache_key(dataset, "extract_pixels")

        # Check cache first
        cached_result = self._check_cache(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            # Get pixel array
            if not hasattr(dataset, "pixel_array"):
                raise DicomPixelProcessingError("No pixel data found")

            pixel_array = dataset.pixel_array

            # Check if we need memory optimization
            array_size_bytes = pixel_array.nbytes

            if array_size_bytes > self.max_memory_bytes:
                # Use memory mapping for very large arrays
                return self._process_large_array(dataset, pixel_array)
            else:
                # Process normally but efficiently
                result = pixel_array.copy()
                self._store_cache(cache_key, result)
                return result

        except Exception as e:
            raise DicomPixelProcessingError(f"Failed to extract pixel array: {e}")

    def _process_large_array(
        self, dataset: Dataset, pixel_array: np.ndarray
    ) -> np.ndarray:
        """
        Process very large arrays using memory mapping.

        Args:
            dataset: DICOM dataset
            pixel_array: Large pixel array

        Returns:
            Processed array reference (memory-mapped)
        """
        # For very large arrays, we return a view or memory-mapped version
        # This is a simplified implementation - in practice, you'd want to
        # create a temporary file and use memory mapping

        # For now, return the original array but log the optimization opportunity
        print(
            f"WARNING: Large array detected ({pixel_array.nbytes / 1024 / 1024:.1f}MB). "
            f"Consider implementing full memory mapping."
        )

        return pixel_array

    def process_with_windowing_optimized(
        self,
        dataset: Dataset,
        window_center: Optional[float] = None,
        window_width: Optional[float] = None,
        output_range: Tuple[float, float] = (0.0, 1.0),
    ) -> np.ndarray:
        """
        Process pixel data with optimized windowing.

        Args:
            dataset: DICOM dataset
            window_center: Window center (uses DICOM value if None)
            window_width: Window width (uses DICOM value if None)
            output_range: Output value range

        Returns:
            Processed and windowed pixel array
        """
        # Generate cache key
        cache_key = self._get_cache_key(
            dataset,
            "windowing",
            wc=window_center,
            ww=window_width,
            out_min=output_range[0],
            out_max=output_range[1],
        )

        # Check cache
        cached_result = self._check_cache(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            # Extract pixel array
            pixel_array = self.extract_pixel_array_optimized(dataset)

            # Get window parameters from DICOM if not provided
            if window_center is None:
                window_center = float(getattr(dataset, "WindowCenter", 0))
            if window_width is None:
                window_width = float(getattr(dataset, "WindowWidth", 1))

            # Apply modality transforms first (in-place where possible)
            processed_array = self._apply_modality_transforms_optimized(
                pixel_array, dataset
            )

            # Apply windowing using chunked processor
            windowed_array = self.chunk_processor.process_windowing_chunked(
                processed_array, window_center, window_width, output_range
            )

            # Store in cache
            self._store_cache(cache_key, windowed_array)

            return windowed_array

        except Exception as e:
            raise DicomPixelProcessingError(f"Optimized windowing failed: {e}")

    def _apply_modality_transforms_optimized(
        self, pixel_array: np.ndarray, dataset: Dataset
    ) -> np.ndarray:
        """
        Apply modality transforms with memory optimization.

        Args:
            pixel_array: Input pixel array
            dataset: DICOM dataset

        Returns:
            Transformed pixel array
        """
        # Get transform parameters
        rescale_slope = float(getattr(dataset, "RescaleSlope", 1.0))
        rescale_intercept = float(getattr(dataset, "RescaleIntercept", 0.0))

        # Check if transforms are needed
        if rescale_slope == 1.0 and rescale_intercept == 0.0:
            return pixel_array

        # Apply transforms efficiently
        if pixel_array.nbytes <= self.max_memory_bytes:
            # Small enough to process in memory
            result = pixel_array.astype(np.float32)
            result *= rescale_slope
            result += rescale_intercept
            return result
        else:
            # Use chunked processing
            return self._apply_transforms_chunked(
                pixel_array, rescale_slope, rescale_intercept
            )

    def _apply_transforms_chunked(
        self, pixel_array: np.ndarray, slope: float, intercept: float
    ) -> np.ndarray:
        """Apply modality transforms using chunked processing."""
        # This would implement chunked modality transforms
        # For now, convert to float32 and apply
        result = pixel_array.astype(np.float32)
        result *= slope
        result += intercept
        return result

    def get_optimized_display_array(
        self,
        dataset: Dataset,
        window_center: Optional[float] = None,
        window_width: Optional[float] = None,
        target_size: Optional[Tuple[int, int]] = None,
    ) -> np.ndarray:
        """
        Get display-ready array with all optimizations.

        Args:
            dataset: DICOM dataset
            window_center: Window center
            window_width: Window width
            target_size: Target display size (width, height)

        Returns:
            Display-ready uint8 array
        """
        # Process with windowing
        processed = self.process_with_windowing_optimized(
            dataset, window_center, window_width, (0.0, 1.0)
        )

        # Convert to display format
        display_array = (processed * 255.0).astype(np.uint8)

        # Resize if target size specified
        if target_size is not None and target_size != display_array.shape[:2]:
            display_array = self._resize_optimized(display_array, target_size)

        return display_array

    def _resize_optimized(
        self, array: np.ndarray, target_size: Tuple[int, int]
    ) -> np.ndarray:
        """Resize array with memory optimization."""
        try:
            # Use optimized OpenCV operations with memory pooling
            from ...core.utils.optimized_cv_ops import resize_optimized
            return resize_optimized(array, target_size)
        except ImportError:
            # Fallback to basic OpenCV
            try:
                import cv2
                return cv2.resize(array, target_size, interpolation=cv2.INTER_LINEAR)
            except ImportError:
                # Final fallback to scikit-image
                from skimage.transform import resize
                return resize(array, target_size, preserve_range=True).astype(array.dtype)

    def clear_cache(self):
        """Clear the processing cache."""
        if self._cache:
            self._cache.clear()

    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self.enable_caching:
            return {"caching_enabled": False}

        return {
            "caching_enabled": True,
            "cache_size": len(self._cache),
            "cached_operations": list(self._cache.keys()),
        }
