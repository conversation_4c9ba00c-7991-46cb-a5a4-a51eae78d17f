"""Async Audit Processor for high-performance logging."""

import asyncio
import logging
from typing import List, Optional
from datetime import datetime

from .service import AuditEvent, AuditService

logger = logging.getLogger(__name__)


class AsyncAuditProcessor:
    """Asynchronous processor for audit events."""
    
    def __init__(self, audit_service: AuditService, batch_size: int = 10):
        """Initialize async processor."""
        self.audit_service = audit_service
        self.batch_size = batch_size
        self._queue: asyncio.Queue = asyncio.Queue()
        self._processing_task: Optional[asyncio.Task] = None
        self._running = False
        
    async def start(self):
        """Start the async processor."""
        if self._running:
            return
            
        self._running = True
        self._processing_task = asyncio.create_task(self._process_events())
        logger.info("Async audit processor started")
    
    async def stop(self):
        """Stop the async processor."""
        if not self._running:
            return
            
        self._running = False
        
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Async audit processor stopped")
    
    async def queue_event(self, event: AuditEvent) -> bool:
        """Queue an audit event for processing."""
        try:
            await self._queue.put(event)
            return True
        except Exception as e:
            logger.error(f"Failed to queue audit event: {e}")
            return False
    
    async def _process_events(self):
        """Main processing loop."""
        batch: List[AuditEvent] = []
        
        while self._running:
            try:
                # Wait for events
                try:
                    event = await asyncio.wait_for(self._queue.get(), timeout=1.0)
                    batch.append(event)
                except asyncio.TimeoutError:
                    if batch:
                        await self._process_batch(batch)
                        batch = []
                    continue
                
                # Process batch when full
                if len(batch) >= self.batch_size:
                    await self._process_batch(batch)
                    batch = []
                    
            except asyncio.CancelledError:
                if batch:
                    await self._process_batch(batch)
                raise
            except Exception as e:
                logger.error(f"Error in async processing: {e}")
    
    async def _process_batch(self, events: List[AuditEvent]):
        """Process a batch of events."""
        if not events:
            return
            
        for event in events:
            try:
                # Process in executor to avoid blocking
                await asyncio.get_event_loop().run_in_executor(
                    None, self.audit_service.log_event, event
                )
            except Exception as e:
                logger.error(f"Error processing event: {e}")


# Global processor
_async_processor: Optional[AsyncAuditProcessor] = None


async def get_async_processor(audit_service: Optional[AuditService] = None) -> AsyncAuditProcessor:
    """Get global async processor."""
    global _async_processor
    
    if not _async_processor:
        if not audit_service:
            from .service import get_audit_service
            audit_service = get_audit_service()
        
        _async_processor = AsyncAuditProcessor(audit_service)
        await _async_processor.start()
    
    return _async_processor 