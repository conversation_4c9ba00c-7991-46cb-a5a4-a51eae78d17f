"""
MedScan AI Test Fixtures

This package provides comprehensive test fixtures and mock utilities for 
medical-grade testing of the MedScan AI application.

Available fixture categories:
- medical_data: Medical data factories and generators
- mock_services: Service mocks for external dependencies
- security_mocks: Security and audit mocks for compliance testing
- gui_mocks: GUI component mocks for interface testing
"""

from .medical_data import (
    MockMedicalDataFactory,
    create_mock_patient,
    create_mock_dicom_metadata,
    create_mock_ai_inference_result,
    create_mock_study,
)

from .mock_services import (
    mock_database_session,
    mock_inference_engine,
    mock_encryption_service,
    mock_audit_service,
    mock_pacs_client,
)

from .security_mocks import (
    mock_security_context,
    mock_user_session,
    mock_rbac_service,
)

__all__ = [
    # Medical data factories
    'MockMedicalDataFactory',
    'create_mock_patient',
    'create_mock_dicom_metadata', 
    'create_mock_ai_inference_result',
    'create_mock_study',
    
    # Service mocks
    'mock_database_session',
    'mock_inference_engine',
    'mock_encryption_service',
    'mock_audit_service',
    'mock_pacs_client',
    
    # Security mocks
    'mock_security_context',
    'mock_user_session',
    'mock_rbac_service',
] 