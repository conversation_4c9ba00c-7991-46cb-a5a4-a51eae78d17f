"""
Image Processing Worker for Background Operations

Provides background image processing capabilities for windowing, filtering,
transformations, and other computationally intensive image operations.
"""

import time
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
import numpy as np
from enum import Enum
from dataclasses import dataclass

from .base_worker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WorkerError
from ..utils.optimized_cv_ops import OptimizedCVOperations

logger = logging.getLogger(__name__)


class ImageOperationType(Enum):
    """Types of image processing operations."""
    WINDOWING = "windowing"
    RESIZE = "resize"
    ROTATION = "rotation"
    FILTERING = "filtering"
    NORMALIZATION = "normalization"
    CONTRAST_ENHANCEMENT = "contrast_enhancement"
    NOISE_REDUCTION = "noise_reduction"
    EDGE_DETECTION = "edge_detection"
    MORPHOLOGICAL = "morphological"
    HISTOGRAM_EQUALIZATION = "histogram_equalization"
    CUSTOM = "custom"


@dataclass
class ImageProcessingParameters:
    """Parameters for image processing operations."""
    operation_type: ImageOperationType
    target_size: Optional[Tuple[int, int]] = None
    window_center: Optional[float] = None
    window_width: Optional[float] = None
    rotation_angle: Optional[float] = None
    filter_kernel_size: Optional[int] = None
    filter_sigma: Optional[float] = None
    contrast_factor: Optional[float] = None
    brightness_offset: Optional[float] = None
    noise_reduction_strength: Optional[float] = None
    custom_function: Optional[callable] = None
    custom_params: Optional[Dict[str, Any]] = None


class ImageProcessingResult:
    """Container for image processing results."""
    
    def __init__(self, 
                 processed_image: Optional[np.ndarray] = None,
                 original_shape: Optional[Tuple[int, ...]] = None,
                 processing_time: float = 0.0,
                 operation_type: Optional[ImageOperationType] = None,
                 parameters: Optional[ImageProcessingParameters] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        self.processed_image = processed_image
        self.original_shape = original_shape
        self.processing_time = processing_time
        self.operation_type = operation_type
        self.parameters = parameters
        self.metadata = metadata or {}
        
        # Calculate processing statistics
        if processed_image is not None:
            self.output_shape = processed_image.shape
            self.output_dtype = processed_image.dtype
            self.memory_usage = processed_image.nbytes
        else:
            self.output_shape = None
            self.output_dtype = None
            self.memory_usage = 0


class ImageProcessingWorker(MedScanWorker):
    """
    Background worker for image processing operations.
    
    Supports various image processing operations including:
    - DICOM windowing and level/width adjustments
    - Image resizing and transformations
    - Filtering and noise reduction
    - Contrast enhancement and normalization
    - Custom processing functions
    """
    
    def __init__(self,
                 input_image: np.ndarray,
                 parameters: ImageProcessingParameters,
                 operation_name: str = None,
                 use_optimized_ops: bool = True,
                 parent=None):
        """
        Initialize image processing worker.
        
        Args:
            input_image: Input image array to process
            parameters: Processing parameters
            operation_name: Custom name for the operation
            use_optimized_ops: Whether to use optimized OpenCV operations
            parent: Parent QObject
        """
        self.input_image = input_image.copy()  # Create copy to avoid external modifications
        self.parameters = parameters
        self.use_optimized_ops = use_optimized_ops
        
        # Initialize optimized operations if requested
        self.cv_ops = None
        if self.use_optimized_ops:
            try:
                self.cv_ops = OptimizedCVOperations(enable_profiling=True)
            except Exception as e:
                logger.warning(f"Failed to initialize optimized operations: {e}")
                self.use_optimized_ops = False
        
        # Set operation name
        if operation_name is None:
            operation_name = f"Image {parameters.operation_type.value.title()}"
        
        super().__init__(operation_name, parent)
        
        # Validate inputs
        self._validate_inputs()
    
    def _validate_inputs(self):
        """Validate input image and parameters."""
        if self.input_image is None:
            raise WorkerError("Input image cannot be None")
        
        if not isinstance(self.input_image, np.ndarray):
            raise WorkerError("Input image must be a numpy array")
        
        if self.input_image.size == 0:
            raise WorkerError("Input image cannot be empty")
        
        if self.parameters is None:
            raise WorkerError("Processing parameters cannot be None")
        
        # Validate specific parameters based on operation type
        op_type = self.parameters.operation_type
        
        if op_type == ImageOperationType.RESIZE and self.parameters.target_size is None:
            raise WorkerError("Target size required for resize operation")
        
        if op_type == ImageOperationType.WINDOWING:
            if self.parameters.window_center is None or self.parameters.window_width is None:
                raise WorkerError("Window center and width required for windowing operation")
        
        if op_type == ImageOperationType.ROTATION and self.parameters.rotation_angle is None:
            raise WorkerError("Rotation angle required for rotation operation")
        
        if op_type == ImageOperationType.CUSTOM and self.parameters.custom_function is None:
            raise WorkerError("Custom function required for custom operation")
    
    def do_work(self) -> ImageProcessingResult:
        """
        Perform image processing work.
        
        Returns:
            ImageProcessingResult with processed image and metadata
        """
        start_time = time.time()
        original_shape = self.input_image.shape
        
        try:
            self.update_progress(10, "Starting image processing...")
            
            # Dispatch to appropriate processing method
            op_type = self.parameters.operation_type
            
            if op_type == ImageOperationType.WINDOWING:
                processed_image = self._apply_windowing()
            elif op_type == ImageOperationType.RESIZE:
                processed_image = self._apply_resize()
            elif op_type == ImageOperationType.ROTATION:
                processed_image = self._apply_rotation()
            elif op_type == ImageOperationType.FILTERING:
                processed_image = self._apply_filtering()
            elif op_type == ImageOperationType.NORMALIZATION:
                processed_image = self._apply_normalization()
            elif op_type == ImageOperationType.CONTRAST_ENHANCEMENT:
                processed_image = self._apply_contrast_enhancement()
            elif op_type == ImageOperationType.NOISE_REDUCTION:
                processed_image = self._apply_noise_reduction()
            elif op_type == ImageOperationType.EDGE_DETECTION:
                processed_image = self._apply_edge_detection()
            elif op_type == ImageOperationType.MORPHOLOGICAL:
                processed_image = self._apply_morphological()
            elif op_type == ImageOperationType.HISTOGRAM_EQUALIZATION:
                processed_image = self._apply_histogram_equalization()
            elif op_type == ImageOperationType.CUSTOM:
                processed_image = self._apply_custom_operation()
            else:
                raise WorkerError(f"Unsupported operation type: {op_type}")
            
            processing_time = time.time() - start_time
            
            self.update_progress(100, f"Processing completed in {processing_time:.2f}s")
            
            # Create result
            result = ImageProcessingResult(
                processed_image=processed_image,
                original_shape=original_shape,
                processing_time=processing_time,
                operation_type=op_type,
                parameters=self.parameters,
                metadata=self._get_processing_metadata()
            )
            
            logger.info(f"Image processing completed: {op_type.value}, "
                       f"time: {processing_time:.2f}s, "
                       f"shape: {original_shape} -> {processed_image.shape}")
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Image processing failed after {processing_time:.2f}s: {str(e)}"
            logger.error(error_msg)
            raise WorkerError(error_msg)
    
    def _apply_windowing(self) -> np.ndarray:
        """Apply DICOM windowing (level/width adjustment)."""
        self.update_progress(30, "Applying windowing...")
        
        center = self.parameters.window_center
        width = self.parameters.window_width
        
        # Calculate window bounds
        window_min = center - width / 2
        window_max = center + width / 2
        
        # Apply windowing
        windowed = np.clip(self.input_image, window_min, window_max)
        
        # Normalize to 0-255 range for display
        windowed = ((windowed - window_min) / width * 255).astype(np.uint8)
        
        return windowed
    
    def _apply_resize(self) -> np.ndarray:
        """Apply image resizing."""
        self.update_progress(30, "Resizing image...")
        
        target_size = self.parameters.target_size
        
        if self.use_optimized_ops and self.cv_ops:
            return self.cv_ops.resize_optimized(self.input_image, target_size)
        else:
            import cv2
            return cv2.resize(self.input_image, target_size)
    
    def _apply_rotation(self) -> np.ndarray:
        """Apply image rotation."""
        self.update_progress(30, "Rotating image...")
        
        angle = self.parameters.rotation_angle
        
        import cv2
        h, w = self.input_image.shape[:2]
        center = (w // 2, h // 2)
        
        # Get rotation matrix
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # Apply rotation
        rotated = cv2.warpAffine(self.input_image, rotation_matrix, (w, h))
        
        return rotated
    
    def _apply_filtering(self) -> np.ndarray:
        """Apply image filtering."""
        self.update_progress(30, "Applying filter...")
        
        kernel_size = self.parameters.filter_kernel_size or 5
        sigma = self.parameters.filter_sigma or 1.0
        
        if self.use_optimized_ops and self.cv_ops:
            return self.cv_ops.denoise_optimized(self.input_image, kernel_size, sigma)
        else:
            import cv2
            return cv2.GaussianBlur(self.input_image, (kernel_size, kernel_size), sigma)
    
    def _apply_normalization(self) -> np.ndarray:
        """Apply image normalization."""
        self.update_progress(30, "Normalizing image...")
        
        # Convert to float for processing
        normalized = self.input_image.astype(np.float32)
        
        # Normalize to 0-1 range
        min_val = normalized.min()
        max_val = normalized.max()
        
        if max_val > min_val:
            normalized = (normalized - min_val) / (max_val - min_val)
        
        # Convert back to original dtype range
        if self.input_image.dtype == np.uint8:
            normalized = (normalized * 255).astype(np.uint8)
        elif self.input_image.dtype == np.uint16:
            normalized = (normalized * 65535).astype(np.uint16)
        
        return normalized
    
    def _apply_contrast_enhancement(self) -> np.ndarray:
        """Apply contrast enhancement."""
        self.update_progress(30, "Enhancing contrast...")
        
        contrast_factor = self.parameters.contrast_factor or 1.5
        brightness_offset = self.parameters.brightness_offset or 0
        
        if self.use_optimized_ops and self.cv_ops:
            return self.cv_ops.adjust_contrast_optimized(
                self.input_image, contrast_factor, brightness_offset
            )
        else:
            # Manual contrast enhancement
            enhanced = self.input_image.astype(np.float32)
            enhanced = enhanced * contrast_factor + brightness_offset
            enhanced = np.clip(enhanced, 0, 255 if self.input_image.dtype == np.uint8 else 65535)
            return enhanced.astype(self.input_image.dtype)
    
    def _apply_noise_reduction(self) -> np.ndarray:
        """Apply noise reduction."""
        self.update_progress(30, "Reducing noise...")
        
        strength = self.parameters.noise_reduction_strength or 5.0
        
        import cv2
        if len(self.input_image.shape) == 2:  # Grayscale
            return cv2.fastNlMeansDenoising(self.input_image, None, strength, 7, 21)
        else:  # Color
            return cv2.fastNlMeansDenoisingColored(self.input_image, None, strength, strength, 7, 21)
    
    def _apply_edge_detection(self) -> np.ndarray:
        """Apply edge detection."""
        self.update_progress(30, "Detecting edges...")
        
        if self.use_optimized_ops and self.cv_ops:
            return self.cv_ops.edge_detection_optimized(self.input_image)
        else:
            import cv2
            # Convert to grayscale if needed
            if len(self.input_image.shape) == 3:
                gray = cv2.cvtColor(self.input_image, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.input_image
            
            # Apply Canny edge detection
            return cv2.Canny(gray, 100, 200)
    
    def _apply_morphological(self) -> np.ndarray:
        """Apply morphological operations."""
        self.update_progress(30, "Applying morphological operations...")
        
        kernel_size = self.parameters.filter_kernel_size or 5
        
        import cv2
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        
        # Apply opening followed by closing
        opened = cv2.morphologyEx(self.input_image, cv2.MORPH_OPEN, kernel)
        closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
        
        return closed
    
    def _apply_histogram_equalization(self) -> np.ndarray:
        """Apply histogram equalization."""
        self.update_progress(30, "Equalizing histogram...")
        
        import cv2
        
        if len(self.input_image.shape) == 2:  # Grayscale
            return cv2.equalizeHist(self.input_image)
        else:  # Color - apply to each channel
            # Convert to YUV
            yuv = cv2.cvtColor(self.input_image, cv2.COLOR_BGR2YUV)
            yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
            return cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
    
    def _apply_custom_operation(self) -> np.ndarray:
        """Apply custom user-defined operation."""
        self.update_progress(30, "Applying custom operation...")
        
        custom_function = self.parameters.custom_function
        custom_params = self.parameters.custom_params or {}
        
        # Call custom function with image and parameters
        return custom_function(self.input_image, **custom_params)
    
    def _get_processing_metadata(self) -> Dict[str, Any]:
        """Get processing metadata."""
        metadata = {
            'input_shape': self.input_image.shape,
            'input_dtype': str(self.input_image.dtype),
            'input_memory_usage': self.input_image.nbytes,
            'operation_type': self.parameters.operation_type.value,
            'used_optimized_ops': self.use_optimized_ops
        }
        
        # Add optimized operations statistics if available
        if self.use_optimized_ops and self.cv_ops:
            try:
                cv_stats = self.cv_ops.get_performance_stats()
                metadata['optimized_ops_stats'] = cv_stats
            except Exception as e:
                logger.warning(f"Failed to get optimized ops stats: {e}")
        
        return metadata
    
    def cleanup_resources(self) -> None:
        """Cleanup image processing resources."""
        try:
            # Clear image references
            if hasattr(self, 'input_image'):
                del self.input_image
            
            # Reset optimized operations
            if self.cv_ops:
                try:
                    self.cv_ops.reset_memory_pool()
                except Exception as e:
                    logger.warning(f"Error resetting optimized operations: {e}")
        
        except Exception as e:
            logger.warning(f"Error cleaning up image worker resources: {e}")


# Utility functions for common image processing operations
def create_windowing_worker(image: np.ndarray, 
                          window_center: float, 
                          window_width: float,
                          parent=None) -> ImageProcessingWorker:
    """Create a worker for DICOM windowing."""
    params = ImageProcessingParameters(
        operation_type=ImageOperationType.WINDOWING,
        window_center=window_center,
        window_width=window_width
    )
    return ImageProcessingWorker(image, params, "DICOM Windowing", parent=parent)


def create_resize_worker(image: np.ndarray,
                        target_size: Tuple[int, int],
                        parent=None) -> ImageProcessingWorker:
    """Create a worker for image resizing."""
    params = ImageProcessingParameters(
        operation_type=ImageOperationType.RESIZE,
        target_size=target_size
    )
    return ImageProcessingWorker(image, params, "Image Resize", parent=parent)


def create_contrast_worker(image: np.ndarray,
                          contrast_factor: float = 1.5,
                          brightness_offset: float = 0,
                          parent=None) -> ImageProcessingWorker:
    """Create a worker for contrast enhancement."""
    params = ImageProcessingParameters(
        operation_type=ImageOperationType.CONTRAST_ENHANCEMENT,
        contrast_factor=contrast_factor,
        brightness_offset=brightness_offset
    )
    return ImageProcessingWorker(image, params, "Contrast Enhancement", parent=parent)
