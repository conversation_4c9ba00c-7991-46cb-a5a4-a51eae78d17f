"""
AI findings visualizer for converting inference results to visual overlays.
"""

from typing import List, Optional
from PySide6.QtCore import Qt, QRectF
from PySide6.QtGui import QBrush, QColor, QFont, QPen
from PySide6.QtWidgets import QGraphicsRectItem, QGraphicsTextItem

# Import AI postprocessing results
try:
    from ...ai.postprocessing.result_interpreter import (
        InterpretationResult, AnomalyDetection, BoundingBox, AnomalyType,
        ConfidenceLevel, SeverityLevel
    )
except ImportError:
    # Fallback if AI modules not available
    InterpretationResult = None
    AnomalyDetection = None
    BoundingBox = None
    AnomalyType = None
    ConfidenceLevel = None
    SeverityLevel = None


class AIFindingsVisualizer:
    """
    Converts AI inference results into visual overlay graphics items.
    Handles bounding boxes, confidence scores, and anomaly type visualization.
    """
    
    # Color mapping for different anomaly types
    ANOMALY_COLORS = {
        AnomalyType.FRACTURE: QColor(255, 0, 0),      # Red - Critical
        AnomalyType.PNEUMONIA: QColor(255, 165, 0),    # Orange - High priority
        AnomalyType.NODULE: QColor(255, 255, 0),       # Yellow - Attention needed
        AnomalyType.MASS: QColor(255, 0, 255),         # Magenta - Suspicious
        AnomalyType.INFILTRATE: QColor(255, 192, 203), # Pink - Infection
        AnomalyType.CONSOLIDATION: QColor(255, 140, 0), # Dark orange
        AnomalyType.ATELECTASIS: QColor(173, 216, 230), # Light blue
        AnomalyType.PLEURAL_EFFUSION: QColor(0, 191, 255), # Deep sky blue
        AnomalyType.CARDIOMEGALY: QColor(220, 20, 60),  # Crimson
        AnomalyType.CALCIFICATION: QColor(255, 255, 255), # White
        AnomalyType.FOREIGN_OBJECT: QColor(255, 0, 255), # Magenta
        AnomalyType.ARTIFACT: QColor(128, 128, 128),     # Gray
        AnomalyType.UNKNOWN: QColor(0, 255, 0)           # Green - Default
    } if AnomalyType else {}
    
    # Confidence-based opacity mapping
    CONFIDENCE_OPACITY = {
        ConfidenceLevel.VERY_HIGH: 0.8,
        ConfidenceLevel.HIGH: 0.7,
        ConfidenceLevel.MODERATE: 0.6,
        ConfidenceLevel.LOW: 0.4,
        ConfidenceLevel.VERY_LOW: 0.3
    } if ConfidenceLevel else {}
    
    # Severity-based line width
    SEVERITY_LINE_WIDTH = {
        SeverityLevel.CRITICAL: 4,
        SeverityLevel.SEVERE: 3,
        SeverityLevel.MODERATE: 2,
        SeverityLevel.MILD: 2,
        SeverityLevel.MINIMAL: 1
    } if SeverityLevel else {}
    
    def __init__(self, image_width: int, image_height: int):
        """
        Initialize AI findings visualizer.
        
        Args:
            image_width: Width of the medical image in pixels
            image_height: Height of the medical image in pixels
        """
        self.image_width = image_width
        self.image_height = image_height
        
    def create_findings_graphics(self, 
                               interpretation_result: 'InterpretationResult') -> List[QGraphicsRectItem]:
        """
        Convert InterpretationResult to list of QGraphicsItems for overlay display.
        
        Args:
            interpretation_result: AI inference interpretation results
            
        Returns:
            List of QGraphicsItems representing AI findings
        """
        if not interpretation_result or not interpretation_result.detections:
            return []
        
        graphics_items = []
        
        for detection in interpretation_result.detections:
            if detection.bounding_box:
                # Create bounding box graphics item
                bbox_item = self._create_bounding_box_item(detection)
                graphics_items.append(bbox_item)
                
                # Create confidence score text item
                text_item = self._create_confidence_text_item(detection)
                graphics_items.append(text_item)
        
        return graphics_items
    
    def _create_bounding_box_item(self, detection: 'AnomalyDetection') -> QGraphicsRectItem:
        """
        Create a QGraphicsRectItem for an anomaly detection bounding box.
        
        Args:
            detection: AnomalyDetection with bounding box information
            
        Returns:
            QGraphicsRectItem representing the bounding box
        """
        # Convert normalized coordinates to pixel coordinates
        bbox = detection.bounding_box.to_pixel_coordinates(self.image_width, self.image_height)
        
        # Create rectangle item
        rect_item = QGraphicsRectItem(
            QRectF(bbox.x1, bbox.y1, bbox.width, bbox.height)
        )
        
        # Get color based on anomaly type
        color = self.ANOMALY_COLORS.get(detection.anomaly_type, QColor(0, 255, 0))
        
        # Get line width based on severity
        line_width = self.SEVERITY_LINE_WIDTH.get(
            detection.severity, 2
        ) if detection.severity else 2
        
        # Get opacity based on confidence level
        opacity = self.CONFIDENCE_OPACITY.get(
            detection.confidence_level, 0.6
        )
        
        # Set pen (border)
        pen = QPen(color)
        pen.setWidth(line_width)
        pen.setStyle(Qt.SolidLine)
        rect_item.setPen(pen)
        
        # Set brush (fill) - semi-transparent
        fill_color = QColor(color)
        fill_color.setAlphaF(opacity * 0.3)  # Very light fill
        rect_item.setBrush(QBrush(fill_color))
        
        # Set overall opacity
        rect_item.setOpacity(opacity)
        
        # Add tooltip with detection information
        tooltip_text = self._create_detection_tooltip(detection)
        rect_item.setToolTip(tooltip_text)
        
        return rect_item
    
    def _create_confidence_text_item(self, detection: 'AnomalyDetection') -> QGraphicsTextItem:
        """
        Create a QGraphicsTextItem showing confidence score and anomaly type.
        
        Args:
            detection: AnomalyDetection with confidence information
            
        Returns:
            QGraphicsTextItem with confidence and type information
        """
        # Create text content
        confidence_percent = int(detection.confidence_score * 100)
        anomaly_name = detection.anomaly_type.value.replace('_', ' ').title()
        text_content = f"{anomaly_name}\n{confidence_percent}%"
        
        # Create text item
        text_item = QGraphicsTextItem(text_content)
        
        # Set font
        font = QFont("Arial", 10, QFont.Bold)
        text_item.setFont(font)
        
        # Set text color based on confidence level
        if detection.confidence_level in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH]:
            text_color = QColor(255, 255, 255)  # White for high confidence
        else:
            text_color = QColor(255, 255, 0)   # Yellow for lower confidence
        
        text_item.setDefaultTextColor(text_color)
        
        # Position text near the bounding box
        if detection.bounding_box:
            bbox = detection.bounding_box.to_pixel_coordinates(self.image_width, self.image_height)
            # Position text above the bounding box
            text_item.setPos(bbox.x1, bbox.y1 - 30)
        
        # Add background for better readability
        text_item.setOpacity(0.9)
        
        return text_item
    
    def _create_detection_tooltip(self, detection: 'AnomalyDetection') -> str:
        """
        Create detailed tooltip text for a detection.
        
        Args:
            detection: AnomalyDetection object
            
        Returns:
            Formatted tooltip string
        """
        lines = [
            f"Anomaly Type: {detection.anomaly_type.value.replace('_', ' ').title()}",
            f"Confidence: {detection.confidence_score:.3f} ({detection.confidence_level.value})",
        ]
        
        if detection.severity:
            lines.append(f"Severity: {detection.severity.value.title()}")
        
        if detection.bounding_box:
            bbox = detection.bounding_box
            lines.extend([
                f"Location: ({bbox.x1:.3f}, {bbox.y1:.3f}) to ({bbox.x2:.3f}, {bbox.y2:.3f})",
                f"Size: {bbox.width:.3f} × {bbox.height:.3f}",
                f"Area: {bbox.area:.6f}"
            ])
        
        if detection.clinical_significance:
            lines.append(f"Clinical Significance: {detection.clinical_significance}")
        
        return "\n".join(lines)
    
    def create_overall_summary_item(self, 
                                  interpretation_result: 'InterpretationResult') -> QGraphicsTextItem:
        """
        Create a summary text item showing overall AI analysis results.
        
        Args:
            interpretation_result: Complete interpretation results
            
        Returns:
            QGraphicsTextItem with overall summary
        """
        # Create summary content
        detection_count = len(interpretation_result.detections)
        high_conf_count = len(interpretation_result.high_confidence_detections)
        critical_count = len(interpretation_result.critical_findings)
        
        summary_lines = [
            f"AI Analysis Summary",
            f"Overall Confidence: {interpretation_result.overall_confidence:.1%}",
            f"Detections Found: {detection_count}",
            f"High Confidence: {high_conf_count}",
            f"Critical Findings: {critical_count}"
        ]
        
        # Create text item
        summary_text = "\n".join(summary_lines)
        text_item = QGraphicsTextItem(summary_text)
        
        # Style the text
        font = QFont("Arial", 11, QFont.Bold)
        text_item.setFont(font)
        text_item.setDefaultTextColor(QColor(255, 255, 255))
        
        # Position at top of image
        text_item.setPos(10, 10)
        
        # Add semi-transparent background for readability
        text_item.setOpacity(0.85)
        
        return text_item
    
    def update_image_dimensions(self, width: int, height: int):
        """
        Update the image dimensions used for coordinate conversion.
        
        Args:
            width: New image width in pixels
            height: New image height in pixels
        """
        self.image_width = width
        self.image_height = height 