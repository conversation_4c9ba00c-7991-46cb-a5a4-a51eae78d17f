"""
Test to validate mocking strategy implementation for MedScan AI.

This test validates that all mock fixtures work correctly and provide
the expected behavior for medical-grade testing.
"""

import pytest
from tests.fixtures.medical_data import create_mock_patient, create_mock_ai_inference_result


class TestMockingStrategy:
    """Test mocking strategy implementation."""
    
    @pytest.mark.unit
    @pytest.mark.fast
    def test_mock_patient_creation(self):
        """Test mock patient data creation."""
        patient = create_mock_patient("TEST001")
        assert patient['anonymized_id'] == "TEST001"
        assert patient['name'] == "Test^Patient^Anonymized"
        assert patient['institution'] == "Test Medical Center"
    
    @pytest.mark.unit
    @pytest.mark.ai_inference
    def test_mock_ai_result_creation(self):
        """Test mock AI inference result creation."""
        result = create_mock_ai_inference_result()
        assert result['confidence'] == 0.95
        assert result['anomaly_detected'] is True
        assert result['model_version'] == 'unified_medical_v1.0'
    
    @pytest.mark.unit
    def test_mock_fixtures_available(self, mock_patient_data, mock_ai_result):
        """Test that pytest fixtures work correctly."""
        assert mock_patient_data is not None
        assert mock_ai_result is not None
        assert mock_patient_data['anonymized_id'] == "TEST001"
        assert mock_ai_result['confidence'] > 0.8