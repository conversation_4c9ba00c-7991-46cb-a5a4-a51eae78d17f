"""
Worker Manager for MedScan AI GUI Threading

Manages all background worker threads for the GUI, providing centralized
control over DICOM loading, AI inference, and image processing operations.
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QProgressBar, QLabel, QPushButton

from ...core.threading import (
    MedScanWorker,
    DicomLoadingWorker,
    AIInferenceWorker,
    BatchProcessingWorker,
    ImageProcessingWorker,
    DicomLoadingResult,
    BatchJob,
    JobPriority
)

logger = logging.getLogger(__name__)


class WorkerManager(QObject):
    """
    Centralized manager for all GUI worker threads.
    
    Provides a clean interface between the main window and background workers,
    handling progress updates, results, and error management.
    """
    
    # Signals for UI updates
    dicom_loading_started = Signal(str)  # (file_path)
    dicom_loading_progress = Signal(int, str)  # (percentage, message)
    dicom_loading_finished = Signal(object)  # (DicomLoadingResult)
    dicom_loading_error = Signal(str, str)  # (error_message, details)
    
    ai_inference_started = Signal(str)  # (operation_type)
    ai_inference_progress = Signal(int, str)  # (percentage, message)
    ai_inference_finished = Signal(object)  # (inference_result)
    ai_inference_error = Signal(str, str)  # (error_message, details)
    
    batch_processing_started = Signal(int)  # (total_jobs)
    batch_processing_progress = Signal(int, str)  # (percentage, message)
    batch_processing_finished = Signal(object)  # (batch_result)
    batch_processing_error = Signal(str, str)  # (error_message, details)
    
    image_processing_started = Signal(str)  # (operation_type)
    image_processing_progress = Signal(int, str)  # (percentage, message)
    image_processing_finished = Signal(object)  # (processing_result)
    image_processing_error = Signal(str, str)  # (error_message, details)
    
    all_operations_completed = Signal()  # When all workers are idle
    worker_performance_stats = Signal(dict)  # (performance_data)

    def __init__(self, parent=None):
        """Initialize the worker manager."""
        super().__init__(parent)
        
        # Active workers tracking
        self._active_workers: Dict[str, MedScanWorker] = {}
        self._worker_counter = 0
        
        # Progress tracking
        self._progress_callbacks: Dict[str, Callable] = {}
        
        # Performance monitoring
        self._performance_stats = {}
        self._stats_timer = QTimer()
        self._stats_timer.timeout.connect(self._collect_performance_stats)
        self._stats_timer.start(5000)  # Collect stats every 5 seconds
        
        logger.info("WorkerManager initialized")

    def load_dicom_file(self, file_path: str, use_lazy_loading: bool = True) -> str:
        """
        Start DICOM file loading in background.
        
        Args:
            file_path: Path to DICOM file
            use_lazy_loading: Whether to use lazy loading optimization
            
        Returns:
            Worker ID for tracking
        """
        worker_id = self._generate_worker_id("dicom_load")
        
        try:
            worker = DicomLoadingWorker()
            worker.set_operation_parameters({
                'file_path': file_path,
                'use_lazy_loading': use_lazy_loading,
                'validate_on_load': True
            })
            
            # Connect signals
            worker.progress_updated.connect(self.dicom_loading_progress.emit)
            worker.finished_with_result.connect(self._handle_dicom_loading_finished)
            worker.error_occurred.connect(self.dicom_loading_error.emit)
            worker.performance_stats.connect(self._handle_performance_stats)
            
            # Start worker
            self._active_workers[worker_id] = worker
            worker.start()
            
            self.dicom_loading_started.emit(file_path)
            logger.info(f"Started DICOM loading: {file_path} (Worker: {worker_id})")
            
            return worker_id
            
        except Exception as e:
            logger.error(f"Failed to start DICOM loading: {e}")
            self.dicom_loading_error.emit(f"Failed to start loading: {str(e)}", "")
            return ""

    def run_ai_inference(self, image_data: Any, model_type: str = "default") -> str:
        """
        Start AI inference in background.
        
        Args:
            image_data: Image data for inference
            model_type: Type of model to use
            
        Returns:
            Worker ID for tracking
        """
        worker_id = self._generate_worker_id("ai_inference")
        
        try:
            worker = AIInferenceWorker()
            worker.set_operation_parameters({
                'image_data': image_data,
                'model_type': model_type,
                'use_preprocessing': True
            })
            
            # Connect signals
            worker.progress_updated.connect(self.ai_inference_progress.emit)
            worker.finished_with_result.connect(self._handle_ai_inference_finished)
            worker.error_occurred.connect(self.ai_inference_error.emit)
            worker.performance_stats.connect(self._handle_performance_stats)
            
            # Start worker
            self._active_workers[worker_id] = worker
            worker.start()
            
            self.ai_inference_started.emit(model_type)
            logger.info(f"Started AI inference: {model_type} (Worker: {worker_id})")
            
            return worker_id
            
        except Exception as e:
            logger.error(f"Failed to start AI inference: {e}")
            self.ai_inference_error.emit(f"Failed to start inference: {str(e)}", "")
            return ""

    def cancel_worker(self, worker_id: str) -> bool:
        """
        Cancel a specific worker operation.
        
        Args:
            worker_id: ID of worker to cancel
            
        Returns:
            True if cancelled successfully
        """
        try:
            if worker_id in self._active_workers:
                worker = self._active_workers[worker_id]
                worker.cancel_operation()
                logger.info(f"Cancelled worker: {worker_id}")
                return True
            else:
                logger.warning(f"Worker not found for cancellation: {worker_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to cancel worker {worker_id}: {e}")
            return False

    def cancel_all_workers(self):
        """Cancel all active workers."""
        try:
            worker_ids = list(self._active_workers.keys())
            for worker_id in worker_ids:
                self.cancel_worker(worker_id)
                
            logger.info(f"Cancelled {len(worker_ids)} workers")
            
        except Exception as e:
            logger.error(f"Failed to cancel all workers: {e}")

    def get_active_workers(self) -> List[str]:
        """Get list of active worker IDs."""
        return list(self._active_workers.keys())

    def is_busy(self) -> bool:
        """Check if any workers are currently active."""
        return len(self._active_workers) > 0

    def _generate_worker_id(self, operation_type: str) -> str:
        """Generate unique worker ID."""
        self._worker_counter += 1
        return f"{operation_type}_{self._worker_counter}"

    def _handle_dicom_loading_finished(self, result):
        """Handle DICOM loading completion."""
        self._cleanup_worker_by_result(result)
        self.dicom_loading_finished.emit(result)
        self._check_all_operations_completed()

    def _handle_ai_inference_finished(self, result):
        """Handle AI inference completion."""
        self._cleanup_worker_by_result(result)
        self.ai_inference_finished.emit(result)
        self._check_all_operations_completed()

    def _cleanup_worker_by_result(self, result):
        """Clean up worker based on result object."""
        try:
            # Find worker by sender
            sender_worker = self.sender()
            worker_id_to_remove = None
            
            for worker_id, worker in self._active_workers.items():
                if worker == sender_worker:
                    worker_id_to_remove = worker_id
                    break
            
            if worker_id_to_remove:
                del self._active_workers[worker_id_to_remove]
                logger.debug(f"Cleaned up worker: {worker_id_to_remove}")
                
        except Exception as e:
            logger.error(f"Failed to cleanup worker: {e}")

    def _handle_performance_stats(self, stats):
        """Handle performance statistics from workers."""
        try:
            sender_worker = self.sender()
            worker_type = type(sender_worker).__name__
            
            if worker_type not in self._performance_stats:
                self._performance_stats[worker_type] = []
                
            self._performance_stats[worker_type].append(stats)
            
            # Keep only last 10 stats per worker type
            if len(self._performance_stats[worker_type]) > 10:
                self._performance_stats[worker_type] = self._performance_stats[worker_type][-10:]
                
        except Exception as e:
            logger.error(f"Failed to handle performance stats: {e}")

    def _collect_performance_stats(self):
        """Collect and emit performance statistics."""
        try:
            if self._performance_stats:
                self.worker_performance_stats.emit(self._performance_stats.copy())
                
        except Exception as e:
            logger.error(f"Failed to collect performance stats: {e}")

    def _check_all_operations_completed(self):
        """Check if all operations are completed and emit signal."""
        if not self._active_workers:
            self.all_operations_completed.emit()

    def cleanup(self):
        """Cleanup all resources."""
        try:
            self.cancel_all_workers()
            self._stats_timer.stop()
            self._performance_stats.clear()
            logger.info("WorkerManager cleaned up")
            
        except Exception as e:
            logger.error(f"Failed to cleanup WorkerManager: {e}")
