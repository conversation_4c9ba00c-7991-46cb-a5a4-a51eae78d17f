"""
Graphics rendering for annotation display.
Separated from AnnotationManager for better modularity.
"""

from typing import Optional
from PySide6.QtCore import QPoint
from PySide6.QtGui import QPen, QBrush, QPainterPath, QPolygonF, QColor, Qt
from PySide6.QtWidgets import (
    QGraphicsRectItem, QGraphicsPolygonItem, QGraphicsPathItem, QGraphicsItem
)

from .types import AnnotationTool, ManualAnnotation


class AnnotationGraphicsRenderer:
    """Handles creation and updating of annotation graphics items."""
    
    def create_graphics_item(self, annotation: ManualAnnotation) -> Optional[QGraphicsItem]:
        """Create a graphics item for displaying an annotation.
        
        Args:
            annotation: ManualAnnotation to create graphics for
            
        Returns:
            QGraphicsItem for display, or None if creation failed
        """
        if annotation.tool_type == AnnotationTool.RECTANGLE:
            return self._create_rectangle_item(annotation)
        elif annotation.tool_type == AnnotationTool.POLYGON:
            return self._create_polygon_item(annotation)
        elif annotation.tool_type == AnnotationTool.FREEHAND:
            return self._create_freehand_item(annotation)
        return None
    
    def update_graphics_item(self, graphics_item: QGraphicsItem, annotation: ManualAnnotation):
        """Update graphics item appearance from annotation metadata."""
        # Update visual properties
        pen_color = QColor(annotation.metadata.get('pen_color', '#FF0000'))
        pen_width = int(annotation.metadata.get('pen_width', 2))
        opacity = annotation.metadata.get('opacity', 100) / 100.0
        
        pen = QPen(pen_color)
        pen.setWidth(pen_width)
        pen.setCosmetic(True)
        
        graphics_item.setPen(pen)
        graphics_item.setOpacity(opacity)
        
        # Handle visibility
        visible = annotation.metadata.get('visible', True)
        graphics_item.setVisible(visible)
    
    def _create_rectangle_item(self, annotation: ManualAnnotation) -> QGraphicsRectItem:
        """Create a rectangle graphics item."""
        geom = annotation.geometry_data
        rect_item = QGraphicsRectItem(
            geom['x'], geom['y'], geom['width'], geom['height']
        )
        
        self._apply_style(rect_item, annotation)
        rect_item.setData(0, 'annotation')
        return rect_item
    
    def _create_polygon_item(self, annotation: ManualAnnotation) -> QGraphicsPolygonItem:
        """Create a polygon graphics item."""
        geom = annotation.geometry_data
        points = geom['points']
        
        # Convert points to QPolygonF
        polygon = QPolygonF()
        for point_data in points:
            polygon.append(QPoint(int(point_data['x']), int(point_data['y'])))
            
        poly_item = QGraphicsPolygonItem(polygon)
        self._apply_style(poly_item, annotation)
        poly_item.setData(0, 'annotation')
        return poly_item
    
    def _create_freehand_item(self, annotation: ManualAnnotation) -> QGraphicsPathItem:
        """Create a freehand path graphics item."""
        geom = annotation.geometry_data
        points = geom['points']
        
        # Create path from points
        path = QPainterPath()
        if points:
            first_point = points[0]
            path.moveTo(first_point['x'], first_point['y'])
            
            for point_data in points[1:]:
                path.lineTo(point_data['x'], point_data['y'])
                
        path_item = QGraphicsPathItem(path)
        
        # Apply style (no fill for freehand)
        pen_color = QColor(annotation.metadata.get('pen_color', '#FF0000'))
        pen_width = int(annotation.metadata.get('pen_width', 2))
        pen = QPen(pen_color)
        pen.setWidth(pen_width)
        path_item.setPen(pen)
        path_item.setBrush(QBrush(Qt.NoBrush))
        
        path_item.setData(0, 'annotation')
        return path_item
    
    def _apply_style(self, graphics_item: QGraphicsItem, annotation: ManualAnnotation):
        """Apply style from annotation metadata to graphics item."""
        pen_color = QColor(annotation.metadata.get('pen_color', '#FF0000'))
        pen_width = int(annotation.metadata.get('pen_width', 2))
        brush_color = annotation.metadata.get('brush_color', None)
        
        pen = QPen(pen_color)
        pen.setWidth(pen_width)
        pen.setCosmetic(True)
        graphics_item.setPen(pen)
        
        # Apply brush if specified
        if brush_color:
            brush = QBrush(QColor(brush_color))
            graphics_item.setBrush(brush)
        else:
            graphics_item.setBrush(QBrush(Qt.NoBrush)) 