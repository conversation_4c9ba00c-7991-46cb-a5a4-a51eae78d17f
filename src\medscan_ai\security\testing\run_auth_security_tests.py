#!/usr/bin/env python3
"""
Authentication Security Testing Runner for MedScan AI
Test runner script for comprehensive authentication security testing

This script runs authentication security tests and generates reports.
"""

import json
import sys
import argparse
from pathlib import Path
from datetime import datetime

# Add the src directory to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from medscan_ai.security.testing.auth_security_tests import run_authentication_security_tests


def save_report(results, output_path):
    """Save test results to file"""
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    return str(output_file)


def print_summary(results):
    """Print test summary to console"""
    print("\n" + "="*60)
    print("AUTHENTICATION SECURITY TESTING SUMMARY")
    print("="*60)
    
    if "test_category" in results:
        print(f"Test Category: {results['test_category']}")
    
    if "timestamp" in results:
        print(f"Test Timestamp: {results['timestamp']}")
    
    if "summary" in results:
        summary = results["summary"]
        print(f"\nTEST RESULTS:")
        print(f"  Total Tests: {summary.get('total_tests', 0)}")
        print(f"  Passed Tests: {summary.get('passed_tests', 0)}")
        print(f"  Failed Tests: {summary.get('failed_tests', 0)}")
        print(f"  Security Score: {summary.get('security_score', 0)}%")
        print(f"  Readiness Level: {summary.get('readiness_level', 'Unknown')}")
    
    if "issues_by_severity" in results:
        issues = results["issues_by_severity"]
        print(f"\nISSUES BY SEVERITY:")
        print(f"  Critical: {issues.get('critical', 0)}")
        print(f"  High: {issues.get('high', 0)}")
        print(f"  Medium: {issues.get('medium', 0)}")
        print(f"  Low: {issues.get('low', 0)}")
    
    if "test_results" in results:
        print(f"\nTEST DETAILS:")
        for i, test in enumerate(results["test_results"], 1):
            status = "✅ PASSED" if test.get("passed", False) else "❌ FAILED"
            severity = test.get("severity", "unknown").upper()
            print(f"  {i:2d}. {test.get('test_name', 'Unknown Test')} - {status} ({severity})")
            
            if not test.get("passed", False) and test.get("details"):
                print(f"      Details: {test['details']}")
            
            if test.get("recommendations"):
                print(f"      Recommendations: {', '.join(test['recommendations'])}")
    
    if "recommendations" in results and results["recommendations"]:
        print(f"\nTOP PRIORITY RECOMMENDATIONS:")
        for i, rec in enumerate(results["recommendations"][:5], 1):
            print(f"  {i}. {rec}")
    
    print("="*60)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Run MedScan AI Authentication Security Tests"
    )
    parser.add_argument(
        "--output", "-o",
        default=".taskmaster/reports/auth_security_tests.json",
        help="Output file path for test results (default: .taskmaster/reports/auth_security_tests.json)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Quiet mode - only show summary"
    )
    
    args = parser.parse_args()
    
    if args.verbose and not args.quiet:
        print("Starting MedScan AI Authentication Security Testing...")
        print(f"Output will be saved to: {args.output}")
    
    try:
        # Run authentication security tests
        results = run_authentication_security_tests()
        
        # Save results to file
        report_path = save_report(results, args.output)
        
        if not args.quiet:
            print_summary(results)
            print(f"\nDetailed report saved to: {report_path}")
        
        # Determine exit code based on results
        if "summary" in results:
            security_score = results["summary"].get("security_score", 0)
            critical_issues = results.get("issues_by_severity", {}).get("critical", 0)
            
            if critical_issues > 0:
                exit_code = 2  # Critical issues found
            elif security_score < 70:
                exit_code = 1  # Low security score
            else:
                exit_code = 0  # All good
        else:
            exit_code = 1  # Unknown results
        
        if args.verbose and not args.quiet:
            print(f"Exit code: {exit_code}")
        
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"Error running authentication security tests: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(3)


if __name__ == "__main__":
    main() 