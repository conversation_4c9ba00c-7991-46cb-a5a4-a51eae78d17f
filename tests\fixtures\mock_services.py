"""
Service Mocks for External Dependencies

This module provides mock implementations of external services and 
dependencies for comprehensive testing of MedScan AI components.
"""

import pytest
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session

from .medical_data import (
    create_mock_patient,
    create_mock_dicom_metadata,
    create_mock_ai_inference_result,
    create_mock_study
)


# Database Mocks

@pytest.fixture
def mock_database_session():
    """Mock SQLAlchemy database session."""
    session = Mock(spec=Session)
    
    # Configure common session methods
    session.query.return_value = Mock()
    session.add.return_value = None
    session.commit.return_value = None
    session.rollback.return_value = None
    session.close.return_value = None
    session.flush.return_value = None
    session.merge.return_value = Mock()
    session.delete.return_value = None
    
    # Configure query chain methods
    query_mock = session.query.return_value
    query_mock.filter.return_value = query_mock
    query_mock.filter_by.return_value = query_mock
    query_mock.order_by.return_value = query_mock
    query_mock.limit.return_value = query_mock
    query_mock.offset.return_value = query_mock
    query_mock.join.return_value = query_mock
    query_mock.first.return_value = None
    query_mock.all.return_value = []
    query_mock.count.return_value = 0
    query_mock.one.return_value = Mock()
    query_mock.one_or_none.return_value = None
    
    return session


@pytest.fixture
def mock_patient_repository():
    """Mock patient repository with medical operations."""
    repo = Mock()
    
    # Configure repository methods
    repo.get_by_id.return_value = create_mock_patient()
    repo.get_all.return_value = [create_mock_patient(f"TEST{str(i).zfill(3)}") for i in range(1, 4)]
    repo.create.return_value = create_mock_patient()
    repo.update.return_value = create_mock_patient()
    repo.delete.return_value = True
    repo.search.return_value = [create_mock_patient()]
    repo.count.return_value = 3
    
    return repo


@pytest.fixture
def mock_study_repository():
    """Mock study repository for medical studies."""
    repo = Mock()
    
    repo.get_by_id.return_value = create_mock_study()
    repo.get_by_patient.return_value = [create_mock_study()]
    repo.create.return_value = create_mock_study()
    repo.update.return_value = create_mock_study()
    repo.delete.return_value = True
    
    return repo


# AI/ML Service Mocks

@pytest.fixture
def mock_inference_engine():
    """Mock AI inference engine for medical image analysis."""
    engine = Mock()
    
    # Configure inference methods
    engine.run_inference.return_value = create_mock_ai_inference_result()
    engine.run_batch_inference.return_value = [
        create_mock_ai_inference_result() for _ in range(3)
    ]
    engine.load_model.return_value = True
    engine.get_model_info.return_value = {
        'model_name': 'unified_medical_v1.0',
        'version': '1.0.0',
        'architecture': 'ResNet50',
        'loaded': True
    }
    engine.is_model_loaded.return_value = True
    
    return engine


@pytest.fixture
def mock_ai_model():
    """Mock AI model with prediction capabilities."""
    model = Mock()
    
    model.predict.return_value = {
        'confidence': 0.95,
        'predictions': [0.05, 0.95],  # [normal, anomaly]
        'features': [0.1, 0.8, 0.3, 0.9],
        'processing_time': 0.25
    }
    model.predict_batch.return_value = [
        {'confidence': 0.95, 'predictions': [0.05, 0.95]} for _ in range(3)
    ]
    model.load_state_dict.return_value = None
    model.eval.return_value = model
    model.train.return_value = model
    
    return model


@pytest.fixture
def mock_model_registry():
    """Mock model registry for version management."""
    registry = Mock()
    
    registry.register_model.return_value = True
    registry.get_model.return_value = {
        'model_path': '/models/unified_medical_v1.pth',
        'version': '1.0.0',
        'metadata': {
            'architecture': 'ResNet50',
            'training_date': '2024-01-15',
            'validation_accuracy': 0.98
        }
    }
    registry.list_models.return_value = ['unified_medical_v1.0', 'baseline_v1.0']
    registry.get_latest_version.return_value = 'unified_medical_v1.0'
    registry.delete_model.return_value = True
    
    return registry


# Security Service Mocks

@pytest.fixture
def mock_encryption_service():
    """Mock encryption service for security testing."""
    service = Mock()
    
    service.encrypt.return_value = b"mock_encrypted_data_" + b"x" * 32
    service.decrypt.return_value = b"mock_decrypted_data"
    service.encrypt_field.return_value = "encrypted_field_value"
    service.decrypt_field.return_value = "decrypted_field_value"
    service.verify_integrity.return_value = True
    service.generate_key.return_value = b"mock_key_32_bytes_for_testing_ok"
    service.rotate_key.return_value = True
    
    return service


@pytest.fixture
def mock_audit_service():
    """Mock audit service for compliance testing."""
    service = Mock()
    
    service.log_event.return_value = True
    service.get_audit_trail.return_value = []
    service.verify_integrity.return_value = True
    service.export_logs.return_value = "mock_audit_export.json"
    service.search_events.return_value = []
    service.get_user_activity.return_value = []
    
    return service


@pytest.fixture
def mock_rbac_service():
    """Mock RBAC service for authorization testing."""
    service = Mock()
    
    service.check_permission.return_value = True
    service.get_user_roles.return_value = ['Radiologist']
    service.get_user_permissions.return_value = ['read_patient_data', 'view_images']
    service.assign_role.return_value = True
    service.revoke_role.return_value = True
    service.create_role.return_value = {'role_id': 'test_role_123'}
    
    return service


# External API Mocks

@pytest.fixture
def mock_pacs_client():
    """Mock PACS client for medical image retrieval."""
    client = Mock()
    
    client.query_studies.return_value = [create_mock_study()]
    client.retrieve_images.return_value = [create_mock_dicom_metadata()]
    client.store_image.return_value = True
    client.check_connection.return_value = True
    client.get_modalities.return_value = ['CT', 'MR', 'CR', 'DX']
    client.search_patients.return_value = [create_mock_patient()]
    
    return client


@pytest.fixture
def mock_his_client():
    """Mock HIS client for hospital information system."""
    client = Mock()
    
    client.get_patient_info.return_value = create_mock_patient()
    client.get_orders.return_value = []
    client.update_study_status.return_value = True
    client.check_connection.return_value = True
    client.authenticate.return_value = {'token': 'mock_token_123'}
    
    return client


@pytest.fixture
def mock_http_client():
    """Mock HTTP client for external API calls."""
    client = Mock()
    
    client.get.return_value = Mock(
        status_code=200,
        json=lambda: {'status': 'success', 'data': {}},
        text='{"status": "success"}'
    )
    client.post.return_value = Mock(
        status_code=201,
        json=lambda: {'status': 'created', 'id': 'test_123'}
    )
    client.put.return_value = Mock(status_code=200)
    client.delete.return_value = Mock(status_code=204)
    
    return client


# File System Mocks

@pytest.fixture
def mock_file_handler():
    """Mock file handler for file operations."""
    handler = Mock()
    
    handler.read_file.return_value = b"mock_file_content"
    handler.write_file.return_value = True
    handler.delete_file.return_value = True
    handler.exists.return_value = True
    handler.get_size.return_value = 1024
    handler.get_metadata.return_value = {
        'size': 1024,
        'modified': '2024-01-15T10:30:00',
        'checksum': 'abc123def456'
    }
    
    return handler


@pytest.fixture
def mock_dicom_reader():
    """Mock DICOM reader for medical image files."""
    reader = Mock()
    
    reader.read_file.return_value = create_mock_dicom_metadata()
    reader.extract_metadata.return_value = create_mock_dicom_metadata()
    reader.get_pixel_data.return_value = Mock(shape=(512, 512))
    reader.validate_file.return_value = True
    reader.is_dicom_file.return_value = True
    reader.get_file_info.return_value = {
        'format': 'DICOM',
        'size': 1048576,
        'compressed': False
    }
    
    return reader


# GUI Component Mocks

@pytest.fixture
def mock_image_viewer():
    """Mock image viewer for GUI testing."""
    viewer = Mock()
    
    viewer.load_image.return_value = True
    viewer.set_zoom.return_value = None
    viewer.pan_to.return_value = None
    viewer.reset_view.return_value = None
    viewer.get_zoom_level.return_value = 1.0
    viewer.set_windowing.return_value = None
    viewer.add_overlay.return_value = None
    viewer.clear_overlays.return_value = None
    
    return viewer


@pytest.fixture
def mock_annotation_manager():
    """Mock annotation manager for GUI annotations."""
    manager = Mock()
    
    manager.add_annotation.return_value = "anno_123"
    manager.remove_annotation.return_value = True
    manager.get_annotations.return_value = []
    manager.save_annotations.return_value = True
    manager.load_annotations.return_value = True
    manager.clear_annotations.return_value = None
    
    return manager


# Performance and Monitoring Mocks

@pytest.fixture
def mock_performance_monitor():
    """Mock performance monitor for system metrics."""
    monitor = Mock()
    
    monitor.start_monitoring.return_value = None
    monitor.stop_monitoring.return_value = None
    monitor.get_metrics.return_value = {
        'cpu_usage': 15.5,
        'memory_usage': 1024,
        'disk_usage': 50.2,
        'inference_time': 0.25
    }
    monitor.log_performance.return_value = True
    
    return monitor


@pytest.fixture
def mock_logger():
    """Mock logger for testing logging functionality."""
    logger = Mock()
    
    logger.info.return_value = None
    logger.warning.return_value = None
    logger.error.return_value = None
    logger.debug.return_value = None
    logger.critical.return_value = None
    logger.exception.return_value = None
    
    return logger


# Context Managers and Patches

@pytest.fixture
def mock_database_context():
    """Context manager for database mocking."""
    with patch('medscan_ai.database.get_session') as mock_session:
        mock_session.return_value = mock_database_session()
        yield mock_session


@pytest.fixture
def mock_ai_context():
    """Context manager for AI service mocking.""" 
    with patch('medscan_ai.ai.inference.InferenceEngine') as mock_engine:
        mock_engine.return_value = mock_inference_engine()
        yield mock_engine


@pytest.fixture
def mock_security_context():
    """Context manager for security service mocking."""
    with patch('medscan_ai.security.encryption.DataEncryptionService') as mock_encryption:
        mock_encryption.return_value = mock_encryption_service()
        yield mock_encryption


# Error Simulation Fixtures

@pytest.fixture
def mock_connection_error():
    """Mock connection error for resilience testing."""
    error = ConnectionError("Simulated connection failure")
    return error


@pytest.fixture
def mock_timeout_error():
    """Mock timeout error for performance testing."""
    error = TimeoutError("Simulated timeout")
    return error


@pytest.fixture
def mock_file_not_found_error():
    """Mock file not found error for file handling tests."""
    error = FileNotFoundError("Simulated file not found")
    return error 