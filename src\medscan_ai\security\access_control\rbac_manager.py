"""RBAC Manager for MedScan AI Role-Based Access Control.

Provides essential role and permission management functionality
for medical applications with HIPAA/GDPR compliance.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple

from sqlalchemy.orm import Session

from ...database.models import Permission, Role, RolePermission, User, UserRole

# from ...database.engine import get_db_session


class RBACManager:
    """Manager for Role-Based Access Control operations."""

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize RBAC manager.

        Args:
            db_session: Database session (optional)
        """
        self.db_session = db_session  # or get_db_session()
        self.logger = logging.getLogger(__name__)

    # ===================
    # Role Management
    # ===================

    def create_role(self, name: str, display_name: str, **kwargs) -> Role:
        """Create a new role."""
        try:
            role = Role(name=name, display_name=display_name, **kwargs)

            self.db_session.add(role)
            self.db_session.commit()

            self.logger.info(f"Created role: {name}")
            return role

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to create role '{name}': {str(e)}")
            raise

    def get_role_by_name(self, name: str) -> Optional[Role]:
        """Get role by name."""
        return self.db_session.query(Role).filter(Role.name == name).first()

    def list_roles(self, active_only: bool = True) -> List[Role]:
        """List all roles."""
        query = self.db_session.query(Role)
        if active_only:
            query = query.filter(Role.is_active == True)
        return query.order_by(Role.hierarchy_level.desc(), Role.name).all()

    # ===================
    # Permission Management
    # ===================

    def create_permission(
        self,
        name: str,
        display_name: str,
        category: str,
        resource_type: str,
        action: str,
        **kwargs,
    ) -> Permission:
        """Create a new permission."""
        try:
            permission = Permission(
                name=name,
                display_name=display_name,
                category=category,
                resource_type=resource_type,
                action=action,
                **kwargs,
            )

            self.db_session.add(permission)
            self.db_session.commit()

            self.logger.info(f"Created permission: {name}")
            return permission

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to create permission '{name}': {str(e)}")
            raise

    def get_permission_by_name(self, name: str) -> Optional[Permission]:
        """Get permission by name."""
        return self.db_session.query(Permission).filter(Permission.name == name).first()

    def list_permissions(self, category: Optional[str] = None) -> List[Permission]:
        """List all permissions."""
        query = self.db_session.query(Permission).filter(Permission.is_active == True)
        if category:
            query = query.filter(Permission.category == category)
        return query.order_by(Permission.category, Permission.name).all()

    # ===================
    # User Role Management
    # ===================

    def assign_role_to_user(
        self, user_id: str, role_id: str, assigned_by: str
    ) -> UserRole:
        """Assign role to user."""
        try:
            user_role = UserRole(
                user_id=user_id, role_id=role_id, assigned_by=assigned_by
            )

            self.db_session.add(user_role)

            # Update role user count
            role = self.db_session.query(Role).filter(Role.id == role_id).first()
            if role:
                role.user_count += 1

            self.db_session.commit()

            self.logger.info(f"Assigned role {role_id} to user {user_id}")
            return user_role

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(
                f"Failed to assign role {role_id} to user {user_id}: {str(e)}"
            )
            raise

    def get_user_roles(self, user_id: str) -> List[Tuple[Role, UserRole]]:
        """Get all roles for a user."""
        return (
            self.db_session.query(Role, UserRole)
            .join(UserRole, Role.id == UserRole.role_id)
            .filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                Role.is_active == True,
            )
            .all()
        )

    # ===================
    # Role Permission Management
    # ===================

    def grant_permission_to_role(
        self, role_id: str, permission_id: str, granted_by: str
    ) -> RolePermission:
        """Grant permission to role."""
        try:
            role_permission = RolePermission(
                role_id=role_id, permission_id=permission_id, granted_by=granted_by
            )

            self.db_session.add(role_permission)
            self.db_session.commit()

            self.logger.info(f"Granted permission {permission_id} to role {role_id}")
            return role_permission

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(
                f"Failed to grant permission {permission_id} to role {role_id}: {str(e)}"
            )
            raise

    def get_role_permissions(
        self, role_id: str
    ) -> List[Tuple[Permission, RolePermission]]:
        """Get all permissions for a role."""
        return (
            self.db_session.query(Permission, RolePermission)
            .join(RolePermission, Permission.id == RolePermission.permission_id)
            .filter(
                RolePermission.role_id == role_id,
                RolePermission.is_active == True,
                Permission.is_active == True,
            )
            .all()
        )

    def get_user_permissions(self, user_id: str) -> Set[str]:
        """Get all effective permissions for a user."""
        permissions = set()

        # Get user's roles
        user_roles = self.get_user_roles(user_id)

        for role, user_role in user_roles:
            if user_role.is_currently_valid():
                # Get role's permissions
                role_permissions = self.get_role_permissions(role.id)
                for permission, role_permission in role_permissions:
                    if role_permission.is_currently_valid():
                        permissions.add(permission.name)

        return permissions

    def user_has_permission(self, user_id: str, permission_name: str) -> bool:
        """Check if user has specific permission."""
        user_permissions = self.get_user_permissions(user_id)
        return permission_name in user_permissions

    # ===================
    # Medical RBAC Initialization
    # ===================

    def initialize_medical_rbac(self) -> Dict[str, int]:
        """Initialize standard medical roles and permissions."""
        try:
            result = {"roles_created": 0, "permissions_created": 0}

            # Define medical roles
            medical_roles = [
                {
                    "name": "SuperAdmin",
                    "display_name": "Super Administrator",
                    "hierarchy_level": 10,
                    "security_clearance": "Admin",
                },
                {
                    "name": "Admin",
                    "display_name": "Administrator",
                    "hierarchy_level": 9,
                    "security_clearance": "Admin",
                },
                {
                    "name": "Radiologist",
                    "display_name": "Radiologist",
                    "hierarchy_level": 8,
                    "security_clearance": "Elevated",
                },
                {
                    "name": "GP",
                    "display_name": "General Practitioner",
                    "hierarchy_level": 7,
                    "security_clearance": "Elevated",
                },
                {
                    "name": "Technician",
                    "display_name": "Medical Technician",
                    "hierarchy_level": 5,
                    "security_clearance": "Standard",
                    "is_default": True,
                },
                {
                    "name": "Viewer",
                    "display_name": "Viewer",
                    "hierarchy_level": 3,
                    "security_clearance": "Standard",
                },
                {
                    "name": "Auditor",
                    "display_name": "Auditor",
                    "hierarchy_level": 6,
                    "security_clearance": "Elevated",
                },
            ]

            # Create roles
            for role_data in medical_roles:
                if not self.get_role_by_name(role_data["name"]):
                    self.create_role(is_system_role=True, **role_data)
                    result["roles_created"] += 1

            # Define medical permissions
            medical_permissions = [
                {
                    "name": "view_patient_data",
                    "display_name": "View Patient Data",
                    "category": "Patient",
                    "resource_type": "Patient",
                    "action": "read",
                },
                {
                    "name": "edit_patient_data",
                    "display_name": "Edit Patient Data",
                    "category": "Patient",
                    "resource_type": "Patient",
                    "action": "update",
                },
                {
                    "name": "view_dicom_images",
                    "display_name": "View DICOM Images",
                    "category": "DICOM",
                    "resource_type": "DicomImage",
                    "action": "read",
                },
                {
                    "name": "upload_dicom_images",
                    "display_name": "Upload DICOM Images",
                    "category": "DICOM",
                    "resource_type": "DicomImage",
                    "action": "create",
                },
                {
                    "name": "run_ai_analysis",
                    "display_name": "Run AI Analysis",
                    "category": "Analysis",
                    "resource_type": "Analysis",
                    "action": "execute",
                },
                {
                    "name": "view_analysis_results",
                    "display_name": "View Analysis Results",
                    "category": "Analysis",
                    "resource_type": "Analysis",
                    "action": "read",
                },
                {
                    "name": "generate_reports",
                    "display_name": "Generate Reports",
                    "category": "Report",
                    "resource_type": "Report",
                    "action": "create",
                },
                {
                    "name": "view_reports",
                    "display_name": "View Reports",
                    "category": "Report",
                    "resource_type": "Report",
                    "action": "read",
                },
                {
                    "name": "manage_users",
                    "display_name": "Manage Users",
                    "category": "System",
                    "resource_type": "User",
                    "action": "update",
                },
                {
                    "name": "view_audit_logs",
                    "display_name": "View Audit Logs",
                    "category": "Audit",
                    "resource_type": "AuditLog",
                    "action": "read",
                },
            ]

            # Create permissions
            for perm_data in medical_permissions:
                if not self.get_permission_by_name(perm_data["name"]):
                    self.create_permission(is_system_permission=True, **perm_data)
                    result["permissions_created"] += 1

            self.logger.info(f"Initialized medical RBAC: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Failed to initialize medical RBAC: {str(e)}")
            raise
