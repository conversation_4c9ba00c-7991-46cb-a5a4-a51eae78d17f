#!/usr/bin/env python3
"""
Debug script for authentication module imports
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("Testing individual imports...")

# Test 1: Password Manager
try:
    from medscan_ai.security.authentication.password_manager import PasswordManager
    print("✅ PasswordManager import successful")
    pm = PasswordManager()
    print("✅ PasswordManager initialization successful")
except Exception as e:
    print(f"❌ PasswordManager error: {e}")

# Test 2: Credential Service
try:
    from medscan_ai.security.authentication.credential_service import UserCredentialService
    print("✅ UserCredentialService import successful")
except Exception as e:
    print(f"❌ UserCredentialService error: {e}")

# Test 3: RBAC Manager
try:
    from medscan_ai.security.access_control.rbac_manager import RBACManager
    print("✅ RBACManager import successful")
except Exception as e:
    print(f"❌ RBACManager error: {e}")

# Test 4: Session Manager
try:
    from medscan_ai.security.authentication.session_manager import JWTSessionManager
    print("✅ JWTSessionManager import successful")
except Exception as e:
    print(f"❌ JWTSessionManager error: {e}")

# Test 5: MFA Service
try:
    from medscan_ai.security.authentication.mfa_service import MFAService
    print("✅ MFAService import successful")
except Exception as e:
    print(f"❌ MFAService error: {e}")

# Test 6: Authorization
try:
    from medscan_ai.security.authentication.authorization import AuthorizationMiddleware
    print("✅ AuthorizationMiddleware import successful")
except Exception as e:
    print(f"❌ AuthorizationMiddleware error: {e}")

# Test 7: Auth Service
try:
    from medscan_ai.security.authentication.auth_service import AuthenticationService
    print("✅ AuthenticationService import successful")
except Exception as e:
    print(f"❌ AuthenticationService error: {e}")

print("\nTesting with mock session...")

# Mock database session
class MockSession:
    def query(self, *args):
        return self
    
    def filter(self, *args):
        return self
    
    def first(self):
        return None
    
    def commit(self):
        pass
    
    def rollback(self):
        pass
    
    def close(self):
        pass

try:
    mock_session = MockSession()
    
    # Try to initialize services
    from medscan_ai.security.authentication.credential_service import UserCredentialService
    credential_service = UserCredentialService(db_session=mock_session)
    print("✅ CredentialService with mock session successful")
    
    from medscan_ai.security.access_control.rbac_manager import RBACManager
    rbac_manager = RBACManager(db_session=mock_session)
    print("✅ RBACManager with mock session successful")
    
    from medscan_ai.security.authentication.session_manager import JWTSessionManager
    session_manager = JWTSessionManager(
        secret_key="test_key",
        algorithm="HS256"
    )
    print("✅ JWTSessionManager with test config successful")
    
    from medscan_ai.security.authentication.mfa_service import MFAService
    mfa_service = MFAService(db_session=mock_session)
    print("✅ MFAService with mock session successful")
    
    from medscan_ai.security.authentication.auth_service import AuthenticationService
    auth_service = AuthenticationService(
        credential_service=credential_service,
        rbac_manager=rbac_manager,
        session_manager=session_manager,
        mfa_service=mfa_service,
        session=mock_session
    )
    print("✅ AuthenticationService with all dependencies successful")
    
    print(f"\n🎉 All services initialized successfully!")
    print(f"   - AuthenticationService: {type(auth_service).__name__}")
    print(f"   - Available methods: {[m for m in dir(auth_service) if not m.startswith('_')][:5]}...")
    
except Exception as e:
    print(f"❌ Service initialization error: {e}")
    import traceback
    traceback.print_exc() 