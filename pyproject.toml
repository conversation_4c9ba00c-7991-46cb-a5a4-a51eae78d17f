[build-system]
requires = ["setuptools>=68.2.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "medscan-ai"
version = "0.1.0"
description = "Advanced medical imaging analysis application with AI-powered anomaly detection, DICOM support, and comprehensive security features"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "MedScan AI Team", email = "<EMAIL>"}
]
keywords = ["medical", "imaging", "ai", "dicom", "anomaly-detection", "healthcare"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Healthcare Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.10,<3.13"
dependencies = [
    # GUI Framework
    "PySide6>=6.9.0",

    # Medical Imaging
    "pydicom>=2.4.0",
    "SimpleITK>=2.3.0",
    "opencv-python>=4.8.0",

    # Basic ML (compatible with Python 3.12)
    "numpy>=1.24.0",
    "pillow>=10.0.0",
    "scikit-learn>=1.3.0",

    # Database & Security
    "sqlalchemy>=2.0.0",
    # "sqlcipher3>=0.5.2",  # SQLCipher for encrypted database - temporary disabled for TF Lite setup
    "cryptography>=41.0.0",
    "bcrypt>=4.0.0",

    # PDF Generation
    "reportlab>=4.0.0",

    # Data Processing
    "pandas>=2.0.0",
    "scipy>=1.11.0",

    # Logging & Configuration
    "loguru>=0.7.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",

    # Networking & PACS
    "pynetdicom>=2.0.0",
    "requests>=2.31.0",

    # Performance
    "psutil>=5.9.0",
]

[project.optional-dependencies]
ai = [
    # TensorFlow (now fully compatible with Python 3.10-3.12)
    "tensorflow>=2.15.0",
    # Note: tensorflow-lite is included in tensorflow package

    # PyTorch (backup ML framework)
    "torch>=2.0.0",
    "torchvision>=0.15.0",

    # Medical AI specific
    "monai>=1.3.0",  # Medical Open Network for AI
    "nibabel>=5.1.0",  # Neuroimaging data I/O
]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-qt>=4.2.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-qt>=4.2.0",
    "pytest-mock>=3.11.0",
    "pytest-benchmark>=4.0.0",
]

[project.urls]
Homepage = "https://github.com/Sakuleta/MedScan-AI"
Documentation = "https://medscan-ai.readthedocs.io"
Repository = "https://github.com/Sakuleta/MedScan-AI.git"
"Bug Tracker" = "https://github.com/Sakuleta/MedScan-AI/issues"

[project.scripts]
medscan = "medscan_ai.main:main"

[project.gui-scripts]
medscan-gui = "medscan_ai.gui.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.setuptools.package-data]
"medscan_ai" = ["assets/*", "models/*", "config/*"]

# Black formatting
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["medscan_ai"]

# MyPy configuration
[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pydicom.*",
    "SimpleITK.*",
    "cv2.*",
    "reportlab.*",
    "sqlcipher3.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "-q", 
    "--strict-markers",
    "--strict-config",
    "--tb=short",
    "--cov=src/medscan_ai",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml:coverage.xml",
    "--cov-fail-under=0",
    "--durations=10"
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    # Performance categories
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "fast: marks tests as fast running tests",
    
    # Test categories
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "functional: marks tests as functional/end-to-end tests",
    "smoke: marks tests as smoke tests for basic functionality",
    
    # GUI and UI testing
    "gui: marks tests that require GUI",
    "headless: marks tests that can run without display",
    
    # Medical-specific categories
    "dicom: marks tests for DICOM file handling",
    "ai_inference: marks tests for AI model inference",
    "security: marks tests for security features",
    "hipaa: marks tests for HIPAA compliance validation",
    "gdpr: marks tests for GDPR compliance validation",
    "medical_data: marks tests involving medical data processing",
    "patient_data: marks tests involving patient information",
    "audit: marks tests for audit logging functionality",
    
    # Database and storage
    "database: marks tests requiring database access",
    "encryption: marks tests for encryption functionality",
    "persistence: marks tests for data persistence",
    
    # Performance and optimization
    "performance: marks performance benchmark tests",
    "memory: marks memory usage tests",
    "optimization: marks optimization validation tests",
    
    # External dependencies
    "network: marks tests requiring network access",
    "external_api: marks tests calling external APIs",
    "file_io: marks tests involving file operations",
    
    # Risk and compliance levels
    "critical: marks critical functionality tests",
    "high_risk: marks high-risk feature tests",
    "regulatory: marks regulatory compliance tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore:.*PySide.*:DeprecationWarning",
    "ignore:.*Qt.*:DeprecationWarning",
]
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# Coverage configuration
[tool.coverage.run]
source = ["src"]
branch = true
parallel = true
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/site-packages/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
sort = "Cover"
fail_under = 0
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "def __str__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if False:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "@overload",
    "except ImportError:",
    "pass",
    "\\.\\.\\.",
]

# Medical-grade coverage thresholds
[tool.coverage.html]
directory = "htmlcov"
title = "MedScan AI Test Coverage Report"

[tool.coverage.xml]
output = "coverage.xml"
