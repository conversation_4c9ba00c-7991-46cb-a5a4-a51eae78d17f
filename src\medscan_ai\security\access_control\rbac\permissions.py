"""
Permission Management Module for MedScan AI RBAC System
Handles creation, update, deletion, and querying of permissions

Extracted from the original rbac_service.py file for better modularity.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple
from uuid import uuid4

from sqlalchemy.orm import Session

from ....core.utils.logging_config import get_logger
from ....database.engine import get_session
from ....database.models import Permission, Role, RolePermission

logger = get_logger(__name__)


class PermissionManager:
    """Manager for permission operations in RBAC system."""

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize permission manager.

        Args:
            db_session: Database session (optional, will create if not provided)
        """
        self.db_session = db_session or get_session()
        self.logger = logger

    def create_permission(
        self,
        name: str,
        display_name: str,
        category: str,
        resource_type: str,
        action: str,
        description: Optional[str] = None,
        scope: str = "All",
        risk_level: str = "Low",
        phi_access_level: str = "None",
        requires_medical_license: bool = False,
        requires_mfa: bool = False,
        created_by: str = None,
        **kwargs,
    ) -> Permission:
        """Create a new permission.

        Args:
            name: Permission name (unique identifier)
            display_name: Human-readable permission name
            category: Permission category
            resource_type: Type of resource this permission applies to
            action: Action type (create, read, update, delete, etc.)
            description: Permission description
            scope: Permission scope (Own, Department, Facility, All)
            risk_level: Risk level (Low, Medium, High, Critical)
            phi_access_level: PHI access level (None, Limited, Full)
            requires_medical_license: Whether medical license is required
            requires_mfa: Whether MFA is required
            created_by: User ID who created the permission
            **kwargs: Additional permission attributes

        Returns:
            Created permission instance

        Raises:
            ValueError: If permission already exists
        """
        try:
            # Check if permission already exists
            existing_permission = self.get_permission_by_name(name)
            if existing_permission:
                raise ValueError(f"Permission '{name}' already exists")

            # Create permission
            permission = Permission(
                name=name,
                display_name=display_name,
                category=category,
                resource_type=resource_type,
                action=action,
                description=description,
                scope=scope,
                risk_level=risk_level,
                phi_access_level=phi_access_level,
                requires_medical_license=requires_medical_license,
                requires_mfa=requires_mfa,
                created_by=created_by,
                **kwargs,
            )

            self.db_session.add(permission)
            self.db_session.commit()

            self.logger.info(f"Created permission: {name} (ID: {permission.id})")
            return permission

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to create permission '{name}': {str(e)}")
            raise

    def get_permission_by_id(self, permission_id: str) -> Optional[Permission]:
        """Get permission by ID.
        
        Args:
            permission_id: Permission identifier
            
        Returns:
            Permission instance if found, None otherwise
        """
        return (
            self.db_session.query(Permission)
            .filter(Permission.id == permission_id)
            .first()
        )

    def get_permission_by_name(self, name: str) -> Optional[Permission]:
        """Get permission by name.
        
        Args:
            name: Permission name
            
        Returns:
            Permission instance if found, None otherwise
        """
        return self.db_session.query(Permission).filter(Permission.name == name).first()

    def list_permissions(
        self,
        category: Optional[str] = None,
        risk_level: Optional[str] = None,
        phi_access_level: Optional[str] = None,
        active_only: bool = True,
    ) -> List[Permission]:
        """List permissions with optional filtering.
        
        Args:
            category: Filter by permission category
            risk_level: Filter by risk level
            phi_access_level: Filter by PHI access level
            active_only: Whether to include only active permissions
            
        Returns:
            List of permissions matching criteria
        """
        query = self.db_session.query(Permission)

        if active_only:
            query = query.filter(Permission.is_active == True)
        if category:
            query = query.filter(Permission.category == category)
        if risk_level:
            query = query.filter(Permission.risk_level == risk_level)
        if phi_access_level:
            query = query.filter(Permission.phi_access_level == phi_access_level)

        return query.order_by(Permission.category, Permission.name).all()

    def update_permission(
        self, permission_id: str, modified_by: str, **updates
    ) -> Permission:
        """Update permission attributes.
        
        Args:
            permission_id: Permission identifier
            modified_by: User ID who modified the permission
            **updates: Fields to update
            
        Returns:
            Updated permission instance
            
        Raises:
            ValueError: If permission not found or invalid update
        """
        try:
            permission = self.get_permission_by_id(permission_id)
            if not permission:
                raise ValueError(f"Permission with ID '{permission_id}' not found")

            if permission.is_system_permission and "name" in updates:
                raise ValueError("Cannot modify name of system permission")

            # Update attributes
            for key, value in updates.items():
                if hasattr(permission, key):
                    setattr(permission, key, value)

            permission.modified_by = modified_by
            permission.updated_at = datetime.utcnow()

            self.db_session.commit()

            self.logger.info(
                f"Updated permission: {permission.name} (ID: {permission_id})"
            )
            return permission

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(
                f"Failed to update permission '{permission_id}': {str(e)}"
            )
            raise

    def delete_permission(self, permission_id: str) -> bool:
        """Delete permission (if not system permission).
        
        Args:
            permission_id: Permission identifier
            
        Returns:
            True if permission was deleted successfully
            
        Raises:
            ValueError: If permission cannot be deleted
        """
        try:
            permission = self.get_permission_by_id(permission_id)
            if not permission:
                raise ValueError(f"Permission with ID '{permission_id}' not found")

            if permission.is_system_permission:
                raise ValueError("Cannot delete system permission")

            # Check if permission is assigned to any roles
            role_count = (
                self.db_session.query(RolePermission)
                .filter(
                    RolePermission.permission_id == permission_id,
                    RolePermission.is_active == True,
                )
                .count()
            )

            if role_count > 0:
                raise ValueError(
                    f"Cannot delete permission assigned to {role_count} roles"
                )

            self.db_session.delete(permission)
            self.db_session.commit()

            self.logger.info(
                f"Deleted permission: {permission.name} (ID: {permission_id})"
            )
            return True

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(
                f"Failed to delete permission '{permission_id}': {str(e)}"
            )
            raise

    def grant_permission_to_role(
        self,
        role_id: str,
        permission_id: str,
        granted_by: str,
        scope_override: Optional[str] = None,
        conditions: Optional[Dict] = None,
        expires_at: Optional[datetime] = None,
        grant_reason: Optional[str] = None,
    ) -> RolePermission:
        """Grant permission to role.
        
        Args:
            role_id: Role identifier
            permission_id: Permission identifier
            granted_by: User ID who granted the permission
            scope_override: Override permission scope
            conditions: Additional conditions for the grant
            expires_at: When the grant expires
            grant_reason: Reason for granting permission
            
        Returns:
            Created role permission instance
            
        Raises:
            ValueError: If role/permission not found or already granted
        """
        try:
            # Validate role and permission exist
            role = self.db_session.query(Role).filter(Role.id == role_id).first()
            if not role:
                raise ValueError(f"Role with ID '{role_id}' not found")

            permission = self.get_permission_by_id(permission_id)
            if not permission:
                raise ValueError(f"Permission with ID '{permission_id}' not found")

            # Check if already granted
            existing_grant = (
                self.db_session.query(RolePermission)
                .filter(
                    RolePermission.role_id == role_id,
                    RolePermission.permission_id == permission_id,
                    RolePermission.is_active == True,
                )
                .first()
            )

            if existing_grant:
                raise ValueError(f"Permission already granted to role")

            # Create grant
            role_permission = RolePermission(
                role_id=role_id,
                permission_id=permission_id,
                granted_by=granted_by,
                scope_override=scope_override,
                conditions=json.dumps(conditions) if conditions else None,
                expires_at=expires_at,
                grant_reason=grant_reason,
            )

            self.db_session.add(role_permission)
            self.db_session.commit()

            self.logger.info(
                f"Granted permission '{permission.name}' to role '{role.name}'"
            )
            return role_permission

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(
                f"Failed to grant permission '{permission_id}' to role '{role_id}': {str(e)}"
            )
            raise

    def revoke_permission_from_role(
        self,
        role_id: str,
        permission_id: str,
        revoked_by: str,
        revocation_reason: Optional[str] = None,
    ) -> bool:
        """Revoke permission from role.
        
        Args:
            role_id: Role identifier
            permission_id: Permission identifier
            revoked_by: User ID who revoked the permission
            revocation_reason: Reason for revoking permission
            
        Returns:
            True if permission was revoked successfully
            
        Raises:
            ValueError: If permission grant not found
        """
        try:
            role_permission = (
                self.db_session.query(RolePermission)
                .filter(
                    RolePermission.role_id == role_id,
                    RolePermission.permission_id == permission_id,
                    RolePermission.is_active == True,
                )
                .first()
            )

            if not role_permission:
                raise ValueError("Permission grant not found")

            # Revoke permission
            role_permission.is_active = False
            role_permission.revoked_by = revoked_by
            role_permission.revoked_at = datetime.utcnow()
            role_permission.revocation_reason = revocation_reason

            self.db_session.commit()

            self.logger.info(
                f"Revoked permission '{permission_id}' from role '{role_id}'"
            )
            return True

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(
                f"Failed to revoke permission '{permission_id}' from role '{role_id}': {str(e)}"
            )
            raise

    def get_role_permissions(
        self, role_id: str, active_only: bool = True
    ) -> List[Tuple[Permission, RolePermission]]:
        """Get all permissions for a role.
        
        Args:
            role_id: Role identifier
            active_only: Whether to include only active permissions
            
        Returns:
            List of tuples containing permission and role permission instances
        """
        query = (
            self.db_session.query(Permission, RolePermission)
            .join(RolePermission, Permission.id == RolePermission.permission_id)
            .filter(RolePermission.role_id == role_id)
        )

        if active_only:
            query = query.filter(
                RolePermission.is_active == True, 
                Permission.is_active == True
            )

        return query.order_by(Permission.category, Permission.name).all()

    def get_permissions_by_category(self, category: str) -> List[Permission]:
        """Get permissions by category.
        
        Args:
            category: Permission category
            
        Returns:
            List of permissions in the specified category
        """
        return (
            self.db_session.query(Permission)
            .filter(
                Permission.is_active == True,
                Permission.category == category
            )
            .order_by(Permission.name)
            .all()
        )

    def get_high_risk_permissions(self) -> List[Permission]:
        """Get all high-risk permissions.
        
        Returns:
            List of permissions with high or critical risk levels
        """
        return (
            self.db_session.query(Permission)
            .filter(
                Permission.is_active == True,
                Permission.risk_level.in_(["High", "Critical"])
            )
            .order_by(Permission.risk_level.desc(), Permission.name)
            .all()
        )

    def get_phi_permissions(self) -> List[Permission]:
        """Get permissions that provide PHI access.
        
        Returns:
            List of permissions with Limited or Full PHI access
        """
        return (
            self.db_session.query(Permission)
            .filter(
                Permission.is_active == True,
                Permission.phi_access_level.in_(["Limited", "Full"])
            )
            .order_by(Permission.phi_access_level.desc(), Permission.name)
            .all()
        )

    def get_mfa_required_permissions(self) -> List[Permission]:
        """Get permissions that require MFA.
        
        Returns:
            List of permissions requiring multi-factor authentication
        """
        return (
            self.db_session.query(Permission)
            .filter(
                Permission.is_active == True,
                Permission.requires_mfa == True
            )
            .order_by(Permission.risk_level.desc(), Permission.name)
            .all()
        )

    def activate_permission(self, permission_id: str, activated_by: str) -> Permission:
        """Activate a permission.
        
        Args:
            permission_id: Permission identifier
            activated_by: User ID who activated the permission
            
        Returns:
            Activated permission instance
        """
        return self.update_permission(
            permission_id, 
            activated_by, 
            is_active=True, 
            activated_at=datetime.utcnow()
        )

    def deactivate_permission(self, permission_id: str, deactivated_by: str) -> Permission:
        """Deactivate a permission.
        
        Args:
            permission_id: Permission identifier
            deactivated_by: User ID who deactivated the permission
            
        Returns:
            Deactivated permission instance
        """
        return self.update_permission(
            permission_id, 
            deactivated_by, 
            is_active=False, 
            deactivated_at=datetime.utcnow()
        )

    def bulk_grant_permissions(
        self, 
        role_id: str, 
        permission_ids: List[str], 
        granted_by: str
    ) -> List[RolePermission]:
        """Grant multiple permissions to a role.
        
        Args:
            role_id: Role identifier
            permission_ids: List of permission identifiers
            granted_by: User ID who granted the permissions
            
        Returns:
            List of created role permission instances
        """
        role_permissions = []
        
        for permission_id in permission_ids:
            try:
                role_permission = self.grant_permission_to_role(
                    role_id, permission_id, granted_by
                )
                role_permissions.append(role_permission)
            except ValueError as e:
                self.logger.warning(f"Failed to grant permission {permission_id}: {e}")
                continue
        
        return role_permissions

    def bulk_revoke_permissions(
        self, 
        role_id: str, 
        permission_ids: List[str], 
        revoked_by: str
    ) -> int:
        """Revoke multiple permissions from a role.
        
        Args:
            role_id: Role identifier
            permission_ids: List of permission identifiers
            revoked_by: User ID who revoked the permissions
            
        Returns:
            Number of permissions successfully revoked
        """
        revoked_count = 0
        
        for permission_id in permission_ids:
            try:
                if self.revoke_permission_from_role(role_id, permission_id, revoked_by):
                    revoked_count += 1
            except ValueError as e:
                self.logger.warning(f"Failed to revoke permission {permission_id}: {e}")
                continue
        
        return revoked_count 
