"""Core Audit Service for MedScan AI.

Centralized audit logging service providing comprehensive logging capabilities
for HIPAA/GDPR compliance, security monitoring, and regulatory compliance.
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from contextlib import asynccontextmanager
from dataclasses import dataclass, asdict
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from ...database.engine import get_session
from ...database.models.audit_log import AuditLog
from ...database.repositories.audit_repository import AuditRepository
from .immutability import get_immutability_manager

logger = logging.getLogger(__name__)


class ActionType(Enum):
    """Standard audit action types."""
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    LOGIN_FAILED = "LOGIN_FAILED"
    READ = "READ"
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    DICOM_VIEW = "DICOM_VIEW"
    AI_ANALYSIS = "AI_ANALYSIS"
    REPORT_GENERATE = "REPORT_GENERATE"


class ActionCategory(Enum):
    """Audit action categories."""
    AUTHENTICATION = "AUTHENTICATION"
    DATA_ACCESS = "DATA_ACCESS"
    DATA_MODIFICATION = "DATA_MODIFICATION"
    SYSTEM_OPERATION = "SYSTEM_OPERATION"


class RiskLevel(Enum):
    """Risk levels for audit events."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


@dataclass
class AuditContext:
    """Context information for audit events."""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    user_role: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    hostname: Optional[str] = None
    correlation_id: Optional[str] = None
    request_id: Optional[str] = None
    department: Optional[str] = None
    facility: Optional[str] = None


@dataclass
class AuditEvent:
    """Structured audit event data."""
    action: Union[ActionType, str]
    action_category: Union[ActionCategory, str]
    success: bool
    context: AuditContext
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    resource_description: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    before_values: Optional[Dict[str, Any]] = None
    after_values: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    status_code: Optional[int] = None
    processing_time_ms: Optional[int] = None
    data_classification: str = "CONFIDENTIAL"
    phi_accessed: bool = False
    compliance_tags: Optional[List[str]] = None
    risk_level: Optional[RiskLevel] = None
    correlation_id: Optional[str] = None


class EventSerializer:
    """Handles serialization and validation of audit events."""
    
    @staticmethod
    def serialize_event(event: AuditEvent) -> Dict[str, Any]:
        """Serialize audit event to dictionary format."""
        try:
            # Convert enums to string values
            event_dict = asdict(event)
            
            # Handle enum conversions
            if isinstance(event.action, ActionType):
                event_dict['action'] = event.action.value
            if isinstance(event.action_category, ActionCategory):
                event_dict['action_category'] = event.action_category.value
            if isinstance(event.risk_level, RiskLevel):
                event_dict['risk_level'] = event.risk_level.value
                
            # Ensure compliance_tags is a comma-separated string
            if event.compliance_tags:
                event_dict['compliance_tags'] = ','.join(event.compliance_tags)
            
            # Convert nested objects to JSON strings
            if event.details:
                event_dict['details'] = json.dumps(event.details)
            if event.before_values:
                event_dict['before_values'] = json.dumps(event.before_values)
            if event.after_values:
                event_dict['after_values'] = json.dumps(event.after_values)
                
            return event_dict
            
        except Exception as e:
            logger.error(f"Failed to serialize audit event: {e}")
            raise ValueError(f"Event serialization failed: {e}")
    
    @staticmethod
    def validate_event(event: AuditEvent) -> bool:
        """Validate audit event data."""
        try:
            # Required fields validation
            if not event.action:
                raise ValueError("Action is required")
            if not event.action_category:
                raise ValueError("Action category is required")
            if event.success is None:
                raise ValueError("Success status is required")
            if not event.context:
                raise ValueError("Audit context is required")
            
            # Data classification validation
            valid_classifications = ["PUBLIC", "INTERNAL", "CONFIDENTIAL", "RESTRICTED"]
            if event.data_classification not in valid_classifications:
                raise ValueError(f"Invalid data classification: {event.data_classification}")
            
            # PHI access validation
            if event.phi_accessed and event.data_classification == "PUBLIC":
                raise ValueError("PHI access cannot be marked for PUBLIC data")
            
            return True
            
        except ValueError as e:
            logger.warning(f"Audit event validation failed: {e}")
            return False


class RiskCalculator:
    """Calculates risk scores for audit events."""
    
    @staticmethod
    def calculate_risk_score(event: AuditEvent) -> float:
        """Calculate risk score (0.0-1.0) for audit event."""
        try:
            score = 0.0
            
            # Base score by action type
            action_scores = {
                ActionType.LOGIN_FAILED: 0.6,
                ActionType.DELETE: 0.7,
                ActionType.EXPORT: 0.8,
                ActionType.CONFIG_CHANGE: 0.9,
                ActionType.BACKUP_RESTORE: 0.9,
                ActionType.AI_MODEL_UPDATE: 0.7,
                ActionType.DICOM_DELETE: 0.8,
                ActionType.DICOM_ANONYMIZE: 0.6,
            }
            
            if isinstance(event.action, ActionType):
                score += action_scores.get(event.action, 0.2)
            
            # PHI access adds risk
            if event.phi_accessed:
                score += 0.3
            
            # Failed operations add risk
            if not event.success:
                score += 0.4
            
            # High data classification adds risk
            classification_scores = {
                "RESTRICTED": 0.3,
                "CONFIDENTIAL": 0.2,
                "INTERNAL": 0.1,
                "PUBLIC": 0.0
            }
            score += classification_scores.get(event.data_classification, 0.1)
            
            # Cap at 1.0
            return min(score, 1.0)
            
        except Exception as e:
            logger.warning(f"Risk calculation failed: {e}")
            return 0.5  # Default medium risk


class AuditService:
    """Core audit logging service for MedScan AI."""
    
    def __init__(self, session: Optional[Session] = None):
        """Initialize audit service."""
        self._session = session
        self._repository = None
        self._immutability_manager = None
        self._async_queue = asyncio.Queue() if asyncio.get_event_loop().is_running() else None
        self._processing_task = None
        self._serializer = EventSerializer()
        self._risk_calculator = RiskCalculator()
        
    @property
    def repository(self) -> AuditRepository:
        """Get audit repository."""
        if not self._repository:
            session = self._session or get_session()
            self._repository = AuditRepository(session)
        return self._repository
    
    @property
    def immutability_manager(self):
        """Get immutability manager."""
        if not self._immutability_manager:
            session = self._session or get_session()
            self._immutability_manager = get_immutability_manager(session)
        return self._immutability_manager
    
    def log_event(self, event: AuditEvent) -> Optional[AuditLog]:
        """Log audit event."""
        try:
            # Create audit log entry
            audit_log = AuditLog(
                timestamp=datetime.now(timezone.utc),
                user_id=event.context.user_id,
                session_id=event.context.session_id,
                user_role=event.context.user_role,
                action=event.action.value if isinstance(event.action, ActionType) else event.action,
                action_category=event.action_category.value if isinstance(event.action_category, ActionCategory) else event.action_category,
                resource_type=event.resource_type,
                resource_id=event.resource_id,
                ip_address=event.context.ip_address,
                user_agent=event.context.user_agent,
                success=event.success,
                details=json.dumps(event.details) if event.details else None,
                phi_accessed=event.phi_accessed,
                data_classification="CONFIDENTIAL" if event.phi_accessed else "INTERNAL"
            )
            
            # Add to hash chain for immutability
            try:
                self.immutability_manager.add_to_chain(audit_log)
                logger.debug("Audit entry added to hash chain successfully")
            except Exception as e:
                logger.error(f"Failed to add audit entry to hash chain: {e}")
                # Continue with logging even if hash chain fails
                audit_log.tamper_verified = False
            
            # Save to database
            created_log = self.repository.create(audit_log)
            
            if created_log:
                logger.info(f"Audit event logged: {event.action} by {event.context.user_id}")
            
            return created_log
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return None
    
    async def log_event_async(self, event: AuditEvent) -> Optional[AuditLog]:
        """Log audit event asynchronously."""
        if self._async_queue:
            await self._async_queue.put(event)
            return None
        else:
            # Fallback to synchronous logging
            return self.log_event(event)
    
    def log_authentication_event(self, user_id: str, action: ActionType, 
                                success: bool, context: AuditContext,
                                error_message: Optional[str] = None) -> Optional[AuditLog]:
        """Log authentication-related events."""
        event = AuditEvent(
            action=action,
            action_category=ActionCategory.AUTHENTICATION,
            success=success,
            context=context,
            resource_type="USER",
            resource_id=user_id,
            error_message=error_message,
            data_classification="INTERNAL",
            phi_accessed=False,
            compliance_tags=["HIPAA", "AUTHENTICATION"]
        )
        return self.log_event(event)
    
    def log_data_access_event(self, user_id: str, resource_type: str, 
                            resource_id: str, success: bool = True,
                            context: Optional[AuditContext] = None,
                            phi_accessed: bool = True) -> Optional[AuditLog]:
        """Log data access events."""
        if not context:
            context = AuditContext(user_id=user_id)
        
        event = AuditEvent(
            action=ActionType.READ,
            action_category=ActionCategory.DATA_ACCESS,
            success=success,
            context=context,
            resource_type=resource_type,
            resource_id=resource_id,
            data_classification="CONFIDENTIAL" if phi_accessed else "INTERNAL",
            phi_accessed=phi_accessed,
            compliance_tags=["HIPAA", "DATA_ACCESS"] if phi_accessed else ["DATA_ACCESS"]
        )
        return self.log_event(event)
    
    def log_dicom_event(self, action: ActionType, user_id: str, 
                       dicom_id: str, context: AuditContext,
                       success: bool = True, details: Optional[Dict] = None) -> Optional[AuditLog]:
        """Log DICOM-specific events."""
        event = AuditEvent(
            action=action,
            action_category=ActionCategory.DATA_ACCESS,
            success=success,
            context=context,
            resource_type="DICOM",
            resource_id=dicom_id,
            details=details,
            data_classification="CONFIDENTIAL",
            phi_accessed=True,
            compliance_tags=["HIPAA", "DICOM", "PHI"]
        )
        return self.log_event(event)
    
    def log_system_event(self, action: ActionType, details: Optional[Dict] = None,
                        context: Optional[AuditContext] = None) -> Optional[AuditLog]:
        """Log system-level events."""
        if not context:
            context = AuditContext(user_id="system")
        
        event = AuditEvent(
            action=action,
            action_category=ActionCategory.SYSTEM_OPERATION,
            success=True,
            context=context,
            resource_type="SYSTEM",
            details=details,
            data_classification="INTERNAL",
            phi_accessed=False,
            compliance_tags=["SYSTEM", "OPERATIONS"]
        )
        return self.log_event(event)
    
    def _alert_high_risk_event(self, audit_log: AuditLog):
        """Alert on high-risk audit events."""
        try:
            logger.warning(f"HIGH RISK AUDIT EVENT: {audit_log.action} by {audit_log.user_id} "
                          f"(Risk Score: {audit_log.risk_score})")
            
            # Here you could integrate with alerting systems:
            # - Send email notifications
            # - Trigger SIEM alerts
            # - Call security team APIs
            # - Log to dedicated high-risk audit channel
            
        except Exception as e:
            logger.error(f"Failed to alert on high-risk event: {e}")
    
    def get_user_activity(self, user_id: str, limit: int = 100) -> List[AuditLog]:
        """Get recent activity for a user."""
        try:
            return self.repository.filter(user_id=user_id).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get user activity: {e}")
            return []
    
    def get_suspicious_activities(self, risk_threshold: float = 0.7) -> List[AuditLog]:
        """Get suspicious activities above risk threshold."""
        try:
            return self.repository.filter(
                AuditLog.risk_score >= risk_threshold
            ).all()
        except Exception as e:
            logger.error(f"Failed to get suspicious activities: {e}")
            return []
    
    def close(self):
        """Close audit service and cleanup resources."""
        if self._processing_task and not self._processing_task.done():
            self._processing_task.cancel()
        
        if self._session:
            self._session.close()


# Global audit service instance
_audit_service: Optional[AuditService] = None


def get_audit_service() -> AuditService:
    """Get global audit service instance."""
    global _audit_service
    if not _audit_service:
        _audit_service = AuditService()
    return _audit_service


def init_audit_service(session: Optional[Session] = None) -> AuditService:
    """Initialize global audit service."""
    global _audit_service
    _audit_service = AuditService(session)
    return _audit_service 