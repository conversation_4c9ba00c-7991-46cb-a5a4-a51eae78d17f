"""
DICOM module for medical image file handling and processing.

This module provides comprehensive DICOM file support including:
- File reading and validation
- Metadata extraction
- Pixel data processing (standard and optimized)
- Medical-grade error handling

The module is organized into specialized submodules:
- io: File input/output operations
- processing: Pixel data processing
- validation: File validation and integrity checking
- metadata: Metadata extraction and handling
"""

# Import exceptions (kept at root level)
from .exceptions import (
    DicomCorruptedFileError,
    DicomError,
    DicomFileNotFoundError,
    DicomFileReadError,
    DicomFormatError,
    DicomMetadataError,
    DicomNormalizationError,
    DicomPixelDataError,
    DicomPixelProcessingError,
    DicomValidationError,
    DicomWindowingError,
)

# Import from specialized submodules
from .io import DicomReader
from .io.lazy_reader import LazyDicomReader, read_dicom_lazy
from .validation import DicomValidator
from .metadata import (
    DicomMetadata,
    DicomMetadataExtractor,
    ImageInfo,
    PatientInfo,
    SeriesInfo,
    StudyInfo,
    TechnicalInfo,
)
from .processing import (
    PixelProcessor,
    OptimizedPixelProcessor,
    MemoryMappedPixelArray,
    ChunkedPixelProcessor,
)

# Main API exports - maintaining backward compatibility
__all__ = [
    # Main classes
    "DicomReader",
    "LazyDicomReader",
    "read_dicom_lazy",
    "DicomValidator",
    "DicomMetadataExtractor",
    "PixelProcessor",
    # Enhanced processing classes
    "OptimizedPixelProcessor",
    "MemoryMappedPixelArray",
    "ChunkedPixelProcessor",
    # Data structures
    "DicomMetadata",
    "PatientInfo",
    "StudyInfo",
    "SeriesInfo",
    "ImageInfo",
    "TechnicalInfo",
    # Exceptions
    "DicomError",
    "DicomFileNotFoundError",
    "DicomFileReadError",
    "DicomFormatError",
    "DicomMetadataError",
    "DicomPixelDataError",
    "DicomValidationError",
    "DicomCorruptedFileError",
    "DicomPixelProcessingError",
    "DicomWindowingError",
    "DicomNormalizationError",
]

# Version information
__version__ = "0.1.0"
