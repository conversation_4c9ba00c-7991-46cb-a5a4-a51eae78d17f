"""
RBAC Manager Module for MedScan AI RBAC System
High-level orchestrator for role and permission management operations

Extracted from the original rbac_service.py file for better modularity.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple
from sqlalchemy.orm import Session

from ....core.utils.logging_config import get_logger
from ....database.engine import get_session
from ....database.models import Role, Permission, User, UserRole
from .roles import RoleManager
from .permissions import PermissionManager

logger = get_logger(__name__)


class RBACManager:
    """
    High-level RBAC Manager that orchestrates role and permission operations.
    
    This manager provides a unified interface for complex RBAC operations
    that involve both roles and permissions, as well as user assignments.
    """

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize RBAC manager."""
        self.db_session = db_session or get_session()
        self.logger = logger
        
        # Initialize component managers
        self.role_manager = RoleManager(self.db_session)
        self.permission_manager = PermissionManager(self.db_session)

    def assign_role_to_user(
        self,
        user_id: str,
        role_id: str,
        assigned_by: str,
        is_primary: bool = False,
        valid_until: Optional[datetime] = None,
        assignment_reason: Optional[str] = None,
        department_scope: Optional[str] = None,
        facility_scope: Optional[str] = None,
    ) -> UserRole:
        """Assign role to user."""
        try:
            # Validate role exists
            role = self.role_manager.get_role_by_id(role_id)
            if not role:
                raise ValueError(f"Role with ID '{role_id}' not found")

            # Check if user already has this role
            existing_assignment = (
                self.db_session.query(UserRole)
                .filter(
                    UserRole.user_id == user_id,
                    UserRole.role_id == role_id,
                    UserRole.is_active == True,
                )
                .first()
            )

            if existing_assignment:
                raise ValueError(f"User already has role '{role.name}'")

            # Create assignment
            user_role = UserRole(
                user_id=user_id,
                role_id=role_id,
                assigned_by=assigned_by,
                is_primary=is_primary,
                valid_until=valid_until,
                assignment_reason=assignment_reason,
                department_scope=department_scope,
                facility_scope=facility_scope,
            )

            self.db_session.add(user_role)
            self.db_session.commit()

            self.logger.info(f"Assigned role '{role.name}' to user '{user_id}'")
            return user_role

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to assign role '{role_id}' to user '{user_id}': {str(e)}")
            raise

    def get_user_permissions(self, user_id: str) -> Set[str]:
        """Get all permissions for a user (through their roles)."""
        user_roles = (
            self.db_session.query(Role, UserRole)
            .join(UserRole, Role.id == UserRole.role_id)
            .filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True,
                Role.is_active == True
            )
            .all()
        )
        
        permissions = set()
        
        for role, user_role in user_roles:
            role_permissions = self.permission_manager.get_role_permissions(
                role.id, active_only=True
            )
            
            for permission, role_permission in role_permissions:
                permissions.add(permission.name)
        
        return permissions

    def user_has_permission(self, user_id: str, permission_name: str) -> bool:
        """Check if user has a specific permission."""
        user_permissions = self.get_user_permissions(user_id)
        return permission_name in user_permissions
