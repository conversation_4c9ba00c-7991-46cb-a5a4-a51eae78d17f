"""Study repository for DICOM study operations."""

from typing import List

from sqlalchemy.orm import Session

from ..models.study import Study
from .base import BaseRepository


class StudyRepository(BaseRepository[Study]):
    """Repository for DICOM study operations."""

    def __init__(self, session: Session):
        super().__init__(Study, session)

    def find_by_patient_id(self, patient_id: int) -> List[Study]:
        """Find all studies for a specific patient."""
        return self.filter_by(patient_id=patient_id)
