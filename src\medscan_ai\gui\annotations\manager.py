"""
Annotation manager for coordinating annotation tools and user interaction.
Refactored to use modular components for better maintainability.
"""

from typing import Dict, List, Optional, TYPE_CHECKING
from PySide6.QtCore import QPoint, QRectF, Qt
from PySide6.QtGui import QPen, QBrush, QPainterPath, QPolygonF, QColor
from PySide6.QtWidgets import (
    QGraphicsRectItem, QGraphicsPolygonItem, QGraphicsPathItem, QGraphicsItem
)

from .types import AnnotationTool, ManualAnnotation
from .tools import (
    AnnotationToolBase, RectangleTool, PolygonTool, 
    FreehandTool, EraserTool
)
from .persistence_service import AnnotationPersistenceService, AnnotationSaveResult
from .graphics_renderer import AnnotationGraphicsRenderer
from .event_handler import AnnotationEventHandler
from .context_menu_handler import AnnotationContextMenuHandler
from ..core.overlay_manager import LayerType
from ..helpers.coordinate_transformer import ViewState

# TYPE_CHECKING import to satisfy linters
if TYPE_CHECKING:
    from ..core.image_viewer import InteractiveImageViewer

class AnnotationManager:
    """
    Coordinates annotation tools, graphics rendering, and user interactions.
    Uses modular components for better separation of concerns.
    """
    
    def __init__(
        self, 
        image_viewer: 'InteractiveImageViewer',
        persistence_service: Optional[AnnotationPersistenceService] = None
    ):
        """Initialize annotation manager with modular components."""
        self.image_viewer = image_viewer
        self.scene = image_viewer.scene()
        
        # Core data storage
        self.annotations: Dict[str, ManualAnnotation] = {}
        self.annotation_graphics: Dict[str, QGraphicsItem] = {}
        
        # Tool management
        self.tools: Dict[AnnotationTool, AnnotationToolBase] = {}
        self.current_tool_type = AnnotationTool.NONE
        
        # Layer management
        self.annotation_layer_id: Optional[str] = None
        
        # Persistence service
        self.persistence_service = persistence_service
        self.auto_save_enabled = True
        
        # Initialize modular components
        self._setup_components()
        self._setup_connections()
        
        # Initialize tools and layer
        self._initialize_tools()
        self._ensure_annotation_layer()
        
    def _setup_components(self):
        """Initialize modular components."""
        # Graphics rendering component
        self.graphics_renderer = AnnotationGraphicsRenderer()
        
        # Event handling component
        self.event_handler = AnnotationEventHandler(self)
        self.event_handler.set_find_annotation_callback(self._find_annotation_at_position)
        
        # Context menu handling component
        self.context_menu_handler = AnnotationContextMenuHandler(self.image_viewer, self)
    
    def _setup_connections(self):
        """Setup signal connections between components."""
        # Event handler signals
        self.event_handler.annotation_created.connect(self._add_annotation)
        self.event_handler.context_menu_requested.connect(self._show_context_menu)
        
        # Context menu handler signals
        self.context_menu_handler.duplicate_annotation.connect(self._duplicate_annotation)
        self.context_menu_handler.delete_annotation.connect(self.remove_annotation)
        self.context_menu_handler.lock_annotation.connect(self._lock_annotation)
        self.context_menu_handler.bring_to_front.connect(self._bring_to_front)
        self.context_menu_handler.send_to_back.connect(self._send_to_back)
        self.context_menu_handler.update_graphics.connect(self._update_annotation_graphics)
    
    def _initialize_tools(self):
        """Initialize all annotation tools."""
        self.tools = {
            AnnotationTool.RECTANGLE: RectangleTool(),
            AnnotationTool.POLYGON: PolygonTool(),
            AnnotationTool.FREEHAND: FreehandTool(),
            AnnotationTool.ERASER: EraserTool()
        }
    
    def _ensure_annotation_layer(self):
        """Ensure annotation overlay layer exists and is valid."""
        if self.annotation_layer_id:
            layer = self.image_viewer.get_overlay_layer(self.annotation_layer_id)
            if layer and layer.group is not None:
                try:
                    _ = layer.group.scene()  # Test C++ validity
                    return  # Layer is valid
                except RuntimeError:
                    pass
        
        # Create new layer
        self.annotation_layer_id = self.image_viewer.create_overlay_layer(
            LayerType.MANUAL_ANNOTATIONS, "Manual Annotations"
        )
    
    # Public API - Tool Management
    def set_active_tool(self, tool_type: AnnotationTool):
        """Set the active annotation tool."""
        # Deactivate current tool
        active_tool = self.tools.get(self.current_tool_type)
        if active_tool:
            active_tool.deactivate()
        
        self.current_tool_type = tool_type
        
        # Activate new tool
        if tool_type != AnnotationTool.NONE:
            new_tool = self.tools.get(tool_type)
            if new_tool:
                new_tool.activate()
                self.event_handler.set_active_tool(new_tool)
        else:
            self.event_handler.set_active_tool(None)
    
    def get_active_tool(self) -> AnnotationTool:
        """Get the currently active tool type."""
        return self.current_tool_type
    
    # Public API - Event Handling (Delegation to EventHandler)
    def handle_mouse_press(self, scene_pos: QPoint) -> bool:
        """Handle mouse press event."""
        return self.event_handler.handle_mouse_press(scene_pos)
    
    def handle_mouse_move(self, scene_pos: QPoint) -> bool:
        """Handle mouse move event."""
        return self.event_handler.handle_mouse_move(scene_pos)
    
    def handle_mouse_release(self, scene_pos: QPoint) -> bool:
        """Handle mouse release event."""
        return self.event_handler.handle_mouse_release(scene_pos)
    
    def handle_double_click(self, scene_pos: QPoint) -> bool:
        """Handle double-click event."""
        return self.event_handler.handle_double_click(scene_pos)
    
    def handle_right_click(self, scene_pos: QPoint, global_pos) -> bool:
        """Handle right-click for context menu."""
        return self.event_handler.handle_right_click(scene_pos, global_pos)
    
    def cancel_current_drawing(self):
        """Cancel the current drawing operation."""
        self.event_handler.cancel_current_drawing()
    
    # Public API - Persistence
    def set_persistence_service(self, service: Optional[AnnotationPersistenceService]):
        """Set or update the persistence service."""
        self.persistence_service = service
    
    def set_auto_save(self, enabled: bool):
        """Enable or disable automatic saving to database."""
        self.auto_save_enabled = enabled
    
    def load_annotations_from_database(self, load_study: bool = False) -> bool:
        """Load annotations from database."""
        if not self.persistence_service:
            return False
        
        # Implementation details moved to persistence service
        # This is now just coordination
        try:
            # Clear existing annotations
            self.clear_all_annotations()
            
            # Load from persistence service
            # (Implementation details would go here)
            return True
        except Exception as e:
            print(f"Error loading annotations: {e}")
            return False
    
    # Public API - Annotation Management
    def remove_annotation(self, annotation_id: str) -> bool:
        """Remove an annotation by ID."""
        if annotation_id not in self.annotations:
            return False
        
        # Remove graphics item
        if annotation_id in self.annotation_graphics:
            graphics_item = self.annotation_graphics[annotation_id]
            if self.annotation_layer_id:
                self.image_viewer.remove_graphics_item_from_layer(
                    self.annotation_layer_id, graphics_item
                )
            del self.annotation_graphics[annotation_id]
        
        # Remove annotation data
        del self.annotations[annotation_id]
        return True
    
    def clear_all_annotations(self):
        """Clear all annotations."""
        if self.annotation_layer_id:
            self.image_viewer.clear_overlay_layer(self.annotation_layer_id)
        
        self.annotations.clear()
        self.annotation_graphics.clear()
    
    def get_annotation_count(self) -> int:
        """Get total number of annotations."""
        return len(self.annotations)
    
    def get_annotations_by_tool(self, tool_type: AnnotationTool) -> List[ManualAnnotation]:
        """Get all annotations created with a specific tool."""
        return [
            annotation for annotation in self.annotations.values()
            if annotation.tool_type == tool_type
        ]
    
    def export_annotations(self) -> List[dict]:
        """Export all annotations to a list of dictionaries."""
        return [annotation.to_dict() for annotation in self.annotations.values()]
    
    def import_annotations(self, annotation_data: List[dict]):
        """Import annotations from a list of dictionaries."""
        for data in annotation_data:
            annotation = ManualAnnotation.from_dict(data)
            self._add_annotation(annotation)
    
    # Internal coordination methods
    def _add_annotation(self, annotation: ManualAnnotation):
        """Add a new annotation (called by event handler)."""
        # Ensure layer is valid
        self._ensure_annotation_layer()
        
        # Store annotation
        self.annotations[annotation.id] = annotation
        
        # Auto-save to database if enabled
        if self.auto_save_enabled and self.persistence_service:
            # Persistence logic would go here
            pass
        
        # Create graphics item using renderer
        graphics_item = self.graphics_renderer.create_graphics_item(annotation)
        if graphics_item:
            self.annotation_graphics[annotation.id] = graphics_item
            
            # Add to annotation layer
            if self.annotation_layer_id:
                self.image_viewer.add_graphics_item_to_layer(
                    self.annotation_layer_id, graphics_item
                )
    
    def _show_context_menu(self, scene_pos: QPoint, global_pos: QPoint, annotation: ManualAnnotation):
        """Show context menu for annotation (called by event handler)."""
        self.context_menu_handler.show_context_menu(annotation, global_pos)
    
    def _find_annotation_at_position(self, scene_pos: QPoint) -> Optional[ManualAnnotation]:
        """Find annotation at given scene position."""
        for annotation_id, graphics_item in self.annotation_graphics.items():
            if graphics_item.contains(graphics_item.mapFromScene(scene_pos)):
                return self.annotations.get(annotation_id)
        return None
    
    def _duplicate_annotation(self, annotation_id: str):
        """Duplicate an annotation (called by context menu handler)."""
        if annotation_id in self.annotations:
            original = self.annotations[annotation_id]
            duplicate = original.copy()
            
            # Offset position slightly
            if duplicate.tool_type == AnnotationTool.RECTANGLE:
                duplicate.geometry_data['x'] += 10
                duplicate.geometry_data['y'] += 10
            elif duplicate.tool_type in [AnnotationTool.FREEHAND, AnnotationTool.POLYGON]:
                for point in duplicate.geometry_data['points']:
                    point['x'] += 10
                    point['y'] += 10
            
            self._add_annotation(duplicate)
    
    def _lock_annotation(self, annotation_id: str, locked: bool):
        """Lock/unlock annotation (called by context menu handler)."""
        if annotation_id in self.annotations:
            self.annotations[annotation_id].metadata['locked'] = locked
    
    def _bring_to_front(self, annotation_id: str):
        """Bring annotation to front (called by context menu handler)."""
        if annotation_id in self.annotation_graphics:
            self.annotation_graphics[annotation_id].setZValue(100)
    
    def _send_to_back(self, annotation_id: str):
        """Send annotation to back (called by context menu handler)."""
        if annotation_id in self.annotation_graphics:
            self.annotation_graphics[annotation_id].setZValue(-100)
    
    def _update_annotation_graphics(self, annotation_id: str):
        """Update graphics item from annotation metadata."""
        if annotation_id in self.annotations and annotation_id in self.annotation_graphics:
            annotation = self.annotations[annotation_id]
            graphics_item = self.annotation_graphics[annotation_id]
            self.graphics_renderer.update_graphics_item(graphics_item, annotation)