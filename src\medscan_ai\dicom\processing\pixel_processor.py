"""
DICOM pixel data processing and normalization utilities.

This module provides comprehensive pixel data processing capabilities including:
- Raw pixel data extraction and normalization
- RescaleSlope and RescaleIntercept application
- PhotometricInterpretation handling
- Windowing and leveling operations
- Medical image data type conversions
"""

from typing import Any, Dict, Optional, Tuple, Union

import numpy as np
import pydicom
from pydicom.dataset import Dataset
from pydicom.pixel_data_handlers.util import apply_modality_lut, apply_voi_lut

from ..exceptions import (
    DicomNormalizationError,
    DicomPixelDataError,
    DicomPixelProcessingError,
    DicomWindowingError,
)


class PixelProcessor:
    """
    Advanced DICOM pixel data processing and normalization utility.

    Handles various DICOM image types and provides medical-grade
    pixel data processing capabilities.
    """

    def __init__(self):
        """Initialize pixel processor."""
        self._supported_photometric = {
            "MONOCHROME1",  # Inverted grayscale (0 = white)
            "MONOCHROME2",  # Normal grayscale (0 = black)
            "RGB",  # RGB color
            "PALETTE COLOR",  # Palette-based color
            "YBR_FULL",  # YBR color space
            "YBR_FULL_422",  # YBR with subsampling
            "YBR_PARTIAL_422",
        }

    def extract_pixel_array(self, dataset: Dataset) -> np.ndarray:
        """
        Extract raw pixel array from DICOM dataset.

        Args:
            dataset: DICOM dataset containing pixel data

        Returns:
            Raw pixel array as numpy array

        Raises:
            DicomPixelDataError: If pixel data cannot be extracted
        """
        try:
            if not hasattr(dataset, "pixel_array"):
                raise DicomPixelDataError("Dataset does not contain pixel data")

            pixel_array = dataset.pixel_array

            if pixel_array is None:
                raise DicomPixelDataError("Pixel array is None")

            return pixel_array.copy()

        except Exception as e:
            raise DicomPixelDataError(f"Failed to extract pixel array: {str(e)}")

    def apply_modality_transforms(
        self, pixel_array: np.ndarray, dataset: Dataset
    ) -> np.ndarray:
        """
        Apply modality-specific transformations (RescaleSlope, RescaleIntercept).

        Args:
            pixel_array: Raw pixel array
            dataset: DICOM dataset containing transformation parameters

        Returns:
            Transformed pixel array with modality LUT applied

        Raises:
            DicomNormalizationError: If transformation fails
        """
        try:
            # Use pydicom's built-in modality LUT if available
            if hasattr(dataset, "RescaleSlope") or hasattr(dataset, "RescaleIntercept"):
                try:
                    # Apply modality LUT using pydicom
                    transformed = apply_modality_lut(pixel_array, dataset)
                    return transformed
                except Exception:
                    # Fallback to manual transformation
                    pass

            # Manual transformation
            rescale_slope = getattr(dataset, "RescaleSlope", 1.0)
            rescale_intercept = getattr(dataset, "RescaleIntercept", 0.0)

            # Convert to float for calculations
            transformed = pixel_array.astype(np.float64)

            # Apply linear transformation: output = slope * input + intercept
            if rescale_slope != 1.0 or rescale_intercept != 0.0:
                transformed = transformed * rescale_slope + rescale_intercept

            return transformed

        except Exception as e:
            raise DicomNormalizationError(
                f"Failed to apply modality transforms: {str(e)}"
            )

    def apply_voi_transforms(
        self,
        pixel_array: np.ndarray,
        dataset: Dataset,
        window_center: Optional[float] = None,
        window_width: Optional[float] = None,
    ) -> np.ndarray:
        """
        Apply Value of Interest (VOI) transformations for windowing/leveling.

        Args:
            pixel_array: Modality-transformed pixel array
            dataset: DICOM dataset
            window_center: Override window center value
            window_width: Override window width value

        Returns:
            Windowed pixel array

        Raises:
            DicomWindowingError: If windowing fails
        """
        try:
            # Use provided values or extract from dataset
            if window_center is None:
                window_center = getattr(dataset, "WindowCenter", None)
            if window_width is None:
                window_width = getattr(dataset, "WindowWidth", None)

            # Handle multiple window center/width values
            if isinstance(window_center, (list, tuple)):
                window_center = window_center[0]
            if isinstance(window_width, (list, tuple)):
                window_width = window_width[0]

            # If no windowing parameters, return as-is
            if window_center is None or window_width is None:
                return pixel_array

            try:
                # Use pydicom's VOI LUT if available
                transformed = apply_voi_lut(pixel_array, dataset)
                return transformed
            except Exception:
                # Fallback to manual windowing
                return self._manual_windowing(pixel_array, window_center, window_width)

        except Exception as e:
            raise DicomWindowingError(f"Failed to apply VOI transforms: {str(e)}")

    def _manual_windowing(
        self, pixel_array: np.ndarray, window_center: float, window_width: float
    ) -> np.ndarray:
        """
        Manually apply windowing transformation.

        Args:
            pixel_array: Input pixel array
            window_center: Window center value
            window_width: Window width value

        Returns:
            Windowed pixel array
        """
        # Calculate window bounds
        window_min = window_center - window_width / 2.0
        window_max = window_center + window_width / 2.0

        # Apply windowing
        windowed = np.clip(pixel_array, window_min, window_max)

        # Normalize to 0-1 range
        if window_max != window_min:
            windowed = (windowed - window_min) / (window_max - window_min)
        else:
            windowed = np.zeros_like(windowed)

        return windowed

    def handle_photometric_interpretation(
        self, pixel_array: np.ndarray, dataset: Dataset
    ) -> np.ndarray:
        """
        Handle different photometric interpretations.

        Args:
            pixel_array: Input pixel array
            dataset: DICOM dataset

        Returns:
            Processed pixel array with correct photometric interpretation

        Raises:
            DicomPixelProcessingError: If photometric interpretation is unsupported
        """
        photometric = getattr(dataset, "PhotometricInterpretation", "MONOCHROME2")

        if photometric not in self._supported_photometric:
            raise DicomPixelProcessingError(
                f"Unsupported PhotometricInterpretation: {photometric}"
            )

        try:
            if photometric == "MONOCHROME1":
                # Invert the image (0 = white in MONOCHROME1)
                if pixel_array.dtype == np.bool_:
                    return ~pixel_array
                else:
                    max_val = (
                        np.iinfo(pixel_array.dtype).max
                        if np.issubdtype(pixel_array.dtype, np.integer)
                        else 1.0
                    )
                    return max_val - pixel_array

            elif photometric == "MONOCHROME2":
                # Normal grayscale, no inversion needed
                return pixel_array

            elif photometric in ["RGB", "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_422"]:
                # Color images - handle appropriately
                return self._process_color_image(pixel_array, photometric)

            elif photometric == "PALETTE COLOR":
                # Apply color palette if available
                return self._apply_color_palette(pixel_array, dataset)

            else:
                # Default handling for other types
                return pixel_array

        except Exception as e:
            raise DicomPixelProcessingError(
                f"Failed to handle photometric interpretation {photometric}: {str(e)}"
            )

    def _process_color_image(
        self, pixel_array: np.ndarray, photometric: str
    ) -> np.ndarray:
        """Process color images based on photometric interpretation."""
        if photometric == "RGB":
            return pixel_array
        elif photometric.startswith("YBR"):
            # Convert YBR to RGB if needed
            # For now, return as-is (could implement YBR->RGB conversion)
            return pixel_array
        return pixel_array

    def _apply_color_palette(
        self, pixel_array: np.ndarray, dataset: Dataset
    ) -> np.ndarray:
        """Apply color palette to palette color images."""
        # Implementation would require palette lookup
        # For now, return as grayscale
        return pixel_array

    def normalize_to_display_range(
        self, pixel_array: np.ndarray, output_range: Tuple[float, float] = (0.0, 1.0)
    ) -> np.ndarray:
        """
        Normalize pixel array to specified display range.

        Args:
            pixel_array: Input pixel array
            output_range: Target range (min, max)

        Returns:
            Normalized pixel array

        Raises:
            DicomNormalizationError: If normalization fails
        """
        try:
            min_val, max_val = output_range

            # Get current range
            current_min = np.min(pixel_array)
            current_max = np.max(pixel_array)

            # Avoid division by zero
            if current_max == current_min:
                return np.full_like(pixel_array, min_val)

            # Normalize to 0-1 first
            normalized = (pixel_array - current_min) / (current_max - current_min)

            # Scale to target range
            scaled = normalized * (max_val - min_val) + min_val

            return scaled

        except Exception as e:
            raise DicomNormalizationError(
                f"Failed to normalize to display range: {str(e)}"
            )

    def get_pixel_info(self, dataset: Dataset) -> Dict[str, Any]:
        """
        Extract comprehensive pixel information from dataset.

        Args:
            dataset: DICOM dataset

        Returns:
            Dictionary containing pixel information
        """
        info = {}

        # Basic pixel attributes
        info["rows"] = getattr(dataset, "Rows", None)
        info["columns"] = getattr(dataset, "Columns", None)
        info["bits_allocated"] = getattr(dataset, "BitsAllocated", None)
        info["bits_stored"] = getattr(dataset, "BitsStored", None)
        info["high_bit"] = getattr(dataset, "HighBit", None)
        info["pixel_representation"] = getattr(dataset, "PixelRepresentation", None)
        info["photometric_interpretation"] = getattr(
            dataset, "PhotometricInterpretation", None
        )
        info["samples_per_pixel"] = getattr(dataset, "SamplesPerPixel", None)

        # Transformation parameters
        info["rescale_slope"] = getattr(dataset, "RescaleSlope", None)
        info["rescale_intercept"] = getattr(dataset, "RescaleIntercept", None)
        info["window_center"] = getattr(dataset, "WindowCenter", None)
        info["window_width"] = getattr(dataset, "WindowWidth", None)

        # Physical parameters
        info["pixel_spacing"] = getattr(dataset, "PixelSpacing", None)
        info["slice_thickness"] = getattr(dataset, "SliceThickness", None)

        return info

    def process_full_pipeline(
        self,
        dataset: Dataset,
        window_center: Optional[float] = None,
        window_width: Optional[float] = None,
        output_range: Tuple[float, float] = (0.0, 1.0),
    ) -> np.ndarray:
        """
        Execute complete pixel processing pipeline.

        Args:
            dataset: DICOM dataset
            window_center: Override window center
            window_width: Override window width
            output_range: Final output range

        Returns:
            Fully processed pixel array ready for display

        Raises:
            DicomPixelProcessingError: If any step fails
        """
        try:
            # Extract raw pixel data
            pixel_array = self.extract_pixel_array(dataset)

            # Apply modality transformations
            pixel_array = self.apply_modality_transforms(pixel_array, dataset)

            # Handle photometric interpretation
            pixel_array = self.handle_photometric_interpretation(pixel_array, dataset)

            # Apply windowing/leveling
            pixel_array = self.apply_voi_transforms(
                pixel_array, dataset, window_center, window_width
            )

            # Final normalization
            pixel_array = self.normalize_to_display_range(pixel_array, output_range)

            return pixel_array

        except Exception as e:
            raise DicomPixelProcessingError(
                f"Full pipeline processing failed: {str(e)}"
            )
