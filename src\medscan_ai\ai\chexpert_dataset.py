#!/usr/bin/env python3
"""
CheXpert Dataset Implementation for Multi-label Chest X-ray Classification

This module implements a PyTorch Dataset for the CheXpert dataset,
handling multi-label classification with proper label preprocessing.
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Tuple, Optional, List
from PIL import Image
import torch
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import logging

logger = logging.getLogger(__name__)

class CheXpertDataset(Dataset):
    """
    CheXpert Dataset for multi-label chest X-ray classification.
    
    Handles the CheXpert labeling scheme:
    - 1.0: Positive
    - 0.0: Negative  
    - -1.0: Uncertain (can be mapped to 0 or 1)
    - NaN/empty: Not mentioned (typically mapped to 0)
    """
    
    # CheXpert label columns (14 pathologies)
    LABEL_COLUMNS = [
        'No Finding', 'Enlarged Cardiomediastinum', 'Cardiomegaly',
        'Lung Opacity', 'Lung Lesion', 'Edema', 'Consolidation',
        'Pneumonia', 'Atelectasis', 'Pneumothorax', 'Pleural Effusion',
        'Pleural Other', 'Fracture', 'Support Devices'
    ]
    
    def __init__(self, 
                 csv_file: str,
                 root_dir: str,
                 transform: Optional[transforms.Compose] = None,
                 uncertain_policy: str = 'zeros',  # 'zeros', 'ones', 'ignore'
                 max_samples: Optional[int] = None):
        """
        Initialize CheXpert dataset.
        
        Args:
            csv_file: Path to train.csv or valid.csv
            root_dir: Root directory containing the dataset
            transform: Optional transforms to apply to images
            uncertain_policy: How to handle uncertain labels (-1.0)
                - 'zeros': Map -1.0 to 0.0
                - 'ones': Map -1.0 to 1.0  
                - 'ignore': Keep -1.0 (for specialized loss functions)
            max_samples: Limit number of samples for testing
        """
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.uncertain_policy = uncertain_policy
        
        # Load CSV data
        self.df = pd.read_csv(csv_file)
        
        # Limit samples if specified
        if max_samples is not None:
            self.df = self.df.head(max_samples)
            logger.info(f"Limited dataset to {max_samples} samples")
        
        # Process labels
        self.labels = self._process_labels()
        
        # Image paths (relative to root_dir)
        self.image_paths = self.df['Path'].tolist()
        
        logger.info(f"Loaded CheXpert dataset: {len(self.df)} samples")
        logger.info(f"Label distribution: {self._get_label_distribution()}")
    
    def _process_labels(self) -> torch.Tensor:
        """
        Process CheXpert labels according to uncertain policy.
        
        Returns:
            Tensor of shape (num_samples, 14) with processed labels
        """
        # Extract label columns
        label_data = self.df[self.LABEL_COLUMNS].values
        
        # Handle NaN values (not mentioned -> 0)
        label_data = np.nan_to_num(label_data, nan=0.0)
        
        # Handle uncertain labels (-1.0)
        if self.uncertain_policy == 'zeros':
            label_data[label_data == -1.0] = 0.0
        elif self.uncertain_policy == 'ones':
            label_data[label_data == -1.0] = 1.0
        # 'ignore' policy keeps -1.0 as is
        
        return torch.tensor(label_data, dtype=torch.float32)
    
    def _get_label_distribution(self) -> dict:
        """Get distribution of positive labels for each pathology."""
        positive_counts = (self.labels == 1.0).sum(dim=0)
        distribution = {}
        for i, label in enumerate(self.LABEL_COLUMNS):
            distribution[label] = positive_counts[i].item()
        return distribution
    
    def __len__(self) -> int:
        """Return number of samples."""
        return len(self.df)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get sample by index.
        
        Args:
            idx: Sample index
            
        Returns:
            Tuple of (image_tensor, label_tensor)
        """
        # Get image path
        img_path = self.root_dir / self.image_paths[idx]
        
        # Handle missing files gracefully
        if not img_path.exists():
            logger.warning(f"Image not found: {img_path}")
            # Return dummy image and labels
            dummy_image = torch.zeros(3, 224, 224)
            return dummy_image, self.labels[idx]
        
        try:
            # Load image
            image = Image.open(img_path).convert('RGB')
            
            # Apply transforms
            if self.transform:
                image = self.transform(image)
            else:
                # Default transform
                image = transforms.ToTensor()(image)
            
            return image, self.labels[idx]
            
        except Exception as e:
            logger.warning(f"Error loading image {img_path}: {e}")
            # Return dummy image and labels
            dummy_image = torch.zeros(3, 224, 224)
            return dummy_image, self.labels[idx]
    
    def get_class_weights(self) -> torch.Tensor:
        """
        Calculate class weights for imbalanced dataset.
        
        Returns:
            Tensor of weights for each pathology
        """
        positive_counts = (self.labels == 1.0).sum(dim=0)
        negative_counts = (self.labels == 0.0).sum(dim=0)
        
        # Calculate inverse frequency weights
        total_samples = len(self.labels)
        weights = total_samples / (2 * positive_counts + 1e-8)  # Add small epsilon
        
        return weights
    
    def get_sample_info(self, idx: int) -> dict:
        """Get detailed information about a sample."""
        row = self.df.iloc[idx]
        info = {
            'path': self.image_paths[idx],
            'sex': row.get('Sex', 'Unknown'),
            'age': row.get('Age', 'Unknown'),
            'view': row.get('Frontal/Lateral', 'Unknown'),
            'labels': {label: self.labels[idx][i].item() 
                      for i, label in enumerate(self.LABEL_COLUMNS)}
        }
        return info 