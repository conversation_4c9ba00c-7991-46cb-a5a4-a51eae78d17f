"""MedScan AI Database Package

Provides database connectivity, ORM models, and repository patterns
for secure medical data storage with HIPAA/GDPR compliance.
Includes SQLCipher encryption support for data-at-rest protection.
"""

from .engine import DatabaseEngine, create_database_engine, get_session, init_database
from .models import Analysis, AuditLog, Base, Image, Patient, Report, Study
from .repositories import (
    AnalysisRepository,
    AuditRepository,
    BaseRepository,
    ImageRepository,
    PatientRepository,
    ReportRepository,
    StudyRepository,
)

__all__ = [
    # Models
    "Base",
    "Patient",
    "Study",
    "Image",
    "Analysis",
    "Report",
    "AuditLog",
    # Engine and session management
    "DatabaseEngine",
    "create_database_engine",
    "get_session",
    "init_database",
    # Repositories
    "BaseRepository",
    "PatientRepository",
    "StudyRepository",
    "ImageRepository",
    "AnalysisRepository",
    "ReportRepository",
    "AuditRepository",
]
