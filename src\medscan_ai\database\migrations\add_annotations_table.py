"""Add annotations table for manual medical image annotations.

Migration: add_annotations_table
Created: 2025-01-25
Description: Creates the annotations table for storing manual annotations 
             on medical images with coordinate data and medical metadata.
"""

from sqlalchemy import text
from sqlalchemy.orm import Session


def upgrade(session: Session) -> None:
    """Apply the migration - create annotations table."""
    
    # Create annotations table
    session.execute(text("""
        CREATE TABLE IF NOT EXISTS annotations (
            id VARCHAR(36) PRIMARY KEY,
            study_id INTEGER NOT NULL,
            image_id INTEGER,
            annotation_type VARCHAR(50) NOT NULL,
            coordinates JSON NOT NULL,
            title VARCHAR(255),
            description TEXT,
            color VARCHAR(7) DEFAULT '#FF0000',
            line_width INTEGER DEFAULT 2,
            clinical_significance VARCHAR(50),
            urgency_level VARCHAR(20) DEFAULT 'normal',
            measurements JSON,
            creator_user_id VARCHAR(36) NOT NULL,
            version INTEGER DEFAULT 1,
            locked_by <PERSON><PERSON><PERSON><PERSON>(36),
            locked_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            
            FOREIGN KEY (study_id) REFERENCES studies(id),
            FOREIGN KEY (image_id) REFERENCES images(id),
            FOREIGN KEY (creator_user_id) REFERENCES users(id),
            FOREIGN KEY (locked_by) REFERENCES users(id)
        )
    """))
    
    # Create indices for performance
    session.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_annotations_study_id 
        ON annotations(study_id)
    """))
    
    session.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_annotations_image_id 
        ON annotations(image_id)
    """))
    
    session.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_annotations_type 
        ON annotations(annotation_type)
    """))
    
    session.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_annotations_creator 
        ON annotations(creator_user_id)
    """))
    
    session.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_annotations_active 
        ON annotations(is_active)
    """))
    
    session.execute(text("""
        CREATE INDEX IF NOT EXISTS idx_annotations_created_at 
        ON annotations(created_at)
    """))
    
    session.commit()
    print("✅ Created annotations table with indices")


def downgrade(session: Session) -> None:
    """Rollback the migration - drop annotations table."""
    
    # Drop indices first
    session.execute(text("DROP INDEX IF EXISTS idx_annotations_created_at"))
    session.execute(text("DROP INDEX IF EXISTS idx_annotations_active"))  
    session.execute(text("DROP INDEX IF EXISTS idx_annotations_creator"))
    session.execute(text("DROP INDEX IF EXISTS idx_annotations_type"))
    session.execute(text("DROP INDEX IF EXISTS idx_annotations_image_id"))
    session.execute(text("DROP INDEX IF EXISTS idx_annotations_study_id"))
    
    # Drop table
    session.execute(text("DROP TABLE IF EXISTS annotations"))
    
    session.commit()
    print("✅ Dropped annotations table and indices")


if __name__ == "__main__":
    """Run migration directly for testing."""
    from ...engine import get_session
    
    print("Running annotations table migration...")
    
    with get_session() as session:
        try:
            upgrade(session)
            print("Migration completed successfully!")
        except Exception as e:
            print(f"Migration failed: {e}")
            session.rollback()
            raise 