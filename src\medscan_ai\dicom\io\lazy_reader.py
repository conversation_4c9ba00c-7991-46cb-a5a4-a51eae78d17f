"""
Lazy Loading DICOM Reader with Streaming Support

This module provides lazy loading and streaming capabilities for DICOM files
to reduce memory footprint and improve performance for large medical images.

Key Features:
- Memory-mapped file access
- On-demand pixel data loading
- Streaming for large multi-frame series
- Chunked processing for memory efficiency

Author: MedScan AI Team
Date: 2025-01-27
Performance Target: Reduce memory usage by 70% for large DICOM files
"""

import os
import mmap
from pathlib import Path
from typing import Op<PERSON>, Dict, Any, Iterator, Tuple, Union
import numpy as np
import pydicom
from pydicom import Dataset
from pydicom.filebase import DicomBytesIO
import logging

from ..exceptions import (
    DicomFileNotFoundError,
    DicomFileReadError,
    DicomPixelDataError,
    DicomFormatError
)

logger = logging.getLogger(__name__)


class MemoryMappedDicomFile:
    """
    Memory-mapped DICOM file handler for efficient large file access.
    
    Uses memory mapping to avoid loading entire file into RAM while
    providing efficient random access to any part of the file.
    """
    
    def __init__(self, file_path: Union[str, Path]):
        """
        Initialize memory-mapped DICOM file.
        
        Args:
            file_path: Path to DICOM file
        """
        self.file_path = Path(file_path)
        self._file_handle = None
        self._mmap = None
        self._dataset = None
        self._metadata_loaded = False
        
        if not self.file_path.exists():
            raise DicomFileNotFoundError(f"DICOM file not found: {file_path}")
    
    def __enter__(self):
        """Context manager entry."""
        self.open()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
    
    def open(self):
        """Open the file and create memory mapping."""
        try:
            self._file_handle = open(self.file_path, 'rb')
            self._mmap = mmap.mmap(self._file_handle.fileno(), 0, access=mmap.ACCESS_READ)
            logger.debug(f"Memory-mapped DICOM file: {self.file_path} ({self.get_file_size()} bytes)")
        except Exception as e:
            raise DicomFileReadError(f"Failed to memory-map DICOM file: {e}")
    
    def close(self):
        """Close memory mapping and file handle."""
        if self._mmap:
            self._mmap.close()
            self._mmap = None
        if self._file_handle:
            self._file_handle.close()
            self._file_handle = None
    
    def get_file_size(self) -> int:
        """Get file size in bytes."""
        return self.file_path.stat().st_size
    
    def read_metadata_only(self) -> Dataset:
        """
        Read only DICOM metadata without pixel data.
        
        Returns:
            DICOM dataset with metadata only
        """
        if self._dataset is not None and self._metadata_loaded:
            return self._dataset
        
        if not self._mmap:
            raise DicomFileReadError("File not open for reading")
        
        try:
            # Create BytesIO from memory map for pydicom
            bytes_io = DicomBytesIO(self._mmap)
            
            # Read dataset without pixel data to save memory
            dataset = pydicom.dcmread(
                bytes_io,
                stop_before_pixels=True,  # Critical: don't load pixel data
                force=True
            )
            
            self._dataset = dataset
            self._metadata_loaded = True
            
            logger.debug(f"Loaded DICOM metadata: {dataset.get('SOPInstanceUID', 'Unknown')}")
            return dataset
            
        except Exception as e:
            raise DicomFileReadError(f"Failed to read DICOM metadata: {e}")
    
    def get_pixel_data_info(self) -> Dict[str, Any]:
        """
        Get pixel data information without loading the actual data.
        
        Returns:
            Dictionary with pixel data dimensions and properties
        """
        dataset = self.read_metadata_only()
        
        try:
            info = {
                'rows': getattr(dataset, 'Rows', None),
                'columns': getattr(dataset, 'Columns', None),
                'samples_per_pixel': getattr(dataset, 'SamplesPerPixel', 1),
                'bits_allocated': getattr(dataset, 'BitsAllocated', None),
                'bits_stored': getattr(dataset, 'BitsStored', None),
                'pixel_representation': getattr(dataset, 'PixelRepresentation', 0),
                'photometric_interpretation': getattr(dataset, 'PhotometricInterpretation', None),
                'number_of_frames': getattr(dataset, 'NumberOfFrames', 1),
                'planar_configuration': getattr(dataset, 'PlanarConfiguration', 0),
            }
            
            # Calculate expected pixel data size
            if info['rows'] and info['columns'] and info['bits_allocated']:
                bytes_per_pixel = info['bits_allocated'] // 8
                pixels_per_frame = info['rows'] * info['columns'] * info['samples_per_pixel']
                total_frames = int(info['number_of_frames'])
                info['expected_pixel_bytes'] = pixels_per_frame * bytes_per_pixel * total_frames
                info['pixels_per_frame'] = pixels_per_frame
                info['bytes_per_pixel'] = bytes_per_pixel
            
            return info
            
        except Exception as e:
            raise DicomPixelDataError(f"Failed to analyze pixel data info: {e}")


class LazyDicomReader:
    """
    Lazy loading DICOM reader with streaming capabilities.
    
    Features:
    - Deferred loading of pixel data
    - Memory-efficient chunked processing
    - Streaming support for large multi-frame datasets
    - Configurable memory limits
    """
    
    def __init__(self, 
                 max_memory_mb: int = 512,
                 chunk_size_mb: int = 64,
                 enable_caching: bool = True):
        """
        Initialize lazy DICOM reader.
        
        Args:
            max_memory_mb: Maximum memory to use for pixel data (MB)
            chunk_size_mb: Size of processing chunks (MB)
            enable_caching: Whether to cache loaded data
        """
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.chunk_size_bytes = chunk_size_mb * 1024 * 1024
        self.enable_caching = enable_caching
        
        # Cache for loaded data
        self._metadata_cache: Dict[str, Dataset] = {}
        self._pixel_cache: Dict[str, np.ndarray] = {}
        
        logger.info(f"LazyDicomReader initialized: max_memory={max_memory_mb}MB, "
                   f"chunk_size={chunk_size_mb}MB, caching={enable_caching}")
    
    def read_metadata(self, file_path: Union[str, Path]) -> Dataset:
        """
        Read DICOM metadata only (no pixel data).
        
        Args:
            file_path: Path to DICOM file
            
        Returns:
            DICOM dataset with metadata
        """
        file_path = Path(file_path)
        cache_key = str(file_path)
        
        # Check cache first
        if self.enable_caching and cache_key in self._metadata_cache:
            logger.debug(f"Using cached metadata for: {file_path.name}")
            return self._metadata_cache[cache_key]
        
        # Load metadata using memory mapping
        with MemoryMappedDicomFile(file_path) as mmap_file:
            dataset = mmap_file.read_metadata_only()
            
            # Cache metadata
            if self.enable_caching:
                self._metadata_cache[cache_key] = dataset
            
            return dataset
    
    def get_pixel_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get pixel data information without loading pixels.
        
        Args:
            file_path: Path to DICOM file
            
        Returns:
            Pixel data information dictionary
        """
        with MemoryMappedDicomFile(file_path) as mmap_file:
            return mmap_file.get_pixel_data_info()
    
    def load_pixel_data_chunked(self, 
                               file_path: Union[str, Path],
                               frame_range: Optional[Tuple[int, int]] = None) -> Iterator[np.ndarray]:
        """
        Load pixel data in chunks for memory-efficient processing.
        
        Args:
            file_path: Path to DICOM file
            frame_range: Optional (start_frame, end_frame) tuple
            
        Yields:
            Numpy arrays containing pixel data chunks
        """
        file_path = Path(file_path)
        
        # Get pixel info to plan chunking
        pixel_info = self.get_pixel_info(file_path)
        total_frames = int(pixel_info.get('number_of_frames', 1))
        
        # Determine frame range
        if frame_range is None:
            start_frame, end_frame = 0, total_frames
        else:
            start_frame, end_frame = frame_range
            start_frame = max(0, start_frame)
            end_frame = min(total_frames, end_frame)
        
        # Calculate optimal chunk size based on memory limits
        bytes_per_frame = pixel_info.get('expected_pixel_bytes', 0) // total_frames
        max_frames_per_chunk = max(1, self.chunk_size_bytes // bytes_per_frame)
        
        logger.info(f"Chunked loading: {end_frame - start_frame} frames, "
                   f"{max_frames_per_chunk} frames per chunk")
        
        # Load and yield chunks
        for chunk_start in range(start_frame, end_frame, max_frames_per_chunk):
            chunk_end = min(chunk_start + max_frames_per_chunk, end_frame)
            
            try:
                # Load specific frame range
                chunk_data = self._load_frame_range(file_path, chunk_start, chunk_end)
                
                logger.debug(f"Loaded chunk: frames {chunk_start}-{chunk_end-1}, "
                           f"shape={chunk_data.shape}")
                
                yield chunk_data
                
            except Exception as e:
                logger.error(f"Failed to load chunk {chunk_start}-{chunk_end}: {e}")
                raise DicomPixelDataError(f"Chunk loading failed: {e}")
    
    def _load_frame_range(self, 
                         file_path: Path, 
                         start_frame: int, 
                         end_frame: int) -> np.ndarray:
        """
        Load specific frame range from DICOM file.
        
        Args:
            file_path: Path to DICOM file
            start_frame: Starting frame index
            end_frame: Ending frame index (exclusive)
            
        Returns:
            Numpy array with pixel data for specified frames
        """
        try:
            # Read full dataset for pixel access
            # Note: This is where we actually load pixel data
            dataset = pydicom.dcmread(file_path, force=True)
            
            if not hasattr(dataset, 'pixel_array'):
                raise DicomPixelDataError("No pixel data found in DICOM file")
            
            pixel_array = dataset.pixel_array
            
            # Handle single frame vs multi-frame
            if len(pixel_array.shape) == 2:
                # Single frame
                if start_frame == 0 and end_frame == 1:
                    return pixel_array
                else:
                    raise DicomPixelDataError("Frame range invalid for single-frame DICOM")
            
            elif len(pixel_array.shape) == 3:
                # Multi-frame: extract specified range
                return pixel_array[start_frame:end_frame]
            
            else:
                raise DicomPixelDataError(f"Unsupported pixel array shape: {pixel_array.shape}")
                
        except Exception as e:
            raise DicomPixelDataError(f"Failed to load frame range {start_frame}-{end_frame}: {e}")
    
    def load_pixel_data_lazy(self, 
                            file_path: Union[str, Path],
                            max_frames: Optional[int] = None) -> np.ndarray:
        """
        Load pixel data with lazy loading and memory management.
        
        Args:
            file_path: Path to DICOM file
            max_frames: Maximum number of frames to load
            
        Returns:
            Numpy array with pixel data
        """
        file_path = Path(file_path)
        cache_key = str(file_path)
        
        # Check cache first
        if self.enable_caching and cache_key in self._pixel_cache:
            logger.debug(f"Using cached pixel data for: {file_path.name}")
            return self._pixel_cache[cache_key]
        
        # Check memory requirements
        pixel_info = self.get_pixel_info(file_path)
        expected_bytes = pixel_info.get('expected_pixel_bytes', 0)
        
        if expected_bytes > self.max_memory_bytes:
            raise DicomPixelDataError(
                f"DICOM file too large for memory limit: "
                f"{expected_bytes / 1024 / 1024:.1f}MB > {self.max_memory_bytes / 1024 / 1024:.1f}MB. "
                f"Use chunked loading instead."
            )
        
        # Determine frame limit
        total_frames = int(pixel_info.get('number_of_frames', 1))
        if max_frames is not None:
            frames_to_load = min(max_frames, total_frames)
        else:
            frames_to_load = total_frames
        
        # Load pixel data
        if frames_to_load == total_frames:
            # Load all frames
            pixel_data = self._load_frame_range(file_path, 0, total_frames)
        else:
            # Load limited frames
            pixel_data = self._load_frame_range(file_path, 0, frames_to_load)
        
        # Cache pixel data if enabled and within limits
        if self.enable_caching and expected_bytes <= self.chunk_size_bytes:
            self._pixel_cache[cache_key] = pixel_data
        
        logger.info(f"Loaded pixel data: {pixel_data.shape}, "
                   f"{pixel_data.nbytes / 1024 / 1024:.1f}MB")
        
        return pixel_data
    
    def clear_cache(self):
        """Clear all cached data to free memory."""
        self._metadata_cache.clear()
        self._pixel_cache.clear()
        logger.info("Cleared DICOM cache")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache usage information
        """
        metadata_count = len(self._metadata_cache)
        pixel_count = len(self._pixel_cache)
        
        # Calculate memory usage
        pixel_memory = sum(
            arr.nbytes for arr in self._pixel_cache.values()
        )
        
        return {
            'metadata_cached': metadata_count,
            'pixel_data_cached': pixel_count,
            'pixel_memory_mb': pixel_memory / 1024 / 1024,
            'cache_enabled': self.enable_caching
        }
    
    def __repr__(self) -> str:
        """String representation of LazyDicomReader."""
        stats = self.get_cache_stats()
        return (f"LazyDicomReader(max_memory={self.max_memory_bytes / 1024 / 1024:.0f}MB, "
                f"cached_files={stats['metadata_cached']}, "
                f"pixel_cache={stats['pixel_memory_mb']:.1f}MB)")


# Convenience function for quick lazy reading
def read_dicom_lazy(file_path: Union[str, Path], 
                   metadata_only: bool = False,
                   max_memory_mb: int = 512) -> Union[Dataset, Tuple[Dataset, np.ndarray]]:
    """
    Convenience function for lazy DICOM reading.
    
    Args:
        file_path: Path to DICOM file
        metadata_only: If True, return only metadata
        max_memory_mb: Maximum memory to use for pixel data
        
    Returns:
        Dataset if metadata_only=True, otherwise (Dataset, pixel_array) tuple
    """
    reader = LazyDicomReader(max_memory_mb=max_memory_mb)
    
    metadata = reader.read_metadata(file_path)
    
    if metadata_only:
        return metadata
    
    pixel_data = reader.load_pixel_data_lazy(file_path)
    return metadata, pixel_data 