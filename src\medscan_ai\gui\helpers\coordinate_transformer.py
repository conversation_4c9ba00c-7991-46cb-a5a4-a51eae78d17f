"""Coordinate transformation utilities for medical image annotations.

Provides conversion between QGraphicsView scene coordinates and DICOM pixel coordinates
to ensure annotations maintain accuracy across zoom, pan, and different display modes.
"""

from typing import List, Tuple, Dict, Any, Optional
import math
from dataclasses import dataclass
from PySide6.QtCore import QRectF, QPointF


@dataclass
class ImageMetadata:
    """Metadata for coordinate transformation calculations."""
    pixel_width: int
    pixel_height: int
    pixel_spacing_x: Optional[float] = None  # mm per pixel
    pixel_spacing_y: Optional[float] = None  # mm per pixel
    
    def get_aspect_ratio(self) -> float:
        """Get image aspect ratio."""
        return self.pixel_width / self.pixel_height if self.pixel_height > 0 else 1.0
    
    def has_pixel_spacing(self) -> bool:
        """Check if real-world pixel spacing is available."""
        return self.pixel_spacing_x is not None and self.pixel_spacing_y is not None


@dataclass
class ViewState:
    """Current view state for coordinate transformations."""
    scene_rect: QRectF
    view_rect: QRectF
    zoom_factor: float = 1.0
    
    def get_scale_factor_x(self) -> float:
        """Get X scale factor from scene to view."""
        return self.view_rect.width() / self.scene_rect.width() if self.scene_rect.width() > 0 else 1.0
    
    def get_scale_factor_y(self) -> float:
        """Get Y scale factor from scene to view."""
        return self.view_rect.height() / self.scene_rect.height() if self.scene_rect.height() > 0 else 1.0


class CoordinateTransformer:
    """Transforms coordinates between scene and pixel coordinate systems."""
    
    def __init__(self, image_metadata: ImageMetadata):
        """Initialize transformer with image metadata."""
        self.image_metadata = image_metadata
        self._last_view_state: Optional[ViewState] = None
    
    def set_view_state(self, view_state: ViewState) -> None:
        """Update view state for transformations."""
        self._last_view_state = view_state
    
    def scene_to_pixel(
        self, 
        scene_x: float, 
        scene_y: float,
        view_state: Optional[ViewState] = None
    ) -> Tuple[float, float]:
        """Convert scene coordinates to pixel coordinates."""
        state = view_state or self._last_view_state
        if not state:
            return scene_x, scene_y
        
        # Calculate relative position in scene rect
        rel_x = (scene_x - state.scene_rect.x()) / state.scene_rect.width()
        rel_y = (scene_y - state.scene_rect.y()) / state.scene_rect.height()
        
        # Map to pixel coordinates
        pixel_x = rel_x * self.image_metadata.pixel_width
        pixel_y = rel_y * self.image_metadata.pixel_height
        
        return pixel_x, pixel_y
    
    def pixel_to_scene(
        self,
        pixel_x: float,
        pixel_y: float,
        view_state: Optional[ViewState] = None
    ) -> Tuple[float, float]:
        """Convert pixel coordinates to scene coordinates."""
        state = view_state or self._last_view_state
        if not state:
            return pixel_x, pixel_y
        
        # Calculate relative position in pixel space
        rel_x = pixel_x / self.image_metadata.pixel_width
        rel_y = pixel_y / self.image_metadata.pixel_height
        
        # Map to scene coordinates
        scene_x = state.scene_rect.x() + rel_x * state.scene_rect.width()
        scene_y = state.scene_rect.y() + rel_y * state.scene_rect.height()
        
        return scene_x, scene_y
    
    def transform_annotation_coordinates(
        self,
        coordinates: Dict[str, Any],
        from_pixel_to_scene: bool = True,
        view_state: Optional[ViewState] = None
    ) -> Dict[str, Any]:
        """Transform annotation coordinates between pixel and scene systems."""
        if not coordinates:
            return coordinates
        
        transformed = coordinates.copy()
        annotation_type = coordinates.get('type', '').upper()
        
        if annotation_type == 'RECTANGLE':
            if from_pixel_to_scene:
                scene_x, scene_y = self.pixel_to_scene(
                    coordinates['x'], coordinates['y'], view_state
                )
                scene_x2, scene_y2 = self.pixel_to_scene(
                    coordinates['x'] + coordinates['width'],
                    coordinates['y'] + coordinates['height'],
                    view_state
                )
                transformed.update({
                    'x': scene_x,
                    'y': scene_y,
                    'width': scene_x2 - scene_x,
                    'height': scene_y2 - scene_y
                })
            else:
                pixel_x, pixel_y = self.scene_to_pixel(
                    coordinates['x'], coordinates['y'], view_state
                )
                pixel_x2, pixel_y2 = self.scene_to_pixel(
                    coordinates['x'] + coordinates['width'],
                    coordinates['y'] + coordinates['height'],
                    view_state
                )
                transformed.update({
                    'x': pixel_x,
                    'y': pixel_y,
                    'width': pixel_x2 - pixel_x,
                    'height': pixel_y2 - pixel_y
                })
        
        elif annotation_type == 'FREEHAND':
            points = coordinates.get('points', [])
            if from_pixel_to_scene:
                transformed['points'] = [
                    self.pixel_to_scene(x, y, view_state) for x, y in points
                ]
            else:
                transformed['points'] = [
                    self.scene_to_pixel(x, y, view_state) for x, y in points
                ]
        
        elif annotation_type == 'POLYGON':
            vertices = coordinates.get('vertices', [])
            if from_pixel_to_scene:
                transformed['vertices'] = [
                    self.pixel_to_scene(x, y, view_state) for x, y in vertices
                ]
            else:
                transformed['vertices'] = [
                    self.scene_to_pixel(x, y, view_state) for x, y in vertices
                ]
        
        # Update coordinate system flag
        transformed['pixel_coords'] = not from_pixel_to_scene
        
        return transformed
    
    def calculate_real_world_distance(
        self,
        pixel_x1: float,
        pixel_y1: float,
        pixel_x2: float,
        pixel_y2: float
    ) -> Optional[float]:
        """Calculate real-world distance between two pixel points."""
        if not self.image_metadata.has_pixel_spacing():
            return None
        
        dx_mm = (pixel_x2 - pixel_x1) * self.image_metadata.pixel_spacing_x
        dy_mm = (pixel_y2 - pixel_y1) * self.image_metadata.pixel_spacing_y
        
        return math.sqrt(dx_mm ** 2 + dy_mm ** 2)
    
    @classmethod
    def create_from_dicom_image(
        cls,
        dicom_image: Any,
        pixel_width: int,
        pixel_height: int
    ) -> "CoordinateTransformer":
        """Create transformer from DICOM image metadata."""
        pixel_spacing_x = None
        pixel_spacing_y = None
        
        try:
            if hasattr(dicom_image, 'PixelSpacing'):
                spacing = dicom_image.PixelSpacing
                if len(spacing) >= 2:
                    pixel_spacing_y = float(spacing[0])  # Row spacing
                    pixel_spacing_x = float(spacing[1])  # Column spacing
        except (AttributeError, ValueError, IndexError):
            pass
        
        metadata = ImageMetadata(
            pixel_width=pixel_width,
            pixel_height=pixel_height,
            pixel_spacing_x=pixel_spacing_x,
            pixel_spacing_y=pixel_spacing_y
        )
        
        return cls(metadata) 