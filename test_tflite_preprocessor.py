#!/usr/bin/env python3
"""
Test script for TensorFlow Lite preprocessor functionality.
Tests the basic capabilities of the TFLitePreprocessor class.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from medscan_ai.ai.preprocessing import TFLitePreprocessor, AIPixelExtractor
from medscan_ai.ai.models.model_loader import Model<PERSON>oader

def test_tflite_preprocessor():
    """Test basic TFLite preprocessor functionality."""
    print("🧪 Testing TensorFlow Lite Preprocessor...")
    
    try:
        # Initialize components
        model_loader = ModelLoader()
        tflite_preprocessor = TFLitePreprocessor(model_loader)
        
        print("✅ Successfully imported and initialized TFLitePreprocessor")
        print(f"   Available presets: {tflite_preprocessor.get_available_presets()}")
        
        # Test preset functionality
        print("\n🔍 Testing preset functionality...")
        
        presets = tflite_preprocessor.get_available_presets()
        assert 'xray_anomaly_detection' in presets, "xray_anomaly_detection preset should be available"
        assert 'chest_xray_classification' in presets, "chest_xray_classification preset should be available"
        assert 'generic_medical_imaging' in presets, "generic_medical_imaging preset should be available"
        
        # Test getting preset config
        xray_config = tflite_preprocessor.get_preset_config('xray_anomaly_detection')
        assert xray_config is not None, "Should be able to get xray preset config"
        assert xray_config['target_size'] == (224, 224), "Xray preset should have correct target size"
        assert xray_config['channels'] == 1, "Xray preset should be grayscale"
        
        print("✅ Preset functionality tests passed")
        
        # Test adding custom preset
        print("\n📋 Testing custom preset functionality...")
        
        custom_preset = {
            'target_size': (256, 256),
            'channels': 1,
            'normalization': 'zero_one',
            'mean': [0.0],
            'std': [1.0],
            'data_type': 'float32'
        }
        
        tflite_preprocessor.add_preprocessing_preset('test_preset', custom_preset)
        
        # Verify custom preset was added
        updated_presets = tflite_preprocessor.get_available_presets()
        assert 'test_preset' in updated_presets, "Custom preset should be added"
        
        retrieved_config = tflite_preprocessor.get_preset_config('test_preset')
        assert retrieved_config['target_size'] == (256, 256), "Custom preset should have correct config"
        
        print("✅ Custom preset functionality tests passed")
        
        # Test model loader integration
        print("\n🔧 Testing model loader integration...")
        
        # List available models (should work even if no models exist)
        available_models = model_loader.list_available_models()
        print(f"   Available models: {available_models}")
        
        print("✅ Model loader integration tests passed")
        
        print("✅ TensorFlow Lite Preprocessor basic tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Check that all required modules are properly implemented")
        return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

def test_imports():
    """Test that all required imports work."""
    print("📦 Testing imports...")
    
    try:
        # Test core imports
        from medscan_ai.ai.preprocessing import TFLitePreprocessor
        print("✅ TFLitePreprocessor imported successfully")
        
        from medscan_ai.ai.preprocessing import AIPixelExtractor
        print("✅ AIPixelExtractor imported successfully")
        
        from medscan_ai.ai.models.model_loader import ModelLoader
        print("✅ ModelLoader imported successfully")
        
        # Test required external libraries
        import numpy as np
        print("✅ NumPy imported successfully")
        
        import tensorflow as tf
        print("✅ TensorFlow imported successfully")
        
        import pydicom
        print("✅ PyDICOM imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting TensorFlow Lite Preprocessor Tests\n")
    
    # Test imports first
    if not test_imports():
        print("\n❌ Import tests failed. Cannot proceed with functionality tests.")
        sys.exit(1)
    
    print()
    
    # Test basic functionality
    if test_tflite_preprocessor():
        print("\n🎉 All tests passed! TensorFlow Lite Preprocessor is ready for model integration.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed. Please check implementation.")
        sys.exit(1) 