#!/usr/bin/env python3
"""
MedScan AI Performance Profiler

Comprehensive profiling tool to identify performance bottlenecks in:
- DICOM file loading and processing
- AI model inference
- GUI image display operations
- Memory and I/O intensive operations

Usage:
    python performance_profiler.py --test-type [dicom|ai|gui|all]
    python performance_profiler.py --large-files --file-size 100MB
"""

import os
import sys
import time
import psutil
import cProfile
import pstats
import io
import tracemalloc
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import numpy as np
from memory_profiler import profile as memory_profile
import tempfile

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from medscan_ai.dicom.io.reader import DicomReader
from medscan_ai.dicom.processing.pixel_processor import PixelProcessor
from medscan_ai.ai.preprocessing.ai_pixel_extractor import AIPixelExtractor
from medscan_ai.ai.preprocessing.tflite_preprocessor import TFLitePreprocessor
from medscan_ai.ai.inference.inference_engine import InferenceEngine

class PerformanceProfiler:
    """Comprehensive performance profiler for MedScan AI components"""
    
    def __init__(self):
        self.results = {}
        self.baseline_metrics = {}
        self.test_files = []
        
    def create_test_dicom_files(self, sizes_mb: List[int]) -> List[str]:
        """Create test DICOM files of various sizes for profiling"""
        test_files = []
        
        for size_mb in sizes_mb:
            filename = f"test_dicom_{size_mb}mb.dcm"
            filepath = Path(tempfile.gettempdir()) / filename
            
            if not filepath.exists():
                print(f"Creating test DICOM file: {size_mb}MB...")
                # Create a realistic DICOM file with pixel data
                self._create_synthetic_dicom(filepath, size_mb)
            
            test_files.append(str(filepath))
            
        return test_files
    
    def _create_synthetic_dicom(self, filepath: Path, size_mb: int):
        """Create synthetic DICOM file with realistic medical imaging data"""
        import pydicom
        from pydicom.dataset import Dataset, FileDataset
        from pydicom.uid import ExplicitVRLittleEndian
        import pydicom.uid
        
        # Calculate image dimensions for target file size
        target_bytes = size_mb * 1024 * 1024
        # Assuming 16-bit grayscale: 2 bytes per pixel
        pixels_needed = target_bytes // 2
        # Square image for simplicity
        image_size = int(np.sqrt(pixels_needed))
        
        # Create dataset
        ds = Dataset()
        ds.PatientName = f"Test^Patient^{size_mb}MB"
        ds.PatientID = f"TEST{size_mb:03d}"
        ds.StudyDate = "20250127"
        ds.StudyTime = "120000"
        ds.Modality = "CR"  # Computed Radiography
        ds.StudyInstanceUID = pydicom.uid.generate_uid()
        ds.SeriesInstanceUID = pydicom.uid.generate_uid()
        ds.SOPInstanceUID = pydicom.uid.generate_uid()
        ds.SOPClassUID = pydicom.uid.ComputedRadiographyImageStorage
        
        # Image parameters
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        ds.Rows = image_size
        ds.Columns = image_size
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 0
        
        # Create realistic medical image data (chest X-ray simulation)
        pixel_array = self._generate_medical_image(image_size, image_size)
        ds.PixelData = pixel_array.tobytes()
        
        # Save as DICOM file
        file_meta = Dataset()
        file_meta.TransferSyntaxUID = ExplicitVRLittleEndian
        file_meta.MediaStorageSOPClassUID = ds.SOPClassUID
        file_meta.MediaStorageSOPInstanceUID = ds.SOPInstanceUID
        
        file_ds = FileDataset(str(filepath), ds, file_meta=file_meta, 
                             preamble=b"\0" * 128, is_implicit_VR=False, 
                             is_little_endian=True)
        
        file_ds.save_as(str(filepath))
        print(f"Created {filepath} - Size: {filepath.stat().st_size / 1024 / 1024:.1f}MB")
    
    def _generate_medical_image(self, height: int, width: int) -> np.ndarray:
        """Generate realistic medical image data"""
        # Create base chest X-ray pattern
        image = np.zeros((height, width), dtype=np.uint16)
        
        # Add anatomical structures (simplified chest X-ray)
        center_x, center_y = width // 2, height // 2
        
        # Lung fields (darker areas)
        for y in range(height):
            for x in range(width):
                dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                if dist_from_center < min(width, height) * 0.4:
                    image[y, x] = 20000  # Lung tissue
                else:
                    image[y, x] = 35000  # Ribs and other structures
        
        # Add noise for realism
        noise = np.random.normal(0, 500, (height, width)).astype(np.int16)
        image = np.clip(image.astype(np.int32) + noise, 0, 65535).astype(np.uint16)
        
        return image
    
    def profile_dicom_operations(self, file_path: str) -> Dict:
        """Profile DICOM file operations comprehensively"""
        print(f"\n🔍 Profiling DICOM operations for: {Path(file_path).name}")
        
        results = {
            'file_size_mb': Path(file_path).stat().st_size / 1024 / 1024,
            'operations': {}
        }
        
        # Profile DICOM reading
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            dicom_reader = DicomReader(file_path)
            dataset = dicom_reader.get_dataset()
            load_time = time.time() - start_time
            load_memory = psutil.Process().memory_info().rss / 1024 / 1024 - start_memory
            
            results['operations']['dicom_load'] = {
                'time_seconds': load_time,
                'memory_mb': load_memory,
                'success': True
            }
            
            # Profile pixel processing
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            pixel_processor = PixelProcessor(dataset)
            pixel_array = pixel_processor.get_pixel_array()
            
            processing_time = time.time() - start_time
            processing_memory = psutil.Process().memory_info().rss / 1024 / 1024 - start_memory
            
            results['operations']['pixel_processing'] = {
                'time_seconds': processing_time,
                'memory_mb': processing_memory,
                'pixel_array_shape': pixel_array.shape if pixel_array is not None else None,
                'success': True
            }
            
            # Profile AI preprocessing
            if pixel_array is not None:
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss / 1024 / 1024
                
                ai_extractor = AIPixelExtractor()
                ai_data = ai_extractor.extract_for_ai_inference(dataset)
                
                ai_time = time.time() - start_time
                ai_memory = psutil.Process().memory_info().rss / 1024 / 1024 - start_memory
                
                results['operations']['ai_preprocessing'] = {
                    'time_seconds': ai_time,
                    'memory_mb': ai_memory,
                    'output_shape': ai_data.pixel_array.shape if ai_data and ai_data.pixel_array is not None else None,
                    'success': True
                }
            
        except Exception as e:
            print(f"❌ Error in DICOM profiling: {e}")
            results['operations']['error'] = str(e)
        
        return results
    
    def profile_ai_inference(self, dicom_path: str) -> Dict:
        """Profile AI inference operations"""
        print(f"\n🧠 Profiling AI inference for: {Path(dicom_path).name}")
        
        results = {'operations': {}}
        
        try:
            # Initialize inference engine
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            inference_engine = InferenceEngine()
            
            init_time = time.time() - start_time
            init_memory = psutil.Process().memory_info().rss / 1024 / 1024 - start_memory
            
            results['operations']['engine_init'] = {
                'time_seconds': init_time,
                'memory_mb': init_memory,
                'success': True
            }
            
            # Profile model inference (using mock since we don't have real models)
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # This would normally run inference
            # result = inference_engine.run_inference_from_dicom(dicom_path)
            
            # Simulate inference time based on file size
            file_size_mb = Path(dicom_path).stat().st_size / 1024 / 1024
            simulated_inference_time = file_size_mb * 0.1  # 0.1 seconds per MB
            time.sleep(min(simulated_inference_time, 2.0))  # Cap at 2 seconds for testing
            
            inference_time = time.time() - start_time
            inference_memory = psutil.Process().memory_info().rss / 1024 / 1024 - start_memory
            
            results['operations']['model_inference'] = {
                'time_seconds': inference_time,
                'memory_mb': inference_memory,
                'simulated': True,
                'success': True
            }
            
        except Exception as e:
            print(f"❌ Error in AI inference profiling: {e}")
            results['operations']['error'] = str(e)
        
        return results
    
    def profile_memory_usage(self, operation_func, *args) -> Dict:
        """Profile memory usage of specific operations"""
        tracemalloc.start()
        
        initial_memory = psutil.Process().memory_info().rss
        peak_memory = initial_memory
        
        # Monitor memory during operation
        start_time = time.time()
        
        try:
            result = operation_func(*args)
            success = True
        except Exception as e:
            result = str(e)
            success = False
        
        end_time = time.time()
        final_memory = psutil.Process().memory_info().rss
        
        current, peak_traced = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        return {
            'execution_time': end_time - start_time,
            'initial_memory_mb': initial_memory / 1024 / 1024,
            'final_memory_mb': final_memory / 1024 / 1024,
            'memory_increase_mb': (final_memory - initial_memory) / 1024 / 1024,
            'peak_traced_mb': peak_traced / 1024 / 1024,
            'success': success,
            'result': result if success else f"Error: {result}"
        }
    
    def run_comprehensive_profiling(self, test_sizes: List[int] = [10, 50, 100, 200]) -> Dict:
        """Run comprehensive performance profiling"""
        print("🚀 Starting Comprehensive MedScan AI Performance Profiling")
        print("=" * 60)
        
        # Create test files
        test_files = self.create_test_dicom_files(test_sizes)
        
        all_results = {
            'system_info': self._get_system_info(),
            'test_files': [],
            'summary': {}
        }
        
        # Profile each test file
        for file_path in test_files:
            file_results = {
                'file_path': file_path,
                'file_size_mb': Path(file_path).stat().st_size / 1024 / 1024
            }
            
            # DICOM operations profiling
            file_results['dicom_profiling'] = self.profile_dicom_operations(file_path)
            
            # AI inference profiling
            file_results['ai_profiling'] = self.profile_ai_inference(file_path)
            
            all_results['test_files'].append(file_results)
        
        # Generate summary
        all_results['summary'] = self._generate_summary(all_results)
        
        return all_results
    
    def _get_system_info(self) -> Dict:
        """Get system information for profiling context"""
        return {
            'cpu_count': psutil.cpu_count(),
            'cpu_freq_mhz': psutil.cpu_freq().current if psutil.cpu_freq() else None,
            'memory_total_gb': psutil.virtual_memory().total / 1024 / 1024 / 1024,
            'memory_available_gb': psutil.virtual_memory().available / 1024 / 1024 / 1024,
            'python_version': sys.version,
            'platform': sys.platform
        }
    
    def _generate_summary(self, results: Dict) -> Dict:
        """Generate performance summary and bottleneck identification"""
        summary = {
            'bottlenecks_identified': [],
            'performance_targets': {},
            'recommendations': []
        }
        
        # Analyze results for bottlenecks
        for file_result in results['test_files']:
            file_size = file_result['file_size_mb']
            
            # Check DICOM loading performance
            dicom_ops = file_result.get('dicom_profiling', {}).get('operations', {})
            if 'dicom_load' in dicom_ops:
                load_time = dicom_ops['dicom_load']['time_seconds']
                load_rate = file_size / load_time if load_time > 0 else 0
                
                if load_rate < 20:  # Less than 20 MB/s
                    summary['bottlenecks_identified'].append({
                        'component': 'DICOM Loading',
                        'issue': f'Slow loading rate: {load_rate:.1f} MB/s',
                        'file_size_mb': file_size,
                        'time_seconds': load_time
                    })
            
            # Check memory usage
            if 'pixel_processing' in dicom_ops:
                memory_usage = dicom_ops['pixel_processing']['memory_mb']
                memory_ratio = memory_usage / file_size
                
                if memory_ratio > 5:  # More than 5x file size in memory
                    summary['bottlenecks_identified'].append({
                        'component': 'Memory Usage',
                        'issue': f'High memory usage: {memory_ratio:.1f}x file size',
                        'file_size_mb': file_size,
                        'memory_mb': memory_usage
                    })
        
        # Performance targets comparison
        summary['performance_targets'] = {
            'dicom_loading_target': '< 5 seconds for 100MB files',
            'ai_analysis_target': '< 30 seconds for standard X-ray',
            'memory_usage_target': '< 3x file size in memory',
            'ui_responsiveness_target': '< 1 second for UI updates'
        }
        
        # Generate recommendations
        if summary['bottlenecks_identified']:
            summary['recommendations'].extend([
                'Implement lazy loading for large DICOM files',
                'Use streaming pixel data access',
                'Implement multi-threading for background operations',
                'Optimize OpenCV operations with memory pooling',
                'Consider using memory-mapped file access'
            ])
        else:
            summary['recommendations'].append('Current performance meets baseline requirements')
        
        return summary
    
    def save_results(self, results: Dict, output_file: str = "performance_profile_results.json"):
        """Save profiling results to file"""
        import json
        
        output_path = Path(output_file)
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {output_path.absolute()}")
    
    def print_summary(self, results: Dict):
        """Print comprehensive summary of profiling results"""
        print("\n" + "=" * 60)
        print("🎯 PERFORMANCE PROFILING SUMMARY")
        print("=" * 60)
        
        summary = results.get('summary', {})
        
        # System info
        sys_info = results.get('system_info', {})
        print(f"\n💻 System: {sys_info.get('cpu_count', 'N/A')} CPU cores, "
              f"{sys_info.get('memory_total_gb', 'N/A'):.1f}GB RAM")
        
        # Bottlenecks
        bottlenecks = summary.get('bottlenecks_identified', [])
        if bottlenecks:
            print(f"\n⚠️  BOTTLENECKS IDENTIFIED ({len(bottlenecks)}):")
            for i, bottleneck in enumerate(bottlenecks, 1):
                print(f"  {i}. {bottleneck['component']}: {bottleneck['issue']}")
        else:
            print("\n✅ No major bottlenecks identified")
        
        # Performance targets
        targets = summary.get('performance_targets', {})
        print(f"\n🎯 PERFORMANCE TARGETS:")
        for target, description in targets.items():
            print(f"  • {description}")
        
        # Recommendations
        recommendations = summary.get('recommendations', [])
        if recommendations:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")


def main():
    """Main profiling execution"""
    parser = argparse.ArgumentParser(description='MedScan AI Performance Profiler')
    parser.add_argument('--test-sizes', nargs='+', type=int, default=[10, 50, 100],
                       help='Test file sizes in MB (default: 10 50 100)')
    parser.add_argument('--output', default='performance_profile_results.json',
                       help='Output file for results')
    parser.add_argument('--quick', action='store_true',
                       help='Quick profiling with smaller test files')
    
    args = parser.parse_args()
    
    if args.quick:
        test_sizes = [5, 25]
    else:
        test_sizes = args.test_sizes
    
    profiler = PerformanceProfiler()
    
    try:
        results = profiler.run_comprehensive_profiling(test_sizes)
        profiler.print_summary(results)
        profiler.save_results(results, args.output)
        
        print("\n🎉 Performance profiling completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Profiling failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 