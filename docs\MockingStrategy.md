# MedScan AI - Mocking Strategy for External Dependencies

## Overview
This document defines the comprehensive mocking strategy for MedScan AI's medical imaging application. The strategy ensures test isolation, reproducibility, and compliance with medical software testing standards.

## Mocking Principles

### 1. Medical Data Protection
- **No Real PHI**: Never use real patient data in tests
- **Anonymized Fixtures**: All medical data must be anonymized
- **HIPAA Compliance**: Mock data generation follows healthcare data standards

### 2. Test Isolation
- **Pure Unit Tests**: Mock all external dependencies
- **Integration Tests**: Mock only external services (DB, APIs, filesystem)
- **Functional Tests**: Use test doubles for expensive operations

### 3. Deterministic Testing
- **Consistent Results**: Mocks return predictable data
- **Time Control**: Mock time-dependent operations
- **Random Control**: Seed or mock random operations

## External Dependencies Categories

### Category 1: Database Dependencies
**Components**: SQLAlchemy sessions, repositories, models
**Mock Level**: High - Critical for test isolation

```python
# Primary Strategy: pytest-mock with SQLAlchemy session fixtures
@pytest.fixture
def mock_db_session():
    """Mock database session for unit tests."""
    session = Mock(spec=Session)
    session.query.return_value = Mock()
    session.commit.return_value = None
    session.rollback.return_value = None
    return session

# Repository Pattern Mocking
@pytest.fixture
def mock_patient_repository():
    """Mock patient repository with medical data."""
    repo = Mock(spec=PatientRepository)
    repo.get_by_id.return_value = create_mock_patient()
    repo.create.return_value = create_mock_patient()
    return repo
```

### Category 2: AI/ML Dependencies  
**Components**: PyTorch models, TensorFlow Lite, inference engines
**Mock Level**: High - Expensive operations, deterministic results needed

```python
# AI Model Mocking Strategy
@pytest.fixture
def mock_ai_model():
    """Mock AI model with predictable inference results."""
    model = Mock()
    model.predict.return_value = {
        'confidence': 0.95,
        'anomaly_detected': True,
        'bounding_boxes': [{'x': 100, 'y': 150, 'width': 200, 'height': 180}]
    }
    return model

# Inference Engine Mocking
@pytest.fixture  
def mock_inference_engine():
    """Mock inference engine with medical predictions."""
    engine = Mock(spec=InferenceEngine)
    engine.run_inference.return_value = create_mock_inference_result()
    return engine
```

### Category 3: DICOM File Dependencies
**Components**: File I/O, pixel data processing, metadata extraction
**Mock Level**: Medium - Use real sample files for critical tests, mock for unit tests

```python
# DICOM File Mocking Strategy
@pytest.fixture
def mock_dicom_file():
    """Mock DICOM file with medical metadata."""
    dicom = Mock()
    dicom.PatientName = "Test^Patient^Anonymized"
    dicom.PatientID = "TEST001"
    dicom.Modality = "CT"
    dicom.pixel_array = np.random.randint(0, 255, (512, 512), dtype=np.uint16)
    return dicom

@pytest.fixture
def sample_dicom_path(tmp_path):
    """Provide path to sample DICOM file for integration tests."""
    # Use actual sample DICOM files for realistic testing
    return tmp_path / "sample.dcm"
```

### Category 4: Security Dependencies
**Components**: Encryption services, audit logging, key management
**Mock Level**: High - Security operations must be testable and deterministic

```python
# Security Mocking Strategy
@pytest.fixture
def mock_encryption_service():
    """Mock encryption service for security tests."""
    service = Mock(spec=DataEncryptionService)
    service.encrypt.return_value = b"mock_encrypted_data"
    service.decrypt.return_value = b"mock_decrypted_data"
    service.verify_integrity.return_value = True
    return service

@pytest.fixture
def mock_audit_service():
    """Mock audit service for compliance tests."""
    service = Mock(spec=AuditService)
    service.log_event.return_value = True
    service.get_audit_trail.return_value = []
    return service
```

### Category 5: External API Dependencies
**Components**: PACS, HIS, cloud services, network requests
**Mock Level**: High - External services unreliable in test environments

```python
# External API Mocking Strategy
@pytest.fixture
def mock_pacs_client():
    """Mock PACS client for medical image retrieval."""
    client = Mock()
    client.query_studies.return_value = [create_mock_study()]
    client.retrieve_images.return_value = [create_mock_dicom()]
    return client

# HTTP Requests Mocking
@pytest.fixture
def mock_http_requests(monkeypatch):
    """Mock HTTP requests using requests-mock."""
    import requests_mock
    with requests_mock.Mocker() as m:
        m.get('http://api.example.com/health', json={'status': 'ok'})
        yield m
```

### Category 6: File System Dependencies
**Components**: File operations, temporary files, configuration files
**Mock Level**: Medium - Use temporary directories, mock expensive operations

```python
# File System Mocking Strategy
@pytest.fixture
def temp_workspace(tmp_path):
    """Provide temporary workspace for file operations."""
    workspace = tmp_path / "test_workspace"
    workspace.mkdir()
    return workspace

@pytest.fixture
def mock_file_operations(monkeypatch):
    """Mock expensive file operations."""
    mock_open = Mock()
    mock_open.return_value.__enter__.return_value.read.return_value = "test content"
    monkeypatch.setattr("builtins.open", mock_open)
    return mock_open
```

### Category 7: GUI Dependencies
**Components**: PySide6 widgets, user interactions, display operations
**Mock Level**: Medium - Mock user interactions, use QTest for widget testing

```python
# GUI Mocking Strategy
@pytest.fixture
def mock_qt_application(qtbot):
    """Provide QApplication for GUI tests."""
    from PySide6.QtWidgets import QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    return app

@pytest.fixture
def mock_image_viewer():
    """Mock image viewer for GUI tests."""
    viewer = Mock(spec=InteractiveImageViewer)
    viewer.load_image.return_value = True
    viewer.set_zoom.return_value = None
    return viewer
```

## Mocking Implementation Patterns

### 1. Pytest-Mock Integration
```python
# Use pytest-mock for simple mocking
def test_patient_creation(mocker):
    mock_db = mocker.patch('medscan_ai.database.get_session')
    mock_db.return_value = mocker.Mock()
    
    # Test implementation
    result = create_patient(patient_data)
    assert result is not None
```

### 2. Fixture-Based Mocking
```python
# Use fixtures for complex, reusable mocks
def test_ai_inference(mock_inference_engine, mock_dicom_file):
    result = mock_inference_engine.run_inference(mock_dicom_file)
    assert result['confidence'] > 0.8
```

### 3. Context Manager Mocking
```python
# Use context managers for temporary mocking
def test_file_encryption():
    with patch('medscan_ai.security.encryption.encrypt_file') as mock_encrypt:
        mock_encrypt.return_value = True
        result = process_secure_file("test.dcm")
        assert result is True
```

### 4. Dependency Injection Patterns
```python
# Design code to accept dependencies for easier testing
class MedicalImageProcessor:
    def __init__(self, ai_engine=None, encryption_service=None):
        self.ai_engine = ai_engine or InferenceEngine()
        self.encryption_service = encryption_service or DataEncryptionService()
    
    def process(self, dicom_file):
        # Implementation uses injected dependencies
        pass

# Test with injected mocks
def test_image_processing():
    mock_ai = Mock()
    mock_encryption = Mock()
    processor = MedicalImageProcessor(mock_ai, mock_encryption)
    # Test with controlled dependencies
```

## Mock Data Generation

### Medical Data Factories
```python
# Factory pattern for generating consistent test data
class MockMedicalDataFactory:
    @staticmethod
    def create_patient(patient_id="TEST001"):
        return {
            'patient_id': patient_id,
            'name': 'Test^Patient^Anonymized',
            'age': 45,
            'gender': 'M',
            'study_date': '2024-01-15'
        }
    
    @staticmethod
    def create_dicom_metadata():
        return {
            'StudyInstanceUID': '1.2.3.4.5.6.7.8.9',
            'SeriesInstanceUID': '1.2.3.4.5.6.7.8.10',
            'SOPInstanceUID': '1.2.3.4.5.6.7.8.11',
            'Modality': 'CT'
        }
    
    @staticmethod
    def create_ai_inference_result():
        return {
            'confidence': 0.95,
            'anomaly_detected': True,
            'anomaly_type': 'pneumonia',
            'bounding_boxes': [
                {'x': 100, 'y': 150, 'width': 200, 'height': 180}
            ],
            'processing_time': 0.5
        }
```

## Performance Considerations

### 1. Mock Performance
- **Lightweight**: Mocks should be faster than real operations
- **Memory Efficient**: Avoid large mock data structures
- **Cached**: Reuse expensive mock setups

### 2. Test Speed Optimization
```python
# Use pytest-benchmark for performance testing
def test_ai_inference_performance(benchmark, mock_ai_engine):
    result = benchmark(mock_ai_engine.run_inference, mock_dicom_data)
    assert result is not None
```

## Error Simulation

### 1. Exception Mocking
```python
# Mock exception scenarios for robust testing
def test_database_connection_failure(mock_db_session):
    mock_db_session.query.side_effect = ConnectionError("Database unavailable")
    
    with pytest.raises(ConnectionError):
        get_patient_data("TEST001")
```

### 2. Network Failure Simulation
```python
# Mock network failures for resilience testing
@pytest.fixture
def mock_network_failure():
    with patch('requests.get') as mock_get:
        mock_get.side_effect = requests.ConnectionError("Network error")
        yield mock_get
```

## Medical Compliance Testing

### 1. HIPAA Compliance Mocks
```python
# Mock audit trails for compliance verification
def test_hipaa_audit_trail(mock_audit_service):
    # Perform operation that should be audited
    access_patient_data("TEST001")
    
    # Verify audit logging
    mock_audit_service.log_event.assert_called_with(
        event_type="PATIENT_DATA_ACCESS",
        user_id="test_user",
        patient_id="TEST001"
    )
```

### 2. Data Integrity Mocks
```python
# Mock data integrity verification
def test_data_integrity(mock_encryption_service):
    mock_encryption_service.verify_integrity.return_value = True
    
    result = verify_medical_data_integrity("test_data")
    assert result is True
```

## Best Practices

### 1. Mock Naming Conventions
- Prefix with `mock_` for clarity
- Use descriptive names: `mock_patient_repository` not `mock_repo`
- Group related mocks: `mock_ai_*`, `mock_db_*`

### 2. Mock Scope Management
- **Unit Tests**: Mock everything external
- **Integration Tests**: Mock only external services
- **End-to-End Tests**: Minimize mocking, use test databases

### 3. Mock Validation
```python
# Validate mock usage to catch test errors
def test_mock_usage_validation(mock_service):
    # Perform operation
    result = some_operation()
    
    # Validate mock was called correctly
    mock_service.method.assert_called_once_with(expected_params)
    assert result.status == "success"
```

### 4. Mock Documentation
- Document mock behavior in test docstrings
- Explain complex mock setups
- Maintain mock data consistency across tests

## Integration with CI/CD

### 1. Mock Environment Variables
```python
# Use environment variables to control mocking
@pytest.fixture(autouse=True)
def setup_test_environment():
    os.environ['MEDSCAN_TEST_MODE'] = 'true'
    os.environ['MEDSCAN_MOCK_EXTERNAL_SERVICES'] = 'true'
```

### 2. Mock Service Health Checks
```python
# Mock service health checks for CI/CD
@pytest.fixture
def mock_service_health():
    with patch('medscan_ai.services.check_service_health') as mock_check:
        mock_check.return_value = {'status': 'healthy', 'services': ['db', 'ai', 'audit']}
        yield mock_check
```

## Conclusion

This mocking strategy provides a comprehensive approach to testing MedScan AI's medical imaging application while ensuring:

- **Medical Data Safety**: No real PHI exposure
- **Test Reliability**: Deterministic, reproducible tests
- **Performance**: Fast test execution
- **Compliance**: HIPAA/GDPR testing support
- **Maintainability**: Clear, documented mocking patterns

The strategy balances thorough testing with practical implementation, supporting both development velocity and medical-grade quality assurance. 