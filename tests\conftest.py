"""
Pytest configuration and fixtures for MedScan AI test suite.

This module provides shared fixtures and configuration for medical-grade testing.
"""

import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock
from typing import Generator, Dict, Any, Optional
from contextlib import contextmanager

# Database imports for integration testing
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.medscan_ai.database.models.base import Base


# Test configuration
def pytest_configure(config):
    """Configure pytest for medical-grade testing."""
    # Set test environment variables
    os.environ['MEDSCAN_TEST_MODE'] = 'true'
    os.environ['MEDSCAN_LOG_LEVEL'] = 'DEBUG'
    os.environ['MEDSCAN_DISABLE_ENCRYPTION'] = 'false'  # Keep encryption enabled in tests


@pytest.fixture(scope="session")
def test_data_dir() -> Path:
    """Provide path to test data directory."""
    return Path(__file__).parent / "fixtures" / "test_data"


@pytest.fixture(scope="session")
def temp_dir() -> Generator[Path, None, None]:
    """Provide temporary directory for test files."""
    with tempfile.TemporaryDirectory(prefix="medscan_test_") as temp_path:
        yield Path(temp_path)


@pytest.fixture
def mock_dicom_file() -> Mock:
    """Mock DICOM file for testing."""
    mock_dicom = Mock()
    mock_dicom.PatientName = "Test^Patient"
    mock_dicom.PatientID = "TEST001"
    mock_dicom.StudyInstanceUID = "1.2.3.4.5.6.7.8.9"
    mock_dicom.SeriesInstanceUID = "1.2.3.4.5.6.7.8.10"
    mock_dicom.SOPInstanceUID = "1.2.3.4.5.6.7.8.11"
    mock_dicom.Modality = "CT"
    mock_dicom.pixel_array = Mock()
    return mock_dicom


@pytest.fixture
def mock_ai_inference_result() -> Dict[str, Any]:
    """Mock AI inference result for testing."""
    return {
        "confidence": 0.95,
        "anomaly_detected": True,
        "anomaly_type": "pneumonia",
        "bounding_boxes": [
            {"x": 100, "y": 150, "width": 200, "height": 180, "confidence": 0.92}
        ],
        "model_version": "test_v1.0",
        "processing_time": 0.5
    }


@pytest.fixture
def mock_patient_data() -> Dict[str, Any]:  # type: ignore
    """Mock patient data for testing (anonymized)."""
    return {
        "anonymized_id": "TEST001",
        "age": 45,
        "gender": "M",
        "study_date": "2024-01-15",
        "modality": "CT",
        "body_part": "CHEST"
    }


@pytest.fixture
def mock_database_session() -> Mock:
    """Mock database session for testing."""
    session = Mock()
    session.query.return_value = session
    session.filter.return_value = session
    session.first.return_value = None
    session.all.return_value = []
    session.commit.return_value = None
    session.rollback.return_value = None
    return session


@pytest.fixture
def mock_encryption_service() -> Mock:
    """Mock encryption service for security testing."""
    encryption_service = Mock()
    encryption_service.encrypt.return_value = b"encrypted_data"
    encryption_service.decrypt.return_value = b"decrypted_data"
    encryption_service.verify_integrity.return_value = True
    return encryption_service


@pytest.fixture
def mock_audit_logger() -> Mock:
    """Mock audit logger for compliance testing."""
    audit_logger = Mock()
    audit_logger.log_event.return_value = True
    audit_logger.get_audit_trail.return_value = []
    return audit_logger


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Automatically set up test environment for each test."""
    # Store original environment
    original_env = dict(os.environ)
    
    # Set test-specific environment variables
    os.environ.update({
        'MEDSCAN_TEST_MODE': 'true',
        'MEDSCAN_DATABASE_URL': 'sqlite:///:memory:',
        'MEDSCAN_ENCRYPTION_KEY': 'test_key_32_bytes_for_testing_only',
        'MEDSCAN_LOG_LEVEL': 'DEBUG'
    })
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


# Medical-specific pytest markers
pytest_plugins = []

# Custom pytest markers for medical testing
def pytest_collection_modifyitems(config, items):
    """Automatically add markers based on test file locations and names."""
    for item in items:
        # Add markers based on file path
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "functional" in str(item.fspath):
            item.add_marker(pytest.mark.functional)
            
        # Add medical-specific markers based on test names
        if "dicom" in item.name.lower():
            item.add_marker(pytest.mark.dicom)
        if "ai" in item.name.lower() or "inference" in item.name.lower():
            item.add_marker(pytest.mark.ai_inference)
        if "security" in item.name.lower() or "encrypt" in item.name.lower():
            item.add_marker(pytest.mark.security)
        if "hipaa" in item.name.lower():
            item.add_marker(pytest.mark.hipaa)
        if "patient" in item.name.lower() or "medical_data" in item.name.lower():
            item.add_marker(pytest.mark.medical_data)


@pytest.fixture
def temp_db():
    """Provide temporary in-memory database for integration testing."""
    
    @contextmanager
    def _temp_db():
        # Create in-memory SQLite database
        engine = create_engine("sqlite:///:memory:", echo=False)
        
        # Create all tables
        Base.metadata.create_all(engine)
        
        # Create session factory
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Create session
        session = SessionLocal()
        
        try:
            yield session
        finally:
            session.close()
            engine.dispose()
    
    return _temp_db


@pytest.fixture
def mock_repositories():
    """Mock repository pattern for testing with dynamic update side effects."""
    
    def update_side_effect(entity_id, data=None, **kwargs):
        if data is None:
            data = {}
        data.update(kwargs)
        response = {"id": entity_id}
        response.update(data)
        return response
    
    # Patient repository
    patient_repo = Mock()
    patient_repo.create.side_effect = lambda **kwargs: {**kwargs, "status": "success"}
    patient_repo.update.side_effect = update_side_effect
    
    study_repo = Mock()
    study_repo.create.side_effect = lambda **kwargs: {**kwargs, "status": "success"}
    study_repo.update.side_effect = update_side_effect
    
    image_repo = Mock()
    image_repo.create.side_effect = lambda **kwargs: {**kwargs, "status": "success"}
    image_repo.update.side_effect = update_side_effect
    
    session_repo = Mock()
    session_repo.create.side_effect = lambda **kwargs: {**kwargs, "status": "success"}
    session_repo.update.side_effect = update_side_effect
    session_repo.find_by_id.side_effect = lambda id: None
    
    user_repo = Mock()
    user_repo.create.side_effect = lambda **kwargs: {**kwargs, "status": "created"}
    user_repo.update.side_effect = update_side_effect
    
    audit_repo = Mock()
    audit_repo.create.side_effect = lambda **kwargs: {**kwargs, "status": "success"}
    
    return {
        "patient": patient_repo,
        "study": study_repo,
        "image": image_repo,
        "session": session_repo,
        "audit": audit_repo,
        "user": user_repo
    }


@pytest.fixture
def mock_user_data() -> Dict[str, Any]:
    """Mock user data for testing."""
    return {
        "id": 1,
        "username": "test_user",
        "email": "<EMAIL>",
        "role": "Technician",
        "phone": "+**********",
        "password": "TestPassword123!",
        "failed_login_attempts": 0
    }


@pytest.fixture
def mock_audit_repository(mock_repositories):
    """Provide audit repository fixture for tests expecting standalone fixture."""
    return mock_repositories["audit"] 