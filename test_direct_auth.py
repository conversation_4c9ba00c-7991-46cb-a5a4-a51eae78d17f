#!/usr/bin/env python3
"""
Direct test for AuthenticationSecurityTests
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("Testing auth_security_tests import...")
try:
    from medscan_ai.security.testing.auth_security_tests import AuthenticationSecurityTests
    print("✅ AuthenticationSecurityTests import successful")
    
    auth_tests = AuthenticationSecurityTests()
    print(f"✅ AuthenticationSecurityTests initialization successful")
    print(f"   Services available: {auth_tests.services_available}")
    
    if hasattr(auth_tests, 'auth_service'):
        print("✅ auth_service attribute exists")
    if hasattr(auth_tests, 'password_manager'):
        print("✅ password_manager attribute exists")
    
    # Try running tests
    if auth_tests.services_available:
        print("🔄 Running test suite...")
        results = auth_tests.run_comprehensive_auth_tests()
        print(f"✅ Test suite completed")
        print(f"   Total tests: {results.get('summary', {}).get('total_tests', 0)}")
        print(f"   Passed tests: {results.get('summary', {}).get('passed_tests', 0)}")
        print(f"   Security score: {results.get('summary', {}).get('security_score', 0)}%")
    else:
        print("⚠️ Services not available, test suite skipped")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc() 