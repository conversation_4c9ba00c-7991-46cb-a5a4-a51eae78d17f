"""
Test suite for JWT Authentication API endpoints.

Tests all JWT-based authentication endpoints including token operations,
session management, and security features for the Flask Blueprint implementation.
"""

import json
import os
import sys
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest
from flask import Flask

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", ".."))

from src.medscan_ai.api.auth_endpoints import auth_service, create_auth_app
from src.medscan_ai.security.authentication import (
    AuthenticationResult,
    RegistrationResult,
)


class TestJWTAuthenticationAPI:
    """Test suite for JWT Authentication API endpoints."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        app = create_auth_app()
        app.config["TESTING"] = True
        app.config["SECRET_KEY"] = "test-secret-key"
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def mock_user_data(self):
        """Mock user data for testing."""
        return {
            "user_id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "username": "testuser",
            "first_name": "Test",
            "last_name": "User",
            "roles": ["radiologist"],
            "permissions": ["patient:read", "dicom:read"],
            "department": "Radiology",
            "medical_license_number": "MD12345",
        }

    @pytest.fixture
    def mock_jwt_auth_data(self, mock_user_data):
        """Mock JWT authentication response data."""
        return {
            "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token",
            "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.refresh.token",
            "token_type": "Bearer",
            "expires_in": 1800,
            "user": mock_user_data,
            "session": {
                "session_id": str(uuid.uuid4()),
                "created_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(days=7)).isoformat(),
                "ip_address": "127.0.0.1",
                "user_agent": "pytest/1.0",
            },
        }

    def test_app_creation(self, app):
        """Test Flask app creation and configuration."""
        assert app is not None
        assert app.config["TESTING"] is True
        # Check that auth blueprint is registered
        auth_rules = [
            rule.rule
            for rule in app.url_map.iter_rules()
            if rule.rule.startswith("/api/auth")
        ]
        assert len(auth_rules) > 0

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_login_success_with_jwt_tokens(
        self, mock_auth_service, client, mock_jwt_auth_data
    ):
        """Test successful login returning JWT tokens."""
        # Setup mock
        mock_auth_service.authenticate_user.return_value = (
            AuthenticationResult.SUCCESS,
            mock_jwt_auth_data,
        )

        # Test login
        response = client.post(
            "/api/auth/login",
            json={"email": "<EMAIL>", "password": "SecurePass123!"},
            content_type="application/json",
        )

        # Assertions
        assert response.status_code == 200
        data = json.loads(response.data)

        assert data["status"] == "success"
        assert data["message"] == "Authentication successful"
        assert "access_token" in data["data"]
        assert "refresh_token" in data["data"]
        assert data["data"]["token_type"] == "Bearer"
        assert "user" in data["data"]
        assert "session" in data["data"]

    def test_login_missing_credentials(self, client):
        """Test login with missing email or password."""
        response = client.post(
            "/api/auth/login",
            json={"email": "<EMAIL>"},
            content_type="application/json",
        )

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "Email and password are required" in data["message"]

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_refresh_token_success(self, mock_auth_service, client):
        """Test successful token refresh."""
        token_data = {
            "access_token": "new.access.token",
            "token_type": "Bearer",
            "expires_in": 1800,
        }
        mock_auth_service.refresh_token.return_value = (True, token_data, "")

        response = client.post(
            "/api/auth/refresh",
            json={"refresh_token": "valid.refresh.token"},
            content_type="application/json",
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert data["data"]["access_token"] == "new.access.token"

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_logout_success(self, mock_auth_service, client):
        """Test successful logout."""
        mock_auth_service.logout_user.return_value = (
            True,
            "Session terminated successfully",
        )

        response = client.post(
            "/api/auth/logout", headers={"Authorization": "Bearer valid.access.token"}
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"

    def test_logout_missing_authorization(self, client):
        """Test logout without authorization header."""
        response = client.post("/api/auth/logout")

        assert response.status_code == 401
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "Authorization header required" in data["message"]

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_validate_token_success(self, mock_auth_service, client, mock_user_data):
        """Test token validation success."""
        mock_auth_service.validate_request_token.return_value = (
            True,
            mock_user_data,
            "",
        )

        response = client.post(
            "/api/auth/validate", headers={"Authorization": "Bearer valid.access.token"}
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert data["data"]["token_valid"] is True

    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_register_user_success(self, mock_auth_service, client):
        """Test successful user registration."""
        user_data = {
            "user_id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "username": "newuser",
            "first_name": "New",
            "last_name": "User",
        }
        mock_auth_service.register_user.return_value = (
            RegistrationResult.SUCCESS,
            user_data,
        )

        registration_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "password": "SecurePass123!",
            "first_name": "New",
            "last_name": "User",
            "role": "technician",
            "department": "Radiology",
        }

        response = client.post(
            "/api/auth/register",
            json=registration_data,
            content_type="application/json",
        )

        assert response.status_code == 201
        data = json.loads(response.data)
        assert data["status"] == "success"


def run_jwt_auth_tests():
    """Run all JWT authentication tests."""
    print("🧪 Starting JWT Authentication API Tests...")

    import subprocess
    import sys

    result = subprocess.run(
        [sys.executable, "-m", "pytest", __file__, "-v"], capture_output=True, text=True
    )

    print("📊 Test Results:")
    print(result.stdout)
    if result.stderr:
        print("❌ Errors:")
        print(result.stderr)

    return result.returncode == 0


if __name__ == "__main__":
    """Run tests when script is executed directly."""
    success = run_jwt_auth_tests()
    if success:
        print("\n✅ All JWT authentication tests passed!")
    else:
        print("\n❌ Some tests failed. Check output above.")
        exit(1)
