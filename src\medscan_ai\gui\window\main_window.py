"""
Main window class for MedScan AI GUI.

This module provides the complete main window implementation by combining
all specialized mixins for a comprehensive medical imaging interface.
"""

from PySide6.QtWidgets import QMainWindow, QStatusBar

# Import DICOM functionality  
from ...dicom import Dicom<PERSON>eader
from ..utils import ImageDisplayHelper

# Import all mixin classes
from .handlers import (
    FileHandlerMixin,
    ImageHandlerMixin,
    ViewHandlerMixin, 
    WindowingHandlerMixin,
    ThreadingHandlerMixin,
)
from .themes import MedicalThemeManager
from .menus import MenuManagerMixin
from .panels import PanelManagerMixin
from .controls import ControlsManagerMixin


class MedScanMainWindow(
    QMainWindow,
    FileHandlerMixin,
    ImageHandlerMixin,
    ViewHandlerMixin,
    WindowingHandlerMixin,
    ThreadingHandlerMixin,
    MenuManagerMixin,
    PanelManagerMixin,
    ControlsManagerMixin,
):
    """
    Main application window for MedScan AI.
    
    Designed for medical professionals with clean, efficient interface.
    Combines all functionality through specialized mixin classes:
    - File handling (DICOM loading, error management)
    - Image display and manipulation
    - View controls (zoom, pan, responsive layout)
    - Windowing controls (window/level, medical presets)
    - Threading operations (background DICOM loading, AI inference)
    - Menu and toolbar management
    - Panel creation and management
    - UI controls creation
    """

    def __init__(self):
        """Initialize the main window with medical-focused UI."""
        super().__init__()

        # Initialize core DICOM functionality
        self._dicom_reader = DicomReader(validate_on_load=True)
        self._image_helper = ImageDisplayHelper()
        self._current_pixmap = None
        self._current_dataset = None
        self._current_file_path = None

        # Initialize windowing state
        self._current_window_center = 0
        self._current_window_width = 100
        self._raw_pixel_array = None
        self._windowing_enabled = False

        # Initialize UI components
        self._init_window()
        self._create_menu_bar()
        self._create_toolbar()
        self._create_central_widget()
        self._create_status_bar()
        
        # Add windowing controls to center panel
        self._add_windowing_controls_to_center_panel()
        
        # Apply medical theme
        self._apply_medical_theme()
        
        # Setup additional functionality
        self._setup_additional_features()

    def _init_window(self):
        """Initialize the main window properties."""
        self.setWindowTitle("MedScan AI - Medical Imaging Analysis v0.1.0")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)

        # Set window icon (placeholder for now)
        # self.setWindowIcon(QIcon("assets/icons/medscan_icon.png"))

    def _create_status_bar(self):
        """Create status bar for displaying application status."""
        status_bar = QStatusBar()
        status_bar.setStyleSheet(
            """
            QStatusBar {
                background-color: #F5F5F5;
                border-top: 1px solid #CCCCCC;
                padding: 4px;
                font-size: 11px;
            }
        """
        )
        status_bar.showMessage("🏥 MedScan AI - Ready for medical imaging analysis")
        
        # Add threading widget to status bar
        try:
            # Ensure threading components are initialized
            if not hasattr(self, '_threading_widget') or not self._threading_widget:
                self._init_threading_components()
            
            threading_widget = self.get_threading_widget()
            if threading_widget:
                status_bar.addPermanentWidget(threading_widget)
                print("✅ Threading widget added to status bar")
            else:
                print("⚠️ Threading widget not available for status bar")
        except Exception as e:
            print(f"Warning: Failed to add threading widget to status bar: {e}")
        
        self.setStatusBar(status_bar)

    def _add_windowing_controls_to_center_panel(self):
        """Add windowing controls to the center panel layout."""
        try:
            # Find the center panel in the splitter
            center_panel = None
            if hasattr(self, 'main_splitter') and self.main_splitter.count() >= 2:
                center_panel = self.main_splitter.widget(1)
            
            if center_panel:
                # Get the layout of center panel
                layout = center_panel.layout()
                if layout:
                    # Create and add windowing controls
                    windowing_panel = self._create_windowing_controls()
                    layout.addWidget(windowing_panel)
                    
        except Exception as e:
            print(f"Warning: Failed to add windowing controls: {e}")

    def _apply_medical_theme(self):
        """Apply comprehensive medical theme to the window."""
        try:
            # Apply medical theme using theme manager
            MedicalThemeManager.apply_medical_theme(self)
            
            # Additional custom styling
            self.setStyleSheet(self.styleSheet() + """
                QMainWindow {
                    background-color: #FAFAFA;
                }
                QSplitter::handle {
                    background-color: #CCCCCC;
                    border: 1px solid #AAAAAA;
                }
                QSplitter::handle:hover {
                    background-color: #BBBBBB;
                }
            """)
            
        except Exception as e:
            print(f"Warning: Failed to apply medical theme: {e}")

    def _setup_additional_features(self):
        """Setup additional features and functionality."""
        try:
            # Setup keyboard shortcuts
            if hasattr(self, '_setup_menu_shortcuts'):
                self._setup_menu_shortcuts()
            
            # Setup context menus
            if hasattr(self, '_create_context_menus'):
                self._create_context_menus()
                
            # Enable windowing controls (initially disabled)
            if hasattr(self, '_enable_windowing_controls'):
                self._enable_windowing_controls(False)
                
        except Exception as e:
            print(f"Warning: Failed to setup additional features: {e}")

    def closeEvent(self, event):
        """
        Handle application close event.
        
        Args:
            event: QCloseEvent
        """
        try:
            # Cleanup any resources
            if hasattr(self, '_dicom_reader'):
                # Any cleanup needed for DICOM reader
                pass
                
            # Clear image data
            self._current_pixmap = None
            self._current_dataset = None
            self._raw_pixel_array = None
            
            # Accept the close event
            event.accept()
            
        except Exception as e:
            print(f"Warning: Error during application close: {e}")
            event.accept()

    def showEvent(self, event):
        """
        Handle window show event.
        
        Args:
            event: QShowEvent
        """
        super().showEvent(event)
        
        try:
            # Update status on show
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(
                    "🏥 MedScan AI - Ready for medical imaging analysis", 
                    5000
                )
                
        except Exception:
            pass

    def get_current_image_info(self) -> dict:
        """
        Get information about currently loaded image.
        
        Returns:
            Dictionary with current image information
        """
        info = {
            'has_image': False,
            'file_path': None,
            'patient_name': None,
            'modality': None,
            'dimensions': None,
            'windowing': None,
        }
        
        try:
            if hasattr(self, '_current_dataset') and self._current_dataset is not None:
                info['has_image'] = True
                info['file_path'] = getattr(self, '_current_file_path', None)
                info['patient_name'] = getattr(self._current_dataset, 'PatientName', 'Unknown')
                info['modality'] = getattr(self._current_dataset, 'Modality', 'Unknown')
                
                rows = getattr(self._current_dataset, 'Rows', None)
                cols = getattr(self._current_dataset, 'Columns', None)
                if rows and cols:
                    info['dimensions'] = f"{cols}x{rows}"
                
                if hasattr(self, '_windowing_enabled') and self._windowing_enabled:
                    info['windowing'] = {
                        'center': getattr(self, '_current_window_center', 0),
                        'width': getattr(self, '_current_window_width', 100),
                    }
                    
        except Exception:
            pass
            
        return info

    def update_ui_state(self):
        """Update UI state based on current context."""
        try:
            # Update menu state
            if hasattr(self, '_update_menu_state'):
                self._update_menu_state()
                
            # Update windowing controls state
            has_image = (hasattr(self, '_current_dataset') and 
                        self._current_dataset is not None)
            
            if hasattr(self, '_enable_windowing_controls'):
                self._enable_windowing_controls(has_image)
                
        except Exception:
            pass 
