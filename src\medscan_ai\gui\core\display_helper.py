"""
Image display helper class for managing zoom and display operations.
"""

from PySide6.QtGui import QPixmap
import numpy as np
from ...dicom.processing.pixel_processor import PixelProcessor

# Shared pixel processor instance (expensive to construct repeatedly)
_PIXEL_PROCESSOR = PixelProcessor()


class ImageDisplayHelper:
    """Helper class to manage zoom and display operations for medical images."""
    
    def __init__(self):
        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        self.zoom_step = 0.25
        
    def set_zoom_limits(self, min_zoom: float, max_zoom: float):
        """Set the minimum and maximum zoom limits."""
        self.min_zoom = max(0.01, min_zoom)
        self.max_zoom = min(100.0, max_zoom)
        
    def zoom_in(self) -> float:
        """Increase zoom factor by the zoom step amount."""
        self.zoom_factor = min(self.zoom_factor + self.zoom_step, self.max_zoom)
        return self.zoom_factor
        
    def zoom_out(self) -> float:
        """Decrease zoom factor by the zoom step amount."""
        self.zoom_factor = max(self.zoom_factor - self.zoom_step, self.min_zoom)
        return self.zoom_factor
        
    def reset_zoom(self) -> float:
        """Reset zoom factor to 1.0 (100%)."""
        self.zoom_factor = 1.0
        return self.zoom_factor
        
    def get_zoom_factor(self) -> float:
        """Get current zoom factor."""
        return self.zoom_factor
        
    def apply_zoom_to_pixmap(self, pixmap: QPixmap) -> QPixmap:
        """
        Apply current zoom factor to a pixmap.
        
        Args:
            pixmap: Original QPixmap
            
        Returns:
            Scaled QPixmap according to current zoom factor
        """
        if self.zoom_factor == 1.0:
            return pixmap
            
        new_width = int(pixmap.width() * self.zoom_factor)
        new_height = int(pixmap.height() * self.zoom_factor)
        
        return pixmap.scaled(new_width, new_height)

    def get_display_array(
        self,
        dataset,
        apply_windowing: bool = True,
        window_center: float | None = None,
        window_width: float | None = None,
        output_range: tuple[float, float] | None = None,
    ) -> np.ndarray:
        """Convert a DICOM *dataset* to a NumPy array ready for GUI display.

        Args:
            dataset: Loaded *pydicom* Dataset
            apply_windowing: Whether to apply VOI LUT / window-level transforms
            window_center: Optional override for window center
            window_width: Optional override for window width
            output_range: Desired output range (default 0-255)

        Returns
        -------
        np.ndarray
            8-bit grayscale array suitable for QImage/QPixmap conversion.
        """
        if output_range is None:
            output_range = (0.0, 1.0)  # Processor expects 0-1

        if apply_windowing:
            normalized = _PIXEL_PROCESSOR.process_full_pipeline(
                dataset,
                window_center=window_center,
                window_width=window_width,
                output_range=output_range,
            )
        else:
            # Manual processing without VOI LUT
            pixel = _PIXEL_PROCESSOR.extract_pixel_array(dataset)
            pixel = _PIXEL_PROCESSOR.apply_modality_transforms(pixel, dataset)
            pixel = _PIXEL_PROCESSOR.handle_photometric_interpretation(pixel, dataset)
            normalized = _PIXEL_PROCESSOR.normalize_to_display_range(pixel, output_range)

        # Scale to 0-255 uint8 for GUI display
        return (normalized * 255.0).astype(np.uint8)

    def apply_windowing(self, dataset, window_center: float, window_width: float) -> np.ndarray:
        """Apply simple window/level and return uint8 array."""
        try:
            arr = dataset.pixel_array.astype(np.float32)
        except Exception:
            return self.get_display_array(dataset, apply_windowing=False)

        arr = _PIXEL_PROCESSOR.apply_modality_transforms(arr, dataset)

        low = window_center - (window_width / 2.0)
        high = window_center + (window_width / 2.0)
        arr = np.clip(arr, low, high)
        arr = (arr - low) / (high - low + 1e-5)
        return (arr * 255.0).astype(np.uint8)
