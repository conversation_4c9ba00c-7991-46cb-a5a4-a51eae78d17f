#!/usr/bin/env python3
"""
OpenCV Performance Test for MedScan AI

Comprehensive benchmarking of optimized OpenCV operations vs standard operations.
Tests memory usage, speed, and quality of various image processing operations.
"""

import sys
import time
import numpy as np
import psutil
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import tracemalloc

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_test_images() -> Dict[str, np.ndarray]:
    """Create test images of various sizes for benchmarking."""
    np.random.seed(42)  # For reproducible results
    
    test_images = {}
    
    # Small image (typical thumbnail)
    test_images['small'] = np.random.randint(0, 255, (256, 256), dtype=np.uint8)
    
    # Medium image (typical display size)
    test_images['medium'] = np.random.randint(0, 255, (512, 512), dtype=np.uint8)
    
    # Large image (high-resolution medical image)
    test_images['large'] = np.random.randint(0, 255, (1024, 1024), dtype=np.uint8)
    
    # Very large image (ultra-high-res)
    test_images['xl'] = np.random.randint(0, 255, (2048, 2048), dtype=np.uint8)
    
    # Color image for color-specific operations
    test_images['color'] = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    return test_images

def benchmark_resize_operations(test_images: Dict[str, np.ndarray]) -> Dict[str, Any]:
    """Benchmark resize operations: standard vs optimized."""
    results = {}
    
    target_sizes = [(128, 128), (256, 256), (512, 512), (1024, 1024)]
    
    for img_name, image in test_images.items():
        if 'color' in img_name:
            continue  # Skip color images for basic resize test
            
        results[img_name] = {'standard': {}, 'optimized': {}}
        
        for target_size in target_sizes:
            if target_size[0] > image.shape[0]:
                continue  # Skip upscaling larger than original
            
            print(f"Testing resize {img_name} ({image.shape}) -> {target_size}")
            
            # Test standard OpenCV resize
            try:
                import cv2
                
                # Standard OpenCV - multiple runs for timing
                times = []
                memory_usage = []
                
                for _ in range(5):
                    process = psutil.Process()
                    memory_before = process.memory_info().rss
                    
                    start_time = time.time()
                    result_std = cv2.resize(image, target_size, interpolation=cv2.INTER_LINEAR)
                    end_time = time.time()
                    
                    memory_after = process.memory_info().rss
                    
                    times.append(end_time - start_time)
                    memory_usage.append(memory_after - memory_before)
                
                results[img_name]['standard'][f'{target_size[0]}x{target_size[1]}'] = {
                    'avg_time': np.mean(times),
                    'min_time': np.min(times),
                    'avg_memory_mb': np.mean(memory_usage) / (1024 * 1024),
                    'output_shape': result_std.shape
                }
                
            except Exception as e:
                results[img_name]['standard'][f'{target_size[0]}x{target_size[1]}'] = {'error': str(e)}
            
            # Test optimized resize
            try:
                from medscan_ai.core.utils.optimized_cv_ops import resize_optimized
                
                times = []
                memory_usage = []
                
                for _ in range(5):
                    process = psutil.Process()
                    memory_before = process.memory_info().rss
                    
                    start_time = time.time()
                    result_opt = resize_optimized(image, target_size)
                    end_time = time.time()
                    
                    memory_after = process.memory_info().rss
                    
                    times.append(end_time - start_time)
                    memory_usage.append(memory_after - memory_before)
                
                results[img_name]['optimized'][f'{target_size[0]}x{target_size[1]}'] = {
                    'avg_time': np.mean(times),
                    'min_time': np.min(times),
                    'avg_memory_mb': np.mean(memory_usage) / (1024 * 1024),
                    'output_shape': result_opt.shape
                }
                
            except Exception as e:
                results[img_name]['optimized'][f'{target_size[0]}x{target_size[1]}'] = {'error': str(e)}
    
    return results

def benchmark_batch_operations(test_images: Dict[str, np.ndarray]) -> Dict[str, Any]:
    """Benchmark batch resize operations."""
    results = {}
    
    # Create batch of medium-sized images
    batch_sizes = [5, 10, 20]
    target_size = (256, 256)
    
    for batch_size in batch_sizes:
        print(f"Testing batch resize with {batch_size} images")
        
        # Create batch
        batch_images = [test_images['medium'].copy() for _ in range(batch_size)]
        
        results[f'batch_{batch_size}'] = {}
        
        # Standard approach - individual resizes
        try:
            import cv2
            
            start_time = time.time()
            std_results = []
            for img in batch_images:
                resized = cv2.resize(img, target_size, interpolation=cv2.INTER_LINEAR)
                std_results.append(resized)
            end_time = time.time()
            
            results[f'batch_{batch_size}']['standard'] = {
                'total_time': end_time - start_time,
                'avg_time_per_image': (end_time - start_time) / batch_size,
                'throughput_images_per_sec': batch_size / (end_time - start_time)
            }
            
        except Exception as e:
            results[f'batch_{batch_size}']['standard'] = {'error': str(e)}
        
        # Optimized batch approach
        try:
            from medscan_ai.core.utils.optimized_cv_ops import batch_resize
            
            start_time = time.time()
            opt_results = batch_resize(batch_images, target_size)
            end_time = time.time()
            
            results[f'batch_{batch_size}']['optimized'] = {
                'total_time': end_time - start_time,
                'avg_time_per_image': (end_time - start_time) / batch_size,
                'throughput_images_per_sec': batch_size / (end_time - start_time)
            }
            
        except Exception as e:
            results[f'batch_{batch_size}']['optimized'] = {'error': str(e)}
    
    return results

def benchmark_other_operations(test_images: Dict[str, np.ndarray]) -> Dict[str, Any]:
    """Benchmark other optimized operations."""
    results = {}
    
    # Test contrast normalization
    print("Testing contrast normalization...")
    image = test_images['medium']
    
    try:
        from medscan_ai.core.utils.optimized_cv_ops import normalize_contrast
        
        start_time = time.time()
        contrast_result = normalize_contrast(image, alpha=1.2, beta=10, clip_limit=2.0)
        end_time = time.time()
        
        results['contrast_normalization'] = {
            'time': end_time - start_time,
            'input_shape': image.shape,
            'output_shape': contrast_result.shape,
            'input_dtype': str(image.dtype),
            'output_dtype': str(contrast_result.dtype)
        }
        
    except Exception as e:
        results['contrast_normalization'] = {'error': str(e)}
    
    # Test denoising
    print("Testing denoising...")
    try:
        from medscan_ai.core.utils.optimized_cv_ops import denoise
        
        denoise_methods = ['bilateral', 'gaussian', 'median']
        results['denoising'] = {}
        
        for method in denoise_methods:
            start_time = time.time()
            denoised = denoise(image, method=method, strength=10.0)
            end_time = time.time()
            
            results['denoising'][method] = {
                'time': end_time - start_time,
                'output_shape': denoised.shape
            }
            
    except Exception as e:
        results['denoising'] = {'error': str(e)}
    
    # Test edge detection
    print("Testing edge detection...")
    try:
        from medscan_ai.core.utils.optimized_cv_ops import _optimized_cv_ops
        
        edge_methods = ['canny', 'sobel', 'laplacian']
        results['edge_detection'] = {}
        
        for method in edge_methods:
            start_time = time.time()
            edges = _optimized_cv_ops.edge_detection_optimized(image, method=method)
            end_time = time.time()
            
            results['edge_detection'][method] = {
                'time': end_time - start_time,
                'output_shape': edges.shape
            }
            
    except Exception as e:
        results['edge_detection'] = {'error': str(e)}
    
    return results

def test_memory_pooling_efficiency() -> Dict[str, Any]:
    """Test memory pooling efficiency."""
    print("Testing memory pooling efficiency...")
    
    results = {}
    
    try:
        from medscan_ai.core.utils.optimized_cv_ops import _optimized_cv_ops
        
        # Clear any existing stats
        _optimized_cv_ops.clear_performance_stats()
        
        # Create test image
        test_image = np.random.randint(0, 255, (512, 512), dtype=np.uint8)
        target_size = (256, 256)
        
        # Perform multiple resize operations to test pooling
        start_time = time.time()
        for i in range(20):
            resized = _optimized_cv_ops.resize_optimized(test_image, target_size)
        end_time = time.time()
        
        # Get performance stats
        stats = _optimized_cv_ops.get_performance_stats()
        
        results['pooling_test'] = {
            'total_time': end_time - start_time,
            'operations_count': 20,
            'avg_time_per_op': (end_time - start_time) / 20,
            'memory_pool_stats': stats.get('memory_pool_stats', {}),
            'operation_stats': stats.get('operation_stats', {})
        }
        
    except Exception as e:
        results['pooling_test'] = {'error': str(e)}
    
    return results

def print_results(resize_results: Dict, batch_results: Dict, 
                 other_results: Dict, pooling_results: Dict):
    """Print comprehensive benchmark results."""
    print("\n" + "="*80)
    print("🎯 OPENCV PERFORMANCE OPTIMIZATION RESULTS")
    print("="*80)
    
    # Resize performance comparison
    print("\n📏 RESIZE OPERATION PERFORMANCE:")
    for img_name, data in resize_results.items():
        print(f"\n  {img_name.upper()} IMAGES:")
        for size, size_data in data.get('standard', {}).items():
            if 'error' not in size_data:
                std_time = size_data['avg_time']
                opt_data = data.get('optimized', {}).get(size, {})
                
                if 'error' not in opt_data and 'avg_time' in opt_data:
                    opt_time = opt_data['avg_time']
                    speedup = std_time / opt_time if opt_time > 0 else 0
                    
                    print(f"    {size}: Standard={std_time:.4f}s, Optimized={opt_time:.4f}s, "
                          f"Speedup={speedup:.2f}x")
                else:
                    print(f"    {size}: Standard={std_time:.4f}s, Optimized=ERROR")
    
    # Batch performance
    print("\n📦 BATCH PROCESSING PERFORMANCE:")
    for batch_name, data in batch_results.items():
        if 'error' not in data.get('standard', {}) and 'error' not in data.get('optimized', {}):
            std_throughput = data['standard']['throughput_images_per_sec']
            opt_throughput = data['optimized']['throughput_images_per_sec']
            improvement = (opt_throughput / std_throughput - 1) * 100
            
            print(f"  {batch_name}: Standard={std_throughput:.1f} img/sec, "
                  f"Optimized={opt_throughput:.1f} img/sec, "
                  f"Improvement={improvement:+.1f}%")
    
    # Other operations
    print("\n🔧 OTHER OPTIMIZED OPERATIONS:")
    for op_name, data in other_results.items():
        if 'error' not in data:
            if op_name == 'contrast_normalization':
                print(f"  Contrast Enhancement: {data['time']:.4f}s")
            elif op_name == 'denoising':
                for method, method_data in data.items():
                    if isinstance(method_data, dict) and 'time' in method_data:
                        print(f"  Denoising ({method}): {method_data['time']:.4f}s")
            elif op_name == 'edge_detection':
                for method, method_data in data.items():
                    if isinstance(method_data, dict) and 'time' in method_data:
                        print(f"  Edge Detection ({method}): {method_data['time']:.4f}s")
    
    # Memory pooling efficiency
    print("\n🧠 MEMORY POOLING EFFICIENCY:")
    pooling_data = pooling_results.get('pooling_test', {})
    if 'error' not in pooling_data:
        pool_stats = pooling_data.get('memory_pool_stats', {})
        hit_rate = pool_stats.get('hit_rate', 0) * 100
        total_memory = pool_stats.get('total_memory_mb', 0)
        
        print(f"  20 resize operations: {pooling_data.get('total_time', 0):.4f}s total")
        print(f"  Memory pool hit rate: {hit_rate:.1f}%")
        print(f"  Total pooled memory: {total_memory:.1f}MB")
        print(f"  Pooled arrays: {pool_stats.get('total_pooled_arrays', 0)}")
    
    # Summary recommendations
    print("\n💡 PERFORMANCE SUMMARY:")
    
    # Calculate average speedup from resize results
    speedups = []
    for img_data in resize_results.values():
        for size in img_data.get('standard', {}):
            std_data = img_data['standard'][size]
            opt_data = img_data.get('optimized', {}).get(size, {})
            
            if ('error' not in std_data and 'error' not in opt_data and 
                'avg_time' in std_data and 'avg_time' in opt_data):
                speedup = std_data['avg_time'] / opt_data['avg_time']
                speedups.append(speedup)
    
    if speedups:
        avg_speedup = np.mean(speedups)
        print(f"  Average resize speedup: {avg_speedup:.2f}x")
        
        if avg_speedup > 1.2:
            print("  ✅ OPTIMIZATION SUCCESS: Significant performance improvement achieved")
        elif avg_speedup > 1.0:
            print("  ⚠️  MODEST IMPROVEMENT: Some optimization benefit detected")
        else:
            print("  ❌ OPTIMIZATION ISSUE: No significant improvement or performance regression")
    else:
        print("  ⚠️  Unable to calculate performance improvements (errors in benchmarks)")

def main():
    """Main benchmarking function."""
    print("🚀 Starting OpenCV Performance Optimization Benchmarks")
    print("="*80)
    
    # System info
    system_info = {
        'cpu_cores': psutil.cpu_count(),
        'memory_gb': psutil.virtual_memory().total / (1024**3),
        'platform': sys.platform
    }
    print(f"System: {system_info['cpu_cores']} cores, {system_info['memory_gb']:.1f}GB RAM")
    
    # Create test images
    print("\nCreating test images...")
    test_images = create_test_images()
    
    for name, img in test_images.items():
        size_mb = img.nbytes / (1024 * 1024)
        print(f"  {name}: {img.shape} ({size_mb:.1f}MB)")
    
    # Run benchmarks
    print("\n🔬 Running resize benchmarks...")
    resize_results = benchmark_resize_operations(test_images)
    
    print("\n🔬 Running batch benchmarks...")
    batch_results = benchmark_batch_operations(test_images)
    
    print("\n🔬 Running other operation benchmarks...")
    other_results = benchmark_other_operations(test_images)
    
    print("\n🔬 Running memory pooling tests...")
    pooling_results = test_memory_pooling_efficiency()
    
    # Print results
    print_results(resize_results, batch_results, other_results, pooling_results)
    
    # Save detailed results
    import json
    all_results = {
        'system_info': system_info,
        'resize_results': resize_results,
        'batch_results': batch_results,
        'other_results': other_results,
        'pooling_results': pooling_results
    }
    
    with open('opencv_performance_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)  # No need for custom serialization
    
    print(f"\n💾 Detailed results saved to: opencv_performance_results.json")
    print("\n🎉 OpenCV optimization benchmarking completed!")

if __name__ == "__main__":
    main() 