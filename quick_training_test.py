#!/usr/bin/env python3
"""
Quick training test with the new D:/datasets/archive_2 path
Limited samples for fast prototyping
"""

import sys
import os
sys.path.append('src')

from medscan_ai.ai.training_pipeline import PyTorchTrainingPipeline, TrainingConfig

def main():
    print("🚀 Starting quick training test with archive_2...")
    
    # CheXpert multi-label configuration
    config = TrainingConfig(
        # Dataset config
        dataset_path="D:/datasets/archive_2",
        max_samples_per_class=1000,  # Limit samples for faster training
        image_size=(224, 224),
        batch_size=16,  # Reasonable batch size for multi-label
        
        # Training config
        epochs=10,  # More epochs for multi-label convergence
        initial_learning_rate=0.001,
        num_classes=14,  # CheXpert has 14 pathologies
        is_multilabel=True,  # Enable multi-label mode
        
        # Model config
        base_model="resnet50",
        dense_layers=[512, 256],  # Sufficient capacity for 14 classes
        dropout_rate=0.4,
        
        # System config
        num_workers=4,  # More workers for larger dataset
        mixed_precision=True,  # GPU optimization
        device="auto"  # Auto-detect GPU
    )
    
    try:
        # Create pipeline
        pipeline = PyTorchTrainingPipeline(config)
        
        # Load datasets
        print("📂 Loading datasets...")
        train_loader, val_loader, test_loader = pipeline.load_datasets()
        
        # Create model
        print("🧠 Creating model...")
        model = pipeline.create_model()
        
        print("✅ Setup completed successfully!")
        print(f"✅ Train samples: {len(train_loader.dataset)}")
        print(f"✅ Val samples: {len(val_loader.dataset)}")
        print(f"✅ Test samples: {len(test_loader.dataset)}")
        print(f"✅ Model device: {model.device}")
        
        # Start actual training
        print("\n🎯 Starting training...")
        print("This may take several minutes...")
        
        # Start training
        history = pipeline.train()
        
        print("\n✅ Training completed!")
        print(f"✅ Final train accuracy: {history['train_acc'][-1]:.2f}%")
        print(f"✅ Final val accuracy: {history['val_acc'][-1]:.2f}%")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 