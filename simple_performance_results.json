{"system_info": {"cpu_cores": 16, "total_memory_gb": 15.420856475830078, "available_memory_gb": 4.178493499755859, "memory_percent": 72.9, "python_version": "3.10.0", "platform": "win32"}, "imports": {"pydicom": {"import_time": 0.41068077087402344, "memory_increase": 27.04296875, "success": true}, "numpy": {"import_time": 0.0, "memory_increase": 0.0, "success": true}, "opencv-python (cv2)": {"import_time": 0.044019460678100586, "memory_increase": 7.84375, "success": true}, "PySide6": {"import_time": 0.09707474708557129, "memory_increase": 5.69140625, "success": true}, "medscan_ai.dicom": {"import_time": 5.004627704620361, "memory_increase": 320.46484375, "success": true}, "medscan_ai.gui": {"import_time": 0.0, "memory_increase": 0.0, "success": true}, "medscan_ai.ai": {"import_time": 0.0, "memory_increase": 0.0, "success": true}}, "file_operations": {"1MB": {"write_time": 0.0, "write_memory": 0.0, "read_time": 0.015551567077636719, "read_memory": 1.00390625, "write_speed_mbps": 0, "read_speed_mbps": 64.3022015085546}, "10MB": {"write_time": 0.00651240348815918, "write_memory": 0.0, "read_time": 0.026027441024780273, "read_memory": 10.00390625, "write_speed_mbps": 1535.5313930075051, "read_speed_mbps": 384.2098802751747}, "50MB": {"write_time": 0.067779541015625, "write_memory": 0.0, "read_time": 0.04953622817993164, "read_memory": 50.0078125, "write_speed_mbps": 737.6857271499325, "read_speed_mbps": 1009.362275593204}}, "memory_allocation": {"numpy_512x512": {"processing_time": 0.004508495330810547, "memory_increase": 0.76953125, "expected_memory": 1.75, "memory_efficiency": 2.2741116751269037, "pixels_per_second": 58144454.139397144}, "numpy_1024x1024": {"processing_time": 0.010517597198486328, "memory_increase": 3.0078125, "expected_memory": 7.0, "memory_efficiency": 2.327272727272727, "pixels_per_second": 99697295.89481798}, "numpy_2048x2048": {"processing_time": 0.05106210708618164, "memory_increase": 12.0078125, "expected_memory": 28.0, "memory_efficiency": 2.33181522446324, "pixels_per_second": 82141224.46848765}, "tracemalloc_peak_mb": 40.05149555206299}, "analysis": {"bottlenecks": ["Slow imports detected: [('medscan_ai.dicom', 5.004627704620361)]", "Slow I/O operations: ['Write: 1MB at 0.0 MB/s']"], "recommendations": ["Consider lazy loading for slow-importing modules", "Consider using SSD storage for DICOM files"]}}