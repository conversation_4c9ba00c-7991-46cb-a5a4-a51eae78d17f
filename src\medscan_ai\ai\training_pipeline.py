"""
Medical CNN Training Pipeline - PyTorch Implementation

This module implements a comprehensive training pipeline for medical imaging
anomaly detection using PyTorch, including data loading, preprocessing, training, and
hyperparameter tuning capabilities.

Features:
- Multi-format dataset loading (.jpg, .png, .jpeg, .nii)
- GPU-optimized training with RTX 3060 support
- Hyperparameter tuning with various strategies
- Real-time monitoring and logging
- Model checkpointing and versioning
- Performance evaluation and visualization
- Integration with our custom MedicalCNN model
"""

import os
import sys
import json
import time
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import torchvision.transforms as transforms
from torchvision.datasets import ImageFolder
from torch.utils.tensorboard import SummaryWriter
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve

# Medical imaging libraries
import nibabel as nib
from PIL import Image
import cv2

# Import our custom modules
from .models.medical_cnn import MedicalCNN, ModelConfig, create_medical_cnn

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """Configuration for PyTorch training pipeline."""
    
    # Dataset Configuration
    dataset_path: str = "D:/datasets/archive_2"  # CheXpert dataset
    image_size: Tuple[int, int] = (224, 224)
    batch_size: int = 32
    validation_split: float = 0.2
    test_split: float = 0.1
    num_workers: int = 4
    
    # Sample limiting for faster prototyping
    max_samples_per_class: Optional[int] = None  # For CheXpert, limit total samples not per class
    
    # Training Configuration
    epochs: int = 20  # Reduced for multi-label training
    initial_learning_rate: float = 0.001
    optimizer: str = "adam"  # adam, adamw, sgd, rmsprop
    scheduler: Optional[str] = "plateau"  # plateau, step, cosine, None
    
    # Model Configuration - Multi-label setup
    base_model: str = "resnet50"
    num_classes: int = 14  # CheXpert has 14 pathologies
    dense_layers: List[int] = field(default_factory=lambda: [512, 256])
    dropout_rate: float = 0.5
    class_weights: Optional[List[float]] = None
    
    # Multi-label specific
    is_multilabel: bool = True  # Enable multi-label mode
    pos_weight: Optional[List[float]] = None  # For BCEWithLogitsLoss
    
    # GPU Configuration
    mixed_precision: bool = True
    device: str = "auto"  # auto, cuda, cpu
    
    # Augmentation Configuration
    use_augmentation: bool = True
    augmentation_strength: float = 0.3
    
    # Monitoring Configuration
    save_checkpoints: bool = True
    checkpoint_dir: str = "./checkpoints"
    log_dir: str = "./logs"
    save_history: bool = True
    enable_tensorboard: bool = True
    
    # NIfTI specific
    nii_slice_axis: int = 2  # Which axis to extract slices from (0, 1, or 2)
    nii_slice_range: Optional[Tuple[int, int]] = None  # None for all slices, or (start, end)
    
    # File format support
    supported_image_extensions: List[str] = field(default_factory=lambda: ['.jpg', '.jpeg', '.png'])
    supported_volume_extensions: List[str] = field(default_factory=lambda: ['.nii', '.nii.gz'])

class MultiFormatMedicalDataset(Dataset):
    """
    PyTorch Dataset for multi-format medical imaging data.
    Supports: .jpg, .jpeg, .png, .nii files
    """
    
    def __init__(self, 
                 dataset_path: str, 
                 config: TrainingConfig,
                 transform: Optional[transforms.Compose] = None,
                 mode: str = 'train'):
        """
        Initialize multi-format dataset.
        
        Args:
            dataset_path: Path to dataset root
            config: Training configuration
            transform: PyTorch transforms to apply
            mode: 'train', 'val', or 'test'
        """
        self.dataset_path = Path(dataset_path)
        self.config = config
        self.transform = transform
        self.mode = mode
        
        # Discover all files and create samples
        self.samples = []
        self.class_to_idx = {}
        self.idx_to_class = {}
        
        self._discover_files()
        logger.info(f"Loaded {len(self.samples)} samples for {mode} mode")
        logger.info(f"Class distribution: {Counter(s[1] for s in self.samples)}")
    
    def _discover_files(self) -> None:
        """Discover and categorize all supported files."""
        file_paths = []
        
        # Collect all supported files
        for ext in (self.config.supported_image_extensions + 
                   self.config.supported_volume_extensions):
            file_paths.extend(self.dataset_path.rglob(f"*{ext}"))
        
        # Group by directory structure to infer classes
        class_groups = defaultdict(list)
        
        for file_path in file_paths:
            # Try to infer class from directory structure
            relative_path = file_path.relative_to(self.dataset_path)
            
            # Check various naming patterns
            class_name = self._infer_class_from_path(relative_path)
            class_groups[class_name].append(file_path)
        
        # Create class mappings
        class_names = sorted(class_groups.keys())
        self.class_to_idx = {name: idx for idx, name in enumerate(class_names)}
        self.idx_to_class = {idx: name for name, idx in self.class_to_idx.items()}
        
        # Create samples list with optional limiting
        for class_name, paths in class_groups.items():
            class_idx = self.class_to_idx[class_name]
            
            # Apply sample limiting if specified
            if self.config.max_samples_per_class is not None:
                paths = paths[:self.config.max_samples_per_class]
                logger.info(f"Limited {class_name} to {len(paths)} samples")
            
            for path in paths:
                if path.suffix.lower() in self.config.supported_volume_extensions:
                    # Handle NIfTI files - extract slices
                    slices = self._extract_nii_slices(path)
                    for slice_data in slices:
                        self.samples.append((slice_data, class_idx, str(path)))
                else:
                    # Handle regular image files
                    self.samples.append((str(path), class_idx, str(path)))
        
        logger.info(f"Discovered classes: {list(self.class_to_idx.keys())}")
    
    def _infer_class_from_path(self, relative_path: Path) -> str:
        """
        Infer class name from file path structure.
        
        Args:
            relative_path: Path relative to dataset root
            
        Returns:
            Inferred class name
        """
        path_parts = relative_path.parts
        
        # Common medical imaging class patterns
        for part in path_parts:
            part_lower = part.lower()
            
            # Direct class indicators
            if any(term in part_lower for term in ['normal', 'healthy', 'negative']):
                return 'normal'
            elif any(term in part_lower for term in ['abnormal', 'disease', 'positive', 'pathology']):
                return 'abnormal'
            elif any(term in part_lower for term in ['covid', 'pneumonia', 'tumor', 'cancer']):
                return 'abnormal'
        
        # Use directory structure
        if len(path_parts) > 1:
            # Use parent directory name
            return path_parts[-2].lower()
        else:
            # Default classification
            return 'unknown'
    
    def _extract_nii_slices(self, nii_path: Path) -> List[np.ndarray]:
        """
        Extract 2D slices from NIfTI volume.
        
        Args:
            nii_path: Path to .nii file
            
        Returns:
            List of 2D numpy arrays (slices)
        """
        try:
            # Load NIfTI file
            nii_img = nib.load(str(nii_path))
            volume_data = nii_img.get_fdata()
            
            # Extract slices along specified axis
            slices = []
            axis = self.config.nii_slice_axis
            
            for i in range(volume_data.shape[axis]):
                if axis == 0:
                    slice_2d = volume_data[i, :, :]
                elif axis == 1:
                    slice_2d = volume_data[:, i, :]
                else:  # axis == 2
                    slice_2d = volume_data[:, :, i]
                
                # Skip empty or mostly empty slices
                if np.mean(slice_2d) > 0.01:  # Threshold for non-empty slice
                    # Normalize to 0-255 range
                    slice_normalized = ((slice_2d - slice_2d.min()) / 
                                      (slice_2d.max() - slice_2d.min() + 1e-8) * 255).astype(np.uint8)
                    
                    # Convert to 3-channel (RGB-like)
                    slice_3ch = np.stack([slice_normalized] * 3, axis=-1)
                    slices.append(slice_3ch)
            
            # Apply slice range if specified
            if self.config.nii_slice_range:
                start, end = self.config.nii_slice_range
                slices = slices[start:end]
            
            return slices
            
        except Exception as e:
            logger.warning(f"Could not process NIfTI file {nii_path}: {e}")
            return []
    
    def __len__(self) -> int:
        """Return number of samples."""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        """
        Get sample by index.
        
        Args:
            idx: Sample index
            
        Returns:
            Tuple of (image_tensor, class_label)
        """
        sample_data, class_idx, file_path = self.samples[idx]
        
        try:
            # Load image
            if isinstance(sample_data, str):
                # Regular image file
                image = Image.open(sample_data).convert('RGB')
            else:
                # NIfTI slice (numpy array)
                image = Image.fromarray(sample_data.astype(np.uint8))
            
            # Apply transforms
            if self.transform:
                image = self.transform(image)
            else:
                # Default transform
                image = transforms.ToTensor()(image)
            
            return image, class_idx
            
        except Exception as e:
            logger.warning(f"Could not load sample {idx} from {file_path}: {e}")
            # Return a dummy sample
            dummy_image = torch.zeros(3, self.config.image_size[0], self.config.image_size[1])
            return dummy_image, class_idx

class PyTorchTrainingPipeline:
    """
    Comprehensive PyTorch training pipeline for medical CNN models.
    
    This class handles end-to-end training including data loading,
    preprocessing, model training, evaluation, and hyperparameter tuning.
    """
    
    def __init__(self, config: TrainingConfig):
        """
        Initialize training pipeline.
        
        Args:
            config: TrainingConfig instance with training parameters
        """
        self.config = config
        self.device = torch.device(
            config.device if config.device != "auto" 
            else "cuda" if torch.cuda.is_available() else "cpu"
        )
        
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        self.optimizer = None
        self.scheduler = None
        self.scaler = None
        self.writer = None
        
        # Training tracking
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': []
        }
        
        # Create directories
        self._create_directories()
        
        # Setup TensorBoard if enabled
        if self.config.enable_tensorboard:
            self.writer = SummaryWriter(log_dir=f"{self.config.log_dir}/tensorboard")
        
        logger.info(f"Initialized PyTorchTrainingPipeline with device: {self.device}")
    
    def _create_directories(self) -> None:
        """Create necessary directories for training."""
        dirs = [
            self.config.checkpoint_dir,
            self.config.log_dir,
            f"{self.config.log_dir}/tensorboard"
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        logger.info("Created training directories")
    
    def get_transforms(self, mode: str = 'train') -> transforms.Compose:
        """
        Get image transforms for different modes.
        
        Args:
            mode: 'train', 'val', or 'test'
            
        Returns:
            Composed transforms
        """
        if mode == 'train' and self.config.use_augmentation:
            # Training transforms with augmentation
            return transforms.Compose([
                transforms.Resize((int(self.config.image_size[0] * 1.1), 
                                 int(self.config.image_size[1] * 1.1))),
                transforms.RandomCrop(self.config.image_size),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=10),
                transforms.ColorJitter(
                    brightness=self.config.augmentation_strength * 0.3,
                    contrast=self.config.augmentation_strength * 0.3,
                    saturation=self.config.augmentation_strength * 0.2
                ),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            # Validation/test transforms (no augmentation)
            return transforms.Compose([
                transforms.Resize(self.config.image_size),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
    
    def load_datasets(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        Load and prepare datasets for training.
        
        Returns:
            Tuple of (train_loader, val_loader, test_loader)
        """
        # Check if this is CheXpert dataset (has CSV files)
        train_csv = Path(self.config.dataset_path) / "train.csv"
        valid_csv = Path(self.config.dataset_path) / "valid.csv"
        
        if train_csv.exists() and valid_csv.exists():
            logger.info("Loading CheXpert multi-label dataset...")
            return self._load_chexpert_datasets()
        else:
            logger.info("Loading multi-format datasets...")
            return self._load_multiformat_datasets()
    
    def _load_chexpert_datasets(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Load CheXpert dataset from CSV files."""
        from .chexpert_dataset import CheXpertDataset
        
        train_csv = Path(self.config.dataset_path) / "train.csv"
        valid_csv = Path(self.config.dataset_path) / "valid.csv"
        
        # Create datasets
        train_dataset = CheXpertDataset(
            csv_file=str(train_csv),
            root_dir=str(self.config.dataset_path),
            transform=self.get_transforms('train'),
            uncertain_policy='zeros',  # Map uncertain (-1) to negative (0)
            max_samples=self.config.max_samples_per_class
        )
        
        val_dataset = CheXpertDataset(
            csv_file=str(valid_csv),
            root_dir=str(self.config.dataset_path),
            transform=self.get_transforms('val'),
            uncertain_policy='zeros',
            max_samples=self.config.max_samples_per_class // 5 if self.config.max_samples_per_class else None
        )
        
        # Split validation set into val/test
        val_size = int(len(val_dataset) * 0.7)  # 70% for validation
        test_size = len(val_dataset) - val_size  # 30% for test
        
        val_dataset, test_dataset = random_split(
            val_dataset, [val_size, test_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=self.config.num_workers,
            pin_memory=True
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=True
        )
        
        self.test_loader = DataLoader(
            test_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=True
        )
        
        logger.info(f"CheXpert dataset splits - Train: {len(train_dataset)}, "
                   f"Val: {len(val_dataset)}, Test: {len(test_dataset)}")
        
        return self.train_loader, self.val_loader, self.test_loader
    
    def _load_multiformat_datasets(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Load original multi-format dataset."""        
        # Create full dataset
        full_dataset = MultiFormatMedicalDataset(
            dataset_path=self.config.dataset_path,
            config=self.config,
            transform=None,  # We'll set transforms per split
            mode='full'
        )
        
        # Calculate split sizes
        total_size = len(full_dataset)
        test_size = int(total_size * self.config.test_split)
        val_size = int(total_size * self.config.validation_split)
        train_size = total_size - test_size - val_size
        
        # Split dataset
        train_dataset, val_dataset, test_dataset = random_split(
            full_dataset, [train_size, val_size, test_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # Apply transforms to each split
        train_dataset.dataset.transform = self.get_transforms('train')
        val_dataset.dataset.transform = self.get_transforms('val')
        test_dataset.dataset.transform = self.get_transforms('test')
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=self.config.num_workers,
            pin_memory=True
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=True
        )
        
        self.test_loader = DataLoader(
            test_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=True
        )
        
        logger.info(f"Dataset splits - Train: {len(train_dataset)}, "
                   f"Val: {len(val_dataset)}, Test: {len(test_dataset)}")
        
        return self.train_loader, self.val_loader, self.test_loader
    
    def create_model(self) -> MedicalCNN:
        """
        Create and configure the medical CNN model.
        
        Returns:
            MedicalCNN instance
        """
        logger.info("Creating PyTorch medical CNN model...")
        
        model_config = ModelConfig(
            base_model=self.config.base_model,
            input_shape=(*self.config.image_size, 3),
            num_classes=self.config.num_classes,
            dense_layers=self.config.dense_layers,
            dropout_rate=self.config.dropout_rate,
            learning_rate=self.config.initial_learning_rate,
            optimizer=self.config.optimizer,
            scheduler=self.config.scheduler,
            mixed_precision=self.config.mixed_precision,
            device=self.device,
            class_weights=self.config.class_weights
        )
        
        self.model = MedicalCNN(model_config)
        self.model.setup_training()
        
        # Setup mixed precision scaler
        if self.config.mixed_precision and self.device.type == "cuda":
            self.scaler = torch.cuda.amp.GradScaler()
        
        logger.info("PyTorch medical CNN model created successfully")
        return self.model
    
    def train_epoch(self, epoch: int) -> Tuple[float, float]:
        """
        Train for one epoch.
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Tuple of (average_loss, accuracy)
        """
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(self.train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            # Zero gradients
            self.model.optimizer.zero_grad()
            
            # Forward pass with mixed precision
            if self.scaler is not None:
                with torch.cuda.amp.autocast():
                    output = self.model(data)
                    if hasattr(self.config, 'is_multilabel') and self.config.is_multilabel:
                        target = target.float()  # Multi-label: keep as (B, 14)
                    elif self.config.num_classes == 2:
                        target = target.float().unsqueeze(1)  # Binary: reshape to (B, 1)
                    loss = self.model.criterion(output, target)
                
                # Backward pass with scaled gradients
                self.scaler.scale(loss).backward()
                self.scaler.step(self.model.optimizer)
                self.scaler.update()
            else:
                output = self.model(data)
                if hasattr(self.config, 'is_multilabel') and self.config.is_multilabel:
                    target = target.float()  # Multi-label: keep as (B, 14)
                elif self.config.num_classes == 2:
                    target = target.float().unsqueeze(1)  # Binary: reshape to (B, 1)
                loss = self.model.criterion(output, target)
                
                # Backward pass
                loss.backward()
                self.model.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            
            # Calculate accuracy
            if hasattr(self.config, 'is_multilabel') and self.config.is_multilabel:
                # Multi-label accuracy: exact match 
                predicted = (torch.sigmoid(output) > 0.5).float()
                correct += (predicted == target).all(dim=1).sum().item()  # Exact match
            elif self.config.num_classes == 2:
                predicted = (torch.sigmoid(output) > 0.5).float()
                correct += (predicted == target).sum().item()
            else:
                _, predicted = torch.max(output.data, 1)
                correct += (predicted == target).sum().item()
            total += target.size(0)
            
            # Log to TensorBoard
            if self.writer and batch_idx % 100 == 0:
                global_step = epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Training/Batch_Loss', loss.item(), global_step)
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def validate_epoch(self) -> Tuple[float, float]:
        """
        Validate for one epoch.
        
        Returns:
            Tuple of (average_loss, accuracy)
        """
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in self.val_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                output = self.model(data)
                if hasattr(self.config, 'is_multilabel') and self.config.is_multilabel:
                    target = target.float()  # Multi-label: keep as (B, 14)
                elif self.config.num_classes == 2:
                    target = target.float().unsqueeze(1)  # Binary: reshape to (B, 1)
                loss = self.model.criterion(output, target)
                
                total_loss += loss.item()
                
                # Calculate accuracy
                if hasattr(self.config, 'is_multilabel') and self.config.is_multilabel:
                    # Multi-label validation accuracy
                    predicted = (torch.sigmoid(output) > 0.5).float()
                    correct += (predicted == target).all(dim=1).sum().item()  # Exact match
                elif self.config.num_classes == 2:
                    predicted = (torch.sigmoid(output) > 0.5).float()
                    correct += (predicted == target).sum().item()
                else:
                    _, predicted = torch.max(output.data, 1)
                    correct += (predicted == target).sum().item()
                total += target.size(0)
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def train(self) -> Dict[str, List[float]]:
        """
        Train the model.
        
        Returns:
            Training history dictionary
        """
        if self.model is None:
            raise ValueError("Model not created. Call create_model() first.")
        
        logger.info(f"Starting training for {self.config.epochs} epochs...")
        
        best_val_acc = 0.0
        patience_counter = 0
        
        for epoch in range(self.config.epochs):
            start_time = time.time()
            
            # Train
            train_loss, train_acc = self.train_epoch(epoch)
            
            # Validate
            val_loss, val_acc = self.validate_epoch()
            
            # Update learning rate scheduler
            if self.model.scheduler is not None:
                if self.config.scheduler == "plateau":
                    self.model.scheduler.step(val_loss)
                else:
                    self.model.scheduler.step()
            
            # Record history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            
            epoch_time = time.time() - start_time
            
            logger.info(f"Epoch {epoch+1}/{self.config.epochs} ({epoch_time:.1f}s) - "
                       f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}% - "
                       f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            
            # TensorBoard logging
            if self.writer:
                self.writer.add_scalar('Training/Loss', train_loss, epoch)
                self.writer.add_scalar('Training/Accuracy', train_acc, epoch)
                self.writer.add_scalar('Validation/Loss', val_loss, epoch)
                self.writer.add_scalar('Validation/Accuracy', val_acc, epoch)
                
                if self.model.scheduler:
                    current_lr = self.model.optimizer.param_groups[0]['lr']
                    self.writer.add_scalar('Training/Learning_Rate', current_lr, epoch)
            
            # Save best model
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                if self.config.save_checkpoints:
                    self.save_checkpoint(epoch, is_best=True)
                    logger.info(f"New best validation accuracy: {val_acc:.2f}%")
            else:
                patience_counter += 1
            
            # Early stopping (optional)
            if patience_counter >= 15:  # Hard-coded patience for now
                logger.info(f"Early stopping after {epoch+1} epochs")
                break
        
        # Save training history
        if self.config.save_history:
            self._save_training_history()
        
        logger.info("Training completed!")
        return self.training_history
    
    def save_checkpoint(self, epoch: int, is_best: bool = False) -> None:
        """Save model checkpoint."""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.model.optimizer.state_dict(),
            'training_history': self.training_history,
            'config': self.config
        }
        
        # Save regular checkpoint
        checkpoint_path = f"{self.config.checkpoint_dir}/checkpoint_epoch_{epoch}.pth"
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = f"{self.config.checkpoint_dir}/best_model.pth"
            torch.save(checkpoint, best_path)
    
    def _save_training_history(self) -> None:
        """Save training history to file."""
        history_path = f"{self.config.log_dir}/training_history.json"
        
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        
        logger.info(f"Training history saved to {history_path}")
    
    def evaluate(self) -> Dict[str, float]:
        """
        Evaluate the trained model on test set.
        
        Returns:
            Dictionary with evaluation metrics
        """
        if self.model is None:
            raise ValueError("Model not trained. Call train() first.")
        
        logger.info("Evaluating model on test set...")
        
        test_loss, test_acc = self.model.validate_epoch(self.test_loader)
        
        results = {
            'test_loss': test_loss,
            'test_accuracy': test_acc
        }
        
        # Generate detailed evaluation report
        self._generate_evaluation_report(results)
        
        return results
    
    def _generate_evaluation_report(self, results: Dict[str, float]) -> None:
        """Generate comprehensive evaluation report."""
        logger.info("Generating evaluation report...")
        
        # Save results
        results_path = f"{self.config.log_dir}/evaluation_results.json"
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        # Create evaluation plots
        self._plot_training_history()
        
        logger.info(f"Evaluation report saved to {self.config.log_dir}")
    
    def _plot_training_history(self) -> None:
        """Plot training history."""
        if not self.training_history['train_loss']:
            return
        
        plt.figure(figsize=(15, 5))
        
        # Plot loss
        plt.subplot(1, 3, 1)
        plt.plot(self.training_history['train_loss'], label='Training Loss')
        plt.plot(self.training_history['val_loss'], label='Validation Loss')
        plt.title('Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        # Plot accuracy
        plt.subplot(1, 3, 2)
        plt.plot(self.training_history['train_acc'], label='Training Accuracy')
        plt.plot(self.training_history['val_acc'], label='Validation Accuracy')
        plt.title('Model Accuracy')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy (%)')
        plt.legend()
        plt.grid(True)
        
        # Plot learning rate (if available)
        plt.subplot(1, 3, 3)
        if self.model and self.model.scheduler:
            # This is approximate - would need to track LR during training
            plt.plot([self.config.initial_learning_rate] * len(self.training_history['train_loss']))
            plt.title('Learning Rate')
            plt.xlabel('Epoch')
            plt.ylabel('LR')
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(f"{self.config.log_dir}/training_history.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("Training history plots saved")
    
    def run_full_pipeline(self) -> Dict[str, Any]:
        """
        Run the complete training pipeline.
        
        Returns:
            Dictionary with training results and metrics
        """
        logger.info("Starting full PyTorch training pipeline...")
        
        start_time = time.time()
        
        try:
            # Load datasets
            self.load_datasets()
            
            # Create model
            self.create_model()
            
            # Train model
            history = self.train()
            
            # Evaluate model
            eval_results = self.evaluate()
            
            # Calculate total training time
            total_time = time.time() - start_time
            
            # Compile results
            results = {
                'training_time_minutes': total_time / 60,
                'final_train_accuracy': float(history['train_acc'][-1]) if history['train_acc'] else 0,
                'final_val_accuracy': float(history['val_acc'][-1]) if history['val_acc'] else 0,
                'test_results': eval_results,
                'config': {
                    'base_model': self.config.base_model,
                    'epochs': self.config.epochs,
                    'batch_size': self.config.batch_size,
                    'learning_rate': self.config.initial_learning_rate,
                    'device': str(self.device)
                }
            }
            
            logger.info("Full training pipeline completed successfully!")
            logger.info(f"Total training time: {total_time/60:.2f} minutes")
            logger.info(f"Final validation accuracy: {results['final_val_accuracy']:.4f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"Training pipeline failed: {e}")
            raise
        finally:
            if self.writer:
                self.writer.close()

# Factory function for easy pipeline creation
def create_training_pipeline(
    dataset_path: str = "D:/datasets/archive_2",
    base_model: str = "resnet50",
    epochs: int = 50,
    batch_size: int = 32,
    **kwargs
) -> PyTorchTrainingPipeline:
    """
    Factory function to create a PyTorch training pipeline with default configuration.
    
    Args:
        dataset_path: Path to the datasets
        base_model: Base model architecture
        epochs: Number of training epochs
        batch_size: Training batch size
        **kwargs: Additional configuration parameters
        
    Returns:
        PyTorchTrainingPipeline instance
    """
    config = TrainingConfig(
        dataset_path=dataset_path,
        base_model=base_model,
        epochs=epochs,
        batch_size=batch_size,
        **kwargs
    )
    
    return PyTorchTrainingPipeline(config)

# Example usage and testing
if __name__ == "__main__":
    # Example: Create and run PyTorch training pipeline
    config = TrainingConfig(
        dataset_path="./data/datasets",
        base_model="resnet50",
        epochs=10,  # Reduced for testing
        batch_size=16,
        image_size=(224, 224),
        mixed_precision=True,
        num_workers=2
    )
    
    # Create pipeline
    pipeline = PyTorchTrainingPipeline(config)
    
    # Run full pipeline
    try:
        results = pipeline.run_full_pipeline()
        print(f"Training completed! Results: {results}")
    except Exception as e:
        print(f"Training failed: {e}")
        logger.error(f"Training pipeline error: {e}")
