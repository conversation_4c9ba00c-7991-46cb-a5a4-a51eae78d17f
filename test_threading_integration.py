#!/usr/bin/env python3
"""
Test script for Threading Integration in MedScan AI
Tests the threaded DICOM loading functionality in GUI.
"""

import sys
import time
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# Disable GUI display for headless testing
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.medscan_ai.gui.window.main_window import MedScanMainWindow


def test_threading_integration():
    """Test threading integration with main window."""
    print("🧵 Testing Threading Integration...")
    
    try:
        # Create main window
        main_window = MedScanMainWindow()
        
        # Check if threading components are available
        print("✅ Main window created successfully")
        
        # Check if worker manager is initialized
        if hasattr(main_window, '_worker_manager') and main_window._worker_manager:
            print("✅ Worker manager initialized")
            
            # Check if threading widget exists
            threading_widget = main_window.get_threading_widget()
            if threading_widget:
                print("✅ Threading widget available")
            else:
                print("❌ Threading widget not available")
                
            # Check worker manager capabilities
            print(f"📊 Worker manager active workers: {len(main_window._worker_manager._active_workers)}")
            print(f"📊 Worker manager is busy: {main_window._worker_manager.is_busy()}")
            
        else:
            print("❌ Worker manager not initialized")
        
        # Check threading UI components
        if hasattr(main_window, '_progress_bar') and main_window._progress_bar:
            print("✅ Progress bar component available")
        else:
            print("❌ Progress bar component not available")
            
        if hasattr(main_window, '_status_label') and main_window._status_label:
            print("✅ Status label component available")
        else:
            print("❌ Status label component not available")
            
        if hasattr(main_window, '_cancel_button') and main_window._cancel_button:
            print("✅ Cancel button component available")
        else:
            print("❌ Cancel button component not available")
        
        # Test threaded loading method
        if hasattr(main_window, 'load_dicom_file_threaded'):
            print("✅ Threaded DICOM loading method available")
            
            # Try loading with a non-existent file (should handle error gracefully)
            test_path = "test_non_existent.dcm"
            result = main_window.load_dicom_file_threaded(test_path)
            print(f"📊 Threaded loading result for non-existent file: {result}")
            
        else:
            print("❌ Threaded DICOM loading method not available")
        
        # Cleanup
        if hasattr(main_window, '_cleanup_threading'):
            main_window._cleanup_threading()
            print("✅ Threading cleanup completed")
        
        # Close window
        main_window.close()
        
        print("✅ Threading integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Threading integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_worker_components():
    """Test individual worker components."""
    print("\n⚙️ Testing Worker Components...")
    
    try:
        # Test individual worker imports
        from src.medscan_ai.core.threading.base_worker import MedScanWorker
        print("✅ Base worker import successful")
        
        from src.medscan_ai.core.threading.dicom_worker import DicomLoadingWorker
        print("✅ DICOM worker import successful")
        
        from src.medscan_ai.core.threading.inference_worker import AIInferenceWorker
        print("✅ AI inference worker import successful")
        
        from src.medscan_ai.core.threading.batch_worker import BatchProcessingWorker
        print("✅ Batch processing worker import successful")
        
        from src.medscan_ai.core.threading.image_worker import ImageProcessingWorker
        print("✅ Image processing worker import successful")
        
        from src.medscan_ai.gui.core.worker_manager import WorkerManager
        print("✅ Worker manager import successful")
        
        print("✅ All worker components available!")
        return True
        
    except Exception as e:
        print(f"❌ Worker components test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all threading integration tests."""
    print("🧪 MedScan AI Threading Integration Test Suite")
    print("=" * 50)
    
    # Create QApplication with minimal configuration
    app = QApplication([])
    app.setQuitOnLastWindowClosed(True)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Worker Components
    if test_worker_components():
        tests_passed += 1
    
    # Test 2: Threading Integration
    if test_threading_integration():
        tests_passed += 1
    
    # Quit application
    app.quit()
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All threading integration tests PASSED!")
        print("\n✅ Threading Phase 2 (Main Window Integration) COMPLETED!")
        return True
    else:
        print("⚠️ Some threading integration tests FAILED!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 