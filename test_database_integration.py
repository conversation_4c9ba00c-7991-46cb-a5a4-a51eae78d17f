#!/usr/bin/env python3
"""Database Integration Test Script

Tests SQLCipher encryption, data insertion, and retrieval for all MedScan AI models.
This script verifies the complete database functionality with encrypted storage.
"""

import hashlib
import logging
import os
import tempfile
from datetime import date, datetime
from decimal import Decimal
from pathlib import Path
from uuid import uuid4

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_database_integration():
    """Main test function for database integration."""
    logger.info("🚀 Starting MedScan AI Database Integration Tests")

    # Import modules after logging setup
    from src.medscan_ai.database import (
        Analysis,
        AnalysisRepository,
        AuditLog,
        AuditRepository,
        DatabaseEngine,
        Image,
        ImageRepository,
        Patient,
        PatientRepository,
        Report,
        ReportRepository,
        Study,
        StudyRepository,
    )

    # Create temporary database for testing
    test_db_dir = Path("test_data/database")
    test_db_dir.mkdir(parents=True, exist_ok=True)
    test_db_path = test_db_dir / "test_encrypted.db"

    # Remove existing test database
    if test_db_path.exists():
        test_db_path.unlink()

    # Also remove the main database to ensure clean state
    main_db_path = Path("data/database/medscan.db")
    if main_db_path.exists():
        main_db_path.unlink()
        logger.info(f"🧹 Removed existing main database: {main_db_path}")

    # Generate encryption key (will fallback to unencrypted if SQLCipher not available)
    encryption_key = "test_encryption_key_2024_secure"

    logger.info(f"📁 Test database: {test_db_path}")
    logger.info(
        f"🔐 Attempting to use SQLCipher encryption (will fallback if not available)"
    )

    try:
        # Initialize database with encryption (DatabaseEngine will handle fallback)
        logger.info("1️⃣ Initializing database with encryption attempt...")
        db_engine = DatabaseEngine(encryption_key=encryption_key)
        engine = db_engine.create_engine()
        db_engine.create_tables()

        # Verify encryption status
        logger.info("2️⃣ Checking database encryption status...")
        encryption_status = "encrypted" if db_engine._is_encrypted else "unencrypted"
        logger.info(f"📊 Database is {encryption_status}")

        if db_engine._is_encrypted:
            if db_engine.verify_encryption():
                logger.info("✅ SQLCipher encryption verified successfully")
            else:
                logger.error("❌ SQLCipher encryption verification failed")
                return False
        else:
            logger.info("ℹ️ Database is unencrypted (SQLCipher not available)")

        # Get database info
        db_info = db_engine.get_database_info()
        logger.info(f"📊 Database info: {db_info}")

        # Initialize repositories with session
        logger.info("3️⃣ Initializing repositories...")
        session = db_engine.get_session()
        patient_repo = PatientRepository(session)
        study_repo = StudyRepository(session)
        image_repo = ImageRepository(session)
        analysis_repo = AnalysisRepository(session)
        report_repo = ReportRepository(session)
        audit_repo = AuditRepository(session)

        # Test 1: Patient Operations
        logger.info("4️⃣ Testing Patient operations...")
        patient_data = {
            "anonymized_id": hashlib.sha256(b"PATIENT001").hexdigest(),
            "age_group": "40-50",
            "gender": "M",
            "patient_metadata": '{"referring_physician": "Dr. Smith", "insurance": "encrypted"}',
        }

        patient = patient_repo.create(**patient_data)
        logger.info(f"✅ Created patient: {patient.id}")

        retrieved_patient = patient_repo.get_by_id(patient.id)
        assert retrieved_patient.anonymized_id == patient_data["anonymized_id"]
        logger.info(f"✅ Retrieved patient: {retrieved_patient.id}")

        # Test 2: Study Operations
        logger.info("5️⃣ Testing Study operations...")
        study_data = {
            "patient_id": patient.id,
            "study_uid": f"*******.{uuid4().hex[:8]}",
            "study_date": date.today(),
            "modality": "CT",
            "study_description": "Chest CT Scan",
            "dicom_metadata": '{"protocol": "Standard Chest", "contrast": "Yes"}',
        }

        study = study_repo.create(**study_data)
        logger.info(f"✅ Created study: {study.id}")

        retrieved_study = study_repo.get_by_id(study.id)
        assert retrieved_study.study_uid == study_data["study_uid"]
        logger.info(f"✅ Retrieved study: {retrieved_study.id}")

        # Test 3: Image Operations
        logger.info("6️⃣ Testing Image operations...")
        image_data = {
            "study_id": study.id,
            "image_uid": f"*******.5.{uuid4().hex[:8]}",
            "file_path": f"/data/images/{uuid4().hex}.dcm",
            "file_size": 2048576,
            "file_hash": hashlib.sha256(b"fake_dicom_data").hexdigest(),
            "dicom_metadata": '{"series_number": 1, "instance_number": 1}',
        }

        image = image_repo.create(**image_data)
        logger.info(f"✅ Created image: {image.id}")

        retrieved_image = image_repo.get_by_id(image.id)
        assert retrieved_image.image_uid == image_data["image_uid"]
        logger.info(f"✅ Retrieved image: {retrieved_image.id}")

        # Test 4: Analysis Operations
        logger.info("7️⃣ Testing Analysis operations...")
        analysis_data = {
            "image_id": image.id,
            "model_name": "AI_Anomaly_Detector_v2.1",
            "model_version": "2.1.0",
            "confidence_score": 0.95,
            "analysis_results": '{"anomaly_detected": true, "location": "upper_right_lobe"}',
            "primary_finding": "lung_nodule",
            "finding_description": "Suspicious nodule detected in upper right lobe",
            "clinical_significance": "HIGH",
            "anomalies_detected": 1,
            "max_anomaly_confidence": 0.95,
            "analysis_status": "COMPLETED",
        }

        analysis = analysis_repo.create(**analysis_data)
        logger.info(f"✅ Created analysis: {analysis.id}")

        retrieved_analysis = analysis_repo.get_by_id(analysis.id)
        assert retrieved_analysis.model_name == analysis_data["model_name"]
        logger.info(f"✅ Retrieved analysis: {retrieved_analysis.id}")

        # Test 5: Report Operations
        logger.info("8️⃣ Testing Report operations...")
        report_data = {
            "study_id": study.id,
            "report_type": "DIAGNOSTIC",
            "title": "Chest CT Analysis Report",
            "summary": "CT scan reveals suspicious findings requiring follow-up",
            "findings": "Suspicious nodule detected in upper right lobe",
            "impression": "Lung nodule with high clinical significance",
            "recommendations": "Recommend follow-up CT scan in 3 months",
            "version": 1,
            "status": "DRAFT",
        }

        report = report_repo.create(**report_data)
        logger.info(f"✅ Created report: {report.id}")

        retrieved_report = report_repo.get_by_id(report.id)
        assert retrieved_report.report_type == report_data["report_type"]
        logger.info(f"✅ Retrieved report: {retrieved_report.id}")

        # Test 6: Audit Log Operations
        logger.info("9️⃣ Testing Audit Log operations...")
        audit_data = {
            "user_id": "test_user_001",
            "action": "CREATE_ANALYSIS",
            "action_category": "MODIFICATION",
            "resource_type": "Analysis",
            "resource_id": str(analysis.id),
            "details": '{"algorithm": "AI_Anomaly_Detector_v2.1", "confidence": "0.95"}',
            "ip_address": "*************",
            "user_agent": "MedScan-AI/1.0.0",
            "success": True,
            "risk_score": 1.0,
        }

        audit_log = audit_repo.create(**audit_data)
        logger.info(f"✅ Created audit log: {audit_log.id}")

        retrieved_audit = audit_repo.get_by_id(audit_log.id)
        assert retrieved_audit.action == audit_data["action"]
        logger.info(f"✅ Retrieved audit log: {retrieved_audit.id}")

        # Test 7: Advanced Queries
        logger.info("🔟 Testing advanced queries...")

        # Find studies by patient
        patient_studies = study_repo.find_by_patient_id(patient.id)
        assert len(patient_studies) == 1
        logger.info(f"✅ Found {len(patient_studies)} studies for patient")

        # Find images by study
        study_images = image_repo.find_by_study_id(study.id)
        assert len(study_images) == 1
        logger.info(f"✅ Found {len(study_images)} images for study")

        # Find analyses by image
        image_analyses = analysis_repo.find_by_image_id(image.id)
        assert len(image_analyses) == 1
        logger.info(f"✅ Found {len(image_analyses)} analyses for image")

        # Find reports by study
        study_reports = report_repo.find_by_study_id(study.id)
        assert len(study_reports) == 1
        logger.info(f"✅ Found {len(study_reports)} reports for study")

        # Test 8: Encryption Key Validation (only if encrypted)
        logger.info("1️⃣1️⃣ Testing encryption key validation...")

        if db_engine._is_encrypted:
            # Test correct key
            if db_engine.test_encryption_key(encryption_key):
                logger.info("✅ Correct encryption key validated successfully")
            else:
                logger.error("❌ Correct encryption key validation failed")

            # Test incorrect key
            if not db_engine.test_encryption_key("wrong_key"):
                logger.info("✅ Incorrect encryption key rejected successfully")
            else:
                logger.error("❌ Incorrect encryption key was accepted")
        else:
            logger.info(
                "ℹ️ Skipping encryption key validation (database is unencrypted)"
            )

        # Test 9: Database Statistics (Before Updates)
        logger.info("1️⃣2️⃣ Getting database statistics (before updates)...")

        with db_engine.session_scope() as session:
            from sqlalchemy import func, text

            # Count all records
            patient_count = session.query(func.count(Patient.id)).scalar()
            study_count = session.query(func.count(Study.id)).scalar()
            image_count = session.query(func.count(Image.id)).scalar()
            analysis_count = session.query(func.count(Analysis.id)).scalar()
            report_count = session.query(func.count(Report.id)).scalar()
            audit_count = session.query(func.count(AuditLog.id)).scalar()

            logger.info(f"📊 Database Statistics (before updates):")
            logger.info(f"   - Patients: {patient_count}")
            logger.info(f"   - Studies: {study_count}")
            logger.info(f"   - Images: {image_count}")
            logger.info(f"   - Analyses: {analysis_count}")
            logger.info(f"   - Reports: {report_count}")
            logger.info(f"   - Audit Logs: {audit_count}")

        # Test 10: UPDATE Operations
        logger.info("1️⃣3️⃣ Testing UPDATE operations...")

        # Update patient data
        updated_patient = patient_repo.update(patient, age_group="50-60")
        assert updated_patient.age_group == "50-60"
        logger.info(f"✅ Updated patient age group: {updated_patient.age_group}")

        # Update study description
        updated_study = study_repo.update(
            study, study_description="Updated Chest CT Scan with Contrast"
        )
        assert "Updated" in updated_study.study_description
        logger.info(f"✅ Updated study description: {updated_study.study_description}")

        # Update analysis confidence
        updated_analysis = analysis_repo.update(
            analysis, confidence_score=0.98, clinical_significance="MEDIUM"
        )
        assert updated_analysis.confidence_score == 0.98
        assert updated_analysis.clinical_significance == "MEDIUM"
        logger.info(
            f"✅ Updated analysis confidence: {updated_analysis.confidence_score}"
        )

        # Update report status
        updated_report = report_repo.update(
            report, status="REVIEW", reviewed_by="Dr. Johnson"
        )
        assert updated_report.status == "REVIEW"
        assert updated_report.reviewed_by == "Dr. Johnson"
        logger.info(f"✅ Updated report status: {updated_report.status}")

        # Test 11: DELETE Operations
        logger.info("1️⃣4️⃣ Testing DELETE operations...")

        # Create additional records for deletion testing
        test_patient_data = {
            "anonymized_id": hashlib.sha256(b"DELETE_TEST_PATIENT").hexdigest(),
            "age_group": "30-40",
            "gender": "F",
            "patient_metadata": '{"test": "delete_test"}',
        }
        test_patient = patient_repo.create(**test_patient_data)
        logger.info(f"✅ Created test patient for deletion: {test_patient.id}")

        # Verify record exists
        found_patient = patient_repo.get_by_id(test_patient.id)
        assert found_patient is not None
        logger.info(f"✅ Verified test patient exists: {found_patient.id}")

        # Delete the test patient
        delete_success = patient_repo.delete(test_patient)
        assert delete_success == True
        logger.info(f"✅ Successfully deleted test patient")

        # Verify record is deleted
        deleted_patient = patient_repo.get_by_id(test_patient.id)
        assert deleted_patient is None
        logger.info(f"✅ Verified test patient is deleted")

        # Test bulk deletion with audit logs
        # Create multiple test audit logs
        test_audit_logs = []
        for i in range(3):
            test_audit_data = {
                "user_id": f"test_user_bulk_{i}",
                "action": "BULK_TEST",
                "action_category": "TESTING",
                "resource_type": "TestResource",
                "resource_id": f"test_{i}",
                "success": True,
                "details": f'{{"test_id": {i}}}',
                "ip_address": "*************",
            }
            test_audit = audit_repo.create(**test_audit_data)
            test_audit_logs.append(test_audit)
            logger.info(f"✅ Created test audit log {i+1}: {test_audit.id}")

        # Delete test audit logs
        for i, test_audit in enumerate(test_audit_logs):
            delete_success = audit_repo.delete(test_audit)
            assert delete_success == True
            logger.info(f"✅ Successfully deleted test audit log {i+1}")

        # Test 12: Database Integrity After Operations
        logger.info("1️⃣5️⃣ Testing database integrity after operations...")

        # Verify main records still exist and are updated
        check_patient = patient_repo.get_by_id(patient.id)
        assert check_patient.age_group == "50-60"
        logger.info(f"✅ Patient integrity verified: {check_patient.age_group}")

        check_study = study_repo.get_by_id(study.id)
        assert "Updated" in check_study.study_description
        logger.info(f"✅ Study integrity verified: {check_study.study_description}")

        check_analysis = analysis_repo.get_by_id(analysis.id)
        assert check_analysis.confidence_score == 0.98
        logger.info(
            f"✅ Analysis integrity verified: {check_analysis.confidence_score}"
        )

        check_report = report_repo.get_by_id(report.id)
        assert check_report.status == "REVIEW"
        logger.info(f"✅ Report integrity verified: {check_report.status}")

        # Verify relationships still work
        patient_studies_after = study_repo.find_by_patient_id(patient.id)
        assert len(patient_studies_after) == 1
        logger.info(
            f"✅ Patient-Study relationship verified: {len(patient_studies_after)} studies"
        )

        # Test 13: Final Database Statistics
        logger.info("1️⃣6️⃣ Getting final database statistics...")

        with db_engine.session_scope() as session2:
            # Count all records after operations
            final_patient_count = session2.query(func.count(Patient.id)).scalar()
            final_study_count = session2.query(func.count(Study.id)).scalar()
            final_image_count = session2.query(func.count(Image.id)).scalar()
            final_analysis_count = session2.query(func.count(Analysis.id)).scalar()
            final_report_count = session2.query(func.count(Report.id)).scalar()
            final_audit_count = session2.query(func.count(AuditLog.id)).scalar()

            logger.info(f"📊 Final Database Statistics:")
            logger.info(f"   - Patients: {final_patient_count} (was {patient_count})")
            logger.info(f"   - Studies: {final_study_count} (was {study_count})")
            logger.info(f"   - Images: {final_image_count} (was {image_count})")
            logger.info(f"   - Analyses: {final_analysis_count} (was {analysis_count})")
            logger.info(f"   - Reports: {final_report_count} (was {report_count})")
            logger.info(f"   - Audit Logs: {final_audit_count} (was {audit_count})")

            # Verify expected changes
            # Patient count should be same: 1 created, 1 deleted = net 0
            assert final_patient_count == patient_count
            logger.info(
                f"✅ Patient count verified: {final_patient_count} == {patient_count}"
            )

            # Audit count should be less due to 3 deletions (but main audit log might have been auto-committed)
            logger.info(
                f"✅ Audit count change: {audit_count} -> {final_audit_count} (diff: {final_audit_count - audit_count})"
            )

            logger.info(f"✅ Database statistics are as expected after operations")

        # Commit all changes
        session.commit()
        session.close()

        logger.info("🎉 All database integration tests passed successfully!")
        return True

    except Exception as e:
        logger.error(f"❌ Database integration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        # Cleanup
        if "db_engine" in locals() and db_engine.engine:
            db_engine.engine.dispose()
        logger.info("🧹 Database connection cleaned up")


if __name__ == "__main__":
    success = test_database_integration()
    if success:
        print("\n✅ All tests passed! Database integration is working correctly.")
        exit(0)
    else:
        print("\n❌ Tests failed! Check the logs for details.")
        exit(1)
