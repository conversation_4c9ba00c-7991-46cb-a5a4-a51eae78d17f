#!/usr/bin/env python3
"""
Simple Memory and Performance Profiler for MedScan AI

Basic profiling tool to identify performance bottlenecks without complex dependencies.
Updated to test lazy loading improvements.
"""

import os
import sys
import time
import psutil
import tempfile
from pathlib import Path
import tracemalloc

def get_system_info():
    """Get basic system information"""
    memory = psutil.virtual_memory()
    cpu_count = psutil.cpu_count()
    
    return {
        'cpu_cores': cpu_count,
        'total_memory_gb': memory.total / (1024**3),
        'available_memory_gb': memory.available / (1024**3),
        'memory_percent': memory.percent,
        'python_version': sys.version.split()[0],
        'platform': sys.platform
    }

def profile_import_times():
    """Profile import times for major MedScan AI modules"""
    import_tests = [
        ('numpy', 'import numpy as np'),
        ('cv2', 'import cv2'),
        ('PySide6', 'import PySide6'),
        ('pydicom', 'import pydicom'),
        ('medscan_ai.dicom (NEW LAZY)', 'from medscan_ai.dicom import LazyDicomReader'),
        ('medscan_ai.ai (NEW LAZY)', 'from medscan_ai.ai import ModelLoader'),
        ('medscan_ai.dicom (OLD)', 'from medscan_ai.dicom import DicomReader'),
    ]
    
    results = {}
    
    for module_name, import_statement in import_tests:
        print(f"Testing import: {module_name}")
        
        # Measure memory before import
        tracemalloc.start()
        process = psutil.Process()
        memory_before = process.memory_info().rss
        
        # Time the import
        start_time = time.time()
        try:
            exec(import_statement)
            end_time = time.time()
            import_time = end_time - start_time
            
            # Measure memory after import
            memory_after = process.memory_info().rss
            memory_used = memory_after - memory_before
            
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            results[module_name] = {
                'import_time': import_time,
                'memory_used_mb': memory_used / (1024 * 1024),
                'tracemalloc_current_mb': current / (1024 * 1024),
                'tracemalloc_peak_mb': peak / (1024 * 1024),
                'status': 'success'
            }
            
        except Exception as e:
            results[module_name] = {
                'import_time': 0,
                'memory_used_mb': 0,
                'tracemalloc_current_mb': 0,
                'tracemalloc_peak_mb': 0,
                'status': f'failed: {e}',
                'error': str(e)
            }
            tracemalloc.stop()
    
    return results

def profile_file_operations():
    """Profile basic file I/O operations"""
    results = {}
    
    # Test small file operations
    small_data = b'x' * (1024 * 1024)  # 1MB
    with tempfile.NamedTemporaryFile(delete=False) as tf:
        temp_path = tf.name
        
        # Write test
        start_time = time.time()
        tf.write(small_data)
        tf.flush()
        write_time = time.time() - start_time
        
        # Calculate write speed
        write_speed = len(small_data) / (1024 * 1024) / max(write_time, 0.001)  # MB/s
        
        results['small_file_write'] = {
            'size_mb': len(small_data) / (1024 * 1024),
            'time_seconds': write_time,
            'speed_mbps': write_speed
        }
    
    # Clean up
    try:
        os.unlink(temp_path)
    except:
        pass
    
    return results

def profile_numpy_operations():
    """Profile basic NumPy operations"""
    import numpy as np
    
    results = {}
    
    # Test array creation and basic operations
    sizes = [1000000, 10000000]  # 1M, 10M elements
    
    for size in sizes:
        # Array creation
        start_time = time.time()
        arr = np.random.rand(size).astype(np.float32)
        creation_time = time.time() - start_time
        
        # Basic operations
        start_time = time.time()
        result = np.sqrt(arr) * 2.0 + 1.0
        operation_time = time.time() - start_time
        
        # Calculate operations per second
        ops_per_sec = size / max(operation_time, 0.001)
        
        results[f'numpy_{size//1000000}M'] = {
            'creation_time': creation_time,
            'operation_time': operation_time,
            'ops_per_sec': ops_per_sec,
            'memory_mb': arr.nbytes / (1024 * 1024)
        }
    
    return results

def test_lazy_loading_benefits():
    """Test the benefits of lazy loading implementation"""
    print("\n=== TESTING LAZY LOADING BENEFITS ===")
    
    results = {}
    
    # Test 1: Import time comparison
    print("Testing import time improvements...")
    
    # Test new lazy imports
    start_time = time.time()
    try:
        from medscan_ai.ai.models import LazyModelLoader
        lazy_import_time = time.time() - start_time
        
        # Check if TensorFlow is actually imported
        loader = LazyModelLoader()
        tf_imported = hasattr(loader._LazyModelLoader__dict__.get('_tf_importer', {}), '_tf') and loader._LazyModelLoader__dict__.get('_tf_importer', {})._tf is not None
        
        results['lazy_model_loader'] = {
            'import_time': lazy_import_time,
            'tensorflow_imported': tf_imported,
            'status': 'success'
        }
        
    except Exception as e:
        results['lazy_model_loader'] = {
            'import_time': 0,
            'tensorflow_imported': False,
            'status': f'failed: {e}'
        }
    
    # Test 2: Memory usage before TensorFlow access
    process = psutil.Process()
    memory_before_tf = process.memory_info().rss / (1024 * 1024)
    
    try:
        # Access TensorFlow through lazy loader
        if 'lazy_model_loader' in results and results['lazy_model_loader']['status'] == 'success':
            start_time = time.time()
            
            # This should trigger TensorFlow import
            loader.is_tensorflow_ready()
            
            tf_access_time = time.time() - start_time
            memory_after_tf = process.memory_info().rss / (1024 * 1024)
            
            results['tensorflow_lazy_access'] = {
                'access_time': tf_access_time,
                'memory_before_mb': memory_before_tf,
                'memory_after_mb': memory_after_tf,
                'memory_increase_mb': memory_after_tf - memory_before_tf
            }
            
    except Exception as e:
        results['tensorflow_lazy_access'] = {
            'status': f'failed: {e}'
        }
    
    return results

def main():
    """Main profiling function"""
    print("MedScan AI Performance Profiler - Lazy Loading Test")
    print("=" * 60)
    
    # System info
    system_info = get_system_info()
    print(f"System: {system_info['cpu_cores']} cores, "
          f"{system_info['total_memory_gb']:.1f}GB RAM, "
          f"{system_info['platform']}")
    print()
    
    # Test import times
    print("=== IMPORT PERFORMANCE TESTS ===")
    import_results = profile_import_times()
    
    for module, data in import_results.items():
        if data['status'] == 'success':
            print(f"{module:30} | {data['import_time']:7.3f}s | {data['memory_used_mb']:7.1f}MB")
        else:
            print(f"{module:30} | FAILED: {data.get('error', 'Unknown error')}")
    
    # Test file operations
    print("\n=== FILE I/O PERFORMANCE ===")
    file_results = profile_file_operations()
    
    for test, data in file_results.items():
        print(f"{test:20} | {data['time_seconds']:7.3f}s | {data['speed_mbps']:7.1f} MB/s")
    
    # Test NumPy operations
    print("\n=== NUMPY PERFORMANCE ===")
    numpy_results = profile_numpy_operations()
    
    for test, data in numpy_results.items():
        print(f"{test:20} | {data['ops_per_sec']/1000000:7.1f}M ops/sec | {data['memory_mb']:7.1f}MB")
    
    # Test lazy loading benefits
    lazy_results = test_lazy_loading_benefits()
    
    if lazy_results:
        print("\n=== LAZY LOADING RESULTS ===")
        for test, data in lazy_results.items():
            if 'import_time' in data:
                print(f"{test:30} | {data['import_time']:7.3f}s | TF imported: {data.get('tensorflow_imported', False)}")
            elif 'access_time' in data:
                print(f"{test:30} | {data['access_time']:7.3f}s | Memory +{data.get('memory_increase_mb', 0):.1f}MB")
    
    # Summary
    print("\n=== PERFORMANCE SUMMARY ===")
    
    # Find the slowest imports
    slow_imports = [(name, data['import_time']) for name, data in import_results.items() 
                   if data['status'] == 'success' and data['import_time'] > 0.1]
    slow_imports.sort(key=lambda x: x[1], reverse=True)
    
    if slow_imports:
        print("Slowest imports:")
        for name, time_taken in slow_imports[:3]:
            print(f"  {name}: {time_taken:.3f}s")
    
    # Memory efficiency
    print(f"\nMemory efficiency: {numpy_results.get('numpy_1M', {}).get('ops_per_sec', 0)/1000000:.0f}M pixels/sec processing")
    
    # Lazy loading benefits
    if 'lazy_model_loader' in lazy_results and lazy_results['lazy_model_loader'].get('import_time', 0) < 0.1:
        print("✅ LAZY LOADING SUCCESS: Fast import without TensorFlow initialization")
    else:
        print("⚠️  LAZY LOADING ISSUE: Import still slow or failed")

if __name__ == "__main__":
    main() 