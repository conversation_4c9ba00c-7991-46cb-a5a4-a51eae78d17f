#!/usr/bin/env python3
"""
Memory profiling script for DICOM loading and image processing.
Tests current implementation to identify memory bottlenecks.
"""

import gc
import os
import sys
import time
import tracemalloc
from pathlib import Path
from typing import Dict, List, Tuple

import numpy as np
import psutil

# Add project to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from medscan_ai.dicom.reader import DicomReader
from medscan_ai.gui.utils import InteractiveImageViewer, numpy_to_qpixmap


class MemoryProfiler:
    """Memory profiling utility for DICOM operations."""

    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = self.get_memory_usage()

    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage in MB."""
        memory_info = self.process.memory_info()
        return {
            "rss": memory_info.rss / 1024 / 1024,  # MB
            "vms": memory_info.vms / 1024 / 1024,  # MB
            "percent": self.process.memory_percent(),
        }

    def profile_operation(self, operation_name: str, operation_func):
        """Profile a specific operation."""
        print(f"\n{'='*60}")
        print(f"PROFILING: {operation_name}")
        print(f"{'='*60}")

        # Force garbage collection before measurement
        gc.collect()

        # Start memory tracing
        tracemalloc.start()
        start_memory = self.get_memory_usage()
        start_time = time.time()

        try:
            # Execute operation
            result = operation_func()

            # Measure after operation
            end_time = time.time()
            end_memory = self.get_memory_usage()
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            # Calculate differences
            memory_delta = {
                "rss_delta": end_memory["rss"] - start_memory["rss"],
                "vms_delta": end_memory["vms"] - start_memory["vms"],
                "percent_delta": end_memory["percent"] - start_memory["percent"],
            }

            # Report results
            print(f"Operation time: {end_time - start_time:.3f} seconds")
            print(
                f"Memory before: RSS={start_memory['rss']:.1f}MB, VMS={start_memory['vms']:.1f}MB"
            )
            print(
                f"Memory after:  RSS={end_memory['rss']:.1f}MB, VMS={end_memory['vms']:.1f}MB"
            )
            print(
                f"Memory delta:  RSS={memory_delta['rss_delta']:.1f}MB, VMS={memory_delta['vms_delta']:.1f}MB"
            )
            print(
                f"Memory percent: {end_memory['percent']:.2f}% ({memory_delta['percent_delta']:+.2f}%)"
            )
            print(f"Tracemalloc current: {current / 1024 / 1024:.1f}MB")
            print(f"Tracemalloc peak: {peak / 1024 / 1024:.1f}MB")

            return result, {
                "time": end_time - start_time,
                "memory_start": start_memory,
                "memory_end": end_memory,
                "memory_delta": memory_delta,
                "tracemalloc_current": current,
                "tracemalloc_peak": peak,
            }

        except Exception as e:
            tracemalloc.stop()
            print(f"ERROR: {e}")
            return None, None


def create_synthetic_dicom_data() -> np.ndarray:
    """Create synthetic image data of various sizes for testing."""
    sizes = [
        (512, 512),  # Small image - ~0.25MB
        (1024, 1024),  # Medium image - ~1MB
        (2048, 2048),  # Large image - ~4MB
        (4096, 4096),  # Very large image - ~16MB
        (8192, 8192),  # Huge image - ~64MB
    ]

    datasets = {}

    for size in sizes:
        # Create random 16-bit image data
        data = np.random.randint(0, 65535, size, dtype=np.uint16)
        size_mb = data.nbytes / 1024 / 1024
        datasets[f"{size[0]}x{size[1]} ({size_mb:.1f}MB)"] = data

    return datasets


def test_dicom_reader_operations():
    """Test DICOM reader memory usage."""
    profiler = MemoryProfiler()

    # Test with synthetic data since we may not have real DICOM files
    synthetic_datasets = create_synthetic_dicom_data()

    print("TESTING DICOM READER OPERATIONS")
    print(f"Initial system memory: {profiler.initial_memory}")

    results = {}

    for name, data in synthetic_datasets.items():
        print(f"\nTesting with {name} data...")

        # Test: Raw array processing
        def process_raw_array():
            # Simulate DICOM reader operations
            # 1. Apply modality transforms (multiply/add)
            processed = data.astype(np.float32)
            processed = processed * 1.0 + 0.0  # RescaleSlope + RescaleIntercept

            # 2. Apply windowing
            window_center = 32767
            window_width = 65535
            windowed = np.clip(
                (processed - (window_center - window_width / 2)) / window_width * 255,
                0,
                255,
            )

            # 3. Convert to display format
            display_array = windowed.astype(np.uint8)

            return display_array

        result, stats = profiler.profile_operation(
            f"Raw array processing - {name}", process_raw_array
        )

        if stats:
            results[f"raw_processing_{name}"] = stats

        # Test: QPixmap conversion
        if result is not None:

            def convert_to_qpixmap():
                return numpy_to_qpixmap(result)

            pixmap, stats = profiler.profile_operation(
                f"QPixmap conversion - {name}", convert_to_qpixmap
            )

            if stats:
                results[f"qpixmap_conversion_{name}"] = stats

        # Force cleanup
        if result is not None:
            del result
        if "pixmap" in locals():
            del pixmap
        gc.collect()

    return results


def test_viewer_operations():
    """Test image viewer memory usage."""
    profiler = MemoryProfiler()

    print("\n\nTESTING VIEWER OPERATIONS")

    # Create test data
    test_data = np.random.randint(0, 255, (2048, 2048), dtype=np.uint8)

    results = {}

    # Test: Multiple zoom operations
    def simulate_zoom_operations():
        pixmap = numpy_to_qpixmap(test_data)

        # Simulate multiple zoom operations
        zoom_factors = [0.5, 2.0, 4.0, 0.25, 1.0]
        scaled_pixmaps = []

        for factor in zoom_factors:
            width = int(pixmap.width() * factor)
            height = int(pixmap.height() * factor)
            scaled = pixmap.scaled(width, height)
            scaled_pixmaps.append(scaled)

        return scaled_pixmaps

    result, stats = profiler.profile_operation(
        "Multiple zoom operations (2048x2048)", simulate_zoom_operations
    )

    if stats:
        results["zoom_operations"] = stats

    return results


def generate_memory_report(results: Dict) -> None:
    """Generate comprehensive memory usage report."""
    print(f"\n{'='*80}")
    print("MEMORY PROFILING SUMMARY REPORT")
    print(f"{'='*80}")

    # Sort by memory usage
    sorted_results = sorted(
        results.items(), key=lambda x: x[1]["memory_delta"]["rss_delta"], reverse=True
    )

    print(f"{'Operation':<50} {'Time(s)':<10} {'Memory(MB)':<12} {'Peak(MB)':<10}")
    print(f"{'-'*50} {'-'*10} {'-'*12} {'-'*10}")

    total_memory_usage = 0
    total_time = 0

    for operation, stats in sorted_results:
        memory_delta = stats["memory_delta"]["rss_delta"]
        peak_memory = stats["tracemalloc_peak"] / 1024 / 1024
        time_taken = stats["time"]

        total_memory_usage += memory_delta
        total_time += time_taken

        print(
            f"{operation:<50} {time_taken:<10.3f} {memory_delta:<12.1f} {peak_memory:<10.1f}"
        )

    print(f"{'-'*50} {'-'*10} {'-'*12} {'-'*10}")
    print(f"{'TOTAL':<50} {total_time:<10.3f} {total_memory_usage:<12.1f}")

    # Memory efficiency analysis
    print(f"\nMEMORY EFFICIENCY ANALYSIS:")
    print(f"• Total memory consumed: {total_memory_usage:.1f} MB")
    print(f"• Average memory per operation: {total_memory_usage/len(results):.1f} MB")
    print(f"• Most memory-intensive operation: {sorted_results[0][0]}")
    print(f"• Least memory-intensive operation: {sorted_results[-1][0]}")

    # Performance recommendations
    print(f"\nPERFORMANCE RECOMMENDATIONS:")

    high_memory_ops = [
        op for op, stats in results.items() if stats["memory_delta"]["rss_delta"] > 50
    ]  # >50MB

    if high_memory_ops:
        print(f"• HIGH MEMORY OPERATIONS (>50MB):")
        for op in high_memory_ops:
            print(f"  - {op}: {results[op]['memory_delta']['rss_delta']:.1f}MB")
        print(f"  → Consider implementing lazy loading or memory mapping")

    slow_ops = [op for op, stats in results.items() if stats["time"] > 1.0]  # >1 second

    if slow_ops:
        print(f"• SLOW OPERATIONS (>1s):")
        for op in slow_ops:
            print(f"  - {op}: {results[op]['time']:.3f}s")
        print(f"  → Consider optimization or background processing")

    print(f"\nOPTIMIZATION TARGETS:")
    print(f"• Memory mapping candidates: Large image processing operations")
    print(f"• Lazy loading candidates: QPixmap conversion operations")
    print(f"• Caching candidates: Frequent zoom/scale operations")


def main():
    """Main profiling execution."""
    print("MEDSCAN AI - MEMORY PROFILING ANALYSIS")
    print("=" * 80)

    # Test DICOM operations
    dicom_results = test_dicom_reader_operations()

    # Test viewer operations
    viewer_results = test_viewer_operations()

    # Combine results
    all_results = {**dicom_results, **viewer_results}

    # Generate report
    generate_memory_report(all_results)

    print(f"\nPROFILING COMPLETE!")


if __name__ == "__main__":
    main()
