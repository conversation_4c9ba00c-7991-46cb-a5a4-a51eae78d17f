"""Basic RBAC functionality test for MedScan AI."""

from ...database.models import Permission, Role, RolePermission, UserR<PERSON>


def test_rbac_models():
    """Test RBAC model creation."""
    # Test Role model
    role = Role(
        name="Technician",
        display_name="Medical Technician",
        description="Medical technician role",
        hierarchy_level=5,
    )

    # Test Permission model
    permission = Permission(
        name="view_patient_data",
        display_name="View Patient Data",
        category="Patient",
        resource_type="Patient",
        action="read",
    )

    print("RBAC models created successfully!")
    return True


if __name__ == "__main__":
    test_rbac_models()
