"""
Multi-Factor Authentication (MFA) Security Tests for MedScan AI
Tests focused on MFA mechanisms and security

Extracted from the original auth_security_tests.py file for better modularity.
"""

import time
import secrets
from typing import Dict, List, Any
from . import (
    SecurityTestResult, MockDatabaseSession, logger,
    MFA_SERVICE_AVAILABLE
)

if MFA_SERVICE_AVAILABLE:
    from medscan_ai.security.authentication.mfa_service import MFAService


class MFASecurityTests:
    """
    Multi-Factor Authentication security tests
    """

    def __init__(self):
        """Initialize MFA security testing"""
        self.test_results: List[SecurityTestResult] = []
        self.available_services = []
        
        # Mock database session
        mock_session = MockDatabaseSession()
        
        # Initialize MFA service with error handling
        self.mfa_service = None
        
        if MFA_SERVICE_AVAILABLE:
            try:
                self.mfa_service = MFAService(db_session=mock_session)
                self.available_services.append('mfa_service')
                logger.info("✅ MFAService initialized")
            except Exception as e:
                logger.warning(f"❌ MFAService failed: {e}")

    def run_mfa_tests(self) -> List[SecurityTestResult]:
        """
        Run all MFA security tests
        
        Returns:
            List of test results
        """
        logger.info("Starting MFA security testing")
        
        # Clear previous results
        self.test_results = []
        
        # Run tests based on available services
        if 'mfa_service' in self.available_services:
            self._test_mfa_security()
            self._test_totp_security()
            self._test_backup_codes()
            self._test_mfa_bypass_protection()
        else:
            self._test_mfa_service_availability()
        
        return self.test_results

    def _test_mfa_service_availability(self):
        """Test MFA service availability"""
        logger.info("Testing MFA service availability")
        
        self.test_results.append(
            SecurityTestResult(
                test_name="MFA Service Availability",
                passed=False,
                details="MFA service not available for testing",
                severity="high",
                recommendations=["Initialize MFA service for enhanced security"]
            )
        )

    def _test_mfa_security(self):
        """Test Multi-Factor Authentication security"""
        logger.info("Testing MFA security")
        
        if not self.mfa_service:
            return
        
        # Test 1: MFA enrollment process
        try:
            test_user_id = f"test_user_{int(time.time())}"
            
            # Test MFA enrollment
            if hasattr(self.mfa_service, 'enroll_user'):
                enrollment_result = self.mfa_service.enroll_user(test_user_id)
                
                if enrollment_result and 'secret' in enrollment_result:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="MFA Enrollment Process",
                            passed=True,
                            details="MFA enrollment working correctly",
                            severity="high"
                        )
                    )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="MFA Enrollment Process",
                            passed=False,
                            details="MFA enrollment returned invalid result",
                            severity="high",
                            recommendations=["Fix MFA enrollment process"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="MFA Enrollment Process",
                        passed=False,
                        details="MFA enrollment method not available",
                        severity="medium",
                        recommendations=["Implement MFA enrollment functionality"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="MFA Enrollment Process",
                    passed=False,
                    details=f"MFA enrollment test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix MFA enrollment implementation"]
                )
            )
        
        # Test 2: MFA verification process
        try:
            test_user_id = f"test_user_verify_{int(time.time())}"
            test_code = "123456"  # Test code
            
            if hasattr(self.mfa_service, 'verify_code'):
                # Test with invalid code (should fail)
                verification_result = self.mfa_service.verify_code(test_user_id, test_code)
                
                if not verification_result:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="MFA Verification Security",
                            passed=True,
                            details="MFA correctly rejected invalid code",
                            severity="high"
                        )
                    )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="MFA Verification Security",
                            passed=False,
                            details="MFA incorrectly accepted invalid code",
                            severity="critical",
                            recommendations=["Fix MFA verification logic"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="MFA Verification Security",
                        passed=False,
                        details="MFA verification method not available",
                        severity="high",
                        recommendations=["Implement MFA verification functionality"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="MFA Verification Security",
                    passed=False,
                    details=f"MFA verification test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix MFA verification implementation"]
                )
            )

    def _test_totp_security(self):
        """Test Time-based One-Time Password (TOTP) security"""
        logger.info("Testing TOTP security")
        
        if not self.mfa_service:
            return
        
        # Test TOTP code generation and validation
        try:
            test_secret = secrets.token_hex(16)
            
            if hasattr(self.mfa_service, 'generate_totp'):
                # Generate TOTP code
                totp_code = self.mfa_service.generate_totp(test_secret)
                
                if totp_code and len(str(totp_code)) == 6:
                    # Test code validation
                    if hasattr(self.mfa_service, 'validate_totp'):
                        is_valid = self.mfa_service.validate_totp(test_secret, totp_code)
                        
                        if is_valid:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="TOTP Generation and Validation",
                                    passed=True,
                                    details="TOTP generation and validation working correctly",
                                    severity="high"
                                )
                            )
                        else:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="TOTP Generation and Validation",
                                    passed=False,
                                    details="TOTP validation failed for valid code",
                                    severity="high",
                                    recommendations=["Fix TOTP validation logic"]
                                )
                            )
                    else:
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="TOTP Generation and Validation",
                                passed=False,
                                details="TOTP validation method not available",
                                severity="medium",
                                recommendations=["Implement TOTP validation"]
                            )
                        )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="TOTP Generation and Validation",
                            passed=False,
                            details="TOTP generation returned invalid code format",
                            severity="high",
                            recommendations=["Fix TOTP generation"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="TOTP Generation and Validation",
                        passed=False,
                        details="TOTP generation method not available",
                        severity="medium",
                        recommendations=["Implement TOTP functionality"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="TOTP Generation and Validation",
                    passed=False,
                    details=f"TOTP security test failed: {str(e)}",
                    severity="high",
                    recommendations=["Fix TOTP implementation"]
                )
            )
        
        # Test TOTP time window security
        try:
            # Test that old TOTP codes are rejected
            if hasattr(self.mfa_service, 'validate_totp_with_window'):
                old_code = "123456"  # Simulated old code
                
                is_valid = self.mfa_service.validate_totp_with_window(test_secret, old_code, time_window=-300)  # 5 minutes ago
                
                if not is_valid:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="TOTP Time Window Security",
                            passed=True,
                            details="TOTP correctly rejects old codes",
                            severity="medium"
                        )
                    )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="TOTP Time Window Security",
                            passed=False,
                            details="TOTP incorrectly accepts old codes",
                            severity="medium",
                            recommendations=["Implement proper TOTP time window validation"]
                        )
                    )
                    
        except Exception as e:
            logger.warning(f"TOTP time window test error: {e}")

    def _test_backup_codes(self):
        """Test backup codes functionality"""
        logger.info("Testing backup codes")
        
        if not self.mfa_service:
            return
        
        # Test backup code generation
        try:
            test_user_id = f"test_user_backup_{int(time.time())}"
            
            if hasattr(self.mfa_service, 'generate_backup_codes'):
                backup_codes = self.mfa_service.generate_backup_codes(test_user_id)
                
                if backup_codes and len(backup_codes) >= 8:  # Should generate at least 8 backup codes
                    # Test backup code validation
                    if hasattr(self.mfa_service, 'verify_backup_code'):
                        test_code = backup_codes[0]
                        
                        is_valid = self.mfa_service.verify_backup_code(test_user_id, test_code)
                        
                        if is_valid:
                            # Test that the same code can't be used twice
                            is_valid_again = self.mfa_service.verify_backup_code(test_user_id, test_code)
                            
                            if not is_valid_again:
                                self.test_results.append(
                                    SecurityTestResult(
                                        test_name="Backup Codes Security",
                                        passed=True,
                                        details="Backup codes working correctly with one-time use enforced",
                                        severity="high"
                                    )
                                )
                            else:
                                self.test_results.append(
                                    SecurityTestResult(
                                        test_name="Backup Codes Security",
                                        passed=False,
                                        details="Backup codes can be reused (security vulnerability)",
                                        severity="critical",
                                        recommendations=["Implement one-time use for backup codes"]
                                    )
                                )
                        else:
                            self.test_results.append(
                                SecurityTestResult(
                                    test_name="Backup Codes Security",
                                    passed=False,
                                    details="Valid backup code was rejected",
                                    severity="high",
                                    recommendations=["Fix backup code validation"]
                                )
                            )
                    else:
                        self.test_results.append(
                            SecurityTestResult(
                                test_name="Backup Codes Security",
                                passed=False,
                                details="Backup code verification method not available",
                                severity="medium",
                                recommendations=["Implement backup code verification"]
                            )
                        )
                else:
                    self.test_results.append(
                        SecurityTestResult(
                            test_name="Backup Codes Security",
                            passed=False,
                            details=f"Insufficient backup codes generated: {len(backup_codes) if backup_codes else 0}",
                            severity="medium",
                            recommendations=["Generate at least 8 backup codes per user"]
                        )
                    )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="Backup Codes Security",
                        passed=False,
                        details="Backup code generation method not available",
                        severity="medium",
                        recommendations=["Implement backup code functionality"]
                    )
                )
                
        except Exception as e:
            self.test_results.append(
                SecurityTestResult(
                    test_name="Backup Codes Security",
                    passed=False,
                    details=f"Backup codes test failed: {str(e)}",
                    severity="medium",
                    recommendations=["Fix backup codes implementation"]
                )
            )

    def _test_mfa_bypass_protection(self):
        """Test MFA bypass protection"""
        logger.info("Testing MFA bypass protection")
        
        if not self.mfa_service:
            return
        
        # Test common MFA bypass attempts
        bypass_attempts = [
            {"user_id": "admin", "code": "000000"},
            {"user_id": "test_user", "code": "123456"},
            {"user_id": "test_user", "code": ""},
            {"user_id": "test_user", "code": None},
            {"user_id": "", "code": "123456"},
            {"user_id": None, "code": "123456"},
            {"user_id": "test_user", "code": "999999"},
        ]
        
        bypass_blocked = 0
        
        for attempt in bypass_attempts:
            try:
                if hasattr(self.mfa_service, 'verify_code'):
                    result = self.mfa_service.verify_code(
                        attempt["user_id"], 
                        attempt["code"]
                    )
                    
                    # All of these should be blocked (return False)
                    if not result:
                        bypass_blocked += 1
                        
            except Exception as e:
                # Exceptions are generally good - they indicate the bypass was caught
                bypass_blocked += 1
        
        if bypass_blocked >= len(bypass_attempts) * 0.9:  # At least 90% blocked
            self.test_results.append(
                SecurityTestResult(
                    test_name="MFA Bypass Protection",
                    passed=True,
                    details=f"MFA bypass protection working: {bypass_blocked}/{len(bypass_attempts)} attempts blocked",
                    severity="high"
                )
            )
        else:
            self.test_results.append(
                SecurityTestResult(
                    test_name="MFA Bypass Protection",
                    passed=False,
                    details=f"MFA bypass vulnerabilities detected: {bypass_blocked}/{len(bypass_attempts)} attempts blocked",
                    severity="critical",
                    recommendations=["Strengthen MFA bypass protection"]
                )
            )
        
        # Test rate limiting on MFA attempts
        try:
            test_user_id = f"test_user_rate_limit_{int(time.time())}"
            failed_attempts = 0
            rate_limited = False
            
            # Try many failed MFA attempts
            for i in range(15):
                try:
                    if hasattr(self.mfa_service, 'verify_code'):
                        result = self.mfa_service.verify_code(test_user_id, "000000")
                        
                        if not result:
                            failed_attempts += 1
                        
                except Exception as e:
                    if "rate limit" in str(e).lower() or "too many" in str(e).lower():
                        rate_limited = True
                        break
            
            if rate_limited or failed_attempts >= 10:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="MFA Rate Limiting",
                        passed=True,
                        details=f"MFA rate limiting active after {failed_attempts} attempts" + (" (rate limited)" if rate_limited else ""),
                        severity="medium"
                    )
                )
            else:
                self.test_results.append(
                    SecurityTestResult(
                        test_name="MFA Rate Limiting",
                        passed=False,
                        details=f"No MFA rate limiting detected after {failed_attempts} attempts",
                        severity="medium",
                        recommendations=["Implement MFA rate limiting"]
                    )
                )
                
        except Exception as e:
            logger.warning(f"MFA rate limiting test error: {e}")


def run_mfa_security_tests() -> List[SecurityTestResult]:
    """
    Convenience function to run MFA security tests
    
    Returns:
        List of test results
    """
    mfa_tests = MFASecurityTests()
    return mfa_tests.run_mfa_tests() 