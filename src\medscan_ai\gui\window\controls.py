"""
UI controls creation for MedScan AI GUI.

This module provides windowing controls, medical presets,
and other specialized controls for medical image manipulation.
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QGridLayout, QGroupBox, QHBoxLayout, QLabel, 
    QPushButton, QSlider, QSpinBox, QWidget
)


class ControlsManagerMixin:
    """
    Mixin class providing UI controls creation capabilities.
    
    This mixin creates windowing controls, medical presets,
    and other specialized controls for medical imaging.
    """

    def _create_windowing_controls(self):
        """Create windowing controls panel for medical image manipulation."""
        panel = QGroupBox("🔧 Window/Level Controls")
        panel.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #CCCCCC;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        )

        layout = QGridLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 15, 12, 12)

        # Window Level (Center) controls
        level_label = QLabel("Window Level:")
        level_label.setStyleSheet("font-weight: bold; color: #1976D2;")
        layout.addWidget(level_label, 0, 0)

        self._window_level_slider = QSlider(Qt.Horizontal)
        self._window_level_slider.setRange(-1000, 1000)
        self._window_level_slider.setValue(0)
        self._window_level_slider.setTickPosition(QSlider.TicksBelow)
        self._window_level_slider.setTickInterval(200)
        if hasattr(self, '_on_window_level_changed'):
            self._window_level_slider.valueChanged.connect(self._on_window_level_changed)
        layout.addWidget(self._window_level_slider, 0, 1, 1, 2)

        self._window_level_spinbox = QSpinBox()
        self._window_level_spinbox.setRange(-1000, 1000)
        self._window_level_spinbox.setValue(0)
        self._window_level_spinbox.setSuffix(" HU")
        if hasattr(self, '_on_window_level_spinbox_changed'):
            self._window_level_spinbox.valueChanged.connect(self._on_window_level_spinbox_changed)
        layout.addWidget(self._window_level_spinbox, 0, 3)

        # Window Width controls
        width_label = QLabel("Window Width:")
        width_label.setStyleSheet("font-weight: bold; color: #1976D2;")
        layout.addWidget(width_label, 1, 0)

        self._window_width_slider = QSlider(Qt.Horizontal)
        self._window_width_slider.setRange(1, 4000)
        self._window_width_slider.setValue(100)
        self._window_width_slider.setTickPosition(QSlider.TicksBelow)
        self._window_width_slider.setTickInterval(500)
        if hasattr(self, '_on_window_width_changed'):
            self._window_width_slider.valueChanged.connect(self._on_window_width_changed)
        layout.addWidget(self._window_width_slider, 1, 1, 1, 2)

        self._window_width_spinbox = QSpinBox()
        self._window_width_spinbox.setRange(1, 4000)
        self._window_width_spinbox.setValue(100)
        self._window_width_spinbox.setSuffix(" HU")
        if hasattr(self, '_on_window_width_spinbox_changed'):
            self._window_width_spinbox.valueChanged.connect(self._on_window_width_spinbox_changed)
        layout.addWidget(self._window_width_spinbox, 1, 3)

        # Medical presets buttons
        presets_label = QLabel("Medical Presets:")
        presets_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        layout.addWidget(presets_label, 2, 0)

        presets_layout = QHBoxLayout()
        presets_layout.setSpacing(4)

        # Create preset buttons
        auto_btn = QPushButton("Auto")
        auto_btn.setToolTip("Auto windowing from DICOM metadata")
        if hasattr(self, '_apply_windowing_preset'):
            auto_btn.clicked.connect(lambda: self._apply_windowing_preset("auto"))

        brain_btn = QPushButton("Brain")
        brain_btn.setToolTip("Brain tissue windowing (WL:40, WW:80)")
        if hasattr(self, '_apply_windowing_preset'):
            brain_btn.clicked.connect(lambda: self._apply_windowing_preset("brain"))

        lung_btn = QPushButton("Lung")
        lung_btn.setToolTip("Lung tissue windowing (WL:-600, WW:1600)")
        if hasattr(self, '_apply_windowing_preset'):
            lung_btn.clicked.connect(lambda: self._apply_windowing_preset("lung"))

        bone_btn = QPushButton("Bone")
        bone_btn.setToolTip("Bone tissue windowing (WL:300, WW:1500)")
        if hasattr(self, '_apply_windowing_preset'):
            bone_btn.clicked.connect(lambda: self._apply_windowing_preset("bone"))

        soft_btn = QPushButton("Soft")
        soft_btn.setToolTip("Soft tissue windowing (WL:50, WW:400)")
        if hasattr(self, '_apply_windowing_preset'):
            soft_btn.clicked.connect(lambda: self._apply_windowing_preset("soft"))

        # Style preset buttons
        preset_style = """
            QPushButton {
                background-color: #E3F2FD;
                border: 1px solid #1976D2;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #BBDEFB;
            }
            QPushButton:pressed {
                background-color: #90CAF9;
            }
        """

        for btn in [auto_btn, brain_btn, lung_btn, bone_btn, soft_btn]:
            btn.setStyleSheet(preset_style)
            btn.setMaximumWidth(50)
            presets_layout.addWidget(btn)

        presets_layout.addStretch()  # Push buttons to left

        # Add presets layout to main layout spanning multiple columns
        presets_widget = QWidget()
        presets_widget.setLayout(presets_layout)
        layout.addWidget(presets_widget, 2, 1, 1, 3)

        return panel

    def _create_analysis_controls(self):
        """Create analysis controls panel."""
        panel = QGroupBox("🔍 Analysis Controls")
        panel.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #FF9800;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #FF9800;
            }
        """
        )

        layout = QGridLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 15, 12, 12)

        # Analysis type selection
        analysis_label = QLabel("Analysis Type:")
        analysis_label.setStyleSheet("font-weight: bold; color: #FF9800;")
        layout.addWidget(analysis_label, 0, 0)

        # Analysis buttons
        pathology_btn = QPushButton("🩺 Pathology")
        pathology_btn.setToolTip("Detect pathological findings")
        pathology_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        measurement_btn = QPushButton("📏 Measure")
        measurement_btn.setToolTip("Measurement tools")
        measurement_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        compare_btn = QPushButton("⚖️ Compare")
        compare_btn.setToolTip("Compare with previous studies")
        compare_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        # Analysis control buttons
        run_btn = QPushButton("▶️ Run Analysis")
        run_btn.setToolTip("Start automated analysis")
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: 1px solid #4CAF50;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
        """)
        run_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        stop_btn = QPushButton("⏹️ Stop")
        stop_btn.setToolTip("Stop current analysis")
        stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: 1px solid #F44336;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
        """)
        stop_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        # Layout analysis buttons
        analysis_layout = QHBoxLayout()
        analysis_layout.addWidget(pathology_btn)
        analysis_layout.addWidget(measurement_btn)
        analysis_layout.addWidget(compare_btn)
        analysis_layout.addStretch()

        controls_layout = QHBoxLayout()
        controls_layout.addWidget(run_btn)
        controls_layout.addWidget(stop_btn)
        controls_layout.addStretch()

        # Add to main layout
        analysis_widget = QWidget()
        analysis_widget.setLayout(analysis_layout)
        layout.addWidget(analysis_widget, 0, 1, 1, 2)

        controls_widget = QWidget()
        controls_widget.setLayout(controls_layout)
        layout.addWidget(controls_widget, 1, 0, 1, 3)

        return panel

    def _create_navigation_controls(self):
        """Create navigation controls for multi-image studies."""
        panel = QGroupBox("📂 Study Navigation")
        panel.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #9C27B0;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #9C27B0;
            }
        """
        )

        layout = QGridLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 15, 12, 12)

        # Series navigation
        series_label = QLabel("Series:")
        series_label.setStyleSheet("font-weight: bold; color: #9C27B0;")
        layout.addWidget(series_label, 0, 0)

        prev_series_btn = QPushButton("⬅️ Prev")
        prev_series_btn.setToolTip("Previous series")
        prev_series_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        next_series_btn = QPushButton("➡️ Next")
        next_series_btn.setToolTip("Next series")
        next_series_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        # Image navigation
        image_label = QLabel("Image:")
        image_label.setStyleSheet("font-weight: bold; color: #9C27B0;")
        layout.addWidget(image_label, 1, 0)

        prev_image_btn = QPushButton("⬅️ Prev")
        prev_image_btn.setToolTip("Previous image")
        prev_image_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        next_image_btn = QPushButton("➡️ Next")
        next_image_btn.setToolTip("Next image")
        next_image_btn.clicked.connect(lambda: self._placeholder_action() if hasattr(self, '_placeholder_action') else None)

        # Current position indicator
        position_label = QLabel("Position: 1 / 1")
        position_label.setStyleSheet("color: #666666; font-size: 10px;")

        # Layout navigation buttons
        series_layout = QHBoxLayout()
        series_layout.addWidget(prev_series_btn)
        series_layout.addWidget(next_series_btn)
        series_layout.addStretch()

        image_layout = QHBoxLayout()
        image_layout.addWidget(prev_image_btn)
        image_layout.addWidget(next_image_btn)
        image_layout.addStretch()

        # Add to main layout
        series_widget = QWidget()
        series_widget.setLayout(series_layout)
        layout.addWidget(series_widget, 0, 1, 1, 2)

        image_widget = QWidget()
        image_widget.setLayout(image_layout)
        layout.addWidget(image_widget, 1, 1, 1, 2)

        layout.addWidget(position_label, 2, 0, 1, 3)

        return panel

    def _update_navigation_position(self, current: int, total: int):
        """
        Update navigation position display.
        
        Args:
            current: Current position (1-based)
            total: Total number of items
        """
        # This would update position display
        # Placeholder for future implementation
        pass

    def _enable_windowing_controls(self, enabled: bool = True):
        """
        Enable or disable windowing controls.
        
        Args:
            enabled: Whether to enable controls
        """
        try:
            if hasattr(self, '_window_level_slider'):
                self._window_level_slider.setEnabled(enabled)
            if hasattr(self, '_window_level_spinbox'):
                self._window_level_spinbox.setEnabled(enabled)
            if hasattr(self, '_window_width_slider'):
                self._window_width_slider.setEnabled(enabled)
            if hasattr(self, '_window_width_spinbox'):
                self._window_width_spinbox.setEnabled(enabled)
        except Exception:
            pass

    def _reset_all_controls(self):
        """Reset all controls to default states."""
        try:
            # Reset windowing controls
            if hasattr(self, '_window_level_slider'):
                self._window_level_slider.setValue(0)
            if hasattr(self, '_window_level_spinbox'):
                self._window_level_spinbox.setValue(0)
            if hasattr(self, '_window_width_slider'):
                self._window_width_slider.setValue(100)
            if hasattr(self, '_window_width_spinbox'):
                self._window_width_spinbox.setValue(100)
                
            # Reset other controls as needed
            
        except Exception:
            pass 