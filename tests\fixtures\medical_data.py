"""
Medical Data Factories and Generators for MedScan AI Testing

This module provides factory functions and classes for generating 
anonymized medical test data that complies with HIPAA standards.
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from unittest.mock import Mock
import pytest


class MockMedicalDataFactory:
    """Factory for generating consistent medical test data."""
    
    @staticmethod
    def create_patient(
        patient_id: str = "TEST001",
        age: Optional[int] = None,
        gender: str = "M"
    ) -> Dict[str, Any]:
        """Create anonymized patient data for testing."""
        return {
            'patient_id': patient_id,
            'name': f'Test^Patient^{patient_id}',
            'age': age or 45,
            'gender': gender,
            'birth_date': '1979-01-15',
            'study_date': '2024-01-15',
            'modality': 'CT',
            'body_part': 'CHEST',
            'referring_physician': 'Test^Doctor',
            'institution': 'Test Medical Center'
        }
    
    @staticmethod
    def create_dicom_metadata(
        patient_id: str = "TEST001",
        modality: str = "CT"
    ) -> Dict[str, Any]:
        """Create DICOM metadata for testing."""
        return {
            # Patient Information
            'PatientName': f'Test^Patient^{patient_id}',
            'PatientID': patient_id,
            'PatientBirthDate': '19790115',
            'PatientSex': 'M',
            'PatientAge': '045Y',
            
            # Study Information
            'StudyInstanceUID': f'*******.*******.{patient_id}',
            'StudyDate': '20240115',
            'StudyTime': '143022',
            'StudyDescription': 'CHEST CT',
            'AccessionNumber': f'ACC{patient_id}',
            
            # Series Information
            'SeriesInstanceUID': f'*******.*******.{patient_id}.1',
            'SeriesNumber': '1',
            'SeriesDescription': 'CHEST CT AXIAL',
            'Modality': modality,
            
            # Image Information
            'SOPInstanceUID': f'*******.*******.{patient_id}.1.1',
            'SOPClassUID': '1.2.840.10008.*******.1.2',
            'InstanceNumber': '1',
            
            # Technical Parameters
            'Rows': 512,
            'Columns': 512,
            'PixelSpacing': [0.5, 0.5],
            'SliceThickness': '1.0',
            'BitsAllocated': 16,
            'BitsStored': 12,
            'HighBit': 11,
            'SamplesPerPixel': 1,
            'PhotometricInterpretation': 'MONOCHROME2',
            
            # Equipment Information
            'Manufacturer': 'Test Medical Systems',
            'ManufacturerModelName': 'Test CT Scanner',
            'SoftwareVersions': 'v1.0.0',
            'InstitutionName': 'Test Medical Center'
        }
    
    @staticmethod
    def create_ai_inference_result(
        confidence: float = 0.95,
        anomaly_detected: bool = True,
        anomaly_type: str = "pneumonia"
    ) -> Dict[str, Any]:
        """Create AI inference result for testing."""
        return {
            'confidence': confidence,
            'anomaly_detected': anomaly_detected,
            'anomaly_type': anomaly_type,
            'bounding_boxes': [
                {
                    'x': 100,
                    'y': 150, 
                    'width': 200,
                    'height': 180,
                    'confidence': confidence - 0.03
                }
            ] if anomaly_detected else [],
            'model_version': 'unified_medical_v1.0',
            'processing_time': 0.5,
            'inference_timestamp': datetime.now().isoformat(),
            'model_metadata': {
                'architecture': 'ResNet50',
                'training_dataset': 'Combined Medical Archives',
                'validation_accuracy': 0.98,
                'total_parameters': 25500000
            }
        }
    
    @staticmethod
    def create_study(
        study_id: str = "STUDY001",
        patient_id: str = "TEST001"
    ) -> Dict[str, Any]:
        """Create study information for testing."""
        return {
            'study_instance_uid': f'*******.*******.{study_id}',
            'study_id': study_id,
            'patient_id': patient_id,
            'study_date': '2024-01-15',
            'study_time': '14:30:22',
            'study_description': 'CHEST CT',
            'modalities': ['CT'],
            'number_of_series': 1,
            'number_of_instances': 120,
            'referring_physician': 'Test^Doctor',
            'accession_number': f'ACC{study_id}',
            'study_status': 'COMPLETED'
        }
    
    @staticmethod
    def create_annotation(
        annotation_id: str = "ANNO001",
        image_id: str = "IMG001"
    ) -> Dict[str, Any]:
        """Create annotation data for testing."""
        return {
            'annotation_id': annotation_id,
            'image_id': image_id,
            'annotation_type': 'RECTANGLE',
            'geometry_data': {
                'x': 100,
                'y': 150,
                'width': 200,
                'height': 180
            },
            'metadata': {
                'clinical_significance': 'HIGH',
                'urgency_level': 'ROUTINE',
                'measurements': {'area': 36000.0},
                'description': 'Test annotation for pneumonia'
            },
            'created_by': 'test_user',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
    
    @staticmethod
    def create_audit_log_entry(
        event_type: str = "PATIENT_DATA_ACCESS",
        user_id: str = "test_user"
    ) -> Dict[str, Any]:
        """Create audit log entry for testing."""
        return {
            'event_id': f'EVENT_{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'event_type': event_type,
            'user_id': user_id,
            'timestamp': datetime.now().isoformat(),
            'resource_type': 'PATIENT_DATA',
            'resource_id': 'TEST001',
            'action': 'READ',
            'ip_address': '127.0.0.1',
            'user_agent': 'MedScan AI Test',
            'session_id': 'test_session_123',
            'success': True,
            'details': {
                'module': 'patient_viewer',
                'operation': 'view_patient_data',
                'duration_ms': 250
            }
        }


# Convenience functions for quick mock data generation

def create_mock_patient(patient_id: str = "TEST001") -> Dict[str, Any]:
    """Quick patient data generator."""
    return MockMedicalDataFactory.create_patient(patient_id)


def create_mock_dicom_metadata(patient_id: str = "TEST001") -> Dict[str, Any]:
    """Quick DICOM metadata generator."""
    return MockMedicalDataFactory.create_dicom_metadata(patient_id)


def create_mock_ai_inference_result(confidence: float = 0.95) -> Dict[str, Any]:
    """Quick AI inference result generator."""
    return MockMedicalDataFactory.create_ai_inference_result(confidence)


def create_mock_study(study_id: str = "STUDY001") -> Dict[str, Any]:
    """Quick study data generator."""
    return MockMedicalDataFactory.create_study(study_id)


def create_mock_dicom_file() -> Mock:
    """Create a mock DICOM file object."""
    dicom = Mock()
    metadata = create_mock_dicom_metadata()
    
    # Set attributes from metadata
    for key, value in metadata.items():
        setattr(dicom, key, value)
    
    # Add pixel array
    dicom.pixel_array = np.random.randint(
        0, 4095, (512, 512), dtype=np.uint16
    )
    
    # Add common methods
    dicom.save_as = Mock(return_value=None)
    dicom.get_item = Mock(side_effect=lambda tag: metadata.get(tag))
    
    return dicom


def create_mock_pixel_array(
    rows: int = 512, 
    cols: int = 512, 
    dtype: Any = np.uint16
) -> np.ndarray:
    """Create mock pixel array for medical images."""
    if dtype == np.uint16:
        # Medical CT range
        return np.random.randint(0, 4095, (rows, cols), dtype=dtype)
    elif dtype == np.uint8:
        # Standard 8-bit range
        return np.random.randint(0, 255, (rows, cols), dtype=dtype)
    else:
        # Float range [0, 1]
        return np.random.random((rows, cols)).astype(dtype)


# Pytest fixtures for easy test integration

@pytest.fixture
def mock_patient_data():
    """Fixture providing mock patient data."""
    return create_mock_patient()


@pytest.fixture
def mock_dicom_metadata():
    """Fixture providing mock DICOM metadata."""
    return create_mock_dicom_metadata()


@pytest.fixture
def mock_ai_result():
    """Fixture providing mock AI inference result."""
    return create_mock_ai_inference_result()


@pytest.fixture
def mock_study_data():
    """Fixture providing mock study data."""
    return create_mock_study()


@pytest.fixture
def mock_dicom_file():
    """Fixture providing mock DICOM file object."""
    return create_mock_dicom_file()


@pytest.fixture
def mock_pixel_array():
    """Fixture providing mock pixel array."""
    return create_mock_pixel_array()


@pytest.fixture
def multiple_mock_patients():
    """Fixture providing multiple patient records."""
    return [
        create_mock_patient(f"TEST{str(i).zfill(3)}")
        for i in range(1, 6)
    ]


@pytest.fixture
def medical_test_dataset():
    """Fixture providing complete medical test dataset."""
    patients = [create_mock_patient(f"TEST{str(i).zfill(3)}") for i in range(1, 4)]
    studies = [create_mock_study(f"STUDY{str(i).zfill(3)}") for i in range(1, 4)]
    ai_results = [create_mock_ai_inference_result() for _ in range(3)]
    
    return {
        'patients': patients,
        'studies': studies,
        'ai_results': ai_results,
        'total_records': len(patients)
    } 