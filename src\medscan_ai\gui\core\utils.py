"""
Core GUI utility functions for image conversion and display operations.
Provides functions to convert DICOM pixel data to Qt image formats.
"""

from typing import Optional, <PERSON><PERSON>
import numpy as np
from PySide6.QtGui import QBrush, QColor, QFont, QImage, QPainter, QPixmap, QPen
from PySide6.QtCore import Qt


def numpy_to_qimage(
    array: np.ndarray, format: QImage.Format = QImage.Format_Grayscale8
) -> QImage:
    """
    Convert a numpy array to QImage.
    
    This function handles proper conversion of numpy arrays to Qt's QImage format,
    accounting for data type conversion and memory layout. It supports both
    grayscale and RGB images.
    
    Args:
        array: Input numpy array. For grayscale, should be 2D array.
               For RGB, should be 3D array with shape (height, width, 3).
        format: QImage format. Default is Format_Grayscale8.
                For RGB use Format_RGB888.
    
    Returns:
        QImage object containing the image data.
        
    Raises:
        ValueError: If array shape or dtype is incompatible with requested format.
    
    Note:
        The function makes a copy of the data to ensure QImage owns its memory.
        This prevents crashes when the numpy array goes out of scope.
    """
    if array.ndim == 2:
        # Grayscale image
        if format not in [QImage.Format_Grayscale8, QImage.Format_Grayscale16]:
            format = QImage.Format_Grayscale8
            
        # Convert to appropriate dtype
        if format == QImage.Format_Grayscale8:
            if array.dtype != np.uint8:
                # Normalize to 0-255 range
                array_min = array.min()
                array_max = array.max()
                if array_max > array_min:
                    array = ((array - array_min) / (array_max - array_min) * 255).astype(np.uint8)
                else:
                    array = np.zeros_like(array, dtype=np.uint8)
        
        height, width = array.shape
        bytes_per_line = width
        
        # Make a copy to ensure QImage owns the data
        image_data = np.ascontiguousarray(array)
        
        # Create QImage - using the buffer interface
        image = QImage(
            image_data.data,
            width,
            height,
            bytes_per_line,
            format
        )
        
        # Make a copy so QImage owns the data
        return image.copy()
        
    elif array.ndim == 3:
        # RGB image
        if array.shape[2] == 3:
            # Convert to Format_RGB888
            height, width, channels = array.shape
            bytes_per_line = width * 3
            
            # Ensure data is uint8 and contiguous
            if array.dtype != np.uint8:
                array = (array * 255).astype(np.uint8)
            
            image_data = np.ascontiguousarray(array)
            
            image = QImage(
                image_data.data,
                width,
                height,
                bytes_per_line,
                QImage.Format_RGB888
            )
            
            return image.copy()
        else:
            raise ValueError(f"For RGB images, expected 3 channels, got {array.shape[2]}")
    else:
        raise ValueError(f"Expected 2D or 3D array, got {array.ndim}D")


def numpy_to_qpixmap(array: np.ndarray) -> QPixmap:
    """
    Convert numpy array directly to QPixmap.
    
    This is a convenience function that combines numpy_to_qimage 
    with QPixmap.fromImage conversion.
    
    Args:
        array: Input numpy array (2D for grayscale, 3D for RGB)
        
    Returns:
        QPixmap object ready for display
    """
    qimage = numpy_to_qimage(array)
    return QPixmap.fromImage(qimage)


def scale_pixmap_to_fit(
    pixmap: QPixmap, max_width: int, max_height: int, keep_aspect_ratio: bool = True
) -> QPixmap:
    """
    Scale a QPixmap to fit within specified dimensions.
    
    Args:
        pixmap: Input QPixmap to scale
        max_width: Maximum width in pixels
        max_height: Maximum height in pixels
        keep_aspect_ratio: Whether to maintain aspect ratio (default True)
        
    Returns:
        Scaled QPixmap that fits within the specified dimensions
    """
    if keep_aspect_ratio:
        mode = Qt.KeepAspectRatio
    else:
        mode = Qt.IgnoreAspectRatio
        
    return pixmap.scaled(
        max_width, max_height, mode, Qt.SmoothTransformation
    )


def create_error_pixmap(
    width: int, height: int, message: str = "Error loading image"
) -> QPixmap:
    """
    Create a pixmap displaying an error message.
    
    Used when image loading fails to provide visual feedback to users.
    
    Args:
        width: Width of the error pixmap
        height: Height of the error pixmap  
        message: Error message to display
        
    Returns:
        QPixmap with error message centered on gray background
    """
    pixmap = QPixmap(width, height)
    pixmap.fill(QColor(128, 128, 128))  # Gray background
    
    painter = QPainter(pixmap)
    painter.setPen(QColor(255, 0, 0))  # Red text
    font = QFont("Arial", 12)
    painter.setFont(font)
    
    painter.drawText(
        pixmap.rect(),
        Qt.AlignCenter,
        message
    )
    painter.end()
    
    return pixmap


def get_image_info_text(array: np.ndarray) -> str:
    """
    Generate informative text about a numpy array representing an image.
    
    Provides details useful for debugging and verification of image data.
    
    Args:
        array: Numpy array containing image data
        
    Returns:
        Multi-line string with image information including:
        - Shape and dimensions
        - Data type
        - Value range (min/max)
        - Memory usage
    """
    info_lines = [
        f"Shape: {array.shape}",
        f"Dtype: {array.dtype}",
        f"Min value: {array.min():.2f}",
        f"Max value: {array.max():.2f}",
        f"Mean value: {array.mean():.2f}",
        f"Std deviation: {array.std():.2f}",
        f"Memory usage: {array.nbytes / 1024 / 1024:.2f} MB"
    ]
    
    if array.ndim == 2:
        info_lines.insert(1, f"Image type: Grayscale")
        info_lines.insert(2, f"Dimensions: {array.shape[1]}x{array.shape[0]}")
    elif array.ndim == 3:
        info_lines.insert(1, f"Image type: Color ({array.shape[2]} channels)")
        info_lines.insert(2, f"Dimensions: {array.shape[1]}x{array.shape[0]}")
        
    return "\n".join(info_lines)


def apply_window_level_to_display(pixel_array, window_center, window_width):
    """
    Apply window/level adjustment to medical image pixel data for display.
    
    Window/level adjustment is a standard medical imaging technique to enhance
    visibility of specific tissue types by mapping a range of pixel values
    to the display range.
    
    Args:
        pixel_array: Input pixel data (numpy array)
        window_center: Center value of the window (HU units for CT)
        window_width: Width of the window (range of values to display)
        
    Returns:
        numpy array: Pixel data normalized to 0-255 range for display
        
    Example:
        # For chest CT (typical values)
        display_data = apply_window_level_to_display(
            ct_pixels, 
            window_center=-600,  # Lung window
            window_width=1500
        )
    """
    # Calculate window bounds
    lower = window_center - window_width / 2
    upper = window_center + window_width / 2
    
    # Apply windowing
    windowed = np.clip(pixel_array, lower, upper)
    
    # Normalize to 0-255 range
    if upper > lower:
        normalized = ((windowed - lower) / (upper - lower) * 255).astype(np.uint8)
    else:
        # Handle edge case where window_width is 0
        normalized = np.zeros_like(pixel_array, dtype=np.uint8)
        
    return normalized 