"""Multi-Factor Authentication (MFA) Service for MedScan AI.

Provides TOTP-based multi-factor authentication with secure secret generation,
QR code creation, backup codes, and comprehensive security features for medical applications.
"""

import base64
import hashlib
import io
import json
import logging
import secrets
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from uuid import UUID

import pyotp
import qrcode
from cryptography.fernet import Ferne<PERSON>

from ...database.models.user import User
from ..encryption.data_encryption import DataEncryptionService
from ..access_control.key_access import SecurityRole

logger = logging.getLogger(__name__)


class MFAResult:
    """Result class for MFA operations."""
    
    def __init__(self, success: bool, message: str, data: Optional[Dict] = None):
        self.success = success
        self.message = message
        self.data = data or {}


class TOTPService:
    """
    TOTP (Time-based One-Time Password) service for multi-factor authentication.
    
    Features:
    - Secure secret generation and storage
    - QR code generation for authenticator apps
    - TOTP verification with time tolerance
    - Backup codes for recovery
    - Rate limiting protection
    - Comprehensive audit logging
    """
    
    def __init__(self, encryption_service: Optional[DataEncryptionService] = None):
        """
        Initialize TOTP service.
        
        Args:
            encryption_service: Service for encrypting sensitive data
        """
        self.encryption_service = encryption_service
        self.issuer_name = "MedScan AI"
        
        # TOTP configuration
        self.totp_window = 1  # ±30 seconds tolerance
        self.backup_codes_count = 10
        self.secret_length = 32  # 32 bytes = 256 bits
        
        # Rate limiting storage (in production use Redis/database)
        self._verification_attempts: Dict[str, List[datetime]] = {}
        self._max_attempts_per_hour = 10
        
        logger.info("TOTP Service initialized")
    
    def generate_secret(self) -> str:
        """
        Generate a cryptographically secure TOTP secret.
        
        Returns:
            Base32-encoded secret string
        """
        try:
            # Generate 32 random bytes (256 bits)
            secret_bytes = secrets.token_bytes(self.secret_length)
            
            # Encode as base32 for compatibility with authenticator apps
            secret_b32 = base64.b32encode(secret_bytes).decode('ascii')
            
            logger.info("TOTP secret generated successfully")
            return secret_b32
            
        except Exception as e:
            logger.error(f"Failed to generate TOTP secret: {e}")
            raise
    
    def generate_qr_code(self, user: User, secret: str) -> bytes:
        """
        Generate QR code for TOTP setup in authenticator apps.
        
        Args:
            user: User object
            secret: Base32-encoded TOTP secret
            
        Returns:
            QR code image as bytes (PNG format)
        """
        try:
            # Create TOTP URI for authenticator apps
            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(
                name=f"{user.email}",
                issuer_name=self.issuer_name
            )
            
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            # Create image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to bytes
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            img_bytes.seek(0)
            
            logger.info(f"QR code generated for user {user.id}")
            return img_bytes.getvalue()
            
        except Exception as e:
            logger.error(f"Failed to generate QR code for user {user.id}: {e}")
            raise
    
    def verify_totp_code(self, secret: str, code: str, user_id: str) -> bool:
        """
        Verify TOTP code with rate limiting protection.
        
        Args:
            secret: Base32-encoded TOTP secret
            code: 6-digit TOTP code from user
            user_id: User identifier for rate limiting
            
        Returns:
            True if code is valid, False otherwise
        """
        try:
            # Check rate limiting
            if not self._check_rate_limit(user_id):
                logger.warning(f"Rate limit exceeded for user {user_id}")
                return False
            
            # Record attempt
            self._record_attempt(user_id)
            
            # Verify code
            totp = pyotp.TOTP(secret)
            is_valid = totp.verify(code, valid_window=self.totp_window)
            
            if is_valid:
                logger.info(f"TOTP verification successful for user {user_id}")
            else:
                logger.warning(f"TOTP verification failed for user {user_id}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"TOTP verification error for user {user_id}: {e}")
            return False
    
    def generate_backup_codes(self) -> List[str]:
        """
        Generate backup codes for MFA recovery.
        
        Returns:
            List of backup codes (10 codes, 8 characters each)
        """
        try:
            backup_codes = []
            
            for _ in range(self.backup_codes_count):
                # Generate 8-character alphanumeric code
                code = secrets.token_hex(4).upper()  # 4 bytes = 8 hex chars
                backup_codes.append(code)
            
            logger.info(f"Generated {len(backup_codes)} backup codes")
            return backup_codes
            
        except Exception as e:
            logger.error(f"Failed to generate backup codes: {e}")
            raise
    
    def encrypt_backup_codes(self, backup_codes: List[str], user_id: str) -> str:
        """
        Encrypt backup codes for secure storage.
        
        Args:
            backup_codes: List of backup codes
            user_id: User identifier
            
        Returns:
            Encrypted JSON string of backup codes
        """
        try:
            # Hash codes for verification (store hashed versions)
            hashed_codes = []
            for code in backup_codes:
                code_hash = hashlib.sha256(code.encode()).hexdigest()
                hashed_codes.append({
                    'hash': code_hash,
                    'used': False,
                    'created_at': datetime.utcnow().isoformat()
                })
            
            codes_json = json.dumps(hashed_codes)
            
            # Encrypt if service available
            if self.encryption_service:
                encrypted_field = self.encryption_service.encrypt_field(
                    data=codes_json,
                    field_type="mfa_backup_codes",
                    user_role=SecurityRole.APPLICATION,
                    user_id=user_id
                )
                return json.dumps(encrypted_field)
            else:
                # Basic encryption fallback
                key = Fernet.generate_key()
                f = Fernet(key)
                encrypted = f.encrypt(codes_json.encode())
                return base64.b64encode(encrypted).decode()
            
        except Exception as e:
            logger.error(f"Failed to encrypt backup codes: {e}")
            raise
    
    def verify_backup_code(self, encrypted_codes: str, code: str, user_id: str) -> bool:
        """
        Verify backup code and mark as used.
        
        Args:
            encrypted_codes: Encrypted backup codes JSON
            code: Backup code to verify
            user_id: User identifier
            
        Returns:
            True if code is valid and unused, False otherwise
        """
        try:
            # Decrypt codes
            if self.encryption_service:
                encrypted_field = json.loads(encrypted_codes)
                decrypted_json = self.encryption_service.decrypt_field(
                    encrypted_field=encrypted_field,
                    user_role=SecurityRole.APPLICATION,
                    user_id=user_id
                )
            else:
                # Basic decryption fallback
                encrypted_bytes = base64.b64decode(encrypted_codes.encode())
                # Note: In production, key should be stored securely
                # This is simplified for demo
                return False
            
            codes_data = json.loads(decrypted_json)
            
            # Hash provided code
            code_hash = hashlib.sha256(code.encode()).hexdigest()
            
            # Find matching unused code
            for code_entry in codes_data:
                if code_entry['hash'] == code_hash and not code_entry['used']:
                    # Mark as used
                    code_entry['used'] = True
                    code_entry['used_at'] = datetime.utcnow().isoformat()
                    
                    logger.info(f"Backup code verified and marked as used for user {user_id}")
                    return True
            
            logger.warning(f"Invalid or already used backup code for user {user_id}")
            return False
            
        except Exception as e:
            logger.error(f"Backup code verification error for user {user_id}: {e}")
            return False
    
    def _check_rate_limit(self, user_id: str) -> bool:
        """Check if user has exceeded rate limit for TOTP verification."""
        now = datetime.utcnow()
        hour_ago = now.replace(minute=0, second=0, microsecond=0)
        
        if user_id not in self._verification_attempts:
            return True
        
        # Count attempts in the last hour
        recent_attempts = [
            attempt for attempt in self._verification_attempts[user_id]
            if attempt > hour_ago
        ]
        
        return len(recent_attempts) < self._max_attempts_per_hour
    
    def _record_attempt(self, user_id: str) -> None:
        """Record TOTP verification attempt for rate limiting."""
        now = datetime.utcnow()
        
        if user_id not in self._verification_attempts:
            self._verification_attempts[user_id] = []
        
        self._verification_attempts[user_id].append(now)
        
        # Clean old attempts (keep only last 24 hours)
        day_ago = now.replace(hour=0, minute=0, second=0, microsecond=0)
        self._verification_attempts[user_id] = [
            attempt for attempt in self._verification_attempts[user_id]
            if attempt > day_ago
        ]


class MFAService:
    """
    Multi-Factor Authentication service coordinating TOTP and backup codes.
    
    Provides high-level MFA operations for user enrollment, verification,
    and management with comprehensive security and audit features.
    """
    
    def __init__(
        self,
        totp_service: Optional[TOTPService] = None,
        encryption_service: Optional[DataEncryptionService] = None,
        db_session = None
    ):
        """
        Initialize MFA service.
        
        Args:
            totp_service: TOTP service (optional, will create if not provided)
            encryption_service: Encryption service
            db_session: Database session
        """
        self.totp_service = totp_service or TOTPService(encryption_service)
        self.encryption_service = encryption_service
        self.db_session = db_session
        
        logger.info("MFA Service initialized")
    
    def enroll_user_mfa(self, user: User) -> MFAResult:
        """
        Enroll user in MFA by generating secret and QR code.
        
        Args:
            user: User object
            
        Returns:
            MFAResult with success status and enrollment data
        """
        try:
            if user.is_mfa_enabled:
                return MFAResult(
                    success=False,
                    message="MFA is already enabled for this user"
                )
            
            # Generate TOTP secret
            secret = self.totp_service.generate_secret()
            
            # Generate QR code
            qr_code_bytes = self.totp_service.generate_qr_code(user, secret)
            qr_code_b64 = base64.b64encode(qr_code_bytes).decode('ascii')
            
            # Generate backup codes
            backup_codes = self.totp_service.generate_backup_codes()
            encrypted_backup_codes = self.totp_service.encrypt_backup_codes(
                backup_codes, str(user.id)
            )
            
            # Update user (but don't enable MFA yet - wait for verification)
            user.mfa_secret = secret
            user.mfa_backup_codes = encrypted_backup_codes
            
            if self.db_session:
                self.db_session.commit()
            
            logger.info(f"MFA enrollment initiated for user {user.id}")
            
            return MFAResult(
                success=True,
                message="MFA enrollment successful. Please verify setup.",
                data={
                    'qr_code': qr_code_b64,
                    'secret': secret,  # For manual entry
                    'backup_codes': backup_codes  # Show once, then encrypt
                }
            )
            
        except Exception as e:
            logger.error(f"MFA enrollment failed for user {user.id}: {e}")
            return MFAResult(
                success=False,
                message=f"MFA enrollment failed: {str(e)}"
            )
    
    def verify_mfa_setup(self, user: User, totp_code: str) -> MFAResult:
        """
        Verify MFA setup and enable MFA for user.
        
        Args:
            user: User object
            totp_code: TOTP code from authenticator app
            
        Returns:
            MFAResult with verification status
        """
        try:
            if user.is_mfa_enabled:
                return MFAResult(
                    success=False,
                    message="MFA is already enabled"
                )
            
            if not user.mfa_secret:
                return MFAResult(
                    success=False,
                    message="MFA setup not initiated. Please start enrollment first."
                )
            
            # Verify TOTP code
            is_valid = self.totp_service.verify_totp_code(
                user.mfa_secret, totp_code, str(user.id)
            )
            
            if is_valid:
                # Enable MFA
                user.is_mfa_enabled = True
                
                if self.db_session:
                    self.db_session.commit()
                
                logger.info(f"MFA enabled for user {user.id}")
                
                return MFAResult(
                    success=True,
                    message="MFA setup verified and enabled successfully"
                )
            else:
                return MFAResult(
                    success=False,
                    message="Invalid TOTP code. Please try again."
                )
                
        except Exception as e:
            logger.error(f"MFA setup verification failed for user {user.id}: {e}")
            return MFAResult(
                success=False,
                message=f"MFA verification failed: {str(e)}"
            )
    
    def verify_mfa(self, user: User, code: str, is_backup_code: bool = False) -> MFAResult:
        """
        Verify MFA code (TOTP or backup code).
        
        Args:
            user: User object
            code: TOTP or backup code
            is_backup_code: Whether the code is a backup code
            
        Returns:
            MFAResult with verification status
        """
        try:
            if not user.is_mfa_enabled:
                return MFAResult(
                    success=False,
                    message="MFA is not enabled for this user"
                )
            
            if is_backup_code:
                # Verify backup code
                if not user.mfa_backup_codes:
                    return MFAResult(
                        success=False,
                        message="No backup codes available"
                    )
                
                is_valid = self.totp_service.verify_backup_code(
                    user.mfa_backup_codes, code, str(user.id)
                )
            else:
                # Verify TOTP code
                if not user.mfa_secret:
                    return MFAResult(
                        success=False,
                        message="MFA secret not found"
                    )
                
                is_valid = self.totp_service.verify_totp_code(
                    user.mfa_secret, code, str(user.id)
                )
            
            if is_valid:
                logger.info(f"MFA verification successful for user {user.id}")
                return MFAResult(
                    success=True,
                    message="MFA verification successful"
                )
            else:
                logger.warning(f"MFA verification failed for user {user.id}")
                return MFAResult(
                    success=False,
                    message="Invalid code. Please try again."
                )
                
        except Exception as e:
            logger.error(f"MFA verification error for user {user.id}: {e}")
            return MFAResult(
                success=False,
                message=f"MFA verification failed: {str(e)}"
            )
    
    def disable_mfa(self, user: User) -> MFAResult:
        """
        Disable MFA for user and clear secrets.
        
        Args:
            user: User object
            
        Returns:
            MFAResult with operation status
        """
        try:
            if not user.is_mfa_enabled:
                return MFAResult(
                    success=False,
                    message="MFA is not enabled for this user"
                )
            
            # Clear MFA data
            user.is_mfa_enabled = False
            user.mfa_secret = None
            user.mfa_backup_codes = None
            
            if self.db_session:
                self.db_session.commit()
            
            logger.info(f"MFA disabled for user {user.id}")
            
            return MFAResult(
                success=True,
                message="MFA disabled successfully"
            )
            
        except Exception as e:
            logger.error(f"MFA disable failed for user {user.id}: {e}")
            return MFAResult(
                success=False,
                message=f"Failed to disable MFA: {str(e)}"
            )
    
    def get_mfa_status(self, user: User) -> Dict:
        """
        Get MFA status for user.
        
        Args:
            user: User object
            
        Returns:
            Dictionary with MFA status information
        """
        return {
            'is_mfa_enabled': user.is_mfa_enabled,
            'has_mfa_secret': bool(user.mfa_secret),
            'has_backup_codes': bool(user.mfa_backup_codes),
            'enrollment_required': not user.is_mfa_enabled and not user.mfa_secret
        } 