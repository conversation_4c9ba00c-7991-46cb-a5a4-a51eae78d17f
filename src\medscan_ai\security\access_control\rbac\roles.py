"""
Role Management Module for MedScan AI RBAC System
Handles creation, update, deletion, and querying of roles

Extracted from the original rbac_service.py file for better modularity.
"""

import logging
from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from sqlalchemy.orm import Session

from ....core.utils.logging_config import get_logger
from ....database.engine import get_session
from ....database.models import Role

logger = get_logger(__name__)


class RoleManager:
    """Manager for role operations in RBAC system."""

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize role manager.

        Args:
            db_session: Database session (optional, will create if not provided)
        """
        self.db_session = db_session or get_session()
        self.logger = logger

    def create_role(
        self,
        name: str,
        display_name: str,
        description: Optional[str] = None,
        hierarchy_level: int = 1,
        security_clearance: str = "Standard",
        medical_specialty: Optional[str] = None,
        department_scope: Optional[str] = None,
        max_users: Optional[int] = None,
        created_by: str = None,
        **kwargs,
    ) -> Role:
        """Create a new role.

        Args:
            name: Role name (must be one of predefined medical roles)
            display_name: Human-readable role name
            description: Role description
            hierarchy_level: Role hierarchy level (1-10)
            security_clearance: Security clearance level
            medical_specialty: Medical specialty (optional)
            department_scope: Department scope (optional)
            max_users: Maximum users allowed (optional)
            created_by: User ID who created the role
            **kwargs: Additional role attributes

        Returns:
            Created role instance

        Raises:
            ValueError: If role name is invalid or already exists
        """
        try:
            # Validate role name
            valid_roles = [
                "Admin",
                "Radiologist",
                "GP",
                "Technician",
                "Viewer",
                "Auditor",
                "SuperAdmin",
            ]
            if name not in valid_roles:
                raise ValueError(f"Invalid role name. Must be one of: {valid_roles}")

            # Check if role already exists
            existing_role = self.get_role_by_name(name)
            if existing_role:
                raise ValueError(f"Role '{name}' already exists")

            # Create role
            role = Role(
                name=name,
                display_name=display_name,
                description=description,
                hierarchy_level=hierarchy_level,
                security_clearance=security_clearance,
                medical_specialty=medical_specialty,
                department_scope=department_scope,
                max_users=max_users,
                created_by=created_by,
                **kwargs,
            )

            self.db_session.add(role)
            self.db_session.commit()

            self.logger.info(f"Created role: {name} (ID: {role.id})")
            return role

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to create role '{name}': {str(e)}")
            raise

    def get_role_by_id(self, role_id: str) -> Optional[Role]:
        """Get role by ID.
        
        Args:
            role_id: Role identifier
            
        Returns:
            Role instance if found, None otherwise
        """
        return self.db_session.query(Role).filter(Role.id == role_id).first()

    def get_role_by_name(self, name: str) -> Optional[Role]:
        """Get role by name.
        
        Args:
            name: Role name
            
        Returns:
            Role instance if found, None otherwise
        """
        return self.db_session.query(Role).filter(Role.name == name).first()

    def list_roles(
        self,
        active_only: bool = True,
        hierarchy_level: Optional[int] = None,
        security_clearance: Optional[str] = None,
    ) -> List[Role]:
        """List roles with optional filtering.
        
        Args:
            active_only: Whether to include only active roles
            hierarchy_level: Filter by hierarchy level
            security_clearance: Filter by security clearance
            
        Returns:
            List of roles matching criteria
        """
        query = self.db_session.query(Role)

        if active_only:
            query = query.filter(Role.is_active == True)
        if hierarchy_level:
            query = query.filter(Role.hierarchy_level == hierarchy_level)
        if security_clearance:
            query = query.filter(Role.security_clearance == security_clearance)

        return query.order_by(Role.hierarchy_level.desc(), Role.name).all()

    def update_role(self, role_id: str, modified_by: str, **updates) -> Role:
        """Update role attributes.
        
        Args:
            role_id: Role identifier
            modified_by: User ID who modified the role
            **updates: Fields to update
            
        Returns:
            Updated role instance
            
        Raises:
            ValueError: If role not found or invalid update
        """
        try:
            role = self.get_role_by_id(role_id)
            if not role:
                raise ValueError(f"Role with ID '{role_id}' not found")

            if role.is_system_role and "name" in updates:
                raise ValueError("Cannot modify name of system role")

            # Update attributes
            for key, value in updates.items():
                if hasattr(role, key):
                    setattr(role, key, value)

            role.modified_by = modified_by
            role.updated_at = datetime.utcnow()

            self.db_session.commit()

            self.logger.info(f"Updated role: {role.name} (ID: {role_id})")
            return role

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to update role '{role_id}': {str(e)}")
            raise

    def delete_role(self, role_id: str) -> bool:
        """Delete role (if not system role and no users assigned).
        
        Args:
            role_id: Role identifier
            
        Returns:
            True if role was deleted successfully
            
        Raises:
            ValueError: If role cannot be deleted
        """
        try:
            role = self.get_role_by_id(role_id)
            if not role:
                raise ValueError(f"Role with ID '{role_id}' not found")

            if role.is_system_role:
                raise ValueError("Cannot delete system role")

            if role.user_count > 0:
                raise ValueError(
                    f"Cannot delete role with {role.user_count} assigned users"
                )

            self.db_session.delete(role)
            self.db_session.commit()

            self.logger.info(f"Deleted role: {role.name} (ID: {role_id})")
            return True

        except Exception as e:
            self.db_session.rollback()
            self.logger.error(f"Failed to delete role '{role_id}': {str(e)}")
            raise

    def activate_role(self, role_id: str, activated_by: str) -> Role:
        """Activate a role.
        
        Args:
            role_id: Role identifier
            activated_by: User ID who activated the role
            
        Returns:
            Activated role instance
        """
        return self.update_role(
            role_id, 
            activated_by, 
            is_active=True, 
            activated_at=datetime.utcnow()
        )

    def deactivate_role(self, role_id: str, deactivated_by: str) -> Role:
        """Deactivate a role.
        
        Args:
            role_id: Role identifier
            deactivated_by: User ID who deactivated the role
            
        Returns:
            Deactivated role instance
        """
        return self.update_role(
            role_id, 
            deactivated_by, 
            is_active=False, 
            deactivated_at=datetime.utcnow()
        )

    def get_role_hierarchy(self) -> List[Role]:
        """Get roles ordered by hierarchy level.
        
        Returns:
            List of roles ordered by hierarchy level (highest first)
        """
        return (
            self.db_session.query(Role)
            .filter(Role.is_active == True)
            .order_by(Role.hierarchy_level.desc())
            .all()
        )

    def get_roles_by_specialty(self, medical_specialty: str) -> List[Role]:
        """Get roles by medical specialty.
        
        Args:
            medical_specialty: Medical specialty name
            
        Returns:
            List of roles for the specified specialty
        """
        return (
            self.db_session.query(Role)
            .filter(
                Role.is_active == True,
                Role.medical_specialty == medical_specialty
            )
            .order_by(Role.hierarchy_level.desc())
            .all()
        )

    def get_roles_by_clearance(self, security_clearance: str) -> List[Role]:
        """Get roles by security clearance level.
        
        Args:
            security_clearance: Security clearance level
            
        Returns:
            List of roles with the specified clearance level
        """
        return (
            self.db_session.query(Role)
            .filter(
                Role.is_active == True,
                Role.security_clearance == security_clearance
            )
            .order_by(Role.hierarchy_level.desc())
            .all()
        )

    def validate_role_hierarchy(self, lower_role_id: str, higher_role_id: str) -> bool:
        """Validate that one role is higher in hierarchy than another.
        
        Args:
            lower_role_id: ID of the lower role
            higher_role_id: ID of the higher role
            
        Returns:
            True if hierarchy is valid, False otherwise
        """
        lower_role = self.get_role_by_id(lower_role_id)
        higher_role = self.get_role_by_id(higher_role_id)
        
        if not lower_role or not higher_role:
            return False
            
        return higher_role.hierarchy_level > lower_role.hierarchy_level

    def get_available_medical_specialties(self) -> List[str]:
        """Get list of available medical specialties.
        
        Returns:
            List of unique medical specialties
        """
        result = (
            self.db_session.query(Role.medical_specialty)
            .filter(
                Role.is_active == True,
                Role.medical_specialty.isnot(None)
            )
            .distinct()
            .all()
        )
        
        return [specialty[0] for specialty in result if specialty[0]] 
