# Pre-commit hooks for MedScan AI medical imaging project
# Ensures code quality, security, and compliance before commits

repos:
  # Code formatting and basic checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-added-large-files
        args: ['--maxkb=10240']  # 10MB limit for medical images in repo
      - id: debug-statements
      - id: check-executables-have-shebangs

  # Python code formatting with Black
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # Import sorting with isort
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Linting with flake8
  - repo: https://github.com/pycqa/flake8
    rev: 7.3.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]

  # Type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.11.2
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-Pillow]
        args: [--ignore-missing-imports]

  # Security scanning with bandit
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: [-c, .bandit]
        exclude: ^tests/

  # Medical data protection checks
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: \.lock$|\.toml$

  # Documentation checks
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        match: ^src/
        args: [--convention=google]

  # YAML, TOML, and JSON validation for configs
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        args: [-c=.yamllint.yaml]

# Global exclude patterns for medical/sensitive files
exclude: |
  (?x)^(
    .*\.dcm$|           # DICOM files
    .*\.nii$|           # NIfTI files
    .*\.nii\.gz$|       # Compressed NIfTI
    .*\.jpg$|           # Patient images
    .*\.jpeg$|          # Patient images
    .*\.png$|           # Patient images
    .*\.tif$|           # Medical images
    .*\.tiff$|          # Medical images
    .*\.h5$|            # AI model files
    .*\.pkl$|           # Pickled data
    .*\.npz$|           # NumPy arrays
    patient_data/.*|    # Patient data directory
    models/.*\.pth$|    # PyTorch models
    models/.*\.h5$|     # Keras models
    .*/test_data/.*     # Test medical data
  )$
