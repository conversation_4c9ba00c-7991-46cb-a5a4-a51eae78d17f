"""
Windowing control operations for MedScan AI GUI.

This module provides window/level adjustments, medical presets,
and DICOM windowing functionality for medical image display.
"""

import numpy as np
from typing import Op<PERSON>, Tuple

from PySide6.QtWidgets import QSlider, QSpinBox


class WindowingHandlerMixin:
    """
    Mixin class providing windowing control capabilities for the main window.
    
    This mixin should be used with a QMainWindow that has:
    - _window_level_slider: QSlider for window level
    - _window_level_spinbox: QSpinBox for window level
    - _window_width_slider: QSlider for window width  
    - _window_width_spinbox: QSpinBox for window width
    - _current_dataset: Currently loaded DICOM dataset
    - _image_helper: ImageDisplayHelper instance
    """

    def _initialize_windowing(self):
        """Initialize windowing controls from DICOM metadata."""
        if not hasattr(self, '_current_dataset') or self._current_dataset is None:
            return

        try:
            dataset = self._current_dataset
            
            # Get window center and width from DICOM
            window_center = getattr(dataset, 'WindowCenter', None)
            window_width = getattr(dataset, 'WindowWidth', None)

            # Handle multiple values (take first)
            if isinstance(window_center, (list, tuple)) and len(window_center) > 0:
                window_center = float(window_center[0])
            elif window_center is not None:
                window_center = float(window_center)
            else:
                window_center = 0.0

            if isinstance(window_width, (list, tuple)) and len(window_width) > 0:
                window_width = float(window_width[0])
            elif window_width is not None:
                window_width = float(window_width)
            else:
                window_width = 100.0

            # Store current values
            self._current_window_center = window_center
            self._current_window_width = window_width
            self._windowing_enabled = True

            # Update controls and enable sliders
            self._update_windowing_controls()
            if hasattr(self, '_enable_windowing_controls'):
                self._enable_windowing_controls(True)

            # Update status
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(
                    f"Windowing initialized: Level={window_center:.0f}, Width={window_width:.0f}", 
                    3000
                )

        except Exception as e:
            # Set default values on error
            self._current_window_center = 0
            self._current_window_width = 100
            self._windowing_enabled = False
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Failed to initialize windowing", 3000)

    def _update_windowing_controls(self):
        """Update windowing control widgets with current values."""
        try:
            # Update sliders and spinboxes without triggering signals
            if hasattr(self, '_window_level_slider'):
                self._window_level_slider.blockSignals(True)
                self._window_level_slider.setValue(int(self._current_window_center))
                self._window_level_slider.blockSignals(False)

            if hasattr(self, '_window_level_spinbox'):
                self._window_level_spinbox.blockSignals(True)
                self._window_level_spinbox.setValue(int(self._current_window_center))
                self._window_level_spinbox.blockSignals(False)

            if hasattr(self, '_window_width_slider'):
                self._window_width_slider.blockSignals(True)
                self._window_width_slider.setValue(int(self._current_window_width))
                self._window_width_slider.blockSignals(False)

            if hasattr(self, '_window_width_spinbox'):
                self._window_width_spinbox.blockSignals(True)
                self._window_width_spinbox.setValue(int(self._current_window_width))
                self._window_width_spinbox.blockSignals(False)

        except Exception:
            pass

    def _apply_current_windowing(self):
        """Apply current windowing settings to the displayed image."""
        if not self._windowing_enabled or not hasattr(self, '_current_dataset'):
            return

        try:
            # Get windowed image
            self._update_windowed_image()
            
        except Exception as e:
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Failed to apply windowing", 2000)

    def _on_window_level_changed(self, value: int):
        """Handle window level slider change."""
        self._current_window_center = float(value)
        
        # Update spinbox
        if hasattr(self, '_window_level_spinbox'):
            self._window_level_spinbox.blockSignals(True)
            self._window_level_spinbox.setValue(value)
            self._window_level_spinbox.blockSignals(False)
        
        # Apply windowing
        self._update_windowed_image()

    def _on_window_level_spinbox_changed(self, value: int):
        """Handle window level spinbox change."""
        self._current_window_center = float(value)
        
        # Update slider
        if hasattr(self, '_window_level_slider'):
            self._window_level_slider.blockSignals(True)
            self._window_level_slider.setValue(value)
            self._window_level_slider.blockSignals(False)
        
        # Apply windowing
        self._update_windowed_image()

    def _on_window_width_changed(self, value: int):
        """Handle window width slider change."""
        self._current_window_width = float(value)
        
        # Update spinbox
        if hasattr(self, '_window_width_spinbox'):
            self._window_width_spinbox.blockSignals(True)
            self._window_width_spinbox.setValue(value)
            self._window_width_spinbox.blockSignals(False)
        
        # Apply windowing
        self._update_windowed_image()

    def _on_window_width_spinbox_changed(self, value: int):
        """Handle window width spinbox change."""
        self._current_window_width = float(value)
        
        # Update slider
        if hasattr(self, '_window_width_slider'):
            self._window_width_slider.blockSignals(True)
            self._window_width_slider.setValue(value)
            self._window_width_slider.blockSignals(False)
        
        # Apply windowing
        self._update_windowed_image()

    def _update_windowed_image(self):
        """Update the displayed image with current windowing settings."""
        if not hasattr(self, '_current_dataset') or self._current_dataset is None:
            return

        try:
            # Get windowed pixel array
            windowed_array = self._image_helper.apply_windowing(
                self._current_dataset,
                window_center=self._current_window_center,
                window_width=self._current_window_width
            )

            # Convert to QPixmap and display
            from ...utils import numpy_to_qpixmap
            pixmap = numpy_to_qpixmap(windowed_array)
            
            if pixmap and not pixmap.isNull():
                self._current_pixmap = pixmap
                
                if hasattr(self, 'image_viewer') and self.image_viewer.isVisible():
                    self.image_viewer.set_image(pixmap)

                # Update status with current windowing values
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage(
                        f"Windowing: Level={self._current_window_center:.0f}, "
                        f"Width={self._current_window_width:.0f}",
                        2000
                    )

        except Exception as e:
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Failed to update windowed image", 2000)

    def _apply_windowing_preset(self, preset_name: str):
        """
        Apply medical windowing preset.
        
        Args:
            preset_name: Name of the preset ('auto', 'brain', 'lung', 'bone', 'soft')
        """
        try:
            if preset_name == "auto":
                self._auto_windowing()
                return

            # Define medical presets
            presets = {
                "brain": (40, 80),      # Brain tissue
                "lung": (-600, 1600),   # Lung tissue  
                "bone": (300, 1500),    # Bone tissue
                "soft": (50, 400),      # Soft tissue
            }

            if preset_name in presets:
                level, width = presets[preset_name]
                self._current_window_center = float(level)
                self._current_window_width = float(width)
                
                # Update controls and apply
                self._update_windowing_controls()
                if hasattr(self, '_enable_windowing_controls'):
                    self._enable_windowing_controls(True)
                self._update_windowed_image()
                
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage(
                        f"Applied {preset_name.title()} preset: "
                        f"Level={level}, Width={width}",
                        3000
                    )

        except Exception as e:
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"Failed to apply {preset_name} preset", 2000)

    def _auto_windowing(self):
        """Apply automatic windowing based on DICOM metadata."""
        if not hasattr(self, '_current_dataset') or self._current_dataset is None:
            return

        try:
            # Try to get windowing from DICOM metadata
            dataset = self._current_dataset
            window_center = getattr(dataset, 'WindowCenter', None)
            window_width = getattr(dataset, 'WindowWidth', None)

            if window_center is not None and window_width is not None:
                # Use DICOM values
                if isinstance(window_center, (list, tuple)):
                    window_center = float(window_center[0])
                else:
                    window_center = float(window_center)

                if isinstance(window_width, (list, tuple)):
                    window_width = float(window_width[0])
                else:
                    window_width = float(window_width)

                self._current_window_center = window_center
                self._current_window_width = window_width
            else:
                # Calculate from pixel data statistics
                pixel_array = getattr(dataset, 'pixel_array', None)
                if pixel_array is not None:
                    pixel_array = np.array(pixel_array, dtype=np.float32)
                    
                    # Calculate percentiles for auto-windowing
                    p1 = np.percentile(pixel_array, 1)
                    p99 = np.percentile(pixel_array, 99)
                    
                    self._current_window_center = (p1 + p99) / 2
                    self._current_window_width = p99 - p1
                else:
                    # Fallback to default
                    self._current_window_center = 0
                    self._current_window_width = 100

            # Update controls and apply
            self._update_windowing_controls()
            if hasattr(self, '_enable_windowing_controls'):
                self._enable_windowing_controls(True)
            self._update_windowed_image()
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Auto windowing applied", 2000)

        except Exception as e:
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Auto windowing failed", 2000)

    def _reset_windowing(self):
        """Reset windowing to original DICOM values."""
        try:
            # Re-initialize from DICOM metadata
            self._initialize_windowing()
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Windowing reset to DICOM values", 2000)
                
        except Exception:
            # Fallback to defaults
            self._current_window_center = 0
            self._current_window_width = 100
            self._update_windowing_controls()
            if hasattr(self, '_enable_windowing_controls'):
                self._enable_windowing_controls(True)
            self._update_windowed_image()
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Windowing reset to defaults", 2000)

    def _get_windowing_info(self) -> str:
        """
        Get current windowing information as string.
        
        Returns:
            Formatted windowing information
        """
        if hasattr(self, '_windowing_enabled') and self._windowing_enabled:
            return f"WL: {self._current_window_center:.0f}, WW: {self._current_window_width:.0f}"
        else:
            return "Windowing: Disabled" 
