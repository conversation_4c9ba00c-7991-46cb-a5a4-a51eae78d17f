#!/usr/bin/env python3
"""
Test script for new dataset path on D: drive
"""

import sys
import os
sys.path.append('src')

from medscan_ai.ai.training_pipeline import MultiFormatMedicalDataset, TrainingConfig
import torch

print("🔍 Testing dataset discovery with new path D:/datasets...")

try:
    # Create config with archive_2 path and sample limiting
    config = TrainingConfig(
        dataset_path='D:/datasets/archive_2',
        batch_size=4,
        image_size=(224, 224),
        max_samples_per_class=50  # Test with only 50 samples per class
    )
    
    print(f"📂 Dataset path: {config.dataset_path}")
    print(f"📊 Sample limit per class: {config.max_samples_per_class}")
    
    # Check if path exists
    if not os.path.exists(config.dataset_path):
        print(f"❌ Error: Dataset path does not exist: {config.dataset_path}")
        sys.exit(1)
    
    print(f"✅ Dataset path exists")
    
    # Create dataset
    dataset = MultiFormatMedicalDataset(
        dataset_path=config.dataset_path,
        config=config,
        mode='train'
    )
    
    print(f"✅ Dataset loaded: {len(dataset)} samples")
    print(f"✅ Classes found: {list(dataset.class_to_idx.keys())}")
    print(f"✅ Number of classes: {len(dataset.class_to_idx)}")
    
    if len(dataset) > 0:
        print("\n🔍 Testing first few samples:")
        for i in range(min(3, len(dataset))):
            try:
                sample, label = dataset[i]
                class_name = dataset.idx_to_class[label]
                print(f"  Sample {i}: shape={sample.shape}, label={label} ({class_name})")
            except Exception as e:
                print(f"  Sample {i}: Error loading - {e}")
    
    print("\n✅ Dataset test completed successfully!")
    
except Exception as e:
    print(f"❌ Error during dataset test: {e}")
    import traceback
    traceback.print_exc()