#!/usr/bin/env python3
"""
Simple performance test for optimized DICOM pixel processing.
Tests memory usage and processing time without GUI dependencies.
"""

import gc
import os
import sys
import time
import tracemalloc
from pathlib import Path

import numpy as np
import psutil

# Add project to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from medscan_ai.dicom.processing.optimized_pixel_processor import OptimizedPixelProcessor


class SimpleProfiler:
    """Simple performance profiling utility."""

    def __init__(self):
        self.process = psutil.Process()

    def get_memory_usage(self):
        """Get current memory usage."""
        memory_info = self.process.memory_info()
        return {
            "rss_mb": memory_info.rss / (1024 * 1024),
            "vms_mb": memory_info.vms / (1024 * 1024),
            "percent": self.process.memory_percent(),
        }

    def profile_operation(self, operation_name, operation_func):
        """Profile a single operation."""
        print(f"\n{'='*60}")
        print(f"PROFILING: {operation_name}")
        print(f"{'='*60}")

        # Force garbage collection
        gc.collect()

        # Start memory tracking
        tracemalloc.start()
        memory_before = self.get_memory_usage()

        # Record start time
        start_time = time.time()

        try:
            # Execute operation
            result = operation_func()

            # Record end time
            end_time = time.time()

            # Get memory usage after
            memory_after = self.get_memory_usage()
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            # Calculate metrics
            execution_time = end_time - start_time
            memory_delta = memory_after["rss_mb"] - memory_before["rss_mb"]

            # Print results
            print(f"Operation time: {execution_time:.3f} seconds")
            print(f"Memory before: {memory_before['rss_mb']:.1f}MB")
            print(f"Memory after:  {memory_after['rss_mb']:.1f}MB")
            print(f"Memory delta:  {memory_delta:+.1f}MB")
            print(f"Peak memory:   {peak / (1024 * 1024):.1f}MB")

            return {
                "success": True,
                "time": execution_time,
                "memory_delta": memory_delta,
                "peak_memory": peak / (1024 * 1024),
                "result_size": (
                    result.nbytes / (1024 * 1024) if hasattr(result, "nbytes") else 0
                ),
            }

        except Exception as e:
            print(f"ERROR: {str(e)}")
            try:
                tracemalloc.stop()
            except:
                pass
            return {"success": False, "error": str(e)}


def create_mock_dataset(pixel_array):
    """Create a mock DICOM dataset."""

    class MockDataset:
        def __init__(self, pixel_array):
            self.pixel_array = pixel_array
            self.WindowCenter = 2048
            self.WindowWidth = 1000
            self.RescaleSlope = 1.0
            self.RescaleIntercept = 0.0
            self.SOPInstanceUID = f"test_{pixel_array.shape[0]}x{pixel_array.shape[1]}"

    return MockDataset(pixel_array)


def test_basic_windowing():
    """Test basic windowing functionality."""
    print("BASIC WINDOWING TEST")
    print("=" * 60)

    profiler = SimpleProfiler()

    # Create test data
    test_array = np.random.randint(0, 4096, (512, 512), dtype=np.uint16)
    mock_dataset = create_mock_dataset(test_array)

    print(f"Test data: {test_array.shape}, {test_array.dtype}")
    print(f"Data size: {test_array.nbytes / (1024*1024):.1f}MB")

    # Test original windowing (manual)
    def original_windowing():
        # Manual windowing calculation
        window_center = 2048
        window_width = 1000
        window_min = window_center - (window_width / 2)

        # Convert to float for calculations
        windowed = test_array.astype(np.float32)
        windowed = (windowed - window_min) / window_width * 255.0
        windowed = np.clip(windowed, 0, 255)
        return windowed.astype(np.uint8)

    result1 = profiler.profile_operation("Original windowing", original_windowing)

    # Test optimized windowing
    def optimized_windowing(dataset=mock_dataset):
        processor = OptimizedPixelProcessor(
            max_memory_mb=256, chunk_size_mb=32, enable_caching=True
        )
        return processor.get_optimized_display_array(dataset, window_center=2048, window_width=1000)  # type: ignore[arg-type]

    result2 = profiler.profile_operation("Optimized windowing", optimized_windowing)

    # Compare results
    if result1["success"] and result2["success"]:
        print(f"\n{'='*60}")
        print("COMPARISON RESULTS")
        print(f"{'='*60}")
        print(f"Original time:    {result1['time']:.3f}s")
        print(f"Optimized time:   {result2['time']:.3f}s")
        print(f"Speedup:          {result1['time'] / result2['time']:.2f}x")
        print(f"Original memory:  {result1['peak_memory']:.1f}MB")
        print(f"Optimized memory: {result2['peak_memory']:.1f}MB")
        print(
            f"Memory reduction: {result1['peak_memory'] / result2['peak_memory']:.2f}x"
        )


def test_large_image_handling():
    """Test large image handling."""
    print(f"\n{'='*80}")
    print("LARGE IMAGE HANDLING TEST")
    print(f"{'='*80}")

    profiler = SimpleProfiler()

    # Test different sizes
    test_sizes = [
        (1024, 1024, "Medium (1024x1024)"),
        (2048, 2048, "Large (2048x2048)"),
        (4096, 4096, "Very Large (4096x4096)"),
    ]

    for width, height, size_name in test_sizes:
        print(f"\nTesting {size_name}...")

        # Create test data
        test_array = np.random.randint(0, 4096, (height, width), dtype=np.uint16)
        mock_dataset = create_mock_dataset(test_array)

        data_size_mb = test_array.nbytes / (1024 * 1024)
        print(f"Data size: {data_size_mb:.1f}MB")

        # Test optimized processing
        def process_large_image(dataset=mock_dataset):
            processor = OptimizedPixelProcessor(
                max_memory_mb=128,  # Limited memory
                chunk_size_mb=16,  # Small chunks
                enable_caching=False,  # Disable caching for large images
            )
            return processor.get_optimized_display_array(dataset, window_center=2048, window_width=1000)  # type: ignore[arg-type]

        result = profiler.profile_operation(
            f"Large image processing - {size_name}", process_large_image
        )

        if result["success"]:
            efficiency = data_size_mb / result["peak_memory"]
            print(f"Memory efficiency: {efficiency:.2f} (input/peak ratio)")

            if result["peak_memory"] > data_size_mb * 3:
                print("⚠️  WARNING: High memory usage detected")
            else:
                print("✅ Good memory efficiency")

        # Cleanup
        del test_array, mock_dataset
        gc.collect()


def test_caching_benefit():
    """Test caching performance benefit."""
    print(f"\n{'='*80}")
    print("CACHING BENEFIT TEST")
    print(f"{'='*80}")

    profiler = SimpleProfiler()

    # Create test data
    test_array = np.random.randint(0, 4096, (1024, 1024), dtype=np.uint16)
    mock_dataset = create_mock_dataset(test_array)

    # Create processor with caching
    processor = OptimizedPixelProcessor(
        max_memory_mb=256, chunk_size_mb=32, enable_caching=True
    )

    # First call (should cache)
    def first_call(dataset=mock_dataset):
        return processor.get_optimized_display_array(dataset, window_center=2048, window_width=1000)  # type: ignore[arg-type]

    result1 = profiler.profile_operation("First call (caching)", first_call)

    # Second call (should use cache)
    def second_call(dataset=mock_dataset):
        return processor.get_optimized_display_array(dataset, window_center=2048, window_width=1000)  # type: ignore[arg-type]

    result2 = profiler.profile_operation("Second call (cached)", second_call)

    # Compare caching benefit
    if result1["success"] and result2["success"]:
        cache_speedup = result1["time"] / result2["time"]
        print(f"\n{'='*60}")
        print("CACHING ANALYSIS")
        print(f"{'='*60}")
        print(f"First call time:  {result1['time']:.3f}s")
        print(f"Cached call time: {result2['time']:.3f}s")
        print(f"Cache speedup:    {cache_speedup:.2f}x")

        if cache_speedup > 2:
            print("✅ Excellent caching performance")
        elif cache_speedup > 1.5:
            print("✅ Good caching performance")
        else:
            print("⚠️  Limited caching benefit")

    # Check cache info
    cache_info = processor.get_cache_info()
    print(f"\nCache status: {cache_info}")


if __name__ == "__main__":
    print("MedScan AI - Pixel Processing Optimization Test")
    print("=" * 80)

    try:
        # Test basic windowing
        test_basic_windowing()

        # Test large image handling
        test_large_image_handling()

        # Test caching benefit
        test_caching_benefit()

        print(f"\n{'='*80}")
        print("ALL TESTS COMPLETED SUCCESSFULLY")
        print(f"{'='*80}")

    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
