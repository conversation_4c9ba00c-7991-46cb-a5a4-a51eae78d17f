"""
TensorFlow Lite Inference Engine for Medical Imaging AI

This module provides the InferenceEngine class that integrates DICOM preprocessing,
TensorFlow Lite model execution, and result processing for medical imaging AI applications.
Designed specifically for X-ray anomaly detection but extensible for other medical imaging tasks.

Author: MedScan AI Team
Date: 2025-01-27
"""

import os
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import logging
from datetime import datetime
from dataclasses import dataclass, field
import pydicom

# Import MedScan AI components
from ..models.model_loader import ModelLoader, ModelLoaderError
from ..models.model_registry import ModelRegistry, DeploymentPolicy
from ..preprocessing.ai_pixel_extractor import AIPixelExtractor
from ..preprocessing.tflite_preprocessor import TFLitePreprocessor

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class InferenceResult:
    """
    Comprehensive inference result with metadata and performance metrics.
    """
    model_name: str
    model_version: str
    raw_output: np.ndarray
    confidence_score: float
    anomaly_detected: bool
    processing_time_ms: float
    input_shape: Tuple[int, ...]
    output_shape: Tuple[int, ...]
    dicom_metadata: Dict[str, Any]
    preprocessing_info: Dict[str, Any]
    timestamp: str
    
    # Additional convenience fields expected by tests
    model_info: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for serialization."""
        return {
            'model_name': self.model_name,
            'model_version': self.model_version,
            'raw_output': self.raw_output.tolist() if isinstance(self.raw_output, np.ndarray) else self.raw_output,
            'confidence_score': self.confidence_score,
            'anomaly_detected': self.anomaly_detected,
            'processing_time_ms': self.processing_time_ms,
            'input_shape': self.input_shape,
            'output_shape': self.output_shape,
            'dicom_metadata': self.dicom_metadata,
            'preprocessing_info': self.preprocessing_info,
            'timestamp': self.timestamp,
            'model_info': self.model_info,
            'performance_metrics': self.performance_metrics
        }


@dataclass
class BatchInferenceResult:
    """
    Results from batch inference processing.
    """
    individual_results: List[InferenceResult]
    batch_size: int
    total_processing_time_ms: float
    average_processing_time_ms: float
    anomalies_detected: int
    success_count: int
    error_count: int
    errors: List[str]
    
    # Convenience aliases for tests
    @property
    def total_batch_time(self) -> float:
        return self.total_processing_time_ms / 1000.0

    @property
    def average_inference_time(self) -> float:
        return self.average_processing_time_ms / 1000.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert batch result to dictionary for serialization."""
        return {
            'individual_results': [result.to_dict() for result in self.individual_results],
            'batch_size': self.batch_size,
            'total_processing_time_ms': self.total_processing_time_ms,
            'average_processing_time_ms': self.average_processing_time_ms,
            'anomalies_detected': self.anomalies_detected,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'errors': self.errors
        }


class InferenceEngineError(Exception):
    """Custom exception for InferenceEngine operations"""
    pass


class InferenceEngine:
    """
    Comprehensive TensorFlow Lite inference engine for medical imaging AI.
    
    This class provides a high-level interface that combines:
    - DICOM data preprocessing (AIPixelExtractor)
    - TensorFlow Lite model-specific preprocessing (TFLitePreprocessor)
    - TensorFlow Lite model execution (ModelLoader)
    - Result processing and analysis
    - Performance monitoring and error handling
    
    Designed for production medical imaging applications with focus on
    reliability, performance, and comprehensive logging.
    """
    
    def __init__(self, 
                 models_dir: str = "src/medscan_ai/models",
                 registry_file: str = ".taskmaster/registry/model_registry.json",
                 default_model: str = "xray_anomaly_detector",
                 anomaly_threshold: float = 0.5):
        """
        Initialize the InferenceEngine.
        
        Args:
            models_dir: Directory containing TensorFlow Lite models
            registry_file: Path to model registry file
            default_model: Default model name for inference
            anomaly_threshold: Threshold for anomaly detection (0.0-1.0)
        """
        self.models_dir = Path(models_dir)
        self.default_model = default_model
        self.anomaly_threshold = anomaly_threshold
        
        # Initialize core components
        self.model_loader = ModelLoader(str(models_dir))
        self.model_registry = ModelRegistry(str(models_dir), registry_file)
        self.pixel_extractor = AIPixelExtractor()
        self.preprocessor = TFLitePreprocessor()
        
        # State tracking
        self.active_model_name: Optional[str] = None
        self.active_model_version: Optional[str] = None
        self.inference_count = 0
        self.total_processing_time = 0.0
        
        # Performance monitoring
        self.performance_stats = {
            'total_inferences': 0,
            'successful_inferences': 0,
            'failed_inferences': 0,
            'anomalies_detected': 0,
            'average_processing_time_ms': 0.0,
            'last_inference_time': None
        }
        
        logger.info(f"InferenceEngine initialized with models_dir: {models_dir}")
        logger.info(f"Default model: {default_model}, Anomaly threshold: {anomaly_threshold}")
    
    def set_model(self, model_name: str, version: str = "latest") -> None:
        """
        Set the active model for inference.
        
        Args:
            model_name: Name of the model to activate
            version: Version to activate ("latest" for most recent)
            
        Raises:
            InferenceEngineError: If model cannot be loaded
        """
        try:
            # Get deployment version if using registry
            if version == "latest":
                deployment_version = self.model_registry.get_deployment_version(model_name)
                if deployment_version:
                    version = deployment_version
                else:
                    # Fallback to ModelLoader's latest resolution
                    pass
            
            # Load and activate model
            self.model_loader.set_active_model(model_name, version)
            
            # Update state
            self.active_model_name = model_name
            self.active_model_version = version
            
            # Get model info for logging
            model_info = self.model_loader.get_model_info()
            
            logger.info(f"Activated model: {model_name}:{version}")
            logger.debug(f"Model input shape: {model_info['input_details'][0]['shape']}")
            logger.debug(f"Model output shape: {model_info['output_details'][0]['shape']}")
            
        except Exception as e:
            raise InferenceEngineError(f"Failed to set model {model_name}:{version}: {str(e)}")
    
    def ensure_model_loaded(self, model_name: Optional[str] = None) -> None:
        """
        Ensure a model is loaded and ready for inference.
        
        Args:
            model_name: Model to load, None for default model
            
        Raises:
            InferenceEngineError: If no model can be loaded
        """
        if model_name is None:
            model_name = self.default_model
        
        # Check if correct model is already loaded
        if (self.active_model_name == model_name and 
            self.model_loader.active_interpreter is not None):
            return
        
        # Load the requested model
        self.set_model(model_name)
    
    def run_inference_from_dicom(self, 
                                dicom_path: Union[str, Path, pydicom.Dataset],
                                model_name: Optional[str] = None,
                                preprocessing_preset: str = "xray_anomaly_detection") -> InferenceResult:
        """
        Run inference on a DICOM file or dataset.
        
        Args:
            dicom_path: Path to DICOM file or pydicom Dataset object
            model_name: Model to use (None for default)
            preprocessing_preset: Preprocessing configuration preset
            
        Returns:
            InferenceResult with comprehensive analysis
            
        Raises:
            InferenceEngineError: If inference fails
        """
        start_time = datetime.now()
        
        # Initialize dicom_path_str early for error handling
        if isinstance(dicom_path, pydicom.Dataset):
            dicom_path_str = "dataset_object"
        else:
            dicom_path_str = str(dicom_path)
        
        try:
            # Ensure model is loaded
            self.ensure_model_loaded(model_name)
            
            # Extract DICOM pixel data and metadata
            if isinstance(dicom_path, pydicom.Dataset):
                dicom_dataset = dicom_path
            else:
                dicom_path = Path(dicom_path)
                
                if not dicom_path.exists():
                    raise InferenceEngineError(f"DICOM file not found: {dicom_path}")
                
                # Load DICOM dataset
                dicom_dataset = pydicom.dcmread(dicom_path)
            
            # Extract AI-ready pixel data
            extraction_result = self.pixel_extractor.extract_for_ai_inference(
                dicom_dataset, output_format='float32'
            )
            pixel_data = extraction_result['pixel_array']
            metadata = extraction_result['metadata']
            
            # Preprocess for TensorFlow Lite model
            preprocessing_result = self.preprocessor.preprocess_for_model(
                dataset=dicom_dataset,
                model_name=self.active_model_name,
                preprocessing_preset=preprocessing_preset
            )
            model_input = preprocessing_result['preprocessed_data']
            preprocessing_info = preprocessing_result['preprocessing_info']
            
            # Run inference
            raw_output = self.model_loader.run_inference(model_input)
            
            # Process results
            confidence_score = float(raw_output.flatten()[0])  # Assuming binary classification
            anomaly_detected = confidence_score > self.anomaly_threshold
            
            # Calculate processing time
            end_time = datetime.now()
            processing_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Create result object
            result = InferenceResult(
                model_name=self.active_model_name,
                model_version=self.active_model_version,
                raw_output=raw_output,
                confidence_score=confidence_score,
                anomaly_detected=anomaly_detected,
                processing_time_ms=processing_time_ms,
                input_shape=model_input.shape,
                output_shape=raw_output.shape,
                dicom_metadata=metadata,
                preprocessing_info=preprocessing_info,
                timestamp=end_time.isoformat(),
                model_info={
                    'name': self.active_model_name,
                    'version': self.active_model_version
                },
                performance_metrics={
                    'inference_time': (processing_time_ms / 1000.0),
                    'total_time': (processing_time_ms / 1000.0)
                }
            )
            
            # Update performance statistics
            self._update_performance_stats(processing_time_ms, True, anomaly_detected)
            
            logger.info(f"Inference completed: {dicom_path_str}, "
                       f"Confidence: {confidence_score:.3f}, "
                       f"Anomaly: {anomaly_detected}, "
                       f"Time: {processing_time_ms:.1f}ms")
            
            return result
            
        except Exception as e:
            # Update error statistics
            processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            self._update_performance_stats(processing_time_ms, False, False)
            
            error_msg = f"Inference failed for {dicom_path_str}: {str(e)}"
            logger.error(error_msg)
            raise InferenceEngineError(error_msg) from e
    
    def run_batch_inference(self, 
                           dicom_paths: List[Union[str, Path]],
                           model_name: Optional[str] = None,
                           preprocessing_preset: str = "xray_anomaly_detection",
                           continue_on_error: bool = True) -> BatchInferenceResult:
        """
        Run inference on multiple DICOM files.
        
        Args:
            dicom_paths: List of paths to DICOM files
            model_name: Model to use (None for default)
            preprocessing_preset: Preprocessing configuration preset
            continue_on_error: Whether to continue processing if individual files fail
            
        Returns:
            BatchInferenceResult with aggregated results
        """
        start_time = datetime.now()
        
        individual_results = []
        errors = []
        anomalies_detected = 0
        success_count = 0
        error_count = 0
        
        logger.info(f"Starting batch inference on {len(dicom_paths)} DICOM files")
        
        for i, dicom_path in enumerate(dicom_paths):
            try:
                result = self.run_inference_from_dicom(
                    dicom_path, model_name, preprocessing_preset
                )
                individual_results.append(result)
                success_count += 1
                
                if result.anomaly_detected:
                    anomalies_detected += 1
                
                logger.debug(f"Batch progress: {i+1}/{len(dicom_paths)} - "
                           f"Success: {success_count}, Errors: {error_count}")
                
            except Exception as e:
                error_count += 1
                error_msg = f"Failed to process {dicom_path}: {str(e)}"
                errors.append(error_msg)
                logger.warning(error_msg)
                
                if not continue_on_error:
                    break
        
        # Calculate batch statistics
        end_time = datetime.now()
        total_processing_time_ms = (end_time - start_time).total_seconds() * 1000
        average_processing_time_ms = (
            total_processing_time_ms / len(dicom_paths) if dicom_paths else 0
        )
        
        batch_result = BatchInferenceResult(
            individual_results=individual_results,
            batch_size=len(dicom_paths),
            total_processing_time_ms=total_processing_time_ms,
            average_processing_time_ms=average_processing_time_ms,
            anomalies_detected=anomalies_detected,
            success_count=success_count,
            error_count=error_count,
            errors=errors
        )
        
        logger.info(f"Batch inference completed: {success_count}/{len(dicom_paths)} successful, "
                   f"{anomalies_detected} anomalies detected, "
                   f"Total time: {total_processing_time_ms:.1f}ms")
        
        return batch_result
    
    def run_inference_from_pixels(self, 
                                 pixel_data: np.ndarray,
                                 model_name: Optional[str] = None,
                                 preprocessing_preset: str = "xray_anomaly_detection",
                                 metadata: Optional[Dict[str, Any]] = None) -> InferenceResult:
        """
        Run inference on pre-extracted pixel data.
        
        Args:
            pixel_data: Preprocessed pixel data array
            model_name: Model to use (None for default)
            preprocessing_preset: Preprocessing configuration preset
            metadata: Optional metadata dictionary
            
        Returns:
            InferenceResult with analysis
        """
        # This method needs redesign for direct pixel input without DICOM dataset
        # For now, we'll raise NotImplementedError
        raise NotImplementedError(
            "Direct pixel input preprocessing needs redesign. "
            "Use run_inference_from_dicom() with DICOM dataset instead."
        )
    
    def set_anomaly_threshold(self, threshold: float) -> None:
        """
        Set the anomaly detection threshold.
        
        Args:
            threshold: New threshold value (0.0-1.0)
            
        Raises:
            ValueError: If threshold is outside valid range
        """
        if not 0.0 <= threshold <= 1.0:
            raise ValueError("Anomaly threshold must be between 0.0 and 1.0")
        
        old_threshold = self.anomaly_threshold
        self.anomaly_threshold = threshold
        
        logger.info(f"Updated anomaly threshold: {old_threshold} -> {threshold}")
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance statistics.
        
        Returns:
            Dictionary with performance metrics
        """
        stats = self.performance_stats.copy()
        
        # Add model information
        if self.active_model_name:
            stats['active_model'] = {
                'name': self.active_model_name,
                'version': self.active_model_version
            }
        
        # Add system information
        stats['anomaly_threshold'] = self.anomaly_threshold
        stats['models_directory'] = str(self.models_dir)
        
        return stats
    
    def get_model_information(self) -> Dict[str, Any]:
        """
        Get detailed information about the active model.
        
        Returns:
            Dictionary with model information
            
        Raises:
            InferenceEngineError: If no model is active
        """
        if not self.active_model_name:
            raise InferenceEngineError("No active model set")
        
        try:
            # Get model loader information
            model_info = self.model_loader.get_model_info()
            
            # Get registry metadata if available
            registry_metadata = self.model_registry.get_model_metadata(
                self.active_model_name, self.active_model_version
            )
            
            # Combine information
            combined_info = {
                'model_name': self.active_model_name,
                'model_version': self.active_model_version,
                'loader_info': model_info,
                'registry_metadata': registry_metadata.to_dict() if registry_metadata else None,
                'anomaly_threshold': self.anomaly_threshold
            }
            
            return combined_info
            
        except Exception as e:
            raise InferenceEngineError(f"Failed to get model information: {str(e)}")
    
    def _update_performance_stats(self, processing_time_ms: float, 
                                 success: bool, anomaly_detected: bool) -> None:
        """Update internal performance statistics."""
        self.performance_stats['total_inferences'] += 1
        
        if success:
            self.performance_stats['successful_inferences'] += 1
            if anomaly_detected:
                self.performance_stats['anomalies_detected'] += 1
        else:
            self.performance_stats['failed_inferences'] += 1
        
        # Update average processing time
        total_successful = self.performance_stats['successful_inferences']
        if total_successful > 0:
            current_avg = self.performance_stats['average_processing_time_ms']
            new_avg = ((current_avg * (total_successful - 1)) + processing_time_ms) / total_successful
            self.performance_stats['average_processing_time_ms'] = new_avg
        
        self.performance_stats['last_inference_time'] = datetime.now().isoformat()
    
    def reset_performance_stats(self) -> None:
        """Reset performance statistics to initial state."""
        self.performance_stats = {
            'total_inferences': 0,
            'successful_inferences': 0,
            'failed_inferences': 0,
            'anomalies_detected': 0,
            'average_processing_time_ms': 0.0,
            'last_inference_time': None
        }
        logger.info("Performance statistics reset")
    
    def __repr__(self) -> str:
        """String representation of InferenceEngine instance."""
        return (f"InferenceEngine(models_dir='{self.models_dir}', "
                f"active_model='{self.active_model_name}:{self.active_model_version}', "
                f"threshold={self.anomaly_threshold})") 