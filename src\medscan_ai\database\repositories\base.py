"""Base repository class for common database operations.

Provides generic CRUD operations and common patterns
for all repository implementations.
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from sqlalchemy import and_, asc, desc, or_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from ..models.base import BaseModel

T = TypeVar("T", bound=BaseModel)


class BaseRepository(Generic[T]):
    """Base repository class with common CRUD operations."""

    def __init__(self, model_class: Type[T], session: Session):
        """Initialize repository with model class and session.

        Args:
            model_class: SQLAlchemy model class
            session: Database session
        """
        self.model_class = model_class
        self.session = session

    def create(self, **kwargs) -> T:
        """Create a new record.

        Args:
            **kwargs: Model field values

        Returns:
            Created model instance
        """
        instance = self.model_class(**kwargs)
        self.session.add(instance)
        self.session.flush()  # Get ID without committing
        self.session.refresh(instance)
        return instance

    def get_by_id(self, id: int) -> Optional[T]:
        """Get record by ID.

        Args:
            id: Record ID

        Returns:
            Model instance or None if not found
        """
        return (
            self.session.query(self.model_class)
            .filter(self.model_class.id == id)
            .first()
        )

    def get_all(
        self,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None,
    ) -> List[T]:
        """Get all records with optional pagination.

        Args:
            limit: Maximum number of records
            offset: Number of records to skip
            order_by: Field to order by (prefix with '-' for descending)

        Returns:
            List of model instances
        """
        query = self.session.query(self.model_class)

        # Apply ordering
        if order_by:
            if order_by.startswith("-"):
                field_name = order_by[1:]
                if hasattr(self.model_class, field_name):
                    query = query.order_by(desc(getattr(self.model_class, field_name)))
            else:
                if hasattr(self.model_class, order_by):
                    query = query.order_by(asc(getattr(self.model_class, order_by)))

        # Apply pagination
        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)

        return query.all()

    def filter_by(self, **kwargs) -> List[T]:
        """Filter records by field values.

        Args:
            **kwargs: Field filters

        Returns:
            List of matching model instances
        """
        return self.session.query(self.model_class).filter_by(**kwargs).all()

    def find_one_by(self, **kwargs) -> Optional[T]:
        """Find single record by field values.

        Args:
            **kwargs: Field filters

        Returns:
            Model instance or None if not found
        """
        return self.session.query(self.model_class).filter_by(**kwargs).first()

    def update(self, instance: T, **kwargs) -> T:
        """Update record with new values.

        Args:
            instance: Model instance to update
            **kwargs: New field values

        Returns:
            Updated model instance
        """
        for key, value in kwargs.items():
            if hasattr(instance, key):
                setattr(instance, key, value)

        self.session.flush()
        self.session.refresh(instance)
        return instance

    def delete(self, instance: T) -> bool:
        """Delete record.

        Args:
            instance: Model instance to delete

        Returns:
            True if deleted successfully
        """
        try:
            self.session.delete(instance)
            self.session.flush()
            return True
        except SQLAlchemyError:
            return False

    def delete_by_id(self, id: int) -> bool:
        """Delete record by ID.

        Args:
            id: Record ID

        Returns:
            True if deleted successfully
        """
        instance = self.get_by_id(id)
        if instance:
            return self.delete(instance)
        return False

    def count(self, **kwargs) -> int:
        """Count records matching criteria.

        Args:
            **kwargs: Field filters

        Returns:
            Number of matching records
        """
        query = self.session.query(self.model_class)
        if kwargs:
            query = query.filter_by(**kwargs)
        return query.count()

    def exists(self, **kwargs) -> bool:
        """Check if record exists.

        Args:
            **kwargs: Field filters

        Returns:
            True if record exists
        """
        return self.count(**kwargs) > 0

    def bulk_create(self, records: List[Dict[str, Any]]) -> List[T]:
        """Create multiple records in bulk.

        Args:
            records: List of record data dictionaries

        Returns:
            List of created model instances
        """
        instances = [self.model_class(**record) for record in records]
        self.session.add_all(instances)
        self.session.flush()

        # Refresh all instances to get IDs
        for instance in instances:
            self.session.refresh(instance)

        return instances

    def bulk_update(self, updates: List[Dict[str, Any]]) -> int:
        """Update multiple records in bulk.

        Args:
            updates: List of update dictionaries with 'id' and update fields

        Returns:
            Number of updated records
        """
        updated_count = 0
        for update_data in updates:
            if "id" not in update_data:
                continue

            record_id = update_data.pop("id")
            instance = self.get_by_id(record_id)
            if instance:
                self.update(instance, **update_data)
                updated_count += 1

        return updated_count

    def search(self, search_term: str, search_fields: List[str]) -> List[T]:
        """Search records by term in specified fields.

        Args:
            search_term: Term to search for
            search_fields: List of field names to search in

        Returns:
            List of matching model instances
        """
        if not search_term or not search_fields:
            return []

        conditions = []
        for field_name in search_fields:
            if hasattr(self.model_class, field_name):
                field = getattr(self.model_class, field_name)
                conditions.append(field.ilike(f"%{search_term}%"))

        if not conditions:
            return []

        return self.session.query(self.model_class).filter(or_(*conditions)).all()

    def get_paginated(
        self, page: int = 1, per_page: int = 20, **filters
    ) -> Dict[str, Any]:
        """Get paginated results.

        Args:
            page: Page number (1-based)
            per_page: Records per page
            **filters: Additional filters

        Returns:
            Dictionary with items, total, page info
        """
        query = self.session.query(self.model_class)

        if filters:
            query = query.filter_by(**filters)

        total = query.count()
        offset = (page - 1) * per_page
        items = query.offset(offset).limit(per_page).all()

        return {
            "items": items,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page,
            "has_prev": page > 1,
            "has_next": page * per_page < total,
        }
