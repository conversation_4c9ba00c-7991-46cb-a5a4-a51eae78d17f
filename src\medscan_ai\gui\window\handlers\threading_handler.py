"""
Threading Handler Mixin for MedScan AI Main Window

Provides background threading capabilities through WorkerManager integration,
enabling responsive UI during heavy operations like DICOM loading and AI inference.
"""

import logging
from typing import Optional, Dict, Any
from pathlib import Path

from PySide6.QtCore import Slot
from PySide6.QtWidgets import QProgressBar, QLabel, QPushButton, QHBoxLayout, QWidget

from ...core import WorkerManager

logger = logging.getLogger(__name__)


class ThreadingHandlerMixin:
    """
    Mixin providing threading capabilities to the main window.
    
    Integrates WorkerManager for background operations and provides
    UI components for progress tracking and user interaction.
    """

    def __init__(self):
        """Initialize threading components."""
        super().__init__()
        
        # Initialize worker manager
        self._worker_manager: Optional[WorkerManager] = None
        self._init_threading_components()
        
        # Threading UI components
        self._progress_bar: Optional[QProgressBar] = None
        self._status_label: Optional[QLabel] = None
        self._cancel_button: Optional[QPushButton] = None
        self._threading_widget: Optional[QWidget] = None
        
        # Active operation tracking
        self._current_operation_id: Optional[str] = None
        self._current_operation_type: Optional[str] = None
        
        logger.debug("ThreadingHandlerMixin initialized")

    def _init_threading_components(self):
        """Initialize threading infrastructure."""
        try:
            # Create worker manager
            self._worker_manager = WorkerManager(parent=self)
            
            # Connect worker manager signals
            self._connect_worker_signals()
            
            # Create threading UI components
            self._create_threading_ui()
            
            logger.info("Threading components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize threading components: {e}")
            self._worker_manager = None

    def _connect_worker_signals(self):
        """Connect WorkerManager signals to UI handlers."""
        if not self._worker_manager:
            return
            
        try:
            # DICOM loading signals
            self._worker_manager.dicom_loading_started.connect(self._on_dicom_loading_started)
            self._worker_manager.dicom_loading_progress.connect(self._on_progress_updated)
            self._worker_manager.dicom_loading_finished.connect(self._on_dicom_loading_finished)
            self._worker_manager.dicom_loading_error.connect(self._on_operation_error)
            
            # General signals
            self._worker_manager.all_operations_completed.connect(self._on_all_operations_completed)
            self._worker_manager.worker_performance_stats.connect(self._on_performance_stats_updated)
            
            logger.debug("Worker manager signals connected")
            
        except Exception as e:
            logger.error(f"Failed to connect worker signals: {e}")

    def _create_threading_ui(self):
        """Create UI components for threading operations."""
        try:
            # Create main threading widget
            self._threading_widget = QWidget()
            layout = QHBoxLayout(self._threading_widget)
            layout.setContentsMargins(5, 5, 5, 5)
            
            # Progress bar
            self._progress_bar = QProgressBar()
            self._progress_bar.setVisible(False)
            self._progress_bar.setTextVisible(True)
            
            # Status label
            self._status_label = QLabel("Ready")
            
            # Cancel button
            self._cancel_button = QPushButton("Cancel")
            self._cancel_button.setVisible(False)
            self._cancel_button.clicked.connect(self._on_cancel_clicked)
            
            # Add to layout
            layout.addWidget(self._status_label)
            layout.addWidget(self._progress_bar)
            layout.addWidget(self._cancel_button)
            layout.addStretch()
            
            logger.debug("Threading UI components created")
            
        except Exception as e:
            logger.error(f"Failed to create threading UI: {e}")

    def get_threading_widget(self) -> Optional[QWidget]:
        """Get the threading UI widget for embedding in status bar."""
        return self._threading_widget

    def load_dicom_file_threaded(self, file_path: str, use_lazy_loading: bool = True) -> bool:
        """
        Load DICOM file in background thread.
        
        Args:
            file_path: Path to DICOM file
            use_lazy_loading: Whether to use lazy loading optimization
            
        Returns:
            True if operation started successfully
        """
        if not self._worker_manager:
            logger.error("Worker manager not available")
            return False
            
        if self._worker_manager.is_busy():
            logger.warning("Background operation already in progress")
            return False
            
        try:
            operation_id = self._worker_manager.load_dicom_file(file_path, use_lazy_loading)
            if operation_id:
                self._current_operation_id = operation_id
                self._current_operation_type = "dicom_loading"
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Failed to start threaded DICOM loading: {e}")
            return False

    @Slot(str)
    def _on_dicom_loading_started(self, file_path: str):
        """Handle DICOM loading start."""
        try:
            file_name = Path(file_path).name
            self._show_progress(f"Loading DICOM: {file_name}")
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"Loading DICOM file: {file_name}")
                
        except Exception as e:
            logger.error(f"Error handling DICOM loading start: {e}")

    @Slot(int, str)
    def _on_progress_updated(self, percentage: int, message: str):
        """Handle progress updates."""
        try:
            if self._progress_bar:
                self._progress_bar.setValue(percentage)
                
            if self._status_label:
                self._status_label.setText(message)
                
        except Exception as e:
            logger.error(f"Error handling progress update: {e}")

    @Slot(object)
    def _on_dicom_loading_finished(self, result):
        """Handle DICOM loading completion."""
        try:
            self._reset_threading_ui()
            
            # Process result using existing file handler methods
            if hasattr(self, '_handle_dicom_file_loaded') and result:
                self._handle_dicom_file_loaded(result.dataset, result.file_path)
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("DICOM file loaded successfully", 3000)
                
            logger.info("DICOM loading completed successfully")
            
        except Exception as e:
            logger.error(f"Error handling DICOM loading completion: {e}")

    @Slot(str, str)
    def _on_operation_error(self, error_message: str, error_details: str):
        """Handle operation errors."""
        try:
            self._reset_threading_ui()
            
            logger.error(f"Operation error: {error_message} - {error_details}")
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"Error: {error_message}", 5000)
                
        except Exception as e:
            logger.error(f"Error handling operation error: {e}")

    @Slot()
    def _on_all_operations_completed(self):
        """Handle all operations completion."""
        try:
            self._reset_threading_ui()
            self._current_operation_id = None
            self._current_operation_type = None
            
        except Exception as e:
            logger.error(f"Error handling operations completion: {e}")

    @Slot(dict)
    def _on_performance_stats_updated(self, stats: Dict[str, Any]):
        """Handle performance statistics updates."""
        logger.debug(f"Performance stats updated: {stats}")

    @Slot()
    def _on_cancel_clicked(self):
        """Handle cancel button click."""
        try:
            if self._worker_manager and self._current_operation_id:
                self._worker_manager.cancel_worker(self._current_operation_id)
                
                if hasattr(self, 'statusBar'):
                    self.statusBar().showMessage("Operation cancelled", 3000)
                    
        except Exception as e:
            logger.error(f"Error handling cancel click: {e}")

    def _show_progress(self, message: str):
        """Show progress UI components."""
        try:
            if self._progress_bar:
                self._progress_bar.setValue(0)
                self._progress_bar.setVisible(True)
                
            if self._status_label:
                self._status_label.setText(message)
                
            if self._cancel_button:
                self._cancel_button.setVisible(True)
                
        except Exception as e:
            logger.error(f"Error showing progress: {e}")

    def _reset_threading_ui(self):
        """Reset threading UI to idle state."""
        try:
            if self._progress_bar:
                self._progress_bar.setVisible(False)
                self._progress_bar.setValue(0)
                
            if self._status_label:
                self._status_label.setText("Ready")
                
            if self._cancel_button:
                self._cancel_button.setVisible(False)
                
        except Exception as e:
            logger.error(f"Error resetting threading UI: {e}")

    def _cleanup_threading(self):
        """Cleanup threading resources."""
        try:
            if self._worker_manager:
                self._worker_manager.cancel_all_workers()
                self._worker_manager.cleanup()
                self._worker_manager = None
                
        except Exception as e:
            logger.error(f"Error cleaning up threading: {e}")

    def closeEvent(self, event):
        """Handle window close event - cleanup threading resources."""
        try:
            self._cleanup_threading()
            super().closeEvent(event)
            
        except Exception as e:
            logger.error(f"Error during threading cleanup on close: {e}")
            if hasattr(super(), 'closeEvent'):
                super().closeEvent(event)