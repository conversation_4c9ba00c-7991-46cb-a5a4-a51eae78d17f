"""Tests for Immutability Manager."""

import unittest
from unittest.mock import Mock, patch
from datetime import datetime

from .immutability import ImmutabilityManager
from .service import AuditService, AuditEvent, AuditContext, ActionType, ActionCategory


class TestImmutabilityManager(unittest.TestCase):
    """Test cases for ImmutabilityManager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_session = Mock()
        self.manager = ImmutabilityManager(session=self.mock_session)
    
    def test_calculate_content_hash(self):
        """Test content hash calculation."""
        mock_audit_log = Mock()
        mock_audit_log.user_id = "test_user"
        mock_audit_log.action = "LOGIN" 
        mock_audit_log.resource_type = "USER"
        mock_audit_log.resource_id = "user_123"
        mock_audit_log.success = True
        mock_audit_log.timestamp = datetime.now()
        
        hash_result = self.manager.calculate_content_hash(mock_audit_log)
        
        self.assertIsNotNone(hash_result)
        self.assertEqual(len(hash_result), 64)  # SHA-256 hex length
    
    def test_calculate_chain_hash(self):
        """Test chain hash calculation."""
        content_hash = "abc123"
        previous_hash = "def456" 
        sequence = 1
        
        chain_hash = self.manager.calculate_chain_hash(content_hash, previous_hash, sequence)
        
        self.assertIsNotNone(chain_hash)
        self.assertEqual(len(chain_hash), 64)  # HMAC SHA-256 hex length
    
    def test_add_to_chain(self):
        """Test adding entry to hash chain."""
        mock_audit_log = Mock()
        mock_audit_log.user_id = "test_user"
        mock_audit_log.action = "READ"
        mock_audit_log.timestamp = datetime.now()
        
        result = self.manager.add_to_chain(mock_audit_log)
        
        self.assertIsNotNone(mock_audit_log.hash_signature)
        self.assertEqual(mock_audit_log.chain_sequence, 1)
        self.assertTrue(mock_audit_log.tamper_verified)
    
    def test_verify_entry_integrity(self):
        """Test entry integrity verification."""
        # Create mock audit log with valid hash
        mock_audit_log = Mock()
        mock_audit_log.user_id = "test_user" 
        mock_audit_log.action = "LOGIN"
        mock_audit_log.resource_type = "USER"
        mock_audit_log.resource_id = "user_123"
        mock_audit_log.success = True
        mock_audit_log.timestamp = datetime.now()
        
        # Add to chain first
        self.manager.add_to_chain(mock_audit_log)
        
        # Verify integrity
        is_valid = self.manager.verify_entry_integrity(mock_audit_log)
        self.assertTrue(is_valid)
        
        # Test with corrupted hash
        mock_audit_log.hash_signature = "invalid_hash"
        is_valid = self.manager.verify_entry_integrity(mock_audit_log)
        self.assertFalse(is_valid)


class TestAuditServiceIntegration(unittest.TestCase):
    """Test AuditService with immutability."""
    
    @patch('medscan_ai.security.audit.service.get_session')
    def test_audit_service_with_immutability(self, mock_get_session):
        """Test AuditService logs with hash chain."""
        mock_session = Mock()
        mock_get_session.return_value = mock_session
        
        audit_service = AuditService(session=mock_session)
        
        # Mock repository
        with patch.object(audit_service, 'repository') as mock_repo:
            mock_audit_log = Mock()
            mock_repo.create.return_value = mock_audit_log
            
            # Create audit event
            context = AuditContext(user_id="test_user", ip_address="127.0.0.1")
            event = AuditEvent(
                action=ActionType.LOGIN,
                action_category=ActionCategory.AUTHENTICATION,
                success=True,
                context=context,
                phi_accessed=False
            )
            
            # Log event
            result = audit_service.log_event(event)
            
            # Verify event was logged
            self.assertIsNotNone(result)
            mock_repo.create.assert_called_once()


if __name__ == '__main__':
    unittest.main() 