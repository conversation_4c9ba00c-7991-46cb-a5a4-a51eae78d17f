"""
Progressive Dataset Downloader for Large Medical Imaging Datasets

This module handles downloading large medical imaging datasets (100GB+) in chunks
with resumption capabilities, integrity verification, and progress tracking.

Supports:
- NIH ChestX-ray14 Dataset
- CheXpert Dataset  
- MIMIC-CXR Dataset
- Custom medical imaging datasets
"""

import os
import sys
import json
import time
import hashlib
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Callable, Any, Union
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from dataclasses import dataclass

# Safe tqdm import with fallback
try:
    import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    print("Warning: tqdm not available. Progress bars will be disabled.")

import psutil

from ...core.utils.logging_config import get_logger
from .memory_monitor import MemoryMonitor

logger = get_logger(__name__)


@dataclass
class DatasetConfig:
    """Configuration for a medical imaging dataset."""
    name: str
    base_url: str
    files: List[Dict[str, Any]]  # [{"url": "...", "filename": "...", "size_mb": 100, "checksum": "..."}]
    total_size_gb: float
    description: str
    license_info: str
    citation: str


class DownloadProgress:
    """Track download progress with thread safety."""
    
    def __init__(self, total_size: int):
        self.total_size = total_size
        self.downloaded = 0
        self.lock = threading.Lock()
        self.start_time = time.time()
        
    def update(self, chunk_size: int):
        """Update downloaded bytes."""
        with self.lock:
            self.downloaded += chunk_size
    
    def get_progress(self) -> Dict[str, float]:
        """Get current progress information."""
        with self.lock:
            elapsed = time.time() - self.start_time
            speed_mbps = (self.downloaded / (1024 * 1024)) / max(elapsed, 1)
            percent = (self.downloaded / self.total_size) * 100 if self.total_size > 0 else 0
            
            return {
                "downloaded_mb": self.downloaded / (1024 * 1024),
                "total_mb": self.total_size / (1024 * 1024),
                "percent": percent,
                "speed_mbps": speed_mbps,
                "elapsed_seconds": elapsed
            }


class ChunkedFileDownloader:
    """Download large files in chunks with resumption."""
    
    def __init__(
        self,
        chunk_size_mb: int = 100,
        max_retries: int = 3,
        timeout_seconds: int = 300,
        verify_checksums: bool = True
    ):
        self.chunk_size_mb = chunk_size_mb
        self.chunk_size_bytes = chunk_size_mb * 1024 * 1024
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds
        self.verify_checksums = verify_checksums
        
        # Memory monitoring
        self.memory_monitor = MemoryMonitor(
            max_ram_usage_gb=12.0,  # Conservative limit during downloads
            max_vram_usage_gb=6.0
        )
        
    def download_file(
        self,
        url: str,
        output_path: Path,
        expected_size: Optional[int] = None,
        expected_checksum: Optional[str] = None,
        progress_callback: Optional[Callable[[int], None]] = None
    ) -> bool:
        """Download a file with chunked resumption."""
        
        logger.info(f"Starting download: {url}")
        logger.info(f"Output path: {output_path}")
        
        # Create output directory
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Check available disk space
        if not self._check_disk_space(output_path.parent, expected_size):
            return False
        
        # Check if file already exists and is complete
        if output_path.exists():
            if self._verify_existing_file(output_path, expected_size, expected_checksum):
                logger.info(f"File already exists and is valid: {output_path}")
                return True
            else:
                logger.info("Existing file is incomplete or corrupted, resuming download")
        
        # Get remote file info
        try:
            response = requests.head(url, timeout=self.timeout_seconds)
            response.raise_for_status()
            
            remote_size = int(response.headers.get('content-length', 0))
            supports_range = 'bytes' in response.headers.get('accept-ranges', '')
            
            logger.info(f"Remote file size: {remote_size / (1024**2):.1f}MB")
            logger.info(f"Supports range requests: {supports_range}")
            
        except requests.RequestException as e:
            logger.error(f"Error getting file info: {e}")
            return False
        
        # Determine starting position for resumption
        start_pos = 0
        if output_path.exists() and supports_range:
            start_pos = output_path.stat().st_size
            logger.info(f"Resuming download from position: {start_pos / (1024**2):.1f}MB")
        
        # Download file in chunks
        try:
            with open(output_path, 'ab' if start_pos > 0 else 'wb') as f:
                current_pos = start_pos
                
                while current_pos < remote_size:
                    # Check memory before downloading chunk
                    if not self.memory_monitor.check_memory_limits()["overall_within_limit"]:
                        logger.warning("Memory limits exceeded, pausing download...")
                        time.sleep(5)  # Wait a bit for memory to free up
                        continue
                    
                    end_pos = min(current_pos + self.chunk_size_bytes - 1, remote_size - 1)
                    
                    # Download chunk with retries
                    chunk_data = self._download_chunk(url, current_pos, end_pos)
                    if chunk_data is None:
                        logger.error(f"Failed to download chunk {current_pos}-{end_pos}")
                        return False
                    
                    # Write chunk
                    f.write(chunk_data)
                    f.flush()
                    
                    # Update progress
                    if progress_callback:
                        progress_callback(len(chunk_data))
                    
                    current_pos = end_pos + 1
                    
                    # Log progress
                    percent = (current_pos / remote_size) * 100
                    logger.debug(f"Download progress: {percent:.1f}%")
        
        except Exception as e:
            logger.error(f"Error during download: {e}")
            return False
        
        # Verify downloaded file
        if self._verify_existing_file(output_path, expected_size, expected_checksum):
            logger.info(f"Download completed successfully: {output_path}")
            return True
        else:
            logger.error("Downloaded file verification failed")
            return False
    
    def _download_chunk(self, url: str, start: int, end: int) -> Optional[bytes]:
        """Download a single chunk with retries."""
        
        headers = {'Range': f'bytes={start}-{end}'}
        
        for attempt in range(self.max_retries):
            try:
                response = requests.get(
                    url,
                    headers=headers,
                    timeout=self.timeout_seconds,
                    stream=True
                )
                response.raise_for_status()
                
                # Read chunk data
                chunk_data = b''
                for data in response.iter_content(chunk_size=8192):
                    chunk_data += data
                
                return chunk_data
                
            except requests.RequestException as e:
                logger.warning(f"Chunk download attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"All chunk download attempts failed for {start}-{end}")
        
        return None
    
    def _check_disk_space(self, directory: Path, required_size: Optional[int]) -> bool:
        """Check if sufficient disk space is available."""
        if not required_size:
            return True
        
        try:
            disk_usage = psutil.disk_usage(str(directory))
            available_bytes = disk_usage.free
            required_bytes = required_size * 1.1  # 10% buffer
            
            if available_bytes < required_bytes:
                logger.error(
                    f"Insufficient disk space: need {required_bytes / (1024**3):.1f}GB, "
                    f"available {available_bytes / (1024**3):.1f}GB"
                )
                return False
                
            return True
            
        except Exception as e:
            logger.warning(f"Error checking disk space: {e}")
            return True  # Continue if we can't check
    
    def _verify_existing_file(
        self,
        file_path: Path,
        expected_size: Optional[int] = None,
        expected_checksum: Optional[str] = None
    ) -> bool:
        """Verify if existing file is complete and valid."""
        
        if not file_path.exists():
            return False
        
        # Check file size
        actual_size = file_path.stat().st_size
        if expected_size and abs(actual_size - expected_size) > 1024:  # Allow 1KB tolerance
            logger.warning(f"File size mismatch: expected {expected_size}, got {actual_size}")
            return False
        
        # Check checksum if available and verification is enabled
        if self.verify_checksums and expected_checksum:
            logger.info("Verifying file checksum...")
            actual_checksum = self._calculate_file_checksum(file_path)
            if actual_checksum != expected_checksum:
                logger.warning(f"Checksum mismatch: expected {expected_checksum}, got {actual_checksum}")
                return False
        
        return True
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate MD5 checksum of file."""
        hash_md5 = hashlib.md5()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()


class MedicalDatasetDownloader:
    """Main class for downloading large medical imaging datasets."""
    
    def __init__(
        self,
        download_dir: str = "data/raw",
        cache_dir: str = "data/cache", 
        chunk_size_mb: int = 100,
        max_concurrent_downloads: int = 2,  # Conservative for 100GB datasets
        verify_checksums: bool = True
    ):
        self.download_dir = Path(download_dir)
        self.cache_dir = Path(cache_dir)
        self.chunk_size_mb = chunk_size_mb
        self.max_concurrent_downloads = max_concurrent_downloads
        self.verify_checksums = verify_checksums
        
        # Create directories
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize downloader
        self.file_downloader = ChunkedFileDownloader(
            chunk_size_mb=chunk_size_mb,
            verify_checksums=verify_checksums
        )
        
        # Dataset configurations
        self.dataset_configs = self._load_dataset_configs()
        
        # Memory monitoring
        self.memory_monitor = MemoryMonitor()
    
    def _load_dataset_configs(self) -> Dict[str, DatasetConfig]:
        """Load predefined dataset configurations."""
        
        configs = {}
        
        # NIH ChestX-ray14 Dataset
        configs['nih_chestx14'] = DatasetConfig(
            name="NIH ChestX-ray14",
            base_url="https://nihcc.box.com/shared/static/",
            files=[
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_01.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_02.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_03.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_04.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_05.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_06.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_07.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_08.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_09.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_10.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_11.tar.gz", "size_mb": 7400, "checksum": None},
                {"url": "https://nihcc.box.com/shared/static/********************************.gz", "filename": "images_12.tar.gz", "size_mb": 7400, "checksum": None},
            ],
            total_size_gb=45.0,
            description="NIH Clinical Center ChestX-ray14 dataset with 112,120 X-ray images",
            license_info="NIH Clinical Center Terms of Use",
            citation="Wang et al. ChestX-ray8: Hospital-scale Chest X-ray Database and Benchmarks..."
        )
        
        # CheXpert Dataset
        configs['chexpert'] = DatasetConfig(
            name="CheXpert",
            base_url="https://stanfordmlgroup.github.io/competitions/chexpert/",
            files=[
                {
                    "url": "http://download.cs.stanford.edu/deep/CheXpert-v1.0-small.zip",
                    "filename": "CheXpert-v1.0-small.zip", 
                    "size_mb": 11000,  # ~11GB
                    "checksum": "d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9"
                },
                # Full dataset is very large, recommend starting with small version
            ],
            total_size_gb=11.0,  # Small version
            description="CheXpert: A Large Chest Radiograph Dataset with Uncertainty Labels",
            license_info="Stanford University License",
            citation="Irvin et al. CheXpert: A Large Chest Radiograph Dataset with Uncertainty Labels..."
        )
        
        # Example custom dataset configuration
        configs['custom_medical'] = DatasetConfig(
            name="Custom Medical Dataset",
            base_url="https://example.com/dataset/",
            files=[
                {
                    "url": "https://example.com/dataset/part1.zip",
                    "filename": "medical_images_part1.zip",
                    "size_mb": 25000,  # 25GB
                    "checksum": "e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                },
                {
                    "url": "https://example.com/dataset/part2.zip", 
                    "filename": "medical_images_part2.zip",
                    "size_mb": 25000,  # 25GB
                    "checksum": "f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1"
                },
                {
                    "url": "https://example.com/dataset/part3.zip",
                    "filename": "medical_images_part3.zip", 
                    "size_mb": 25000,  # 25GB
                    "checksum": "g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2"
                },
                {
                    "url": "https://example.com/dataset/part4.zip",
                    "filename": "medical_images_part4.zip",
                    "size_mb": 25000,  # 25GB  
                    "checksum": "h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3"
                }
            ],
            total_size_gb=100.0,
            description="Custom 100GB Medical Imaging Dataset",
            license_info="Custom License",
            citation="Custom Dataset Citation"
        )
        
        return configs
    
    def list_available_datasets(self) -> Dict[str, Dict]:
        """List all available datasets with their information."""
        
        dataset_info = {}
        for name, config in self.dataset_configs.items():
            dataset_info[name] = {
                "name": config.name,
                "description": config.description,
                "total_size_gb": config.total_size_gb,
                "num_files": len(config.files),
                "license_info": config.license_info,
                "citation": config.citation
            }
        
        return dataset_info
    
    def download_dataset(
        self,
        dataset_name: str,
        subset: Optional[str] = None,
        progress_callback: Optional[Callable[[Dict], None]] = None,
        max_files: Optional[int] = None
    ) -> bool:
        """Download a complete dataset."""
        
        if dataset_name not in self.dataset_configs:
            logger.error(f"Unknown dataset: {dataset_name}")
            logger.info(f"Available datasets: {list(self.dataset_configs.keys())}")
            return False
        
        config = self.dataset_configs[dataset_name]
        dataset_dir = self.download_dir / dataset_name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Starting download of {config.name}")
        logger.info(f"Total size: {config.total_size_gb}GB")
        logger.info(f"Files to download: {len(config.files)}")
        
        # Check available disk space
        if not self._check_disk_space(config.total_size_gb * 1.2):  # 20% buffer
            logger.error("Insufficient disk space for download")
            return False
        
        # Filter files by subset or max_files
        files_to_download = config.files
        if subset and subset.lower() == 'small':
            files_to_download = [f for f in config.files if 'small' in f['filename'].lower()]
            if not files_to_download:
                files_to_download = config.files[:2]  # First 2 files as fallback
        
        if max_files:
            files_to_download = files_to_download[:max_files]
        
        # Calculate total size for progress tracking
        total_size = sum(file_info['size_mb'] for file_info in files_to_download) * 1024 * 1024
        progress = DownloadProgress(total_size)
        
        logger.info(f"Downloading {len(files_to_download)} files ({total_size / (1024**3):.1f}GB)")
        
        # Download files with progress tracking
        def update_progress(chunk_size: int):
            progress.update(chunk_size)
            if progress_callback:
                progress_callback(progress.get_progress())
        
        success_count = 0
        total_files = len(files_to_download)
        
        # Sequential download for better control with large files
        for i, file_info in enumerate(files_to_download):
            logger.info(f"Downloading file {i+1}/{total_files}: {file_info['filename']}")
            
            # Check memory before starting each file
            memory_info = self.memory_monitor.get_current_memory_info()
            logger.info(f"Memory before download: RAM {memory_info['ram_used_gb']:.1f}GB, VRAM {memory_info['vram_used_gb']:.1f}GB")
            
            output_path = dataset_dir / file_info['filename']
            expected_size = file_info['size_mb'] * 1024 * 1024
            expected_checksum = file_info.get('checksum')
            
            success = self.file_downloader.download_file(
                url=file_info['url'],
                output_path=output_path,
                expected_size=expected_size,
                expected_checksum=expected_checksum,
                progress_callback=update_progress
            )
            
            if success:
                success_count += 1
                logger.info(f"Successfully downloaded: {file_info['filename']}")
            else:
                logger.error(f"Failed to download: {file_info['filename']}")
                
                # Ask user if they want to continue with remaining files
                user_choice = input(f"Download failed for {file_info['filename']}. Continue with remaining files? (y/n): ")
                if user_choice.lower() != 'y':
                    break
        
        # Save download metadata
        self._save_download_metadata(dataset_name, config, success_count, total_files, files_to_download)
        
        download_success = success_count == total_files
        if download_success:
            logger.info(f"Dataset {dataset_name} downloaded successfully!")
        else:
            logger.warning(f"Dataset download incomplete: {success_count}/{total_files} files")
        
        return download_success
    
    def _check_disk_space(self, required_gb: float) -> bool:
        """Check if sufficient disk space is available."""
        
        try:
            disk_usage = psutil.disk_usage(str(self.download_dir))
            available_gb = disk_usage.free / (1024**3)
            
            logger.info(f"Available disk space: {available_gb:.1f}GB")
            logger.info(f"Required disk space: {required_gb:.1f}GB")
            
            return available_gb >= required_gb
        except Exception as e:
            logger.error(f"Error checking disk space: {e}")
            return False
    
    def _save_download_metadata(
        self,
        dataset_name: str,
        config: DatasetConfig,
        success_count: int,
        total_files: int,
        files_downloaded: List[Dict]
    ):
        """Save download metadata for tracking."""
        
        metadata = {
            "dataset_name": dataset_name,
            "download_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "files_downloaded": success_count,
            "total_files": total_files,
            "download_complete": success_count == total_files,
            "downloaded_files": [f['filename'] for f in files_downloaded[:success_count]],
            "dataset_info": {
                "name": config.name,
                "description": config.description,
                "total_size_gb": config.total_size_gb,
                "license_info": config.license_info,
                "citation": config.citation
            }
        }
        
        metadata_file = self.cache_dir / f"{dataset_name}_download_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Download metadata saved to: {metadata_file}")
    
    def get_download_status(self, dataset_name: str) -> Optional[Dict]:
        """Get download status for a dataset."""
        
        metadata_file = self.cache_dir / f"{dataset_name}_download_metadata.json"
        if not metadata_file.exists():
            return None
        
        try:
            with open(metadata_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error reading download metadata: {e}")
            return None
    
    def verify_dataset_integrity(self, dataset_name: str) -> bool:
        """Verify integrity of downloaded dataset."""
        
        if dataset_name not in self.dataset_configs:
            logger.error(f"Unknown dataset: {dataset_name}")
            return False
        
        config = self.dataset_configs[dataset_name]
        dataset_dir = self.download_dir / dataset_name
        
        if not dataset_dir.exists():
            logger.error(f"Dataset directory not found: {dataset_dir}")
            return False
        
        logger.info(f"Verifying integrity of {config.name}...")
        
        verification_results = []
        for file_info in config.files:
            file_path = dataset_dir / file_info['filename']
            
            if not file_path.exists():
                logger.warning(f"Missing file: {file_info['filename']}")
                verification_results.append(False)
                continue
            
            # Check file size
            expected_size = file_info['size_mb'] * 1024 * 1024
            actual_size = file_path.stat().st_size
            
            if abs(actual_size - expected_size) > 1024:  # Allow 1KB tolerance
                logger.warning(f"Size mismatch for {file_info['filename']}: expected {expected_size}, got {actual_size}")
                verification_results.append(False)
                continue
            
            # Check checksum if available and verification enabled
            if self.verify_checksums and file_info.get('checksum'):
                logger.info(f"Verifying checksum for {file_info['filename']}")
                actual_checksum = self.file_downloader._calculate_file_checksum(file_path)
                expected_checksum = file_info['checksum']
                
                if actual_checksum != expected_checksum:
                    logger.warning(f"Checksum mismatch for {file_info['filename']}")
                    verification_results.append(False)
                    continue
            
            verification_results.append(True)
            logger.debug(f"File verified: {file_info['filename']}")
        
        all_verified = all(verification_results)
        if all_verified:
            logger.info("✅ Dataset integrity verification passed!")
        else:
            logger.warning("❌ Dataset integrity verification failed for some files")
        
        return all_verified


# Example usage and CLI interface
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Download large medical imaging datasets")
    parser.add_argument("--dataset", required=True, help="Dataset to download")
    parser.add_argument("--subset", help="Download subset (small, full)")
    parser.add_argument("--download-dir", default="data/raw", help="Download directory")
    parser.add_argument("--chunk-size", type=int, default=100, help="Chunk size in MB")
    parser.add_argument("--verify", action="store_true", help="Verify dataset integrity after download")
    parser.add_argument("--list", action="store_true", help="List available datasets")
    parser.add_argument("--max-files", type=int, help="Maximum number of files to download")
    
    args = parser.parse_args()
    
    # Initialize downloader
    downloader = MedicalDatasetDownloader(
        download_dir=args.download_dir,
        chunk_size_mb=args.chunk_size
    )
    
    if args.list:
        print("Available datasets:")
        datasets = downloader.list_available_datasets()
        for name, info in datasets.items():
            print(f"\n{name}:")
            print(f"  Name: {info['name']}")
            print(f"  Description: {info['description']}")
            print(f"  Size: {info['total_size_gb']}GB")
            print(f"  Files: {info['num_files']}")
            print(f"  License: {info['license_info']}")
    else:
        # Progress callback
        def show_progress(progress_info):
            percent = progress_info['percent']
            speed = progress_info['speed_mbps']
            downloaded = progress_info['downloaded_mb']
            total = progress_info['total_mb']
            print(f"\rProgress: {percent:.1f}% ({downloaded:.1f}/{total:.1f}MB at {speed:.1f}MB/s)", end='', flush=True)
        
        # Download dataset
        success = downloader.download_dataset(
            dataset_name=args.dataset,
            subset=args.subset,
            progress_callback=show_progress,
            max_files=args.max_files
        )
        
        print()  # New line after progress
        
        if success:
            print(f"✅ Dataset {args.dataset} downloaded successfully!")
            
            if args.verify:
                print("🔍 Verifying dataset integrity...")
                if downloader.verify_dataset_integrity(args.dataset):
                    print("✅ Dataset verification passed!")
                else:
                    print("❌ Dataset verification failed!")
        else:
            print(f"❌ Failed to download dataset {args.dataset}")
