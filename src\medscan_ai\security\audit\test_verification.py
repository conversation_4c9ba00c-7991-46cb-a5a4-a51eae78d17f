"""Comprehensive Test Suite for Audit Verification System.

Tests the integrity, completeness, and security of the audit logging system
with end-to-end verification procedures.
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json
from dataclasses import dataclass

from .verification import (
    AuditLogVerificationService,
    VerificationResult,
    IntegrityCheckResult,
    VerificationReport,
    generate_verification_report_markdown
)
from .service import AuditService, AuditEvent, AuditContext, ActionType, ActionCategory
from .immutability import ImmutabilityManager


class TestAuditVerificationSystem(unittest.TestCase):
    """Comprehensive test suite for audit verification system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_session = Mock()
        self.verification_service = AuditLogVerificationService(self.mock_session)
        
    def test_hash_chain_integrity_verification_pass(self):
        """Test successful hash chain integrity verification."""
        # Mock audit logs with valid hash chain
        mock_logs = []
        for i in range(5):
            log = Mock()
            log.id = i + 1
            log.chain_sequence = i + 1
            log.hash_signature = f"hash_{i}"
            log.previous_hash = f"hash_{i-1}" if i > 0 else None
            log.timestamp = datetime.utcnow() - timedelta(hours=i)
            mock_logs.append(log)
        
        # Mock immutability manager
        self.verification_service._immutability_manager.calculate_content_hash = Mock()
        self.verification_service._immutability_manager.calculate_content_hash.side_effect = [
            f"hash_{i}" for i in range(5)
        ]
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.all.return_value = mock_logs
        self.mock_session.query.return_value = mock_query
        
        # Test verification
        result = self.verification_service.verify_hash_chain_integrity()
        
        self.assertEqual(result.status, VerificationResult.PASS)
        self.assertEqual(result.check_type, "hash_chain_integrity")
        self.assertIn("PASSED", result.message)
        self.assertEqual(result.details["total_records"], 5)
        self.assertEqual(len(result.details["tampered_records"]), 0)
        
    def test_hash_chain_integrity_verification_fail(self):
        """Test hash chain integrity verification with tampered records."""
        # Mock audit logs with tampered hash
        mock_logs = []
        for i in range(3):
            log = Mock()
            log.id = i + 1
            log.chain_sequence = i + 1
            log.hash_signature = f"hash_{i}"
            log.previous_hash = f"hash_{i-1}" if i > 0 else None
            log.timestamp = datetime.utcnow() - timedelta(hours=i)
            mock_logs.append(log)
        
        # Mock immutability manager - return different hash for second record (tampered)
        self.verification_service._immutability_manager.calculate_content_hash = Mock()
        self.verification_service._immutability_manager.calculate_content_hash.side_effect = [
            "hash_0", "tampered_hash_1", "hash_2"  # Second hash is different
        ]
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.all.return_value = mock_logs
        self.mock_session.query.return_value = mock_query
        
        # Test verification
        result = self.verification_service.verify_hash_chain_integrity()
        
        self.assertEqual(result.status, VerificationResult.FAIL)
        self.assertIn("FAILED", result.message)
        self.assertEqual(len(result.details["tampered_records"]), 1)
        self.assertEqual(result.details["tampered_records"][0]["audit_id"], 2)
        
    def test_log_completeness_verification_with_gaps(self):
        """Test log completeness verification with sequence gaps."""
        # Mock audit logs with sequence gap
        mock_logs = []
        sequences = [1, 2, 5, 6]  # Missing sequences 3, 4
        for i, seq in enumerate(sequences):
            log = Mock()
            log.id = i + 1
            log.chain_sequence = seq
            log.timestamp = datetime.utcnow() - timedelta(hours=i)
            mock_logs.append(log)
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.all.return_value = mock_logs
        self.mock_session.query.return_value = mock_query
        
        # Test verification
        result = self.verification_service.verify_log_completeness()
        
        self.assertEqual(result.status, VerificationResult.FAIL)
        self.assertIn("FAILED", result.message)
        self.assertEqual(len(result.details["sequence_gaps"]), 1)
        self.assertEqual(result.details["sequence_gaps"][0]["missing_sequences"], [3, 4])
        
    def test_data_consistency_verification(self):
        """Test data consistency verification."""
        # Mock database queries for consistency checks
        self.mock_session.query.return_value.filter.return_value.count.return_value = 0
        self.mock_session.query.return_value.filter.return_value.group_by.return_value.having.return_value.all.return_value = []
        
        # Test verification
        result = self.verification_service.verify_data_consistency()
        
        self.assertEqual(result.status, VerificationResult.PASS)
        self.assertEqual(result.check_type, "data_consistency")
        self.assertIn("PASSED", result.message)
        
    def test_comprehensive_verification_report(self):
        """Test comprehensive verification report generation."""
        # Mock all verification methods to return PASS
        self.verification_service.verify_hash_chain_integrity = Mock(return_value=IntegrityCheckResult(
            check_type="hash_chain_integrity",
            status=VerificationResult.PASS,
            message="Hash chain verification passed",
            details={},
            timestamp=datetime.utcnow(),
            affected_records=0
        ))
        
        self.verification_service.verify_log_completeness = Mock(return_value=IntegrityCheckResult(
            check_type="log_completeness",
            status=VerificationResult.PASS,
            message="Log completeness verification passed",
            details={},
            timestamp=datetime.utcnow(),
            affected_records=0
        ))
        
        self.verification_service.verify_data_consistency = Mock(return_value=IntegrityCheckResult(
            check_type="data_consistency",
            status=VerificationResult.PASS,
            message="Data consistency verification passed",
            details={},
            timestamp=datetime.utcnow(),
            affected_records=0
        ))
        
        # Mock total records count
        self.mock_session.query.return_value.filter.return_value.count.return_value = 100
        
        # Test comprehensive verification
        report = self.verification_service.run_comprehensive_verification()
        
        self.assertEqual(report.overall_status, VerificationResult.PASS)
        self.assertEqual(len(report.checks_performed), 3)
        self.assertEqual(report.total_records_checked, 100)
        self.assertEqual(report.summary["checks_passed"], 3)
        self.assertEqual(report.summary["checks_failed"], 0)
        
    def test_verification_report_markdown_generation(self):
        """Test markdown report generation."""
        # Create a sample verification report
        checks = [
            IntegrityCheckResult(
                check_type="hash_chain_integrity",
                status=VerificationResult.PASS,
                message="Test passed",
                details={"test": "data"},
                timestamp=datetime.utcnow(),
                affected_records=0
            )
        ]
        
        report = VerificationReport(
            report_id="test_report_123",
            start_time=datetime.utcnow() - timedelta(seconds=30),
            end_time=datetime.utcnow(),
            total_records_checked=100,
            checks_performed=checks,
            overall_status=VerificationResult.PASS,
            summary={
                "duration_seconds": 30.0,
                "checks_passed": 1,
                "checks_failed": 0,
                "checks_with_warnings": 0,
                "checks_with_errors": 0,
                "total_affected_records": 0
            }
        )
        
        # Generate markdown
        markdown = generate_verification_report_markdown(report)
        
        self.assertIn("# Audit Log Integrity Verification Report", markdown)
        self.assertIn("test_report_123", markdown)
        self.assertIn("🟢 PASS", markdown)
        self.assertIn("Total Records Checked:** 100", markdown)
        self.assertIn("✅", markdown)  # Check for pass icon


class TestEndToEndAuditSystem(unittest.TestCase):
    """End-to-end tests for the complete audit system."""
    
    def setUp(self):
        """Set up end-to-end test fixtures."""
        self.mock_session = Mock()
        self.audit_service = AuditService(self.mock_session)
        self.verification_service = AuditLogVerificationService(self.mock_session)
        
    def test_audit_logging_to_verification_pipeline(self):
        """Test complete pipeline from audit logging to verification."""
        # Mock repository
        mock_repo = Mock()
        self.audit_service._repository = mock_repo
        
        # Mock immutability manager
        mock_immutability = Mock()
        mock_immutability.add_to_chain.return_value = ("test_hash", "prev_hash", 1)
        self.audit_service._immutability_manager = mock_immutability
        
        # Create test audit event
        audit_context = AuditContext(
            user_id="test_user",
            ip_address="***********",
            user_agent="Test Agent"
        )
        
        audit_event = AuditEvent(
            action=ActionType.LOGIN,
            action_category=ActionCategory.AUTHENTICATION,
            success=True,
            context=audit_context,
            resource_type="USER_SESSION",
            resource_id="session_123",
            details={"test": "login"},
            phi_accessed=False
        )
        
        # Test audit logging
        result = self.audit_service.log_event(audit_event)
        self.assertTrue(result)
        
        # Verify audit log was created with proper parameters
        mock_repo.create.assert_called_once()
        created_log = mock_repo.create.call_args[0][0]
        
        self.assertEqual(created_log.user_id, "test_user")
        self.assertEqual(created_log.action, "LOGIN")
        self.assertEqual(created_log.success, True)
        self.assertEqual(created_log.resource_type, "USER_SESSION")
        self.assertEqual(created_log.ip_address, "***********")
        
        # Verify immutability was applied
        mock_immutability.add_to_chain.assert_called_once()
        
    def test_security_event_audit_verification(self):
        """Test security event auditing and verification."""
        # Mock high-risk security event
        security_context = AuditContext(
            user_id="suspicious_user",
            ip_address="********",
            user_agent="Suspicious Agent"
        )
        
        security_event = AuditEvent(
            action=ActionType.LOGIN_FAILED,
            action_category=ActionCategory.AUTHENTICATION,
            success=False,
            context=security_context,
            resource_type="USER_SESSION",
            details={
                "failure_reason": "account_locked",
                "security_event": "multiple_failed_attempts",
                "risk_level": "high"
            },
            phi_accessed=False
        )
        
        # Mock repository and immutability
        mock_repo = Mock()
        mock_immutability = Mock()
        mock_immutability.add_to_chain.return_value = ("security_hash", "prev_hash", 2)
        
        self.audit_service._repository = mock_repo
        self.audit_service._immutability_manager = mock_immutability
        
        # Log security event
        result = self.audit_service.log_event(security_event)
        self.assertTrue(result)
        
        # Verify security event was logged with appropriate risk scoring
        mock_repo.create.assert_called_once()
        created_log = mock_repo.create.call_args[0][0]
        
        self.assertEqual(created_log.action, "LOGIN_FAILED")
        self.assertFalse(created_log.success)
        self.assertIn("high", str(created_log.details))


class TestPenetrationTestingProcedures(unittest.TestCase):
    """Penetration testing procedures for audit log security."""
    
    def test_hash_collision_resistance(self):
        """Test hash collision resistance in audit logs."""
        immutability = ImmutabilityManager()
        
        # Create two similar audit events with real data structures (not Mock)
        @dataclass
        class MockAuditLog:
            user_id: str
            action: str
            timestamp: datetime
            resource_type: str
            resource_id: str = "test_resource"
            details: dict = None
            success: bool = True
        
        log1 = MockAuditLog(
            user_id="user1",
            action="LOGIN",
            timestamp=datetime.utcnow(),
            resource_type="USER",
            details={"test": "data1"}
        )
        
        log2 = MockAuditLog(
            user_id="user1", 
            action="LOGIN",
            timestamp=datetime.utcnow(),
            resource_type="USER",
            details={"test": "data2"}  # Slightly different data
        )
        
        # Calculate hashes
        hash1 = immutability.calculate_content_hash(log1)
        hash2 = immutability.calculate_content_hash(log2)
        
        # Verify hashes are different (no collision)
        self.assertNotEqual(hash1, hash2)
        self.assertEqual(len(hash1), 64)  # SHA-256 produces 64-character hex
        self.assertEqual(len(hash2), 64)
        
    def test_tamper_detection_sensitivity(self):
        """Test sensitivity of tamper detection mechanisms."""
        immutability = ImmutabilityManager()
        
        # Create audit log data structures (not Mock)
        @dataclass
        class MockAuditLog:
            user_id: str
            action: str
            timestamp: datetime
            resource_type: str
            resource_id: str = "test_resource"
            details: dict = None
            success: bool = True
        
        # Create original audit log
        original_log = MockAuditLog(
            user_id="test_user",
            action="READ",
            timestamp=datetime.utcnow(),
            resource_type="PATIENT_DATA",
            details={"patient_id": "12345"}
        )
        
        original_hash = immutability.calculate_content_hash(original_log)
        
        # Test minor modification detection
        tampered_log = MockAuditLog(
            user_id="test_user",
            action="READ", 
            timestamp=original_log.timestamp,
            resource_type="PATIENT_DATA",
            details={"patient_id": "12346"}  # Changed one digit
        )
        
        tampered_hash = immutability.calculate_content_hash(tampered_log)
        
        # Verify even minor changes are detected
        self.assertNotEqual(original_hash, tampered_hash)
        
    def test_audit_log_injection_protection(self):
        """Test protection against audit log injection attacks."""
        audit_service = AuditService(Mock())
        
        # Test malicious input in audit event
        malicious_context = AuditContext(
            user_id="admin'; DROP TABLE audit_logs; --",
            ip_address="<script>alert('xss')</script>",
            user_agent="'; SELECT * FROM users; --"
        )
        
        malicious_event = AuditEvent(
            action=ActionType.CREATE,
            action_category=ActionCategory.DATA_ACCESS,
            success=True,
            context=malicious_context,
            resource_type="<iframe src='evil.com'>",
            details={"injection": "'; UPDATE audit_logs SET user_id='hacker'; --"},
            phi_accessed=False
        )
        
        # Mock repository to capture what would be stored
        mock_repo = Mock()
        audit_service._repository = mock_repo
        
        # Test that malicious input is properly handled
        result = audit_service.log_event(malicious_event)
        
        # Verify the event was processed (input sanitization should happen at DB level)
        # In a real implementation, you would verify that the inputs are properly escaped/sanitized
        mock_repo.create.assert_called_once()


def run_comprehensive_audit_test_suite():
    """Run the complete audit system test suite."""
    print("🧪 Running Comprehensive Audit System Test Suite...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestAuditVerificationSystem,
        TestEndToEndAuditSystem,
        TestPenetrationTestingProcedures
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=None)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"🎯 Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback[:100]}...")
    
    if result.errors:
        print(f"\n🚨 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback[:100]}...")
    
    overall_success = len(result.failures) == 0 and len(result.errors) == 0
    status = "✅ PASS" if overall_success else "❌ FAIL"
    print(f"\n🏁 Overall Status: {status}")
    
    return overall_success


if __name__ == "__main__":
    run_comprehensive_audit_test_suite() 