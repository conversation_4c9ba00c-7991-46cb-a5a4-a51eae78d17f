"""
File handling operations for DICOM files in MedScan AI GUI.

This module provides file loading, DICOM processing, and metadata handling
for the medical imaging workflow.
"""

import os
from typing import Op<PERSON>

from PySide6.QtWidgets import QFileDialog, QMessageBox

from ....dicom import <PERSON><PERSON><PERSON><PERSON><PERSON>, DicomReader


class FileHandlerMixin:
    """
    Mixin class providing file handling capabilities for the main window.
    
    This mixin should be used with a QMainWindow that has:
    - _dicom_reader: DicomReader instance
    - _patient_info_widget: QTextEdit for patient info
    - _update_patient_info(): method to update patient display
    - _display_image(): method to display loaded image
    """

    def _open_dicom_files(self):
        """Open and load DICOM files using file dialog."""
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("Open DICOM Files - MedScan AI")
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("DICOM Files (*.dcm *.dicom *.DCM *.DICOM);;All Files (*)")
        file_dialog.setDirectory(os.path.expanduser("~"))

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                # For now, load the first selected file
                first_file = selected_files[0]
                self._load_dicom_file(first_file)

    def _load_dicom_file(self, file_path: str) -> bool:
        """
        Load a DICOM file and update the interface.
        
        Args:
            file_path: Path to the DICOM file
            
        Returns:
            True if loading was successful, False otherwise
        """
        try:
            # Update status
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"Loading DICOM file: {os.path.basename(file_path)}")

            # Load the DICOM file
            dataset = self._dicom_reader.load_file(file_path)
            
            # Store the dataset and file path
            self._current_dataset = dataset
            self._current_file_path = file_path

            # Initialize windowing from DICOM metadata
            if hasattr(self, '_initialize_windowing'):
                self._initialize_windowing()

            # Update patient information display
            self._update_patient_info()

            # Display the image
            self._display_image()

            # Update status
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"Loaded: {os.path.basename(file_path)}", 3000)

            return True

        except DicomError as e:
            error_msg = f"DICOM Error: {str(e)}"
            self._show_error_message("DICOM Loading Error", error_msg)
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Failed to load DICOM file", 3000)
            return False

        except Exception as e:
            error_msg = f"Unexpected error loading DICOM file: {str(e)}"
            self._show_error_message("File Loading Error", error_msg)
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Failed to load file", 3000)
            return False

    def _show_error_message(self, title: str, message: str):
        """
        Show error message dialog.
        
        Args:
            title: Dialog title
            message: Error message to display
        """
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()

    def _show_info_message(self, title: str, message: str):
        """
        Show information message dialog.
        
        Args:
            title: Dialog title
            message: Information message to display
        """
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()

    def _get_file_info_summary(self, file_path: str) -> str:
        """
        Get a summary of file information for display.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Formatted file information string
        """
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            return f"""File: {os.path.basename(file_path)}
Path: {file_path}
Size: {file_size_mb:.2f} MB
Status: Successfully loaded"""
            
        except Exception:
            return f"File: {os.path.basename(file_path)}\nStatus: Information unavailable" 