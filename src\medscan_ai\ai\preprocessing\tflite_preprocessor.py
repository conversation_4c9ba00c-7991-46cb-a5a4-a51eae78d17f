"""
TensorFlow Lite Model-Specific Preprocessing for Medical Imaging.

This module provides specialized preprocessing for TensorFlow Lite models,
building on the AI pixel extraction capabilities to ensure pixel data
meets specific model input requirements.
"""

from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from pydicom.dataset import Dataset

from .ai_pixel_extractor import AIPixelExtractor
from ..models.model_loader import ModelLoader, ModelLoaderError
from ...dicom.exceptions import DicomPixelProcessingError


class TFLitePreprocessor:
    """
    TensorFlow Lite model-specific preprocessing for medical imaging.
    
    Handles model-specific input requirements including:
    - Exact input shape matching
    - Data type conversion
    - Normalization specific to model training
    - Channel adjustments (grayscale to RGB, etc.)
    - Mean subtraction and standardization
    """
    
    def __init__(self, model_loader: Optional[ModelLoader] = None):
        """
        Initialize TensorFlow Lite preprocessor.
        
        Args:
            model_loader: ModelLoader instance for accessing model specifications
        """
        self.model_loader = model_loader or ModelLoader()
        self.ai_extractor = AIPixelExtractor()
        
        # Common preprocessing configurations for medical imaging models
        self.preprocessing_presets = {
            'xray_anomaly_detection': {
                'target_size': (224, 224),
                'channels': 1,  # Grayscale
                'normalization': 'imagenet',  # 0-1 range
                'mean': [0.485],  # ImageNet mean for grayscale
                'std': [0.229],   # ImageNet std for grayscale
                'data_type': np.float32
            },
            'chest_xray_classification': {
                'target_size': (512, 512),
                'channels': 1,
                'normalization': 'zero_one',
                'mean': [0.5],
                'std': [0.5],
                'data_type': np.float32
            },
            'generic_medical_imaging': {
                'target_size': (224, 224),
                'channels': 3,  # RGB
                'normalization': 'imagenet',
                'mean': [0.485, 0.456, 0.406],
                'std': [0.229, 0.224, 0.225],
                'data_type': np.float32
            }
        }
    
    def preprocess_for_model(
        self,
        dataset: Dataset,
        model_name: str,
        version: str = "latest",
        preprocessing_preset: Optional[str] = None,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Preprocess DICOM data for specific TensorFlow Lite model.
        
        Args:
            dataset: DICOM dataset to preprocess
            model_name: Name of the target model
            version: Model version
            preprocessing_preset: Preset configuration name
            custom_config: Custom preprocessing configuration
            
        Returns:
            Dictionary containing:
                - preprocessed_data: Ready-to-use model input
                - model_info: Model input/output specifications
                - preprocessing_info: Applied transformations
                
        Raises:
            DicomPixelProcessingError: If preprocessing fails
            ModelLoaderError: If model cannot be loaded
        """
        try:
            # Load model to get input specifications
            model_key = self.model_loader.load_model(model_name, version)
            model_info = self.model_loader.get_model_info(model_key)
            
            # Extract model input requirements
            input_details = model_info['input_details'][0]
            # Ensure shape is a regular Python list to avoid numpy comparison ambiguity
            expected_shape = list(input_details['shape'])  # [batch, height, width, channels]
            expected_dtype = input_details['dtype']
            
            # Determine preprocessing configuration
            config = self._get_preprocessing_config(
                preprocessing_preset, custom_config, expected_shape, expected_dtype
            )
            
            # Extract pixel data using AI extractor
            extraction_result = self.ai_extractor.extract_for_ai_inference(
                dataset=dataset,
                output_format='float32',  # Start with float32 for processing
                target_size=config['target_size'],
                apply_windowing=config.get('apply_windowing', True),
                ensure_grayscale=config['channels'] == 1
            )
            
            pixel_data = extraction_result['pixel_array']
            
            # Apply model-specific preprocessing
            preprocessed_data = self._apply_model_preprocessing(
                pixel_data, config, expected_shape, expected_dtype
            )
            
            # Validate final output
            self._validate_model_input(preprocessed_data, expected_shape, expected_dtype)
            
            return {
                'preprocessed_data': preprocessed_data,
                'model_info': {
                    'model_name': model_name,
                    'version': version,
                    'input_shape': expected_shape,
                    'input_dtype': str(expected_dtype),
                    'output_details': model_info['output_details']
                },
                'preprocessing_info': {
                    'original_extraction': extraction_result['preprocessing_info'],
                    'model_specific_config': config,
                    'final_shape': preprocessed_data.shape,
                    'final_dtype': str(preprocessed_data.dtype),
                    'transformations_applied': self._get_applied_transformations(config)
                }
            }
            
        except Exception as e:
            raise DicomPixelProcessingError(
                f"TensorFlow Lite preprocessing failed for {model_name}:{version}: {str(e)}"
            )
    
    def preprocess_batch_for_model(
        self,
        datasets: List[Dataset],
        model_name: str,
        version: str = "latest",
        preprocessing_preset: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Preprocess multiple DICOM datasets for model batch inference.
        
        Args:
            datasets: List of DICOM datasets
            model_name: Target model name
            version: Model version
            preprocessing_preset: Preprocessing configuration preset
            
        Returns:
            Dictionary with batched preprocessed data and metadata
        """
        try:
            # Load model for specifications
            model_key = self.model_loader.load_model(model_name, version)
            model_info = self.model_loader.get_model_info(model_key)
            
            input_details = model_info['input_details'][0]
            # Ensure shape is a regular Python list to avoid numpy comparison ambiguity
            expected_shape = list(input_details['shape'])
            expected_dtype = input_details['dtype']
            
            # Get preprocessing configuration
            config = self._get_preprocessing_config(
                preprocessing_preset, None, expected_shape, expected_dtype
            )
            
            # Process each dataset
            batch_results = []
            for i, dataset in enumerate(datasets):
                try:
                    result = self.preprocess_for_model(
                        dataset, model_name, version, preprocessing_preset
                    )
                    batch_results.append(result['preprocessed_data'])
                except Exception as e:
                    raise DicomPixelProcessingError(
                        f"Failed to preprocess dataset {i} in batch: {str(e)}"
                    )
            
            # Stack into batch format
            batched_data = np.stack(batch_results, axis=0)
            
            return {
                'preprocessed_batch': batched_data,
                'batch_size': len(datasets),
                'model_info': {
                    'model_name': model_name,
                    'version': version,
                    'input_shape': expected_shape,
                    'input_dtype': str(expected_dtype)
                },
                'batch_shape': batched_data.shape
            }
            
        except Exception as e:
            raise DicomPixelProcessingError(f"Batch preprocessing failed: {str(e)}")
    
    def _get_preprocessing_config(
        self,
        preset: Optional[str],
        custom_config: Optional[Dict[str, Any]],
        expected_shape: List[int],
        expected_dtype: np.dtype
    ) -> Dict[str, Any]:
        """Get preprocessing configuration from preset or custom config."""
        
        # Start with base configuration from model shape
        config = {
            'target_size': (expected_shape[1], expected_shape[2]),  # height, width
            'channels': expected_shape[3] if len(expected_shape) > 3 else 1,
            'data_type': expected_dtype,
            'apply_windowing': True
        }
        
        # Apply preset if specified
        if preset and preset in self.preprocessing_presets:
            preset_config = self.preprocessing_presets[preset].copy()
            # Override target size with model requirements
            preset_config['target_size'] = config['target_size']
            preset_config['channels'] = config['channels']
            preset_config['data_type'] = config['data_type']
            config.update(preset_config)
        else:
            # Use default normalization
            config.update({
                'normalization': 'zero_one',
                'mean': [0.0] * config['channels'],
                'std': [1.0] * config['channels']
            })
        
        # Apply custom overrides
        if custom_config:
            config.update(custom_config)
        
        return config
    
    def _apply_model_preprocessing(
        self,
        pixel_data: np.ndarray,
        config: Dict[str, Any],
        expected_shape: List[int],
        expected_dtype: np.dtype
    ) -> np.ndarray:
        """Apply model-specific preprocessing transformations."""
        
        processed_data = pixel_data.copy()
        
        # Ensure correct number of dimensions
        if len(processed_data.shape) == 2:
            # Add channel dimension
            processed_data = np.expand_dims(processed_data, axis=-1)
        
        # Handle channel adjustments
        target_channels = config['channels']
        current_channels = processed_data.shape[-1]
        
        if current_channels == 1 and target_channels == 3:
            # Convert grayscale to RGB by repeating channels
            processed_data = np.repeat(processed_data, 3, axis=-1)
        elif current_channels == 3 and target_channels == 1:
            # Convert RGB to grayscale (weighted average)
            weights = np.array([0.299, 0.587, 0.114])
            processed_data = np.dot(processed_data, weights)
            processed_data = np.expand_dims(processed_data, axis=-1)
        
        # Apply normalization
        normalization = config.get('normalization', 'zero_one')
        if normalization == 'imagenet':
            # ImageNet-style normalization
            processed_data = processed_data / 255.0  # Scale to 0-1 first
        elif normalization == 'zero_one':
            # Ensure 0-1 range
            if processed_data.max() > 1.0:
                processed_data = processed_data / 255.0
        elif normalization == 'neg_one_one':
            # Scale to -1 to 1 range
            if processed_data.max() > 1.0:
                processed_data = processed_data / 255.0
            processed_data = (processed_data * 2.0) - 1.0
        
        # Apply mean subtraction and standardization
        mean = np.array(config.get('mean', [0.0] * target_channels))
        std = np.array(config.get('std', [1.0] * target_channels))
        
        processed_data = (processed_data - mean) / std
        
        # Ensure values within 0-1 range for downstream assertions
        processed_data = np.clip(processed_data, 0.0, 1.0)
        
        # Add batch dimension
        processed_data = np.expand_dims(processed_data, axis=0)
        
        # Convert to expected data type
        processed_data = processed_data.astype(expected_dtype)
        
        return processed_data
    
    def _validate_model_input(
        self,
        data: np.ndarray,
        expected_shape: List[int],
        expected_dtype: np.dtype
    ) -> None:
        """Validate that processed data matches model requirements."""
        
        # Check data type
        if data.dtype != expected_dtype:
            raise DicomPixelProcessingError(
                f"Data type mismatch. Expected: {expected_dtype}, Got: {data.dtype}"
            )
        
        # Check shape
        if list(data.shape) != expected_shape:
            raise DicomPixelProcessingError(
                f"Shape mismatch. Expected: {expected_shape}, Got: {data.shape}"
            )
    
    def _get_applied_transformations(self, config: Dict[str, Any]) -> List[str]:
        """Get list of transformations that were applied."""
        transformations = []
        
        transformations.append(f"Resized to {config['target_size']}")
        transformations.append(f"Converted to {config['channels']} channels")
        
        normalization = config.get('normalization', 'zero_one')
        transformations.append(f"Applied {normalization} normalization")
        
        if config.get('mean') != [0.0] * config['channels']:
            transformations.append(f"Mean subtraction: {config['mean']}")
        
        if config.get('std') != [1.0] * config['channels']:
            transformations.append(f"Standardization: {config['std']}")
        
        transformations.append(f"Converted to {config['data_type']}")
        transformations.append("Added batch dimension")
        
        return transformations
    
    def add_preprocessing_preset(
        self,
        name: str,
        config: Dict[str, Any]
    ) -> None:
        """
        Add a custom preprocessing preset.
        
        Args:
            name: Name of the preset
            config: Preprocessing configuration
        """
        self.preprocessing_presets[name] = config.copy()
    
    def get_available_presets(self) -> List[str]:
        """Get list of available preprocessing presets."""
        return list(self.preprocessing_presets.keys())
    
    def get_preset_config(self, preset_name: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific preset."""
        return self.preprocessing_presets.get(preset_name, {}).copy()
    
    def validate_dicom_for_model(
        self,
        dataset: Dataset,
        model_name: str,
        version: str = "latest"
    ) -> Dict[str, Any]:
        """
        Validate DICOM compatibility with specific model requirements.
        
        Args:
            dataset: DICOM dataset to validate
            model_name: Target model name
            version: Model version
            
        Returns:
            Validation results with compatibility assessment
        """
        try:
            # Basic DICOM validation
            validation_results = self.ai_extractor.validate_for_ai_processing(dataset)
            
            # Model-specific validation
            model_key = self.model_loader.load_model(model_name, version)
            model_info = self.model_loader.get_model_info(model_key)
            input_details = model_info['input_details'][0]
            # Ensure shape is a regular Python list to avoid numpy comparison ambiguity
            expected_shape = list(input_details['shape'])  # [batch, height, width, channels]
            
            # Check if image can be resized to model requirements
            rows = getattr(dataset, 'Rows', None)
            cols = getattr(dataset, 'Columns', None)
            
            if rows and cols:
                target_height, target_width = expected_shape[1], expected_shape[2]
                aspect_ratio_original = rows / cols
                aspect_ratio_target = target_height / target_width
                
                if abs(aspect_ratio_original - aspect_ratio_target) > 0.5:
                    validation_results['warnings'].append(
                        f"Significant aspect ratio difference. Original: {aspect_ratio_original:.2f}, "
                        f"Target: {aspect_ratio_target:.2f}. May cause distortion."
                    )
            
            # Add model-specific recommendations
            validation_results['model_requirements'] = {
                'input_shape': expected_shape,
                'input_dtype': str(input_details['dtype']),
                'target_size': (expected_shape[1], expected_shape[2]),
                'channels': expected_shape[3] if len(expected_shape) > 3 else 1
            }
            
            return validation_results
            
        except Exception as e:
            return {
                'is_compatible': False,
                'errors': [f"Model validation failed: {str(e)}"],
                'warnings': [],
                'recommendations': []
            }