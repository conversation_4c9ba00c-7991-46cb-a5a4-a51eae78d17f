"""
Image handling and display operations for MedScan AI GUI.

This module provides image processing, display, and patient information 
management for medical imaging workflows.
"""

import numpy as np
from typing import Optional

from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import QTextEdit

from ...utils import ImageDisplayHelper, numpy_to_qpixmap


class ImageHandlerMixin:
    """
    Mixin class providing image handling capabilities for the main window.
    
    This mixin should be used with a QMainWindow that has:
    - image_viewer: InteractiveImageViewer instance
    - image_placeholder: QLabel for placeholder text
    - _image_helper: ImageDisplayHelper instance
    - _current_dataset: Currently loaded DICOM dataset
    - _patient_info_widget: QTextEdit for patient information
    """

    def _display_image(self):
        """Display the current DICOM image in the viewer."""
        if not hasattr(self, '_current_dataset') or self._current_dataset is None:
            return

        try:
            # Get processed pixel array
            if hasattr(self, '_raw_pixel_array') and self._raw_pixel_array is not None:
                # Use existing processed array
                pixel_array = self._raw_pixel_array
            else:
                # Process fresh from dataset
                pixel_array = self._image_helper.get_display_array(
                    self._current_dataset,
                    apply_windowing=hasattr(self, '_windowing_enabled') and self._windowing_enabled
                )
                self._raw_pixel_array = pixel_array

            # Convert to QPixmap
            pixmap = numpy_to_qpixmap(pixel_array)
            
            if pixmap and not pixmap.isNull():
                # Store current pixmap
                self._current_pixmap = pixmap

                # Show image viewer, hide placeholder
                if hasattr(self, 'image_placeholder') and hasattr(self, 'image_viewer'):
                    self.image_placeholder.hide()
                    self.image_viewer.show()

                # Display image in viewer
                if hasattr(self, 'image_viewer'):
                    self.image_viewer.set_image(pixmap)

                # Update status
                if hasattr(self, 'statusBar'):
                    height, width = pixel_array.shape[:2] if len(pixel_array.shape) >= 2 else (0, 0)
                    self.statusBar().showMessage(
                        f"Image: {width}×{height} pixels | "
                        f"Modality: {getattr(self._current_dataset, 'Modality', 'Unknown')}"
                    )

        except Exception as e:
            # Show error and keep placeholder visible
            if hasattr(self, 'image_viewer') and hasattr(self, 'image_placeholder'):
                self.image_viewer.hide()
                self.image_placeholder.show()
                self.image_placeholder.setText(f"❌ Error displaying image:\n{str(e)}")
            
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("Failed to display image", 3000)

    def _update_patient_info(self):
        """Update patient information display with current DICOM data."""
        if not hasattr(self, '_current_dataset') or self._current_dataset is None:
            return

        try:
            dataset = self._current_dataset
            
            # Extract patient information
            patient_name = getattr(dataset, 'PatientName', 'Unknown')
            patient_id = getattr(dataset, 'PatientID', 'Unknown')
            patient_birth_date = getattr(dataset, 'PatientBirthDate', 'Unknown')
            patient_sex = getattr(dataset, 'PatientSex', 'Unknown')
            
            # Extract study information
            study_date = getattr(dataset, 'StudyDate', 'Unknown')
            study_time = getattr(dataset, 'StudyTime', 'Unknown')
            study_description = getattr(dataset, 'StudyDescription', 'Unknown')
            modality = getattr(dataset, 'Modality', 'Unknown')
            
            # Extract series information
            series_number = getattr(dataset, 'SeriesNumber', 'Unknown')
            series_description = getattr(dataset, 'SeriesDescription', 'Unknown')
            
            # Extract image information
            rows = getattr(dataset, 'Rows', 'Unknown')
            columns = getattr(dataset, 'Columns', 'Unknown')
            pixel_spacing = getattr(dataset, 'PixelSpacing', 'Unknown')
            
            # Format the information
            info_text = f"""👤 PATIENT INFORMATION
Name: {patient_name}
ID: {patient_id}
DOB: {patient_birth_date}
Gender: {patient_sex}

📅 STUDY INFORMATION  
Date: {study_date}
Time: {study_time}
Description: {study_description}
Modality: {modality}

📊 SERIES INFORMATION
Series #: {series_number}
Description: {series_description}

🖼️ IMAGE INFORMATION
Dimensions: {columns} × {rows}
Pixel Spacing: {pixel_spacing}

✅ Status: Image loaded and ready
"""

            # Update the patient info widget
            if hasattr(self, '_patient_info_widget') and self._patient_info_widget:
                self._patient_info_widget.setPlainText(info_text)

        except Exception as e:
            # Show error in patient info
            error_text = f"""❌ PATIENT INFORMATION ERROR

Failed to extract patient information:
{str(e)}

Please try loading a different DICOM file.
"""
            if hasattr(self, '_patient_info_widget') and self._patient_info_widget:
                self._patient_info_widget.setPlainText(error_text)

    def _clear_image_display(self):
        """Clear the current image display and reset to placeholder."""
        # Clear stored data
        self._current_pixmap = None
        self._raw_pixel_array = None
        
        # Show placeholder, hide viewer
        if hasattr(self, 'image_viewer') and hasattr(self, 'image_placeholder'):
            self.image_viewer.hide()
            self.image_placeholder.show()
            
            # Reset placeholder text
            self.image_placeholder.setText("""
🏥 MedScan AI - Interactive Image Viewer

No medical images loaded

Supported formats:
• DICOM (.dcm)
• JPEG, PNG
• TIFF, BMP

Features:
• Mouse wheel zoom at cursor
• Left-click drag to pan
• Keyboard shortcuts (+, -, 0, 1)

Drag & drop files here
or use File > Open DICOM Files
            """)

        # Clear patient info
        if hasattr(self, '_patient_info_widget') and self._patient_info_widget:
            self._patient_info_widget.setPlainText("""Patient: [No patient selected]
ID: ---
DOB: ---
Gender: ---
Study Date: ---
Modality: ---
Series: ---

Status: Ready for new patient
""")

        # Update status
        if hasattr(self, 'statusBar'):
            self.statusBar().showMessage("Image display cleared", 2000)

    def _refresh_image_display(self):
        """Refresh the current image display with updated processing."""
        if hasattr(self, '_current_dataset') and self._current_dataset is not None:
            # Clear processed array to force reprocessing
            self._raw_pixel_array = None
            
            # Redisplay image
            self._display_image()
        else:
            self._clear_image_display()

    def _get_image_info_summary(self) -> str:
        """
        Get a summary of current image information.
        
        Returns:
            Formatted image information string
        """
        if not hasattr(self, '_current_dataset') or self._current_dataset is None:
            return "No image loaded"

        try:
            dataset = self._current_dataset
            rows = getattr(dataset, 'Rows', 'Unknown')
            columns = getattr(dataset, 'Columns', 'Unknown')
            modality = getattr(dataset, 'Modality', 'Unknown')
            
            return f"Image: {columns}×{rows} | Modality: {modality}"
            
        except Exception:
            return "Image information unavailable" 