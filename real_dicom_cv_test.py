#!/usr/bin/env python3
"""
Real DICOM OpenCV Performance Test

Comprehensive performance testing of optimized OpenCV operations using actual DICOM files.
Tests both lazy loading improvements and OpenCV optimization benefits.
"""

import sys
import time
import numpy as np
import psutil
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import tracemalloc
import glob

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def find_dicom_files() -> List[Path]:
    """Find all available DICOM test files."""
    dicom_paths = []
    test_dir = Path("test_data/dicom_samples")
    
    if test_dir.exists():
        # Look for DICOM files (various extensions)
        for pattern in ["*.dcm", "*IMAGEI", "*IMAGEA"]:
            dicom_paths.extend(test_dir.glob(pattern))
    
    # Sort by file size for consistent testing
    dicom_paths.sort(key=lambda p: p.stat().st_size)
    
    return dicom_paths

def categorize_dicom_files(dicom_paths: List[Path]) -> Dict[str, List[Path]]:
    """Categorize DICOM files by size for testing."""
    categories = {
        'small': [],    # < 100KB
        'medium': [],   # 100KB - 1MB
        'large': [],    # 1MB - 5MB
        'xlarge': []    # > 5MB
    }
    
    for path in dicom_paths:
        size_mb = path.stat().st_size / (1024 * 1024)
        
        if size_mb < 0.1:
            categories['small'].append(path)
        elif size_mb < 1.0:
            categories['medium'].append(path)
        elif size_mb < 5.0:
            categories['large'].append(path)
        else:
            categories['xlarge'].append(path)
    
    return categories

def test_dicom_loading_performance(dicom_files: Dict[str, List[Path]]) -> Dict[str, Any]:
    """Test DICOM loading performance: standard vs lazy loading."""
    results = {}
    
    for category, files in dicom_files.items():
        if not files:
            continue
            
        print(f"\n📁 Testing {category.upper()} DICOM files ({len(files)} files)...")
        
        results[category] = {
            'file_count': len(files),
            'standard_loading': {},
            'lazy_loading': {},
            'file_sizes_mb': [f.stat().st_size / (1024*1024) for f in files[:5]]  # First 5 for stats
        }
        
        # Test standard DICOM loading
        print(f"  Testing standard DICOM loading...")
        
        standard_times = []
        standard_memory = []
        
        for i, dicom_path in enumerate(files[:5]):  # Test first 5 files of each category
            try:
                import pydicom
                
                process = psutil.Process()
                memory_before = process.memory_info().rss
                
                start_time = time.time()
                dataset = pydicom.dcmread(str(dicom_path))
                pixel_array = dataset.pixel_array if hasattr(dataset, 'pixel_array') else None
                end_time = time.time()
                
                memory_after = process.memory_info().rss
                
                standard_times.append(end_time - start_time)
                standard_memory.append(memory_after - memory_before)
                
            except Exception as e:
                print(f"    Failed to load {dicom_path.name}: {e}")
                continue
        
        if standard_times:
            results[category]['standard_loading'] = {
                'avg_time': np.mean(standard_times),
                'total_time': sum(standard_times),
                'avg_memory_mb': np.mean(standard_memory) / (1024*1024),
                'files_tested': len(standard_times)
            }
        
        # Test lazy DICOM loading
        print(f"  Testing lazy DICOM loading...")
        
        lazy_times = []
        lazy_memory = []
        
        for i, dicom_path in enumerate(files[:5]):
            try:
                from medscan_ai.dicom.io.lazy_reader import read_dicom_lazy
                
                process = psutil.Process()
                memory_before = process.memory_info().rss
                
                start_time = time.time()
                dataset = read_dicom_lazy(str(dicom_path), metadata_only=True)
                end_time = time.time()
                
                memory_after = process.memory_info().rss
                
                lazy_times.append(end_time - start_time)
                lazy_memory.append(memory_after - memory_before)
                
            except Exception as e:
                print(f"    Failed to lazy load {dicom_path.name}: {e}")
                continue
        
        if lazy_times:
            results[category]['lazy_loading'] = {
                'avg_time': np.mean(lazy_times),
                'total_time': sum(lazy_times),
                'avg_memory_mb': np.mean(lazy_memory) / (1024*1024),
                'files_tested': len(lazy_times)
            }
    
    return results

def test_dicom_opencv_pipeline(dicom_files: Dict[str, List[Path]]) -> Dict[str, Any]:
    """Test full DICOM -> OpenCV processing pipeline."""
    results = {}
    
    target_sizes = [(128, 128), (256, 256), (512, 512)]
    
    for category, files in dicom_files.items():
        if not files or category == 'xlarge':  # Skip xlarge for this test
            continue
            
        print(f"\n🔬 Testing OpenCV pipeline for {category.upper()} files...")
        
        results[category] = {}
        
        # Test first file of each category
        test_file = files[0]
        
        try:
            # Load DICOM data
            import pydicom
            dataset = pydicom.dcmread(str(test_file))
            
            if not hasattr(dataset, 'pixel_array'):
                print(f"    Skipping {test_file.name} - no pixel data")
                continue
                
            pixel_array = dataset.pixel_array
            print(f"    Testing with {test_file.name}: {pixel_array.shape}, {pixel_array.dtype}")
            
            # Ensure 2D for testing
            if len(pixel_array.shape) > 2:
                pixel_array = pixel_array[0] if len(pixel_array.shape) == 3 else pixel_array[:,:,0]
            
            results[category]['original_shape'] = pixel_array.shape
            results[category]['file_size_mb'] = test_file.stat().st_size / (1024*1024)
            
            # Test different target sizes
            for target_size in target_sizes:
                size_key = f"{target_size[0]}x{target_size[1]}"
                results[category][size_key] = {}
                
                print(f"      Testing resize to {target_size}...")
                
                # Standard OpenCV resize
                try:
                    import cv2
                    
                    # Convert to uint8 if needed
                    if pixel_array.dtype != np.uint8:
                        normalized = ((pixel_array - pixel_array.min()) / 
                                    (pixel_array.max() - pixel_array.min()) * 255).astype(np.uint8)
                    else:
                        normalized = pixel_array
                    
                    times = []
                    for _ in range(3):  # 3 runs for averaging
                        start_time = time.time()
                        resized_std = cv2.resize(normalized, target_size, interpolation=cv2.INTER_LINEAR)
                        end_time = time.time()
                        times.append(end_time - start_time)
                    
                    results[category][size_key]['standard_opencv'] = {
                        'avg_time': np.mean(times),
                        'min_time': np.min(times),
                        'output_shape': resized_std.shape
                    }
                    
                except Exception as e:
                    results[category][size_key]['standard_opencv'] = {'error': str(e)}
                
                # Optimized OpenCV resize
                try:
                    from medscan_ai.core.utils.optimized_cv_ops import resize_optimized
                    
                    times = []
                    for _ in range(3):
                        start_time = time.time()
                        resized_opt = resize_optimized(normalized, target_size)
                        end_time = time.time()
                        times.append(end_time - start_time)
                    
                    results[category][size_key]['optimized_opencv'] = {
                        'avg_time': np.mean(times),
                        'min_time': np.min(times),
                        'output_shape': resized_opt.shape
                    }
                    
                except Exception as e:
                    results[category][size_key]['optimized_opencv'] = {'error': str(e)}
        
        except Exception as e:
            print(f"    Failed to process {test_file.name}: {e}")
            results[category] = {'error': str(e)}
    
    return results

def test_ai_preprocessing_pipeline(dicom_files: Dict[str, List[Path]]) -> Dict[str, Any]:
    """Test AI preprocessing pipeline performance."""
    results = {}
    
    print(f"\n🤖 Testing AI preprocessing pipeline...")
    
    # Test with medium-sized files
    test_files = dicom_files.get('medium', [])[:3]  # Test first 3 medium files
    
    if not test_files:
        print("  No medium-sized files available for AI preprocessing test")
        return results
    
    ai_target_sizes = [(224, 224), (256, 256), (512, 512)]  # Common AI input sizes
    
    for i, test_file in enumerate(test_files):
        file_key = f"file_{i+1}"
        results[file_key] = {
            'filename': test_file.name,
            'file_size_mb': test_file.stat().st_size / (1024*1024)
        }
        
        try:
            import pydicom
            dataset = pydicom.dcmread(str(test_file))
            
            if not hasattr(dataset, 'pixel_array'):
                results[file_key]['error'] = 'No pixel data'
                continue
            
            # Test AI pixel extractor
            from medscan_ai.ai.preprocessing.ai_pixel_extractor import AIPixelExtractor
            
            extractor = AIPixelExtractor()
            
            for target_size in ai_target_sizes:
                size_key = f"ai_{target_size[0]}x{target_size[1]}"
                
                print(f"    Testing {test_file.name} -> {target_size}...")
                
                try:
                    start_time = time.time()
                    ai_result = extractor.extract_for_ai_inference(
                        dataset=dataset,
                        output_format='float32',
                        target_size=target_size,
                        apply_windowing=True,
                        ensure_grayscale=True
                    )
                    end_time = time.time()
                    
                    results[file_key][size_key] = {
                        'processing_time': end_time - start_time,
                        'output_shape': ai_result['pixel_array'].shape,
                        'output_dtype': str(ai_result['pixel_array'].dtype),
                        'preprocessing_info': ai_result['preprocessing_info']
                    }
                    
                except Exception as e:
                    results[file_key][size_key] = {'error': str(e)}
        
        except Exception as e:
            results[file_key]['error'] = str(e)
    
    return results

def test_batch_processing_performance(dicom_files: Dict[str, List[Path]]) -> Dict[str, Any]:
    """Test batch processing performance with multiple DICOM files."""
    results = {}
    
    print(f"\n📦 Testing batch processing performance...")
    
    # Use small files for batch testing
    test_files = dicom_files.get('small', [])[:10]  # Up to 10 small files
    
    if len(test_files) < 3:
        print("  Not enough small files for batch testing")
        return results
    
    batch_sizes = [3, 5, min(10, len(test_files))]
    target_size = (256, 256)
    
    for batch_size in batch_sizes:
        batch_key = f"batch_{batch_size}"
        results[batch_key] = {}
        
        print(f"    Testing batch size {batch_size}...")
        
        # Prepare batch data
        batch_data = []
        for i in range(batch_size):
            try:
                import pydicom
                dataset = pydicom.dcmread(str(test_files[i]))
                
                if hasattr(dataset, 'pixel_array'):
                    pixel_array = dataset.pixel_array
                    
                    # Ensure 2D
                    if len(pixel_array.shape) > 2:
                        pixel_array = pixel_array[0] if len(pixel_array.shape) == 3 else pixel_array[:,:,0]
                    
                    # Normalize to uint8
                    if pixel_array.dtype != np.uint8:
                        normalized = ((pixel_array - pixel_array.min()) / 
                                    (pixel_array.max() - pixel_array.min()) * 255).astype(np.uint8)
                    else:
                        normalized = pixel_array
                    
                    batch_data.append(normalized)
                
            except Exception as e:
                print(f"      Failed to prepare {test_files[i].name}: {e}")
                continue
        
        if len(batch_data) < batch_size:
            results[batch_key]['error'] = f'Only prepared {len(batch_data)}/{batch_size} files'
            continue
        
        results[batch_key]['prepared_files'] = len(batch_data)
        results[batch_key]['avg_shape'] = np.mean([arr.shape for arr in batch_data], axis=0).tolist()
        
        # Test individual processing
        try:
            import cv2
            
            start_time = time.time()
            individual_results = []
            for img in batch_data:
                resized = cv2.resize(img, target_size, interpolation=cv2.INTER_LINEAR)
                individual_results.append(resized)
            end_time = time.time()
            
            results[batch_key]['individual_processing'] = {
                'total_time': end_time - start_time,
                'avg_time_per_image': (end_time - start_time) / len(batch_data),
                'throughput_images_per_sec': len(batch_data) / (end_time - start_time)
            }
            
        except Exception as e:
            results[batch_key]['individual_processing'] = {'error': str(e)}
        
        # Test batch processing
        try:
            from medscan_ai.core.utils.optimized_cv_ops import batch_resize
            
            start_time = time.time()
            batch_results = batch_resize(batch_data, target_size)
            end_time = time.time()
            
            results[batch_key]['batch_processing'] = {
                'total_time': end_time - start_time,
                'avg_time_per_image': (end_time - start_time) / len(batch_data),
                'throughput_images_per_sec': len(batch_data) / (end_time - start_time)
            }
            
        except Exception as e:
            results[batch_key]['batch_processing'] = {'error': str(e)}
    
    return results

def print_comprehensive_results(dicom_loading_results: Dict, opencv_results: Dict, 
                               ai_results: Dict, batch_results: Dict, 
                               dicom_file_stats: Dict):
    """Print comprehensive test results."""
    print("\n" + "="*100)
    print("🎯 COMPREHENSIVE DICOM + OPENCV PERFORMANCE RESULTS")
    print("="*100)
    
    # File statistics
    print(f"\n📊 DICOM FILE STATISTICS:")
    total_files = sum(len(files) for files in dicom_file_stats.values())
    print(f"  Total DICOM files: {total_files}")
    
    for category, files in dicom_file_stats.items():
        if files:
            avg_size = np.mean([f.stat().st_size / (1024*1024) for f in files])
            print(f"  {category.capitalize()}: {len(files)} files (avg: {avg_size:.1f}MB)")
    
    # DICOM loading performance
    print(f"\n📁 DICOM LOADING PERFORMANCE:")
    for category, results in dicom_loading_results.items():
        if 'standard_loading' in results and 'lazy_loading' in results:
            std_time = results['standard_loading']['avg_time']
            lazy_time = results['lazy_loading']['avg_time']
            speedup = std_time / lazy_time if lazy_time > 0 else 0
            
            std_memory = results['standard_loading']['avg_memory_mb']
            lazy_memory = results['lazy_loading']['avg_memory_mb']
            memory_reduction = ((std_memory - lazy_memory) / std_memory * 100) if std_memory > 0 else 0
            
            print(f"  {category.capitalize()}: Standard={std_time:.4f}s, Lazy={lazy_time:.4f}s, "
                  f"Speedup={speedup:.2f}x, Memory reduction={memory_reduction:+.1f}%")
    
    # OpenCV processing performance
    print(f"\n🔬 OPENCV PROCESSING PERFORMANCE:")
    for category, results in opencv_results.items():
        if 'error' not in results:
            print(f"  {category.capitalize()} files ({results.get('original_shape', 'unknown')} pixels):")
            
            for size_key, size_results in results.items():
                if 'x' in size_key and isinstance(size_results, dict):
                    std_data = size_results.get('standard_opencv', {})
                    opt_data = size_results.get('optimized_opencv', {})
                    
                    if 'avg_time' in std_data and 'avg_time' in opt_data:
                        speedup = std_data['avg_time'] / opt_data['avg_time']
                        print(f"    {size_key}: Standard={std_data['avg_time']:.4f}s, "
                              f"Optimized={opt_data['avg_time']:.4f}s, Speedup={speedup:.2f}x")
    
    # AI preprocessing performance
    print(f"\n🤖 AI PREPROCESSING PERFORMANCE:")
    for file_key, results in ai_results.items():
        if 'error' not in results:
            filename = results.get('filename', 'unknown')
            print(f"  {filename}:")
            
            for size_key, size_results in results.items():
                if 'ai_' in size_key and isinstance(size_results, dict) and 'processing_time' in size_results:
                    time_taken = size_results['processing_time']
                    output_shape = size_results['output_shape']
                    print(f"    {size_key}: {time_taken:.4f}s -> {output_shape}")
    
    # Batch processing performance
    print(f"\n📦 BATCH PROCESSING PERFORMANCE:")
    for batch_key, results in batch_results.items():
        if 'error' not in results and 'individual_processing' in results and 'batch_processing' in results:
            individual_throughput = results['individual_processing']['throughput_images_per_sec']
            batch_throughput = results['batch_processing']['throughput_images_per_sec']
            improvement = (batch_throughput / individual_throughput - 1) * 100
            
            print(f"  {batch_key}: Individual={individual_throughput:.1f} img/sec, "
                  f"Batch={batch_throughput:.1f} img/sec, Improvement={improvement:+.1f}%")
    
    # Summary and recommendations
    print(f"\n💡 PERFORMANCE SUMMARY & RECOMMENDATIONS:")
    
    # Calculate overall improvements
    lazy_improvements = []
    opencv_improvements = []
    batch_improvements = []
    
    for results in dicom_loading_results.values():
        if 'standard_loading' in results and 'lazy_loading' in results:
            std_time = results['standard_loading']['avg_time']
            lazy_time = results['lazy_loading']['avg_time']
            if lazy_time > 0:
                lazy_improvements.append(std_time / lazy_time)
    
    for results in opencv_results.values():
        if 'error' not in results:
            for size_key, size_results in results.items():
                if 'x' in size_key:
                    std_data = size_results.get('standard_opencv', {})
                    opt_data = size_results.get('optimized_opencv', {})
                    if 'avg_time' in std_data and 'avg_time' in opt_data and opt_data['avg_time'] > 0:
                        opencv_improvements.append(std_data['avg_time'] / opt_data['avg_time'])
    
    for results in batch_results.values():
        if 'individual_processing' in results and 'batch_processing' in results:
            individual = results['individual_processing']['throughput_images_per_sec']
            batch = results['batch_processing']['throughput_images_per_sec']
            if individual > 0:
                batch_improvements.append(batch / individual)
    
    if lazy_improvements:
        avg_lazy_improvement = np.mean(lazy_improvements)
        print(f"  ✅ Lazy Loading: {avg_lazy_improvement:.2f}x average speedup for DICOM loading")
    
    if opencv_improvements:
        avg_opencv_improvement = np.mean(opencv_improvements)
        print(f"  🔧 OpenCV Optimization: {avg_opencv_improvement:.2f}x average speedup for image processing")
    
    if batch_improvements:
        avg_batch_improvement = np.mean(batch_improvements)
        print(f"  📦 Batch Processing: {avg_batch_improvement:.2f}x average throughput improvement")
    
    # Overall assessment
    total_improvements = len([x for x in [lazy_improvements, opencv_improvements, batch_improvements] if x])
    if total_improvements >= 2:
        print(f"  🎉 OPTIMIZATION SUCCESS: Multiple performance improvements achieved!")
    elif total_improvements == 1:
        print(f"  ⚠️  PARTIAL SUCCESS: Some optimizations working, others need improvement")
    else:
        print(f"  ❌ OPTIMIZATION NEEDS WORK: Limited performance improvements detected")

def main():
    """Main comprehensive testing function."""
    print("🚀 Starting Comprehensive DICOM + OpenCV Performance Testing")
    print("="*100)
    
    # System info
    system_info = {
        'cpu_cores': psutil.cpu_count(),
        'memory_gb': psutil.virtual_memory().total / (1024**3),
        'platform': sys.platform
    }
    print(f"System: {system_info['cpu_cores']} cores, {system_info['memory_gb']:.1f}GB RAM")
    
    # Find and categorize DICOM files
    print(f"\n🔍 Discovering DICOM files...")
    dicom_paths = find_dicom_files()
    
    if not dicom_paths:
        print("❌ No DICOM files found in test_data/dicom_samples/")
        print("Please ensure DICOM test files are available.")
        return
    
    dicom_file_stats = categorize_dicom_files(dicom_paths)
    
    # Run comprehensive tests
    print(f"\n🧪 Running DICOM loading tests...")
    dicom_loading_results = test_dicom_loading_performance(dicom_file_stats)
    
    print(f"\n🧪 Running OpenCV pipeline tests...")
    opencv_results = test_dicom_opencv_pipeline(dicom_file_stats)
    
    print(f"\n🧪 Running AI preprocessing tests...")
    ai_results = test_ai_preprocessing_pipeline(dicom_file_stats)
    
    print(f"\n🧪 Running batch processing tests...")
    batch_results = test_batch_processing_performance(dicom_file_stats)
    
    # Print comprehensive results
    print_comprehensive_results(dicom_loading_results, opencv_results, 
                               ai_results, batch_results, dicom_file_stats)
    
    # Save detailed results
    import json
    all_results = {
        'system_info': system_info,
        'file_statistics': {k: [str(f) for f in v] for k, v in dicom_file_stats.items()},
        'dicom_loading_results': dicom_loading_results,
        'opencv_results': opencv_results,
        'ai_results': ai_results,
        'batch_results': batch_results
    }
    
    with open('comprehensive_dicom_opencv_results.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: comprehensive_dicom_opencv_results.json")
    print("\n🎉 Comprehensive DICOM + OpenCV performance testing completed!")

if __name__ == "__main__":
    main() 