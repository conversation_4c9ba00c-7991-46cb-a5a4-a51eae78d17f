"""Audit Log Data Integrity Verification Tools.

Provides comprehensive verification procedures for audit log integrity,
completeness, and tamper detection for HIPAA/GDPR compliance.
"""

import logging
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_, func

from ...database.engine import get_session
from ...database.models.audit_log import AuditLog
from ...database.repositories.audit_repository import AuditRepository
from .immutability import ImmutabilityManager

logger = logging.getLogger(__name__)


class VerificationResult(Enum):
    """Verification result status."""
    PASS = "PASS"
    FAIL = "FAIL"
    WARNING = "WARNING"
    ERROR = "ERROR"


@dataclass
class IntegrityCheckResult:
    """Result of integrity verification check."""
    check_type: str
    status: VerificationResult
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    affected_records: int = 0


@dataclass 
class VerificationReport:
    """Comprehensive verification report."""
    report_id: str
    start_time: datetime
    end_time: Optional[datetime]
    total_records_checked: int
    checks_performed: List[IntegrityCheckResult]
    overall_status: VerificationResult
    summary: Dict[str, Any]


class AuditLogVerificationService:
    """Service for comprehensive audit log verification."""
    
    def __init__(self, session: Optional[Session] = None):
        """Initialize verification service."""
        self._session = session or get_session()
        self._repository = AuditRepository(self._session)
        self._immutability_manager = ImmutabilityManager(self._session)
        
    def verify_hash_chain_integrity(self, days_back: int = 30) -> IntegrityCheckResult:
        """Verify hash chain integrity for recent audit logs."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days_back)
            
            # Get audit logs in sequence order
            audit_logs = self._session.query(AuditLog).filter(
                and_(
                    AuditLog.timestamp >= start_date,
                    AuditLog.hash_signature.isnot(None),
                    AuditLog.chain_sequence.isnot(None)
                )
            ).order_by(asc(AuditLog.chain_sequence)).all()
            
            if not audit_logs:
                return IntegrityCheckResult(
                    check_type="hash_chain_integrity",
                    status=VerificationResult.WARNING,
                    message="No hash chain entries found in specified time range",
                    details={"days_back": days_back, "records_found": 0},
                    timestamp=datetime.utcnow()
                )
            
            verification_failures = []
            tampered_records = []
            
            for i, log_entry in enumerate(audit_logs):
                # Verify content hash
                calculated_hash = self._immutability_manager.calculate_content_hash(log_entry)
                if calculated_hash != log_entry.hash_signature:
                    tampered_records.append({
                        "audit_id": log_entry.id,
                        "sequence": log_entry.chain_sequence,
                        "expected_hash": log_entry.hash_signature,
                        "calculated_hash": calculated_hash,
                        "timestamp": log_entry.timestamp.isoformat()
                    })
                
                # Verify chain linkage (except for first entry)
                if i > 0:
                    previous_log = audit_logs[i - 1]
                    if log_entry.previous_hash != previous_log.hash_signature:
                        verification_failures.append({
                            "audit_id": log_entry.id,
                            "sequence": log_entry.chain_sequence,
                            "issue": "broken_chain_link",
                            "expected_previous_hash": previous_log.hash_signature,
                            "actual_previous_hash": log_entry.previous_hash
                        })
            
            # Determine verification status
            if tampered_records or verification_failures:
                status = VerificationResult.FAIL
                message = f"Hash chain integrity verification FAILED. {len(tampered_records)} tampered records, {len(verification_failures)} chain breaks found."
            else:
                status = VerificationResult.PASS
                message = f"Hash chain integrity verification PASSED for {len(audit_logs)} records."
            
            return IntegrityCheckResult(
                check_type="hash_chain_integrity",
                status=status,
                message=message,
                details={
                    "total_records": len(audit_logs),
                    "tampered_records": tampered_records,
                    "chain_breaks": verification_failures,
                    "verification_period_days": days_back
                },
                timestamp=datetime.utcnow(),
                affected_records=len(tampered_records) + len(verification_failures)
            )
            
        except Exception as e:
            logger.error(f"Hash chain verification error: {str(e)}")
            return IntegrityCheckResult(
                check_type="hash_chain_integrity",
                status=VerificationResult.ERROR,
                message=f"Hash chain verification failed due to system error: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )
    
    def verify_log_completeness(self, days_back: int = 7) -> IntegrityCheckResult:
        """Verify audit log completeness by checking for gaps."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days_back)
            
            # Get audit logs ordered by timestamp
            audit_logs = self._session.query(AuditLog).filter(
                AuditLog.timestamp >= start_date
            ).order_by(asc(AuditLog.timestamp)).all()
            
            if not audit_logs:
                return IntegrityCheckResult(
                    check_type="log_completeness",
                    status=VerificationResult.WARNING,
                    message="No audit logs found in specified time range",
                    details={"days_back": days_back},
                    timestamp=datetime.utcnow()
                )
            
            # Check for sequence gaps in hash chain
            chain_entries = [log for log in audit_logs if log.chain_sequence is not None]
            chain_entries.sort(key=lambda x: x.chain_sequence)
            
            sequence_gaps = []
            if chain_entries:
                for i in range(1, len(chain_entries)):
                    current_seq = chain_entries[i].chain_sequence
                    previous_seq = chain_entries[i - 1].chain_sequence
                    
                    if current_seq - previous_seq > 1:
                        sequence_gaps.append({
                            "missing_sequences": list(range(previous_seq + 1, current_seq)),
                            "gap_start": previous_seq,
                            "gap_end": current_seq,
                            "gap_size": current_seq - previous_seq - 1
                        })
            
            # Check for large time gaps (suspicious)
            time_gaps = []
            for i in range(1, len(audit_logs)):
                time_diff = audit_logs[i].timestamp - audit_logs[i - 1].timestamp
                if time_diff.total_seconds() > 3600:  # More than 1 hour gap
                    time_gaps.append({
                        "start_time": audit_logs[i - 1].timestamp.isoformat(),
                        "end_time": audit_logs[i].timestamp.isoformat(),
                        "gap_duration_hours": time_diff.total_seconds() / 3600,
                        "preceding_audit_id": audit_logs[i - 1].id,
                        "following_audit_id": audit_logs[i].id
                    })
            
            # Determine status
            if sequence_gaps:
                status = VerificationResult.FAIL
                message = f"Log completeness verification FAILED. {len(sequence_gaps)} sequence gaps found."
            elif time_gaps:
                status = VerificationResult.WARNING  
                message = f"Log completeness verification shows {len(time_gaps)} suspicious time gaps."
            else:
                status = VerificationResult.PASS
                message = f"Log completeness verification PASSED for {len(audit_logs)} records."
            
            return IntegrityCheckResult(
                check_type="log_completeness",
                status=status,
                message=message,
                details={
                    "total_records": len(audit_logs),
                    "chain_records": len(chain_entries),
                    "sequence_gaps": sequence_gaps,
                    "time_gaps": time_gaps,
                    "verification_period_days": days_back
                },
                timestamp=datetime.utcnow(),
                affected_records=len(sequence_gaps)
            )
            
        except Exception as e:
            logger.error(f"Log completeness verification error: {str(e)}")
            return IntegrityCheckResult(
                check_type="log_completeness",
                status=VerificationResult.ERROR,
                message=f"Log completeness verification failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )
    
    def verify_data_consistency(self) -> IntegrityCheckResult:
        """Verify data consistency across audit log records."""
        try:
            # Check for mandatory field completeness
            incomplete_records = self._session.query(AuditLog).filter(
                or_(
                    AuditLog.user_id.is_(None),
                    AuditLog.action.is_(None),
                    AuditLog.timestamp.is_(None),
                    AuditLog.ip_address.is_(None)
                )
            ).count()
            
            # Check for orphaned records (invalid user references, etc.)
            # This would require joins with user tables - simplified for now
            orphaned_records = 0
            
            # Check for duplicate correlation IDs (should be unique per operation)
            duplicate_correlations = self._session.query(
                AuditLog.correlation_id,
                func.count(AuditLog.correlation_id).label('count')
            ).filter(
                AuditLog.correlation_id.isnot(None)
            ).group_by(
                AuditLog.correlation_id
            ).having(
                func.count(AuditLog.correlation_id) > 1
            ).all()
            
            # Check for invalid status combinations
            invalid_combinations = self._session.query(AuditLog).filter(
                and_(
                    AuditLog.success == True,
                    AuditLog.action.in_(['LOGIN_FAILED', 'AUTHENTICATION_FAILED'])
                )
            ).count()
            
            total_issues = incomplete_records + orphaned_records + len(duplicate_correlations) + invalid_combinations
            
            if total_issues > 0:
                status = VerificationResult.FAIL
                message = f"Data consistency verification FAILED. {total_issues} issues found."
            else:
                status = VerificationResult.PASS
                message = "Data consistency verification PASSED."
            
            return IntegrityCheckResult(
                check_type="data_consistency",
                status=status,
                message=message,
                details={
                    "incomplete_records": incomplete_records,
                    "orphaned_records": orphaned_records,
                    "duplicate_correlations": len(duplicate_correlations),
                    "invalid_combinations": invalid_combinations,
                    "total_issues": total_issues
                },
                timestamp=datetime.utcnow(),
                affected_records=total_issues
            )
            
        except Exception as e:
            logger.error(f"Data consistency verification error: {str(e)}")
            return IntegrityCheckResult(
                check_type="data_consistency",
                status=VerificationResult.ERROR,
                message=f"Data consistency verification failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.utcnow()
            )
    
    def run_comprehensive_verification(self, days_back: int = 30) -> VerificationReport:
        """Run comprehensive audit log verification."""
        report_id = f"audit_verification_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.utcnow()
        
        logger.info(f"Starting comprehensive audit verification: {report_id}")
        
        # Run all verification checks
        checks = [
            self.verify_hash_chain_integrity(days_back),
            self.verify_log_completeness(days_back // 4),  # More frequent completeness check
            self.verify_data_consistency()
        ]
        
        # Calculate overall status
        failed_checks = [c for c in checks if c.status == VerificationResult.FAIL]
        error_checks = [c for c in checks if c.status == VerificationResult.ERROR]
        warning_checks = [c for c in checks if c.status == VerificationResult.WARNING]
        
        if failed_checks or error_checks:
            overall_status = VerificationResult.FAIL
        elif warning_checks:
            overall_status = VerificationResult.WARNING
        else:
            overall_status = VerificationResult.PASS
        
        # Get total records checked
        total_records = self._session.query(AuditLog).filter(
            AuditLog.timestamp >= datetime.utcnow() - timedelta(days=days_back)
        ).count()
        
        end_time = datetime.utcnow()
        
        report = VerificationReport(
            report_id=report_id,
            start_time=start_time,
            end_time=end_time,
            total_records_checked=total_records,
            checks_performed=checks,
            overall_status=overall_status,
            summary={
                "duration_seconds": (end_time - start_time).total_seconds(),
                "checks_passed": len([c for c in checks if c.status == VerificationResult.PASS]),
                "checks_failed": len(failed_checks),
                "checks_with_warnings": len(warning_checks),
                "checks_with_errors": len(error_checks),
                "total_affected_records": sum(c.affected_records for c in checks)
            }
        )
        
        logger.info(f"Audit verification completed: {report_id}, Status: {overall_status.value}")
        return report


def get_verification_service() -> AuditLogVerificationService:
    """Get audit log verification service instance."""
    return AuditLogVerificationService()


def generate_verification_report_markdown(report: VerificationReport) -> str:
    """Generate a markdown verification report."""
    md_content = f"""# Audit Log Integrity Verification Report

**Report ID:** {report.report_id}  
**Generated:** {report.end_time.strftime('%Y-%m-%d %H:%M:%S UTC')}  
**Duration:** {report.summary['duration_seconds']:.2f} seconds  
**Overall Status:** {'🟢 PASS' if report.overall_status == VerificationResult.PASS else '🔴 FAIL' if report.overall_status == VerificationResult.FAIL else '🟡 WARNING'}

## Summary
- **Total Records Checked:** {report.total_records_checked:,}
- **Checks Performed:** {len(report.checks_performed)}
- **Passed:** {report.summary['checks_passed']} ✅
- **Failed:** {report.summary['checks_failed']} ❌
- **Warnings:** {report.summary['checks_with_warnings']} ⚠️
- **Errors:** {report.summary['checks_with_errors']} 🚨
- **Affected Records:** {report.summary['total_affected_records']}

## Detailed Results

"""
    
    for check in report.checks_performed:
        status_icon = {
            VerificationResult.PASS: "✅",
            VerificationResult.FAIL: "❌", 
            VerificationResult.WARNING: "⚠️",
            VerificationResult.ERROR: "🚨"
        }[check.status]
        
        md_content += f"""### {status_icon} {check.check_type.replace('_', ' ').title()}

**Status:** {check.status.value}  
**Message:** {check.message}  
**Affected Records:** {check.affected_records}  
**Check Time:** {check.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        if check.details:
            md_content += "**Details:**\n```json\n"
            import json
            md_content += json.dumps(check.details, indent=2, default=str)
            md_content += "\n```\n\n"
    
    md_content += f"""
## Recommendations

Based on the verification results:
"""
    
    if report.overall_status == VerificationResult.PASS:
        md_content += "- ✅ All verification checks passed. Audit logging system integrity is confirmed.\n"
    else:
        md_content += "- ❌ Issues detected that require immediate attention.\n"
        md_content += "- 🔧 Review failed checks and implement corrective measures.\n"
        md_content += "- 🔍 Investigate root causes of integrity failures.\n"
        
    md_content += f"""
---
*Generated by MedScan AI Audit Verification System*  
*Report generated on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}*
"""
    
    return md_content 