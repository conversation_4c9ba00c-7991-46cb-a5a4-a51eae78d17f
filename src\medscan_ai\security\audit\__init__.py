"""MedScan AI Audit Module.

Comprehensive audit logging system for HIPAA/GDPR compliance,
security monitoring, and regulatory compliance.
"""

from .service import (
    AuditService,
    AuditEvent,
    AuditContext,
    ActionType,
    ActionCategory,
    get_audit_service,
    init_audit_service
)

from .async_processor import (
    AsyncAuditProcessor,
    get_async_processor
)

from .context import (
    AuditContextManager,
    get_audit_context_manager,
    audit_data_access,
    audit_dicom_operation,
    audit_ai_analysis
)

__all__ = [
    # Core service
    'AuditService',
    'AuditEvent',
    'AuditContext',
    'ActionType',
    'ActionCategory',
    'get_audit_service',
    'init_audit_service',
    
    # Async processing
    'AsyncAuditProcessor',
    'get_async_processor',
    
    # Context management
    'AuditContextManager',
    'get_audit_context_manager',
    'audit_data_access',
    'audit_dicom_operation',
    'audit_ai_analysis',
]

# Module version
__version__ = '1.0.0'
