"""Annotation persistence service for bridging GUI and database layers.

Provides high-level interface for saving, loading, and managing annotations
with coordinate transformations and medical workflow integration.
"""

from typing import List, Optional, Dict, Any, Tuple
import logging
from dataclasses import dataclass
from PySide6.QtCore import QRectF, QPointF
from PySide6.QtWidgets import QGraphicsItem

from ...database.repositories.annotation_repository import AnnotationRepository
from ...database.models.annotation import Annotation, AnnotationType
from ..helpers.coordinate_transformer import CoordinateTransformer, ImageMetadata, ViewState


logger = logging.getLogger(__name__)


@dataclass
class AnnotationSaveResult:
    """Result of annotation save operation."""
    success: bool
    annotation_id: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class AnnotationLoadResult:
    """Result of annotation load operation."""
    success: bool
    annotations: List[Annotation] = None
    error_message: Optional[str] = None


class AnnotationPersistenceService:
    """Service for persisting annotations between GUI and database.
    
    Handles coordinate transformations, validation, and provides
    a clean interface for the annotation management system.
    """
    
    def __init__(
        self,
        repository: AnnotationRepository,
        transformer: CoordinateTransformer
    ):
        """Initialize persistence service.
        
        Args:
            repository: Database repository for annotations
            transformer: Coordinate transformation utility
        """
        self.repository = repository
        self.transformer = transformer
        self._current_study_id: Optional[int] = None
        self._current_image_id: Optional[int] = None
        self._current_user_id: Optional[str] = None
    
    def set_current_context(
        self,
        study_id: int,
        image_id: Optional[int] = None,
        user_id: Optional[str] = None
    ) -> None:
        """Set current medical context for annotations.
        
        Args:
            study_id: Current study ID
            image_id: Optional specific image ID
            user_id: Current user ID for annotation creation
        """
        self._current_study_id = study_id
        self._current_image_id = image_id
        self._current_user_id = user_id
        logger.info(f"Set annotation context: study={study_id}, image={image_id}, user={user_id}")
    
    def _validate_context(self) -> bool:
        """Validate that required context is set for operations.
        
        Returns:
            True if context is valid
        """
        return (
            self._current_study_id is not None and
            self._current_user_id is not None
        )
    
    def save_rectangle_annotation(
        self,
        scene_rect: QRectF,
        view_state: ViewState,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: str = "#FF0000",
        line_width: int = 2,
        **kwargs
    ) -> AnnotationSaveResult:
        """Save rectangle annotation from scene coordinates.
        
        Args:
            scene_rect: Rectangle in scene coordinates
            view_state: Current view state for transformation
            title: Optional annotation title
            description: Optional description
            color: Annotation color
            line_width: Line width for display
            **kwargs: Additional annotation properties
            
        Returns:
            Save operation result
        """
        if not self._validate_context():
            return AnnotationSaveResult(
                success=False,
                error_message="Medical context not set (study_id, user_id required)"
            )
        
        try:
            # Convert scene coordinates to pixel coordinates
            scene_coords = {
                'type': AnnotationType.RECTANGLE,
                'x': scene_rect.x(),
                'y': scene_rect.y(),
                'width': scene_rect.width(),
                'height': scene_rect.height()
            }
            
            pixel_coords = self.transformer.transform_annotation_coordinates(
                scene_coords,
                from_pixel_to_scene=False,  # scene -> pixel
                view_state=view_state
            )
            
            # Create annotation in database
            annotation = self.repository.create_annotation(
                study_id=self._current_study_id,
                image_id=self._current_image_id,
                creator_user_id=self._current_user_id,
                annotation_type=AnnotationType.RECTANGLE,
                coordinates=pixel_coords,
                title=title,
                description=description,
                color=color,
                line_width=line_width,
                **kwargs
            )
            
            # Commit the transaction
            self.repository.session.commit()
            
            logger.info(f"Saved rectangle annotation: {annotation.id}")
            return AnnotationSaveResult(
                success=True,
                annotation_id=annotation.id
            )
            
        except Exception as e:
            self.repository.session.rollback()
            logger.error(f"Failed to save rectangle annotation: {e}")
            return AnnotationSaveResult(
                success=False,
                error_message=str(e)
            )
    
    def save_freehand_annotation(
        self,
        scene_points: List[Tuple[float, float]],
        view_state: ViewState,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: str = "#00FF00",
        line_width: int = 2,
        **kwargs
    ) -> AnnotationSaveResult:
        """Save freehand annotation from scene coordinates.
        
        Args:
            scene_points: List of (x, y) points in scene coordinates
            view_state: Current view state for transformation
            title: Optional annotation title
            description: Optional description
            color: Annotation color
            line_width: Line width for display
            **kwargs: Additional annotation properties
            
        Returns:
            Save operation result
        """
        if not self._validate_context():
            return AnnotationSaveResult(
                success=False,
                error_message="Medical context not set (study_id, user_id required)"
            )
        
        try:
            # Convert scene coordinates to pixel coordinates
            scene_coords = {
                'type': AnnotationType.FREEHAND,
                'points': scene_points
            }
            
            pixel_coords = self.transformer.transform_annotation_coordinates(
                scene_coords,
                from_pixel_to_scene=False,  # scene -> pixel
                view_state=view_state
            )
            
            # Create annotation in database
            annotation = self.repository.create_annotation(
                study_id=self._current_study_id,
                image_id=self._current_image_id,
                creator_user_id=self._current_user_id,
                annotation_type=AnnotationType.FREEHAND,
                coordinates=pixel_coords,
                title=title,
                description=description,
                color=color,
                line_width=line_width,
                **kwargs
            )
            
            # Commit the transaction
            self.repository.session.commit()
            
            logger.info(f"Saved freehand annotation: {annotation.id}")
            return AnnotationSaveResult(
                success=True,
                annotation_id=annotation.id
            )
            
        except Exception as e:
            self.repository.session.rollback()
            logger.error(f"Failed to save freehand annotation: {e}")
            return AnnotationSaveResult(
                success=False,
                error_message=str(e)
            )
    
    def save_polygon_annotation(
        self,
        scene_vertices: List[Tuple[float, float]],
        view_state: ViewState,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: str = "#0000FF",
        line_width: int = 2,
        **kwargs
    ) -> AnnotationSaveResult:
        """Save polygon annotation from scene coordinates.
        
        Args:
            scene_vertices: List of (x, y) vertices in scene coordinates
            view_state: Current view state for transformation
            title: Optional annotation title
            description: Optional description
            color: Annotation color
            line_width: Line width for display
            **kwargs: Additional annotation properties
            
        Returns:
            Save operation result
        """
        if not self._validate_context():
            return AnnotationSaveResult(
                success=False,
                error_message="Medical context not set (study_id, user_id required)"
            )
        
        try:
            # Convert scene coordinates to pixel coordinates
            scene_coords = {
                'type': AnnotationType.POLYGON,
                'vertices': scene_vertices
            }
            
            pixel_coords = self.transformer.transform_annotation_coordinates(
                scene_coords,
                from_pixel_to_scene=False,  # scene -> pixel
                view_state=view_state
            )
            
            # Create annotation in database
            annotation = self.repository.create_annotation(
                study_id=self._current_study_id,
                image_id=self._current_image_id,
                creator_user_id=self._current_user_id,
                annotation_type=AnnotationType.POLYGON,
                coordinates=pixel_coords,
                title=title,
                description=description,
                color=color,
                line_width=line_width,
                **kwargs
            )
            
            # Commit the transaction
            self.repository.session.commit()
            
            logger.info(f"Saved polygon annotation: {annotation.id}")
            return AnnotationSaveResult(
                success=True,
                annotation_id=annotation.id
            )
            
        except Exception as e:
            self.repository.session.rollback()
            logger.error(f"Failed to save polygon annotation: {e}")
            return AnnotationSaveResult(
                success=False,
                error_message=str(e)
            )
    
    def load_annotations_for_current_image(
        self,
        view_state: ViewState,
        annotation_types: Optional[List[str]] = None
    ) -> AnnotationLoadResult:
        """Load annotations for current image and convert to scene coordinates.
        
        Args:
            view_state: Current view state for transformation
            annotation_types: Optional filter by annotation types
            
        Returns:
            Load operation result with transformed annotations
        """
        if not self._current_image_id:
            return AnnotationLoadResult(
                success=False,
                error_message="No current image set"
            )
        
        try:
            # Load annotations from database
            annotations = self.repository.get_annotations_for_image(
                image_id=self._current_image_id,
                annotation_types=annotation_types
            )
            
            # Transform coordinates to scene coordinates for each annotation
            for annotation in annotations:
                if annotation.coordinates:
                    scene_coords = self.transformer.transform_annotation_coordinates(
                        annotation.coordinates,
                        from_pixel_to_scene=True,  # pixel -> scene
                        view_state=view_state
                    )
                    # Store transformed coordinates temporarily
                    annotation._scene_coordinates = scene_coords
            
            logger.info(f"Loaded {len(annotations)} annotations for image {self._current_image_id}")
            return AnnotationLoadResult(
                success=True,
                annotations=annotations
            )
            
        except Exception as e:
            logger.error(f"Failed to load annotations: {e}")
            return AnnotationLoadResult(
                success=False,
                error_message=str(e)
            )
    
    def load_annotations_for_current_study(
        self,
        view_state: ViewState,
        annotation_types: Optional[List[str]] = None
    ) -> AnnotationLoadResult:
        """Load annotations for current study and convert to scene coordinates.
        
        Args:
            view_state: Current view state for transformation
            annotation_types: Optional filter by annotation types
            
        Returns:
            Load operation result with transformed annotations
        """
        if not self._current_study_id:
            return AnnotationLoadResult(
                success=False,
                error_message="No current study set"
            )
        
        try:
            # Load annotations from database
            annotations = self.repository.get_annotations_for_study(
                study_id=self._current_study_id,
                annotation_types=annotation_types
            )
            
            # Transform coordinates to scene coordinates for each annotation
            for annotation in annotations:
                if annotation.coordinates:
                    scene_coords = self.transformer.transform_annotation_coordinates(
                        annotation.coordinates,
                        from_pixel_to_scene=True,  # pixel -> scene
                        view_state=view_state
                    )
                    # Store transformed coordinates temporarily
                    annotation._scene_coordinates = scene_coords
            
            logger.info(f"Loaded {len(annotations)} annotations for study {self._current_study_id}")
            return AnnotationLoadResult(
                success=True,
                annotations=annotations
            )
            
        except Exception as e:
            logger.error(f"Failed to load annotations: {e}")
            return AnnotationLoadResult(
                success=False,
                error_message=str(e)
            )
    
    def update_annotation(
        self,
        annotation_id: str,
        scene_coordinates: Dict[str, Any],
        view_state: ViewState,
        **update_fields
    ) -> bool:
        """Update annotation with new coordinates and properties.
        
        Args:
            annotation_id: Annotation UUID
            scene_coordinates: New coordinates in scene space
            view_state: Current view state for transformation
            **update_fields: Additional fields to update
            
        Returns:
            True if update successful
        """
        try:
            # Convert scene coordinates to pixel coordinates
            pixel_coords = self.transformer.transform_annotation_coordinates(
                scene_coordinates,
                from_pixel_to_scene=False,  # scene -> pixel
                view_state=view_state
            )
            
            # Update annotation coordinates
            annotation = self.repository.update_annotation_coordinates(
                annotation_id=annotation_id,
                new_coordinates=pixel_coords
            )
            
            if annotation and update_fields:
                # Update additional fields
                for field, value in update_fields.items():
                    if hasattr(annotation, field):
                        setattr(annotation, field, value)
                
                self.repository.session.flush()
            
            # Commit the transaction
            self.repository.session.commit()
            
            logger.info(f"Updated annotation: {annotation_id}")
            return True
            
        except Exception as e:
            self.repository.session.rollback()
            logger.error(f"Failed to update annotation {annotation_id}: {e}")
            return False
    
    def delete_annotation(self, annotation_id: str) -> bool:
        """Soft delete an annotation.
        
        Args:
            annotation_id: Annotation UUID
            
        Returns:
            True if deletion successful
        """
        try:
            success = self.repository.soft_delete_annotation(annotation_id)
            if success:
                self.repository.session.commit()
                logger.info(f"Deleted annotation: {annotation_id}")
            return success
            
        except Exception as e:
            self.repository.session.rollback()
            logger.error(f"Failed to delete annotation {annotation_id}: {e}")
            return False
    
    def get_annotation_statistics(self) -> Dict[str, Any]:
        """Get statistics for current medical context.
        
        Returns:
            Statistics dictionary
        """
        try:
            return self.repository.get_annotation_statistics(
                study_id=self._current_study_id,
                creator_user_id=self._current_user_id
            )
        except Exception as e:
            logger.error(f"Failed to get annotation statistics: {e}")
            return {}