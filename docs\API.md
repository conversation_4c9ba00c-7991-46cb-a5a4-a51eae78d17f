# MedScan AI - API Documentation

## Overview
This document provides comprehensive API documentation for MedScan AI, enabling developers to integrate with or extend the medical imaging analysis capabilities programmatically.

## Table of Contents
- [Authentication](#authentication)
- [Core API](#core-api)
- [DICOM Processing API](#dicom-processing-api)
- [AI Analysis API](#ai-analysis-api)
- [Report Generation API](#report-generation-api)
- [Security API](#security-api)
- [Integration API](#integration-api)
- [Erro<PERSON> Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [SDK Examples](#sdk-examples)

## Authentication

### API Key Authentication
```python
from medscan.auth import APIKeyAuth

# Initialize with API key
auth = APIKeyAuth(api_key="your_api_key_here")
client = MedScanClient(auth=auth)
```

### OAuth 2.0 Authentication
```python
from medscan.auth import OAuth2Auth

# OAuth 2.0 flow
auth = OAuth2Auth(
    client_id="your_client_id",
    client_secret="your_client_secret",
    redirect_uri="https://your-app.com/callback"
)
client = MedScanClient(auth=auth)
```

### JWT Token Authentication
```python
from medscan.auth import JWTAuth

# JWT token authentication
auth = JWTAuth(token="your_jwt_token")
client = MedScanClient(auth=auth)
```

## Core API

### MedScanClient
Main client class for interacting with MedScan AI services.

```python
from medscan import MedScanClient, APIKeyAuth

# Initialize client
client = MedScanClient(
    auth=APIKeyAuth("your_api_key"),
    base_url="https://api.medscan-ai.com/v1",
    timeout=30,
    retry_attempts=3
)
```

#### Methods

##### get_version()
```python
def get_version() -> dict:
    """Get API version information."""
    return {
        "version": "1.0.0",
        "api_version": "v1",
        "supported_features": ["ai_analysis", "batch_processing"],
        "supported_modalities": ["CR", "DX", "MR", "CT"]
    }
```

##### get_system_status()
```python
def get_system_status() -> dict:
    """Get system health status."""
    return {
        "status": "healthy",
        "uptime": "99.9%",
        "ai_models_loaded": True,
        "database_connected": True,
        "last_updated": "2025-01-15T10:30:00Z"
    }
```

## DICOM Processing API

### DICOMProcessor
Handle DICOM file operations and metadata extraction.

```python
from medscan.dicom import DICOMProcessor

processor = DICOMProcessor(client)
```

#### Methods

##### load_dicom(file_path)
```python
def load_dicom(file_path: str) -> DICOMImage:
    """
    Load and validate a DICOM file.
    
    Args:
        file_path (str): Path to DICOM file
        
    Returns:
        DICOMImage: Processed DICOM image object
        
    Raises:
        DICOMError: If file is invalid or corrupted
        SecurityError: If file fails security validation
    """
```

##### extract_metadata(dicom_image)
```python
def extract_metadata(dicom_image: DICOMImage) -> dict:
    """
    Extract metadata from DICOM image.
    
    Args:
        dicom_image (DICOMImage): DICOM image object
        
    Returns:
        dict: Extracted metadata including patient info, study details
    """
```

##### anonymize_dicom(dicom_image, anonymization_level)
```python
def anonymize_dicom(
    dicom_image: DICOMImage, 
    anonymization_level: str = "full"
) -> DICOMImage:
    """
    Anonymize DICOM image according to HIPAA requirements.
    
    Args:
        dicom_image (DICOMImage): Original DICOM image
        anonymization_level (str): "basic", "full", or "custom"
        
    Returns:
        DICOMImage: Anonymized DICOM image
    """
```

#### Example Usage
```python
# Load and process DICOM file
processor = DICOMProcessor(client)
dicom_image = processor.load_dicom("/path/to/xray.dcm")

# Extract metadata
metadata = processor.extract_metadata(dicom_image)
print(f"Study Date: {metadata['study_date']}")
print(f"Modality: {metadata['modality']}")

# Anonymize for processing
anonymized = processor.anonymize_dicom(dicom_image, "full")
```

## AI Analysis API

### AIAnalyzer
Perform AI-powered medical image analysis.

```python
from medscan.ai import AIAnalyzer

analyzer = AIAnalyzer(client)
```

#### Methods

##### analyze_image(dicom_image, model_version)
```python
def analyze_image(
    dicom_image: DICOMImage,
    model_version: str = "latest"
) -> AnalysisResult:
    """
    Perform AI analysis on medical image.
    
    Args:
        dicom_image (DICOMImage): DICOM image to analyze
        model_version (str): AI model version to use
        
    Returns:
        AnalysisResult: Analysis results with findings and confidence scores
    """
```

##### batch_analyze(dicom_images, options)
```python
def batch_analyze(
    dicom_images: List[DICOMImage],
    options: BatchAnalysisOptions = None
) -> List[AnalysisResult]:
    """
    Perform batch analysis on multiple images.
    
    Args:
        dicom_images (List[DICOMImage]): List of images to analyze
        options (BatchAnalysisOptions): Batch processing options
        
    Returns:
        List[AnalysisResult]: List of analysis results
    """
```

##### get_available_models()
```python
def get_available_models() -> List[dict]:
    """
    Get list of available AI models.
    
    Returns:
        List[dict]: Available models with metadata
    """
```

#### Data Structures

##### AnalysisResult
```python
class AnalysisResult:
    def __init__(self):
        self.image_id: str
        self.model_version: str
        self.findings: List[Finding]
        self.overall_confidence: float
        self.processing_time: float
        self.created_at: datetime
        
class Finding:
    def __init__(self):
        self.type: str  # "fracture", "lesion", "abnormality"
        self.confidence: float  # 0.0 to 1.0
        self.location: BoundingBox
        self.description: str
        self.severity: str  # "low", "medium", "high"
        
class BoundingBox:
    def __init__(self):
        self.x: int
        self.y: int
        self.width: int
        self.height: int
```

#### Example Usage
```python
# Analyze single image
analyzer = AIAnalyzer(client)
result = analyzer.analyze_image(dicom_image)

print(f"Analysis confidence: {result.overall_confidence:.2f}")
for finding in result.findings:
    print(f"Found {finding.type} with {finding.confidence:.2f} confidence")

# Batch analysis
results = analyzer.batch_analyze([image1, image2, image3])
```

## Report Generation API

### ReportGenerator
Generate medical reports from analysis results.

```python
from medscan.reports import ReportGenerator

generator = ReportGenerator(client)
```

#### Methods

##### generate_report(analysis_result, template_id)
```python
def generate_report(
    analysis_result: AnalysisResult,
    template_id: str = "standard",
    custom_fields: dict = None
) -> Report:
    """
    Generate medical report from analysis.
    
    Args:
        analysis_result (AnalysisResult): AI analysis results
        template_id (str): Report template identifier
        custom_fields (dict): Additional custom fields
        
    Returns:
        Report: Generated report object
    """
```

##### export_pdf(report, output_path)
```python
def export_pdf(report: Report, output_path: str) -> str:
    """
    Export report as PDF file.
    
    Args:
        report (Report): Report object to export
        output_path (str): Output file path
        
    Returns:
        str: Path to generated PDF file
    """
```

##### get_templates()
```python
def get_templates() -> List[dict]:
    """
    Get available report templates.
    
    Returns:
        List[dict]: Available templates with metadata
    """
```

#### Example Usage
```python
# Generate report
generator = ReportGenerator(client)
report = generator.generate_report(
    analysis_result,
    template_id="radiology_standard",
    custom_fields={
        "physician_name": "Dr. Sarah Chen",
        "department": "Radiology"
    }
)

# Export as PDF
pdf_path = generator.export_pdf(report, "/path/to/report.pdf")
```

## Security API

### SecurityManager
Handle security and compliance operations.

```python
from medscan.security import SecurityManager

security = SecurityManager(client)
```

#### Methods

##### audit_log(action, resource_type, resource_id)
```python
def audit_log(
    action: str,
    resource_type: str,
    resource_id: str,
    additional_data: dict = None
) -> None:
    """
    Log security audit event.
    
    Args:
        action (str): Action performed
        resource_type (str): Type of resource accessed
        resource_id (str): Resource identifier
        additional_data (dict): Additional audit information
    """
```

##### encrypt_data(data, encryption_key)
```python
def encrypt_data(data: bytes, encryption_key: str = None) -> bytes:
    """
    Encrypt sensitive data using AES-256.
    
    Args:
        data (bytes): Data to encrypt
        encryption_key (str): Optional custom encryption key
        
    Returns:
        bytes: Encrypted data
    """
```

##### validate_hipaa_compliance(operation)
```python
def validate_hipaa_compliance(operation: dict) -> ComplianceResult:
    """
    Validate operation against HIPAA requirements.
    
    Args:
        operation (dict): Operation details to validate
        
    Returns:
        ComplianceResult: Compliance validation result
    """
```

## Integration API

### PACSIntegration
Integration with PACS (Picture Archiving and Communication System).

```python
from medscan.integrations import PACSIntegration

pacs = PACSIntegration(
    host="pacs.hospital.com",
    port=11112,
    aet_title="MEDSCAN",
    auth=auth
)
```

#### Methods

##### query_studies(patient_id, date_range)
```python
def query_studies(
    patient_id: str = None,
    date_range: tuple = None,
    modality: str = None
) -> List[Study]:
    """
    Query PACS for studies.
    
    Args:
        patient_id (str): Patient identifier
        date_range (tuple): (start_date, end_date)
        modality (str): DICOM modality filter
        
    Returns:
        List[Study]: Matching studies
    """
```

##### retrieve_images(study_uid)
```python
def retrieve_images(study_uid: str) -> List[DICOMImage]:
    """
    Retrieve images for a study.
    
    Args:
        study_uid (str): Study unique identifier
        
    Returns:
        List[DICOMImage]: Retrieved DICOM images
    """
```

## Error Handling

### Exception Classes

```python
class MedScanAPIError(Exception):
    """Base API exception."""
    def __init__(self, message: str, status_code: int = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class AuthenticationError(MedScanAPIError):
    """Authentication failed."""
    pass

class AuthorizationError(MedScanAPIError):
    """Insufficient permissions."""
    pass

class ValidationError(MedScanAPIError):
    """Input validation failed."""
    pass

class ProcessingError(MedScanAPIError):
    """Processing operation failed."""
    pass

class ComplianceError(MedScanAPIError):
    """Compliance validation failed."""
    pass
```

### Error Response Format
```json
{
    "error": {
        "code": "INVALID_DICOM",
        "message": "DICOM file validation failed",
        "details": {
            "field": "StudyDate",
            "reason": "Invalid date format"
        },
        "timestamp": "2025-01-15T10:30:00Z",
        "request_id": "req_123456789"
    }
}
```

## Rate Limiting

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
X-RateLimit-Window: 3600
```

### Rate Limits by Endpoint
- **AI Analysis**: 100 requests/hour
- **DICOM Processing**: 500 requests/hour
- **Report Generation**: 200 requests/hour
- **Batch Operations**: 10 requests/hour

## SDK Examples

### Complete Workflow Example
```python
from medscan import MedScanClient, APIKeyAuth
from medscan.dicom import DICOMProcessor
from medscan.ai import AIAnalyzer
from medscan.reports import ReportGenerator

# Initialize client
client = MedScanClient(auth=APIKeyAuth("your_api_key"))

# Process DICOM file
processor = DICOMProcessor(client)
dicom_image = processor.load_dicom("/path/to/xray.dcm")
anonymized_image = processor.anonymize_dicom(dicom_image)

# Perform AI analysis
analyzer = AIAnalyzer(client)
result = analyzer.analyze_image(anonymized_image)

# Generate report
generator = ReportGenerator(client)
report = generator.generate_report(result, "standard")
pdf_path = generator.export_pdf(report, "/path/to/report.pdf")

print(f"Analysis complete. Report saved to: {pdf_path}")
```

### Async API Example
```python
import asyncio
from medscan.async_client import AsyncMedScanClient

async def process_images_async():
    async with AsyncMedScanClient(auth=auth) as client:
        tasks = []
        for image_path in image_paths:
            task = client.analyze_image_async(image_path)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return results

# Run async processing
results = asyncio.run(process_images_async())
```

### Integration with Flask Web App
```python
from flask import Flask, request, jsonify
from medscan import MedScanClient, APIKeyAuth

app = Flask(__name__)
client = MedScanClient(auth=APIKeyAuth("your_api_key"))

@app.route('/analyze', methods=['POST'])
def analyze_dicom():
    try:
        file = request.files['dicom_file']
        temp_path = save_temp_file(file)
        
        # Process and analyze
        dicom_image = client.dicom.load_dicom(temp_path)
        result = client.ai.analyze_image(dicom_image)
        
        return jsonify({
            'status': 'success',
            'findings': [f.to_dict() for f in result.findings],
            'confidence': result.overall_confidence
        })
    
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 400
```

## Webhooks

### Webhook Events
```python
# Analysis completion webhook
{
    "event": "analysis.completed",
    "data": {
        "analysis_id": "analysis_123",
        "image_id": "img_456",
        "status": "completed",
        "findings_count": 2,
        "confidence": 0.95
    },
    "timestamp": "2025-01-15T10:30:00Z"
}

# Batch processing webhook
{
    "event": "batch.completed",
    "data": {
        "batch_id": "batch_789",
        "total_images": 10,
        "completed": 10,
        "failed": 0,
        "average_confidence": 0.92
    },
    "timestamp": "2025-01-15T10:35:00Z"
}
```

### Webhook Verification
```python
import hmac
import hashlib

def verify_webhook(payload, signature, secret):
    """Verify webhook signature."""
    expected = hmac.new(
        secret.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(f"sha256={expected}", signature)
```

## Testing

### Mock Client for Testing
```python
from medscan.testing import MockMedScanClient

# Use mock client in tests
mock_client = MockMedScanClient()
mock_client.set_response("analyze_image", {
    "findings": [{"type": "fracture", "confidence": 0.95}],
    "overall_confidence": 0.95
})

# Test your code with mock responses
result = mock_client.ai.analyze_image(test_image)
assert result.overall_confidence == 0.95
```

## Support and Resources

- **API Documentation**: https://docs.medscan-ai.com/api
- **SDK Repository**: https://github.com/medscan-ai/python-sdk
- **Support**: <EMAIL>
- **Status Page**: https://status.medscan-ai.com

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**API Version**: v1  
**Next Review**: March 2025 