"""
DICOM metadata extraction and handling.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, Optional

import pydicom
from pydicom.dataset import Dataset

from ..exceptions import DicomMetadataError


@dataclass
class PatientInfo:
    """Patient information extracted from DICOM."""

    patient_name: Optional[str] = None
    patient_id: Optional[str] = None
    patient_birth_date: Optional[str] = None
    patient_sex: Optional[str] = None
    patient_age: Optional[str] = None
    patient_weight: Optional[str] = None
    patient_comments: Optional[str] = None


@dataclass
class StudyInfo:
    """Study information extracted from DICOM."""

    study_instance_uid: Optional[str] = None
    study_date: Optional[str] = None
    study_time: Optional[str] = None
    study_description: Optional[str] = None
    study_id: Optional[str] = None
    accession_number: Optional[str] = None
    referring_physician_name: Optional[str] = None
    institution_name: Optional[str] = None


@dataclass
class SeriesInfo:
    """Series information extracted from DICOM."""

    series_instance_uid: Optional[str] = None
    series_number: Optional[str] = None
    series_description: Optional[str] = None
    modality: Optional[str] = None
    body_part_examined: Optional[str] = None
    series_date: Optional[str] = None
    series_time: Optional[str] = None
    protocol_name: Optional[str] = None


@dataclass
class ImageInfo:
    """Image-specific information extracted from DICOM."""

    sop_instance_uid: Optional[str] = None
    instance_number: Optional[str] = None
    image_orientation_patient: Optional[str] = None
    image_position_patient: Optional[str] = None
    pixel_spacing: Optional[str] = None
    slice_thickness: Optional[str] = None
    slice_location: Optional[str] = None
    rows: Optional[int] = None
    columns: Optional[int] = None
    bits_allocated: Optional[int] = None
    bits_stored: Optional[int] = None
    high_bit: Optional[int] = None
    pixel_representation: Optional[int] = None
    samples_per_pixel: Optional[int] = None
    photometric_interpretation: Optional[str] = None


@dataclass
class TechnicalInfo:
    """Technical and display parameters from DICOM."""

    window_center: Optional[str] = None
    window_width: Optional[str] = None
    rescale_intercept: Optional[str] = None
    rescale_slope: Optional[str] = None
    kvp: Optional[str] = None
    exposure_time: Optional[str] = None
    x_ray_tube_current: Optional[str] = None
    exposure: Optional[str] = None
    filter_type: Optional[str] = None


@dataclass
class DicomMetadata:
    """Complete DICOM metadata container."""

    patient_info: PatientInfo
    study_info: StudyInfo
    series_info: SeriesInfo
    image_info: ImageInfo
    technical_info: TechnicalInfo
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    transfer_syntax_uid: Optional[str] = None
    sop_class_uid: Optional[str] = None
    manufacturer: Optional[str] = None
    manufacturer_model_name: Optional[str] = None
    software_versions: Optional[str] = None


class DicomMetadataExtractor:
    """Extracts and structures DICOM metadata from pydicom datasets."""

    @staticmethod
    def extract_patient_info(dataset: Dataset) -> PatientInfo:
        """Extract patient information from DICOM dataset."""
        return PatientInfo(
            patient_name=DicomMetadataExtractor._safe_get_tag(dataset, "PatientName"),
            patient_id=DicomMetadataExtractor._safe_get_tag(dataset, "PatientID"),
            patient_birth_date=DicomMetadataExtractor._safe_get_tag(
                dataset, "PatientBirthDate"
            ),
            patient_sex=DicomMetadataExtractor._safe_get_tag(dataset, "PatientSex"),
            patient_age=DicomMetadataExtractor._safe_get_tag(dataset, "PatientAge"),
            patient_weight=DicomMetadataExtractor._safe_get_tag(
                dataset, "PatientWeight"
            ),
            patient_comments=DicomMetadataExtractor._safe_get_tag(
                dataset, "PatientComments"
            ),
        )

    @staticmethod
    def extract_study_info(dataset: Dataset) -> StudyInfo:
        """Extract study information from DICOM dataset."""
        return StudyInfo(
            study_instance_uid=DicomMetadataExtractor._safe_get_tag(
                dataset, "StudyInstanceUID"
            ),
            study_date=DicomMetadataExtractor._safe_get_tag(dataset, "StudyDate"),
            study_time=DicomMetadataExtractor._safe_get_tag(dataset, "StudyTime"),
            study_description=DicomMetadataExtractor._safe_get_tag(
                dataset, "StudyDescription"
            ),
            study_id=DicomMetadataExtractor._safe_get_tag(dataset, "StudyID"),
            accession_number=DicomMetadataExtractor._safe_get_tag(
                dataset, "AccessionNumber"
            ),
            referring_physician_name=DicomMetadataExtractor._safe_get_tag(
                dataset, "ReferringPhysicianName"
            ),
            institution_name=DicomMetadataExtractor._safe_get_tag(
                dataset, "InstitutionName"
            ),
        )

    @staticmethod
    def extract_series_info(dataset: Dataset) -> SeriesInfo:
        """Extract series information from DICOM dataset."""
        return SeriesInfo(
            series_instance_uid=DicomMetadataExtractor._safe_get_tag(
                dataset, "SeriesInstanceUID"
            ),
            series_number=DicomMetadataExtractor._safe_get_tag(dataset, "SeriesNumber"),
            series_description=DicomMetadataExtractor._safe_get_tag(
                dataset, "SeriesDescription"
            ),
            modality=DicomMetadataExtractor._safe_get_tag(dataset, "Modality"),
            body_part_examined=DicomMetadataExtractor._safe_get_tag(
                dataset, "BodyPartExamined"
            ),
            series_date=DicomMetadataExtractor._safe_get_tag(dataset, "SeriesDate"),
            series_time=DicomMetadataExtractor._safe_get_tag(dataset, "SeriesTime"),
            protocol_name=DicomMetadataExtractor._safe_get_tag(dataset, "ProtocolName"),
        )

    @staticmethod
    def extract_image_info(dataset: Dataset) -> ImageInfo:
        """Extract image-specific information from DICOM dataset."""
        return ImageInfo(
            sop_instance_uid=DicomMetadataExtractor._safe_get_tag(
                dataset, "SOPInstanceUID"
            ),
            instance_number=DicomMetadataExtractor._safe_get_tag(
                dataset, "InstanceNumber"
            ),
            image_orientation_patient=DicomMetadataExtractor._safe_get_tag(
                dataset, "ImageOrientationPatient"
            ),
            image_position_patient=DicomMetadataExtractor._safe_get_tag(
                dataset, "ImagePositionPatient"
            ),
            pixel_spacing=DicomMetadataExtractor._safe_get_tag(dataset, "PixelSpacing"),
            slice_thickness=DicomMetadataExtractor._safe_get_tag(
                dataset, "SliceThickness"
            ),
            slice_location=DicomMetadataExtractor._safe_get_tag(
                dataset, "SliceLocation"
            ),
            rows=DicomMetadataExtractor._safe_get_tag(dataset, "Rows"),
            columns=DicomMetadataExtractor._safe_get_tag(dataset, "Columns"),
            bits_allocated=DicomMetadataExtractor._safe_get_tag(
                dataset, "BitsAllocated"
            ),
            bits_stored=DicomMetadataExtractor._safe_get_tag(dataset, "BitsStored"),
            high_bit=DicomMetadataExtractor._safe_get_tag(dataset, "HighBit"),
            pixel_representation=DicomMetadataExtractor._safe_get_tag(
                dataset, "PixelRepresentation"
            ),
            samples_per_pixel=DicomMetadataExtractor._safe_get_tag(
                dataset, "SamplesPerPixel"
            ),
            photometric_interpretation=DicomMetadataExtractor._safe_get_tag(
                dataset, "PhotometricInterpretation"
            ),
        )

    @staticmethod
    def extract_technical_info(dataset: Dataset) -> TechnicalInfo:
        """Extract technical and display parameters from DICOM dataset."""
        return TechnicalInfo(
            window_center=DicomMetadataExtractor._safe_get_tag(dataset, "WindowCenter"),
            window_width=DicomMetadataExtractor._safe_get_tag(dataset, "WindowWidth"),
            rescale_intercept=DicomMetadataExtractor._safe_get_tag(
                dataset, "RescaleIntercept"
            ),
            rescale_slope=DicomMetadataExtractor._safe_get_tag(dataset, "RescaleSlope"),
            kvp=DicomMetadataExtractor._safe_get_tag(dataset, "KVP"),
            exposure_time=DicomMetadataExtractor._safe_get_tag(dataset, "ExposureTime"),
            x_ray_tube_current=DicomMetadataExtractor._safe_get_tag(
                dataset, "XRayTubeCurrent"
            ),
            exposure=DicomMetadataExtractor._safe_get_tag(dataset, "Exposure"),
            filter_type=DicomMetadataExtractor._safe_get_tag(dataset, "FilterType"),
        )

    @staticmethod
    def extract_complete_metadata(
        dataset: Dataset, file_path: Optional[str] = None
    ) -> DicomMetadata:
        """Extract complete metadata from DICOM dataset."""
        try:
            return DicomMetadata(
                patient_info=DicomMetadataExtractor.extract_patient_info(dataset),
                study_info=DicomMetadataExtractor.extract_study_info(dataset),
                series_info=DicomMetadataExtractor.extract_series_info(dataset),
                image_info=DicomMetadataExtractor.extract_image_info(dataset),
                technical_info=DicomMetadataExtractor.extract_technical_info(dataset),
                file_path=file_path,
                transfer_syntax_uid=DicomMetadataExtractor._safe_get_tag(
                    dataset, "TransferSyntaxUID"
                ),
                sop_class_uid=DicomMetadataExtractor._safe_get_tag(
                    dataset, "SOPClassUID"
                ),
                manufacturer=DicomMetadataExtractor._safe_get_tag(
                    dataset, "Manufacturer"
                ),
                manufacturer_model_name=DicomMetadataExtractor._safe_get_tag(
                    dataset, "ManufacturerModelName"
                ),
                software_versions=DicomMetadataExtractor._safe_get_tag(
                    dataset, "SoftwareVersions"
                ),
            )
        except Exception as e:
            raise DicomMetadataError(f"Failed to extract metadata: {str(e)}")

    @staticmethod
    def _safe_get_tag(dataset: Dataset, tag_name: str) -> Optional[str]:
        """Safely extract a tag value from DICOM dataset."""
        try:
            if hasattr(dataset, tag_name):
                value = getattr(dataset, tag_name)
                if value is not None:
                    return str(value)
            return None
        except Exception:
            return None

    @staticmethod
    def get_formatted_metadata_dict(metadata: DicomMetadata) -> Dict[str, Any]:
        """Convert metadata to a formatted dictionary for display."""
        return {
            "Patient Information": {
                "Name": metadata.patient_info.patient_name,
                "ID": metadata.patient_info.patient_id,
                "Birth Date": metadata.patient_info.patient_birth_date,
                "Sex": metadata.patient_info.patient_sex,
                "Age": metadata.patient_info.patient_age,
            },
            "Study Information": {
                "Study UID": metadata.study_info.study_instance_uid,
                "Study Date": metadata.study_info.study_date,
                "Study Time": metadata.study_info.study_time,
                "Description": metadata.study_info.study_description,
                "Institution": metadata.study_info.institution_name,
            },
            "Series Information": {
                "Series UID": metadata.series_info.series_instance_uid,
                "Series Number": metadata.series_info.series_number,
                "Modality": metadata.series_info.modality,
                "Description": metadata.series_info.series_description,
                "Body Part": metadata.series_info.body_part_examined,
            },
            "Image Information": {
                "Rows": metadata.image_info.rows,
                "Columns": metadata.image_info.columns,
                "Pixel Spacing": metadata.image_info.pixel_spacing,
                "Slice Thickness": metadata.image_info.slice_thickness,
                "Bits Allocated": metadata.image_info.bits_allocated,
            },
            "Technical Parameters": {
                "Window Center": metadata.technical_info.window_center,
                "Window Width": metadata.technical_info.window_width,
                "Rescale Intercept": metadata.technical_info.rescale_intercept,
                "Rescale Slope": metadata.technical_info.rescale_slope,
            },
            "File Information": {
                "File Path": metadata.file_path,
                "Transfer Syntax": metadata.transfer_syntax_uid,
                "SOP Class": metadata.sop_class_uid,
                "Manufacturer": metadata.manufacturer,
            },
        }
