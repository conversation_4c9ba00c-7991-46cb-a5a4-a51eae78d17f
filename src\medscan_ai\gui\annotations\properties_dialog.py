"""
Annotation properties dialog for editing annotation metadata and settings.
"""

from typing import Op<PERSON>, Dict, Any
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QColorDialog,
    QPushButton, QLabel, QCheckBox, QDialogButtonBox,
    QSlider, QDateTimeEdit
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor

from .types import AnnotationTool, ManualAnnotation


class AnnotationPropertiesDialog(QDialog):
    """Dialog for editing annotation properties and metadata."""
    
    annotation_updated = Signal(str, dict)  # annotation_id, updated_properties
    
    def __init__(self, annotation: ManualAnnotation, parent=None):
        """Initialize the properties dialog."""
        super().__init__(parent)
        self.annotation = annotation
        self.original_metadata = annotation.metadata.copy()
        
        self.setWindowTitle(f"Annotation Properties - {annotation.tool_type.value}")
        self.setModal(True)
        self.resize(400, 500)
        
        self._setup_ui()
        self._load_annotation_data()
        self._connect_signals()
    
    def _setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        
        # Basic Information Group
        basic_group = QGroupBox("Basic Information")
        basic_layout = QFormLayout(basic_group)
        
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Enter annotation title...")
        basic_layout.addRow("Title:", self.title_edit)
        
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Enter description...")
        self.description_edit.setMaximumHeight(80)
        basic_layout.addRow("Description:", self.description_edit)
        
        self.tool_type_label = QLabel()
        basic_layout.addRow("Tool Type:", self.tool_type_label)
        
        layout.addWidget(basic_group)
        
        # Visual Appearance Group
        self._setup_appearance_group(layout)
        
        # Medical Information Group (if applicable)
        if self._has_medical_fields():
            self._setup_medical_group(layout)
        
        # Dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_changes)
        
        layout.addWidget(button_box)
    
    def _setup_appearance_group(self, layout: QVBoxLayout):
        """Set up the appearance group."""
        appearance_group = QGroupBox("Visual Appearance")
        appearance_layout = QFormLayout(appearance_group)
        
        # Color selection
        color_layout = QHBoxLayout()
        self.color_button = QPushButton()
        self.color_button.setFixedSize(50, 30)
        self.color_button.clicked.connect(self._select_color)
        self.color_label = QLabel("#FF0000")
        color_layout.addWidget(self.color_button)
        color_layout.addWidget(self.color_label)
        color_layout.addStretch()
        appearance_layout.addRow("Color:", color_layout)
        
        # Line width
        self.line_width_spin = QSpinBox()
        self.line_width_spin.setRange(1, 10)
        self.line_width_spin.setValue(2)
        self.line_width_spin.setSuffix(" px")
        appearance_layout.addRow("Line Width:", self.line_width_spin)
        
        # Opacity
        opacity_layout = QHBoxLayout()
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(10, 100)
        self.opacity_slider.setValue(100)
        self.opacity_label = QLabel("100%")
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        appearance_layout.addRow("Opacity:", opacity_layout)
        
        layout.addWidget(appearance_group)
    
    def _setup_medical_group(self, layout: QVBoxLayout):
        """Set up the medical group."""
        medical_group = QGroupBox("Medical Information")
        medical_layout = QFormLayout(medical_group)
        
        self.clinical_significance_combo = QComboBox()
        self.clinical_significance_combo.addItems([
            "None", "Low", "Moderate", "High", "Critical"
        ])
        medical_layout.addRow("Clinical Significance:", self.clinical_significance_combo)
        
        self.urgency_combo = QComboBox()
        self.urgency_combo.addItems([
            "normal", "low", "medium", "high", "urgent"
        ])
        medical_layout.addRow("Urgency Level:", self.urgency_combo)
        
        layout.addWidget(medical_group)
    
    def _has_medical_fields(self) -> bool:
        """Check if annotation has medical-specific fields."""
        return any(key in self.annotation.metadata for key in [
            'clinical_significance', 'urgency_level', 'measurements'
        ])
    
    def _load_annotation_data(self):
        """Load annotation data into the dialog controls."""
        metadata = self.annotation.metadata
        
        # Basic information
        self.title_edit.setText(metadata.get('title', ''))
        self.description_edit.setPlainText(metadata.get('description', ''))
        self.tool_type_label.setText(self.annotation.tool_type.value)
        
        # Visual appearance
        color = metadata.get('pen_color', '#FF0000')
        self._set_color_button(color)
        self.color_label.setText(color)
        
        line_width = metadata.get('pen_width', 2)
        self.line_width_spin.setValue(int(line_width))
        
        opacity = int(metadata.get('opacity', 100))
        self.opacity_slider.setValue(opacity)
        
        # Medical information
        if self._has_medical_fields():
            clinical_sig = metadata.get('clinical_significance', 'None')
            index = self.clinical_significance_combo.findText(clinical_sig)
            if index >= 0:
                self.clinical_significance_combo.setCurrentIndex(index)
            
            urgency = metadata.get('urgency_level', 'normal')
            index = self.urgency_combo.findText(urgency)
            if index >= 0:
                self.urgency_combo.setCurrentIndex(index)
    
    def _set_color_button(self, color_str: str):
        """Set the color button appearance."""
        try:
            color = QColor(color_str)
            if color.isValid():
                self.color_button.setStyleSheet(
                    f"QPushButton {{ background-color: {color_str}; border: 1px solid black; }}"
                )
        except:
            pass
    
    def _select_color(self):
        """Open color selection dialog."""
        current_color = QColor(self.color_label.text())
        color = QColorDialog.getColor(current_color, self, "Select Annotation Color")
        
        if color.isValid():
            color_hex = color.name()
            self._set_color_button(color_hex)
            self.color_label.setText(color_hex)
    
    def _connect_signals(self):
        """Connect widget signals."""
        # Connect change signals for live preview
        self.title_edit.textChanged.connect(self._on_properties_changed)
        self.line_width_spin.valueChanged.connect(self._on_properties_changed)
        self.opacity_slider.valueChanged.connect(self._on_properties_changed)
    
    def _on_properties_changed(self):
        """Handle property changes for live preview."""
        # Emit signal for live preview updates
        properties = self._get_current_properties()
        self.annotation_updated.emit(self.annotation.id, properties)
    
    def _get_current_properties(self) -> Dict[str, Any]:
        """Get current properties from dialog controls."""
        properties = {}
        
        # Basic information
        properties['title'] = self.title_edit.text().strip()
        properties['description'] = self.description_edit.toPlainText().strip()
        
        # Visual appearance
        properties['pen_color'] = self.color_label.text()
        properties['pen_width'] = self.line_width_spin.value()
        properties['opacity'] = self.opacity_slider.value()
        
        # Medical information
        if self._has_medical_fields():
            properties['clinical_significance'] = self.clinical_significance_combo.currentText()
            properties['urgency_level'] = self.urgency_combo.currentText()
        
        return properties
    
    def _apply_changes(self):
        """Apply changes without closing dialog."""
        properties = self._get_current_properties()
        self.annotation_updated.emit(self.annotation.id, properties)
        
        # Update annotation metadata
        self.annotation.metadata.update(properties)
    
    def get_updated_properties(self) -> Dict[str, Any]:
        """Get the updated properties for the annotation."""
        return self._get_current_properties()
    
    def accept(self):
        """Accept dialog and apply changes."""
        self._apply_changes()
        super().accept()
    
    def reject(self):
        """Reject dialog and revert changes."""
        # Restore original metadata
        self.annotation.metadata = self.original_metadata
        super().reject() 