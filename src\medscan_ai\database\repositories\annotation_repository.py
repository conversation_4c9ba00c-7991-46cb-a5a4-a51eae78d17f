"""Annotation repository for managing manual annotations.

Provides CRUD operations and specialized queries for annotation management
including coordinate transformations, filtering, and medical workflow support.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, desc
from sqlalchemy.orm import Session, joinedload

from .base import BaseRepository
from ..models.annotation import Annotation, AnnotationType


class AnnotationRepository(BaseRepository[Annotation]):
    """Repository for annotation CRUD operations and specialized queries."""

    def __init__(self, session: Session):
        """Initialize annotation repository.
        
        Args:
            session: Database session
        """
        super().__init__(Annotation, session)

    def create_annotation(
        self,
        study_id: int,
        creator_user_id: str,
        annotation_type: str,
        coordinates: Dict[str, Any],
        image_id: Optional[int] = None,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: str = "#FF0000",
        line_width: int = 2,
        opacity: float = 1.0,
        **kwargs
    ) -> Annotation:
        """Create a new annotation.

        Args:
            study_id: Study ID
            creator_user_id: User who created the annotation
            annotation_type: Type of annotation (RECTANGLE, FREEHAND, etc.)
            coordinates: Coordinate data as dictionary
            image_id: Optional specific image ID
            title: Optional annotation title
            description: Optional description
            color: Annotation color (hex code)
            line_width: Line width for drawing
            opacity: Annotation opacity (0.0 to 1.0)
            **kwargs: Additional annotation fields

        Returns:
            Created annotation instance
        """
        annotation = self.create(
            study_id=study_id,
            image_id=image_id,
            creator_user_id=creator_user_id,
            annotation_type=annotation_type,
            coordinates=coordinates,
            title=title,
            description=description,
            color=color,
            line_width=line_width,
            opacity=opacity,
            **kwargs
        )
        return annotation

    def get_annotations_for_study(
        self,
        study_id: int,
        include_inactive: bool = False,
        annotation_types: Optional[List[str]] = None
    ) -> List[Annotation]:
        """Get all annotations for a study.

        Args:
            study_id: Study ID
            include_inactive: Whether to include soft-deleted annotations
            annotation_types: Optional filter by annotation types

        Returns:
            List of annotations for the study
        """
        query = (
            self.session.query(Annotation)
            .filter(Annotation.study_id == study_id)
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
        )

        if not include_inactive:
            query = query.filter(Annotation.is_active == True)

        if annotation_types:
            query = query.filter(Annotation.annotation_type.in_(annotation_types))

        return query.order_by(desc(Annotation.created_at)).all()

    def get_annotations_for_image(
        self,
        image_id: int,
        include_inactive: bool = False,
        annotation_types: Optional[List[str]] = None
    ) -> List[Annotation]:
        """Get all annotations for a specific image.

        Args:
            image_id: Image ID
            include_inactive: Whether to include soft-deleted annotations
            annotation_types: Optional filter by annotation types

        Returns:
            List of annotations for the image
        """
        query = (
            self.session.query(Annotation)
            .filter(Annotation.image_id == image_id)
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
        )

        if not include_inactive:
            query = query.filter(Annotation.is_active == True)

        if annotation_types:
            query = query.filter(Annotation.annotation_type.in_(annotation_types))

        return query.order_by(desc(Annotation.created_at)).all()

    def get_annotations_by_creator(
        self,
        creator_user_id: str,
        include_inactive: bool = False,
        limit: Optional[int] = None
    ) -> List[Annotation]:
        """Get annotations created by a specific user.

        Args:
            creator_user_id: User ID of creator
            include_inactive: Whether to include soft-deleted annotations
            limit: Optional limit on number of results

        Returns:
            List of annotations created by the user
        """
        query = (
            self.session.query(Annotation)
            .filter(Annotation.creator_user_id == creator_user_id)
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
        )

        if not include_inactive:
            query = query.filter(Annotation.is_active == True)

        query = query.order_by(desc(Annotation.created_at))

        if limit:
            query = query.limit(limit)

        return query.all()

    def get_annotation_by_id(self, annotation_id: str) -> Optional[Annotation]:
        """Get annotation by ID with relationships loaded.

        Args:
            annotation_id: Annotation UUID

        Returns:
            Annotation instance or None if not found
        """
        return (
            self.session.query(Annotation)
            .filter(Annotation.id == annotation_id)
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
            .first()
        )

    def update_annotation_coordinates(
        self,
        annotation_id: str,
        new_coordinates: Dict[str, Any]
    ) -> Optional[Annotation]:
        """Update annotation coordinates.

        Args:
            annotation_id: Annotation UUID
            new_coordinates: New coordinate data

        Returns:
            Updated annotation or None if not found
        """
        annotation = self.get_annotation_by_id(annotation_id)
        if annotation and not annotation.is_locked:
            annotation.update_coordinates(new_coordinates)
            self.session.flush()
            self.session.refresh(annotation)
            return annotation
        return None

    def soft_delete_annotation(self, annotation_id: str) -> bool:
        """Soft delete an annotation.

        Args:
            annotation_id: Annotation UUID

        Returns:
            True if deleted successfully
        """
        annotation = self.get_annotation_by_id(annotation_id)
        if annotation and not annotation.is_locked:
            annotation.soft_delete()
            self.session.flush()
            return True
        return False

    def restore_annotation(self, annotation_id: str) -> bool:
        """Restore a soft-deleted annotation.

        Args:
            annotation_id: Annotation UUID

        Returns:
            True if restored successfully
        """
        annotation = self.get_annotation_by_id(annotation_id)
        if annotation:
            annotation.restore()
            self.session.flush()
            return True
        return False

    def lock_annotation(self, annotation_id: str) -> bool:
        """Lock annotation from editing.

        Args:
            annotation_id: Annotation UUID

        Returns:
            True if locked successfully
        """
        annotation = self.get_annotation_by_id(annotation_id)
        if annotation:
            annotation.lock()
            self.session.flush()
            return True
        return False

    def unlock_annotation(self, annotation_id: str) -> bool:
        """Unlock annotation for editing.

        Args:
            annotation_id: Annotation UUID

        Returns:
            True if unlocked successfully
        """
        annotation = self.get_annotation_by_id(annotation_id)
        if annotation:
            annotation.unlock()
            self.session.flush()
            return True
        return False

    def get_annotations_by_clinical_significance(
        self,
        clinical_significance: str,
        urgency_level: Optional[str] = None
    ) -> List[Annotation]:
        """Get annotations by clinical significance and urgency.

        Args:
            clinical_significance: Clinical significance filter
            urgency_level: Optional urgency level filter

        Returns:
            List of matching annotations
        """
        query = (
            self.session.query(Annotation)
            .filter(
                and_(
                    Annotation.clinical_significance == clinical_significance,
                    Annotation.is_active == True
                )
            )
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
        )

        if urgency_level:
            query = query.filter(Annotation.urgency_level == urgency_level)

        return query.order_by(desc(Annotation.created_at)).all()

    def search_annotations(
        self,
        search_term: str,
        study_id: Optional[int] = None,
        annotation_types: Optional[List[str]] = None
    ) -> List[Annotation]:
        """Search annotations by title, description, or tags.

        Args:
            search_term: Search term
            study_id: Optional study filter
            annotation_types: Optional annotation type filter

        Returns:
            List of matching annotations
        """
        if not search_term.strip():
            return []

        search_pattern = f"%{search_term}%"
        conditions = [
            Annotation.title.ilike(search_pattern),
            Annotation.description.ilike(search_pattern),
            Annotation.tags.ilike(search_pattern)
        ]

        query = (
            self.session.query(Annotation)
            .filter(
                and_(
                    or_(*conditions),
                    Annotation.is_active == True
                )
            )
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
        )

        if study_id:
            query = query.filter(Annotation.study_id == study_id)

        if annotation_types:
            query = query.filter(Annotation.annotation_type.in_(annotation_types))

        return query.order_by(desc(Annotation.created_at)).all()

    def get_annotation_statistics(
        self, 
        study_id: Optional[int] = None,
        creator_user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get annotation statistics.

        Args:
            study_id: Optional study filter
            creator_user_id: Optional creator filter

        Returns:
            Dictionary with annotation statistics
        """
        query = self.session.query(Annotation).filter(Annotation.is_active == True)

        if study_id:
            query = query.filter(Annotation.study_id == study_id)
        if creator_user_id:
            query = query.filter(Annotation.creator_user_id == creator_user_id)

        annotations = query.all()
        
        # Count by type
        type_counts = {}
        for annotation_type in AnnotationType.get_all_types():
            type_counts[annotation_type] = sum(
                1 for a in annotations if a.annotation_type == annotation_type
            )

        return {
            'total_annotations': len(annotations),
            'annotations_by_type': type_counts,
            'locked_annotations': sum(1 for a in annotations if a.is_locked),
            'annotations_with_measurements': sum(
                1 for a in annotations if a.measurement_value is not None
            )
        }

    def bulk_update_annotations(
        self,
        annotation_ids: List[str],
        update_data: Dict[str, Any]
    ) -> List[Annotation]:
        """Bulk update multiple annotations.

        Args:
            annotation_ids: List of annotation UUIDs
            update_data: Data to update

        Returns:
            List of updated annotations
        """
        annotations = (
            self.session.query(Annotation)
            .filter(
                and_(
                    Annotation.id.in_(annotation_ids),
                    Annotation.is_active == True,
                    Annotation.is_locked == False
                )
            )
            .all()
        )

        updated_annotations = []
        for annotation in annotations:
            for key, value in update_data.items():
                if hasattr(annotation, key) and key not in ['id', 'created_at']:
                    setattr(annotation, key, value)
            updated_annotations.append(annotation)

        self.session.flush()
        return updated_annotations

    def get_recent_annotations(
        self,
        limit: int = 10,
        study_id: Optional[int] = None
    ) -> List[Annotation]:
        """Get most recent annotations.

        Args:
            limit: Maximum number of annotations to return
            study_id: Optional study filter

        Returns:
            List of recent annotations
        """
        query = (
            self.session.query(Annotation)
            .filter(Annotation.is_active == True)
            .options(joinedload(Annotation.study), joinedload(Annotation.image))
        )

        if study_id:
            query = query.filter(Annotation.study_id == study_id)

        return (
            query.order_by(desc(Annotation.created_at))
            .limit(limit)
            .all()
        ) 