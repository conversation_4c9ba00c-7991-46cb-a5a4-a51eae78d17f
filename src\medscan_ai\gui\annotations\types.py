"""
Annotation type definitions for the medical image viewer.
"""

from enum import Enum
from typing import Dict, Optional, Any
import uuid


class AnnotationTool(Enum):
    """Enumeration for different annotation tool types."""
    NONE = "none"
    RECTANGLE = "rectangle"
    POLYGON = "polygon"
    FREEHAND = "freehand"
    ERASER = "eraser"
    SELECT = "select"


class ManualAnnotation:
    """
    Represents a manual annotation created by the user.
    Stores the annotation type, geometry data, and metadata.
    """
    
    def __init__(self, annotation_id: str, tool_type: AnnotationTool, 
                 geometry_data: dict, metadata: dict = None):
        """
        Initialize manual annotation.
        
        Args:
            annotation_id: Unique identifier for the annotation
            tool_type: Type of annotation tool used
            geometry_data: Dictionary containing geometric information
                          (specific to each tool type)
            metadata: Optional metadata like creation time, author, etc.
        """
        self.id = annotation_id if annotation_id else str(uuid.uuid4())
        self.tool_type = tool_type
        self.geometry_data = geometry_data
        self.metadata = metadata or {}
        
        # Add timestamp if not present
        if 'created_at' not in self.metadata:
            from datetime import datetime
            self.metadata['created_at'] = datetime.now().isoformat()
            
    def to_dict(self) -> dict:
        """Convert annotation to dictionary for serialization."""
        return {
            'id': self.id,
            'tool_type': self.tool_type.value,
            'geometry_data': self.geometry_data,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: dict) -> 'ManualAnnotation':
        """Create annotation from dictionary."""
        return cls(
            annotation_id=data['id'],
            tool_type=AnnotationTool(data['tool_type']),
            geometry_data=data['geometry_data'],
            metadata=data.get('metadata', {})
        ) 