"""
Test suite for Authorization Decorators and RBAC Integration.

Tests role-based access control, permission-based access, medical hierarchy,
department-based authorization, and comprehensive security compliance.
"""

import json
import os
import sys
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import pytest
from flask import Flask, g

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", ".."))

from src.medscan_ai.api.auth_endpoints import create_auth_app, auth_bp
from src.medscan_ai.security.authentication.session_manager import TokenPayload, TokenType
from src.medscan_ai.security.authentication.authorization import AuthorizationResult


class TestAuthorizationDecorators:
    """Test suite for Authorization Decorators and RBAC functionality."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        app = Flask(__name__)
        app.config["TESTING"] = True
        app.config["SECRET_KEY"] = "test-secret-key"
        
        # Register the auth blueprint
        app.register_blueprint(auth_bp)
        
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def mock_auth_middleware(self):
        """Create mock authorization middleware."""
        middleware = Mock()
        middleware.get_current_user_info = Mock()
        middleware.check_role_authorization = Mock()
        middleware.check_permission_authorization = Mock()
        middleware.log_authorization_event = Mock()
        return middleware
    
    @pytest.fixture
    def valid_token(self):
        """Mock valid JWT token."""
        return "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.admin.token"
    
    @pytest.fixture
    def invalid_token(self):
        """Mock invalid JWT token."""
        return "Bearer invalid.token.here"
    
    @pytest.fixture
    def admin_payload(self):
        """Mock admin user payload."""
        return TokenPayload(
            user_id=str(uuid.uuid4()),
            session_id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="admin_user",
            roles=["admin"],
            permissions=["user:read", "user:write", "admin:access", "patient:read", "dicom:read", "patient_data_access", "dicom_access"],
            department="Administration",
            medical_license="ADM123456",
            hierarchy_level=9,
            token_type=TokenType.ACCESS,
            issued_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )
    
    @pytest.fixture
    def radiologist_payload(self):
        """Mock radiologist user payload."""
        return TokenPayload(
            user_id=str(uuid.uuid4()),
            session_id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="radiologist_user",
            roles=["radiologist"],
            permissions=["patient:read", "dicom:read", "dicom:analyze", "report:write", "patient_data_access", "dicom_access"],
            department="Radiology",
            medical_license="RAD123456",
            hierarchy_level=6,
            token_type=TokenType.ACCESS,
            issued_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )
    
    @pytest.fixture
    def guest_payload(self):
        """Mock guest user payload."""
        return TokenPayload(
            user_id=str(uuid.uuid4()),
            session_id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="guest_user",
            roles=["guest"],
            permissions=["basic:read"],
            department="Public",
            medical_license=None,
            hierarchy_level=0,
            token_type=TokenType.ACCESS,
            issued_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )

    # Basic Authentication Tests
    def test_profile_endpoint_requires_authentication(self, app, client, mock_auth_middleware):
        """Test that profile endpoint requires authentication."""
        # Mock no authentication - get_current_user_info returns None
        mock_auth_middleware.get_current_user_info.return_value = None
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get("/api/auth/profile")
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "Authentication required" in data["message"]
        
    def test_profile_endpoint_with_valid_auth(self, app, client, mock_auth_middleware, 
                                            valid_token, radiologist_payload):
        """Test profile endpoint with valid authentication."""
        # Mock successful authentication
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/profile",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert data["data"]["email"] == "<EMAIL>"

    # Admin Access Tests
    def test_admin_endpoint_requires_admin_role(self, app, client, mock_auth_middleware,
                                               valid_token, radiologist_payload):
        """Test that admin endpoint requires admin role."""
        # Mock authenticated but non-admin user
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        # Mock role check failure
        auth_result = AuthorizationResult(
            authorized=False,
            message="Insufficient role privileges: User roles '['radiologist']' do not include any of required roles: ['admin', 'super_admin']"
        )
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/admin/users",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 403
        data = json.loads(response.data)
        assert data["status"] == "error"
        assert "Insufficient role privileges" in data["message"]
        
    def test_admin_endpoint_with_admin_role(self, app, client, mock_auth_middleware,
                                          valid_token, admin_payload):
        """Test admin endpoint with admin role."""
        # Mock successful admin authentication
        mock_auth_middleware.get_current_user_info.return_value = admin_payload
        
        # Mock successful role check
        auth_result = AuthorizationResult(
            authorized=True,
            message="User has required role"
        )
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/admin/users",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert "admin_info" in data["data"]

    def test_admin_endpoint_with_radiologist_denied(self, app, client, mock_auth_middleware,
                                                  valid_token, radiologist_payload):
        """Test admin endpoint denies radiologist access."""
        # Mock authenticated radiologist
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        # Mock failed admin authorization
        auth_result = AuthorizationResult(
            authorized=False,
            message="Insufficient role hierarchy - admin access required"
        )
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/admin/users",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 403

    # Permission-Based Access Tests
    def test_dicom_endpoint_requires_permission(self, app, client, mock_auth_middleware,
                                              valid_token, guest_payload):
        """Test that DICOM endpoint requires dicom_access permission."""
        # Mock authenticated but insufficient permissions
        mock_auth_middleware.get_current_user_info.return_value = guest_payload
        
        # Mock permission check failure
        auth_result = AuthorizationResult(
            authorized=False,
            message="Missing required permission: dicom_access"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/dicom",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 403

    def test_dicom_endpoint_with_permission(self, app, client, mock_auth_middleware,
                                          valid_token, radiologist_payload):
        """Test DICOM endpoint with required permission."""
        # Mock successful authentication and permission check
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        auth_result = AuthorizationResult(
            authorized=True,
            message="User has required permission"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/dicom",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert "DICOM access granted" in data["message"]

    def test_dicom_endpoint_guest_denied(self, app, client, mock_auth_middleware,
                                       valid_token, guest_payload):
        """Test DICOM endpoint denies guest access."""
        # Mock authenticated guest
        mock_auth_middleware.get_current_user_info.return_value = guest_payload
        
        # Mock permission denied for guest
        auth_result = AuthorizationResult(
            authorized=False,
            message="Permission 'dicom_access' not in user permissions"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/dicom",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 403

    # Medical Role-Based Tests
    def test_patient_data_endpoint_requires_medical_role(self, app, client, mock_auth_middleware,
                                                       valid_token, guest_payload):
        """Test patient data endpoint requires medical role."""
        # Mock authenticated but non-medical role
        mock_auth_middleware.get_current_user_info.return_value = guest_payload
        
        # Mock permission check failure (first decorator)
        perm_result = AuthorizationResult(
            authorized=False,
            message="Missing required permission: patient_data_access"
        )
        mock_auth_middleware.check_permission_authorization.return_value = perm_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/patients",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 403

    def test_patient_data_endpoint_with_radiologist(self, app, client, mock_auth_middleware,
                                                  valid_token, radiologist_payload):
        """Test patient data endpoint with radiologist role."""
        # Mock successful authentication
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        # Mock successful permission and role checks
        auth_result = AuthorizationResult(
            authorized=True,
            message="Authorization granted"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/patients",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert "Patient data access granted" in data["message"]
        assert data["data"]["access_info"]["department"] == "Radiology"

    # Department-Based Access Tests
    @pytest.fixture
    def cardiology_payload(self):
        """Mock cardiologist user payload."""
        return TokenPayload(
            user_id=str(uuid.uuid4()),
            session_id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="cardio_user",
            roles=["cardiologist"],
            permissions=["patient:read", "cardio:read", "ecg:analyze", "patient_data_access"],
            department="Cardiology",
            medical_license="CAR123456",
            hierarchy_level=6,
            token_type=TokenType.ACCESS,
            issued_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )

    def test_department_based_access_cardiology(self, app, client, mock_auth_middleware,
                                              valid_token, cardiology_payload):
        """Test department-based access for cardiology department."""
        # Mock successful authentication
        mock_auth_middleware.get_current_user_info.return_value = cardiology_payload
        
        # Mock successful authorization
        auth_result = AuthorizationResult(
            authorized=True,
            message="Authorization granted"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/patients",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["data"]["access_info"]["department"] == "Cardiology"
        assert data["data"]["access_info"]["medical_license"] == "CAR123456"

    # Medical Hierarchy Tests
    @pytest.fixture
    def super_admin_payload(self):
        """Mock super admin user payload."""
        return TokenPayload(
            user_id=str(uuid.uuid4()),
            session_id=str(uuid.uuid4()),
            email="<EMAIL>",
            username="super_admin_user",
            roles=["super_admin"],
            permissions=["*:*"],  # All permissions
            department="Administration",
            medical_license="SUPER123456",
            hierarchy_level=10,
            token_type=TokenType.ACCESS,
            issued_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )

    def test_hierarchy_super_admin_access(self, app, client, mock_auth_middleware,
                                        valid_token, super_admin_payload):
        """Test super admin has highest hierarchy access."""
        # Mock successful authentication
        mock_auth_middleware.get_current_user_info.return_value = super_admin_payload
        
        # Mock successful authorization
        auth_result = AuthorizationResult(
            authorized=True,
            message="Super admin has all access"
        )
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/admin/users",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["data"]["admin_info"]["access_level"] == "admin"

    # Invalid Token Tests
    def test_invalid_token_format(self, app, client, mock_auth_middleware):
        """Test invalid token format."""
        # Mock no authentication
        mock_auth_middleware.get_current_user_info.return_value = None
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/profile",
                headers={"Authorization": "InvalidTokenFormat"}
            )
        
        assert response.status_code == 401

    def test_missing_authorization_header(self, app, client, mock_auth_middleware):
        """Test missing authorization header."""
        # Mock no authentication
        mock_auth_middleware.get_current_user_info.return_value = None
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get("/api/auth/profile")
        
        assert response.status_code == 401

    # Edge Cases and Error Handling
    def test_authorization_middleware_exception(self, app, client, mock_auth_middleware,
                                              valid_token, admin_payload):
        """Test authorization middleware exception handling."""
        # Mock authentication success but role check raises exception
        mock_auth_middleware.get_current_user_info.return_value = admin_payload
        mock_auth_middleware.check_role_authorization.side_effect = Exception("Middleware error")
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/admin/users",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data["status"]

    # Token Validation Tests
    @patch("src.medscan_ai.api.auth_endpoints.auth_service")
    def test_token_validation_in_protected_endpoints(self, mock_auth_service, app, client):
        """Test token validation in protected endpoints."""
        # Mock token validation failure
        mock_auth_service.validate_request_token.return_value = (
            False, None, "Token expired"
        )
        
        response = client.get(
            "/api/auth/mfa/status",
            headers={"Authorization": "Bearer expired.token"}
        )
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert "Token expired" in data["message"]

    # Authorization Audit Tests
    def test_authorization_audit_logging(self, app, client, mock_auth_middleware,
                                       valid_token, radiologist_payload):
        """Test that authorization events are properly audited."""
        # Mock successful authentication and authorization
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        auth_result = AuthorizationResult(
            authorized=True,
            message="Authorization granted"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/dicom",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "Access logged for HIPAA compliance" in data["data"]["compliance_note"]
        
        # Verify audit logging was called
        mock_auth_middleware.log_authorization_event.assert_called()

    # Multi-Layer Authorization Tests
    def test_multi_layer_authorization_patient_endpoint(self, app, client, mock_auth_middleware,
                                                      valid_token, radiologist_payload):
        """Test multi-layer authorization (permission + role) for patient endpoint."""
        # Mock successful authentication
        mock_auth_middleware.get_current_user_info.return_value = radiologist_payload
        
        # Mock successful authorization for both checks
        auth_result = AuthorizationResult(
            authorized=True,
            message="Authorization granted"
        )
        mock_auth_middleware.check_permission_authorization.return_value = auth_result
        mock_auth_middleware.check_role_authorization.return_value = auth_result
        
        with app.app_context():
            g.auth_middleware = mock_auth_middleware
            
            response = client.get(
                "/api/auth/medical/patients",
                headers={"Authorization": valid_token}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "Multi-layer authorization: permission + role check" in data["data"]["security_note"]


def run_authorization_tests():
    """Run all authorization decorator tests."""
    print("🧪 Starting Authorization Decorator Tests...")
    
    import subprocess
    import sys
    
    result = subprocess.run(
        [sys.executable, "-m", "pytest", __file__, "-v"], 
        capture_output=True, text=True
    )
    
    print("📊 Authorization Test Results:")
    print(result.stdout)
    if result.stderr:
        print(result.stderr)
        
    if result.returncode == 0:
        print("\n✅ All authorization tests passed!")
    else:
        print("\n❌ Some authorization tests failed. Check output above.")
        
    return result.returncode == 0


if __name__ == "__main__":
    success = run_authorization_tests()
    sys.exit(0 if success else 1) 