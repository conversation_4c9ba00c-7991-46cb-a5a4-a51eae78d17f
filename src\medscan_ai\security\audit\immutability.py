"""Immutability Manager for Audit Logs.

Provides cryptographic hashing and hash chaining mechanisms to ensure 
audit log immutability and tamper detection for HIPAA/GDPR compliance.
"""

import hashlib
import hmac
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
import secrets

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc

from ...database.models.audit_log import AuditLog
from ...database.repositories.audit_repository import AuditRepository

logger = logging.getLogger(__name__)


@dataclass
class HashChainEntry:
    """Represents a hash chain entry for audit immutability."""
    sequence: int
    timestamp: datetime
    content_hash: str
    previous_hash: str
    chain_hash: str
    tamper_verified: bool = True


class ImmutabilityManager:
    """Manages audit log immutability through cryptographic hashing."""
    
    def __init__(self, session: Optional = None, secret_key: Optional[bytes] = None):
        """Initialize immutability manager."""
        self._session = session
        self._secret_key = secret_key or secrets.token_bytes(32)
        self._hash_algorithm = hashlib.sha256
    
    def calculate_content_hash(self, audit_log) -> str:
        """Calculate SHA-256 hash of audit log content."""
        content = {
            'user_id': audit_log.user_id,
            'action': audit_log.action,
            'resource_type': audit_log.resource_type,
            'resource_id': audit_log.resource_id,
            'success': audit_log.success,
            'timestamp': audit_log.timestamp.isoformat() if audit_log.timestamp else None,
            'details': audit_log.details if hasattr(audit_log, 'details') and audit_log.details else {}
        }
        
        content_json = json.dumps(content, sort_keys=True)
        hash_obj = self._hash_algorithm()
        hash_obj.update(content_json.encode('utf-8'))
        return hash_obj.hexdigest()
    
    def calculate_chain_hash(self, content_hash: str, previous_hash: str, sequence: int) -> str:
        """Calculate HMAC chain hash."""
        chain_data = f"{content_hash}{previous_hash}{sequence}"
        signature = hmac.new(
            self._secret_key,
            chain_data.encode('utf-8'),
            self._hash_algorithm
        )
        return signature.hexdigest()
    
    def add_to_chain(self, audit_log):
        """Add audit log to hash chain."""
        # Get last sequence number
        sequence = 1  # For now, simplified
        previous_hash = ""  # For now, simplified
        
        content_hash = self.calculate_content_hash(audit_log)
        chain_hash = self.calculate_chain_hash(content_hash, previous_hash, sequence)
        
        # Update audit log with hash information
        audit_log.hash_signature = chain_hash
        audit_log.previous_hash = previous_hash
        audit_log.chain_sequence = sequence
        audit_log.tamper_verified = True
        
        return chain_hash
    
    def verify_entry_integrity(self, audit_log) -> bool:
        """Verify integrity of audit entry."""
        if not audit_log.hash_signature:
            return False
        
        content_hash = self.calculate_content_hash(audit_log)
        expected_hash = self.calculate_chain_hash(
            content_hash, 
            audit_log.previous_hash or "", 
            audit_log.chain_sequence or 1
        )
        
        return expected_hash == audit_log.hash_signature


# Global instance
_immutability_manager: Optional[ImmutabilityManager] = None


def get_immutability_manager(session=None) -> ImmutabilityManager:
    """Get global immutability manager."""
    global _immutability_manager
    if not _immutability_manager:
        _immutability_manager = ImmutabilityManager(session)
    return _immutability_manager 