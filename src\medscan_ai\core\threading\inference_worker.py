"""
AI Inference Worker for Background Model Processing

Provides background AI inference capabilities with progress tracking,
allowing the UI to remain responsive during model execution.
"""

import time
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
import pydicom
from pydicom.dataset import Dataset

from .base_worker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WorkerError
from ...ai.inference import InferenceEngine, InferenceResult, BatchInferenceResult, InferenceEngineError

logger = logging.getLogger(__name__)


class AIInferenceWorker(MedScanWorker):
    """
    Background worker for AI model inference operations.
    
    Supports both single inference and batch processing with:
    - Progress tracking and UI updates
    - Model loading and management
    - Error handling for inference failures
    - Memory-efficient processing
    - Cancellation support
    """
    
    def __init__(self,
                 datasets: Union[Dataset, List[Dataset], str, Path, List[Union[str, Path]]],
                 model_name: Optional[str] = None,
                 preprocessing_preset: str = "xray_anomaly_detection",
                 anomaly_threshold: Optional[float] = None,
                 batch_processing: bool = None,
                 parent=None):
        """
        Initialize AI inference worker.
        
        Args:
            datasets: DICOM dataset(s) or file path(s) for inference
            model_name: Model to use (None for default)
            preprocessing_preset: Preprocessing configuration
            anomaly_threshold: Custom anomaly threshold (None to use model default)
            batch_processing: Force batch mode (None for auto-detection)
            parent: Parent QObject
        """
        # Normalize inputs
        self._prepare_datasets(datasets)
        self.model_name = model_name
        self.preprocessing_preset = preprocessing_preset
        self.anomaly_threshold = anomaly_threshold
        
        # Determine processing mode
        if batch_processing is None:
            self.batch_processing = len(self.dataset_inputs) > 1
        else:
            self.batch_processing = batch_processing
        
        # Initialize base worker
        if self.batch_processing:
            operation_name = f"AI Inference Batch ({len(self.dataset_inputs)} items)"
        else:
            operation_name = "AI Inference"
        
        super().__init__(operation_name, parent)
        
        # Initialize inference engine
        self.inference_engine = None
        self._inference_results = []
    
    def _prepare_datasets(self, datasets):
        """Prepare and normalize dataset inputs."""
        self.dataset_inputs = []
        
        # Convert various input types to standardized format
        if isinstance(datasets, (str, Path)):
            # Single file path
            self.dataset_inputs.append({'type': 'file', 'data': str(datasets)})
        elif isinstance(datasets, Dataset):
            # Single DICOM dataset
            self.dataset_inputs.append({'type': 'dataset', 'data': datasets})
        elif isinstance(datasets, list):
            # Multiple inputs
            for item in datasets:
                if isinstance(item, (str, Path)):
                    self.dataset_inputs.append({'type': 'file', 'data': str(item)})
                elif isinstance(item, Dataset):
                    self.dataset_inputs.append({'type': 'dataset', 'data': item})
                else:
                    raise ValueError(f"Unsupported dataset type: {type(item)}")
        else:
            raise ValueError(f"Unsupported datasets type: {type(datasets)}")
    
    def do_work(self) -> Union[InferenceResult, BatchInferenceResult]:
        """
        Perform AI inference work.
        
        Returns:
            InferenceResult for single inference or BatchInferenceResult for batch
        """
        try:
            # Initialize inference engine
            self.update_progress(5, "Initializing AI inference engine...")
            self._initialize_inference_engine()
            
            if self.batch_processing:
                return self._process_batch()
            else:
                return self._process_single()
                
        except Exception as e:
            raise WorkerError(f"AI inference failed: {str(e)}")
    
    def _initialize_inference_engine(self):
        """Initialize the inference engine with proper configuration."""
        try:
            self.inference_engine = InferenceEngine()
            
            # Set custom anomaly threshold if provided
            if self.anomaly_threshold is not None:
                self.inference_engine.set_anomaly_threshold(self.anomaly_threshold)
            
            # Ensure model is loaded
            if self.model_name:
                self.update_progress(10, f"Loading model: {self.model_name}...")
                self.inference_engine.ensure_model_loaded(self.model_name)
            else:
                self.update_progress(10, "Loading default model...")
                self.inference_engine.ensure_model_loaded()
                
            self.update_progress(15, "AI inference engine ready")
            
        except Exception as e:
            raise WorkerError(f"Failed to initialize inference engine: {str(e)}")
    
    def _process_single(self) -> InferenceResult:
        """Process a single inference request."""
        if len(self.dataset_inputs) != 1:
            raise WorkerError("Single processing mode but multiple inputs provided")
        
        dataset_input = self.dataset_inputs[0]
        
        self.update_progress(20, "Starting AI inference...")
        
        try:
            # Load dataset if needed
            if dataset_input['type'] == 'file':
                self.update_progress(25, f"Loading DICOM file: {Path(dataset_input['data']).name}")
                dataset = pydicom.dcmread(dataset_input['data'])
            else:
                dataset = dataset_input['data']
            
            # Check for cancellation
            if self.is_cancellation_requested():
                raise WorkerError("Inference cancelled by user")
            
            self.update_progress(40, "Running AI model inference...")
            
            # Run inference
            result = self.inference_engine.run_inference_from_dicom(
                dataset,
                model_name=self.model_name,
                preprocessing_preset=self.preprocessing_preset
            )
            
            # Check for cancellation after inference
            if self.is_cancellation_requested():
                raise WorkerError("Inference cancelled by user")
            
            self.update_progress(90, "Processing results...")
            
            # Track processing statistics
            self.increment_processed_count(1)
            
            self.update_progress(100, f"Inference completed: {result.confidence_score:.3f} confidence")
            
            return result
            
        except InferenceEngineError as e:
            self.increment_error_count(1)
            raise WorkerError(f"Inference engine error: {str(e)}")
        except Exception as e:
            self.increment_error_count(1)
            raise WorkerError(f"Inference processing error: {str(e)}")
    
    def _process_batch(self) -> BatchInferenceResult:
        """Process multiple inference requests."""
        total_items = len(self.dataset_inputs)
        
        self.update_progress(20, f"Starting batch inference ({total_items} items)...")
        
        # Prepare batch inputs
        batch_inputs = []
        
        for i, dataset_input in enumerate(self.dataset_inputs):
            # Check for cancellation
            if self.is_cancellation_requested():
                raise WorkerError("Batch inference cancelled by user")
            
            # Update progress for preparation
            prep_progress = 20 + int((i / total_items) * 20)  # 20-40% for preparation
            self.update_progress(prep_progress, f"Preparing input {i+1}/{total_items}...")
            
            try:
                if dataset_input['type'] == 'file':
                    # Load DICOM file
                    file_path = dataset_input['data']
                    dataset = pydicom.dcmread(file_path)
                    batch_inputs.append(dataset)
                else:
                    # Use dataset directly
                    batch_inputs.append(dataset_input['data'])
                    
            except Exception as e:
                logger.warning(f"Failed to prepare input {i+1}: {str(e)}")
                self.increment_error_count(1)
                # Continue with other inputs in batch mode
                continue
        
        if not batch_inputs:
            raise WorkerError("No valid inputs for batch processing")
        
        self.update_progress(40, f"Running batch inference on {len(batch_inputs)} valid inputs...")
        
        try:
            # Process batch - note: InferenceEngine.run_batch_inference expects file paths
            # So we need to handle this differently for mixed inputs
            individual_results = []
            
            for i, dataset in enumerate(batch_inputs):
                # Check for cancellation
                if self.is_cancellation_requested():
                    raise WorkerError("Batch inference cancelled by user")
                
                # Update progress for inference
                inference_progress = 40 + int((i / len(batch_inputs)) * 50)  # 40-90% for inference
                self.update_progress(inference_progress, f"Processing item {i+1}/{len(batch_inputs)}...")
                
                try:
                    # Run individual inference
                    result = self.inference_engine.run_inference_from_dicom(
                        dataset,
                        model_name=self.model_name,
                        preprocessing_preset=self.preprocessing_preset
                    )
                    individual_results.append(result)
                    self.increment_processed_count(1)
                    
                except Exception as e:
                    logger.warning(f"Failed to process item {i+1}: {str(e)}")
                    self.increment_error_count(1)
                    # Continue with other items
                    continue
            
            # Create batch result
            batch_result = BatchInferenceResult(
                individual_results=individual_results,
                batch_size=len(batch_inputs),
                total_processing_time_ms=0,  # Will be calculated by base class
                average_processing_time_ms=0,  # Will be calculated
                anomalies_detected=sum(1 for r in individual_results if r.anomaly_detected),
                success_count=len(individual_results),
                error_count=len(batch_inputs) - len(individual_results),
                errors=[]  # Individual errors already logged
            )
            
            # Calculate timing statistics
            if individual_results:
                total_time = sum(r.processing_time_ms for r in individual_results)
                batch_result.total_processing_time_ms = total_time
                batch_result.average_processing_time_ms = total_time / len(individual_results)
            
            self.update_progress(100, f"Batch completed: {len(individual_results)}/{len(batch_inputs)} successful")
            
            return batch_result
            
        except Exception as e:
            raise WorkerError(f"Batch inference processing error: {str(e)}")
    
    def get_inference_statistics(self) -> Dict[str, Any]:
        """Get inference-specific statistics."""
        base_stats = self.get_current_stats()
        
        inference_stats = {
            'model_name': self.model_name or 'default',
            'preprocessing_preset': self.preprocessing_preset,
            'anomaly_threshold': self.anomaly_threshold,
            'batch_processing': self.batch_processing,
            'total_inputs': len(self.dataset_inputs),
            'processing_mode': 'batch' if self.batch_processing else 'single'
        }
        
        # Add inference engine stats if available
        if self.inference_engine:
            try:
                engine_stats = self.inference_engine.get_performance_statistics()
                inference_stats['engine_stats'] = engine_stats
            except Exception:
                pass
        
        return {**base_stats, **inference_stats}
    
    def cleanup_resources(self) -> None:
        """Cleanup inference engine resources."""
        try:
            # Clear any cached results
            self._inference_results.clear()
            
            # Reset inference engine if needed
            if self.inference_engine:
                # The inference engine should handle its own cleanup
                pass
                
        except Exception as e:
            logger.warning(f"Error cleaning up inference worker resources: {e}") 